import { connect } from "react-redux";
import React, { useState, useRef, useEffect, forwardRef } from "react";
import {
    Badge, Button, DatePicker, Empty, Flex, Form, Input,
    Select, Spin, Tabs, TabsProps, Tag, Tooltip, Row, Col, Upload, List, message, Modal, Table
} from 'antd'
import { useTranslation } from 'react-i18next';
import SvgIcon from '@components/svgicon';
import RichTextEditor from '@/components/richTextEditor/index2';
import dayjs from 'dayjs';

import FilterSelect from "@/components/form/select";
import FormUtils from '@/views/common/formUtils'

import './index.scss'
import classNames from "classnames";
import fileDeleteIcon from '@/icons/png/icon_file_delete.png'
import { CalculateLength } from '@/views/common/formRule'
import { api_get_my_direct_departments } from "@/api/contacts";
import { api_save_notification, api_get_admin_notification_details, api_publish_notification, api_get_ann_manage_permission, api_upload_attachments } from '@/api/information'
import AnnPublish from '../publish'
import AnnO<PERSON>ateLog from './log/index'

const RowGutter = 40
const RowSpanHalf = 12
const RowSpanFull = 24
const FileMaxCountLimit = 10 // 附件个数
const FileSizeLimit = 20 // 附件大小 20M
const FileAccept = ".jpeg,.png,.pdf,.doc,.docx,.xlsx,.xls,.pptx,.ppt" // 附件格式
const DateFormat = 'YYYY-MM-DD HH:mm'

// 公告新增、编辑、详情
const AnnOperate = forwardRef((props: any, ref) => {
    const { t } = useTranslation();
    // const editorRef = useRef<any>(null)
    const { userInfo, activeCompanyId, activeCompanyName, isActiveCompanyAdmin, resource, onBack, onRefresh, globalLang } = props
    /*操作类型
    * create 新增 
    * edit 编辑
    * detail 详情
    * publishEdit 已发布-编辑 (仅“公告的有效期”字段可编辑)
    */
    const operateType = resource.type
    const isReadOnly = operateType === 'detail'
    const isPublishEdit = operateType === 'publishEdit'

    const [form] = Form.useForm(); // 新增表单
    const [formValues, setFormValues] = useState<any>({});
    const [details, setDetails] = useState<any>({}); // 详情
    const [isSubmiting, setIsSubmiting] = useState(false) // 是否提交中
    const [isUploading, setIsUploading] = useState(false) // 是否上传附件中
    const [isLoading, setIsLoading] = useState<any>(false);
    const [operationLogVisible, setOperationLogVisible] = useState<any>(false);
    const [publishResource, setPublishResource] = useState<any>({ visible: false, id: null }); // 发布

    const [fileList, setFileList] = useState([]) // 上传的文件
    const [uploadedFilelist, setUploadedFilelist] = useState<any>([]) // 附件列表数据
    const [behalfOfId, setBehalfOfId] = useState<any>(null) // 用户ID/部门ID/公司ID

    const [behalfOfOptions, setBehalfOfOptions] = useState<any>([
        {
            //个人机器人 
            value: 'PERSONAL',//在Admin平台配置了发布权限-个人权限
            label: t('info-hub.personal'),
            label2: 'info-hub.personal',
            icon: <SvgIcon svgName="icon_user" />,
            desc: 'info-hub.personal-tip',
            visible: false
        },
        {
            //公司机器人
            value: 'COMPANY', // 仅公司管理员创建时显示此选项，选项显示公司名
            label: activeCompanyName,
            icon: <SvgIcon svgName="icon_company" />,
            desc: 'info-hub.company-tip',
            visible: false
        }
    ]); // 发布代表选项

    const priorityOptions = [
        {
            value: 'URGENT',
            label: 'info-hub.critical'
        },
        {
            value: 'IMPORTANT',
            label: 'info-hub.important'
        },
        {
            value: 'NORMAL',
            label: 'info-hub.normal'
        }
    ] // 优先级选项
    const publishTypes = [
        {
            value: 'PUBLISH_AT',
            label: 'info-hub.publish-at' //定时发布
        },
        {
            value: 'PUBLISH_NOW',
            label: 'info-hub.publish-now' // 立即发布
        }
    ] // 发布类型

    const validityPeriodOptions = [
        {
            // 0-永久有效，1-指定时间后过期
            value: 'PERMANENT',
            label: 'info-hub.forever'
        },
        {
            value: 'TIMED',
            label: 'info-hub.validity-until'
        }
    ] // 发布类型

    const [departmentList, setDepartmentList] = useState<any>([]); // 当前用户 作为supervisor 的 直属部门 集合
    const [formItemDisabledStatus, setFormItemDisabledStatus] = useState(
        {
            title: true,
            behalfOfType: true,
            priority: true,
            content: true,
            publishType: true,
            publishAt: true,
            validityAt: true,
            validUntil: true,
            attachments: true
        }) // 表单项状态， 是否disable


    // 初始化
    useEffect(() => {
        // console.log('behalfOfOptions',behalfOfOptions)
        init()
    }, [])

    // 监听 resource 变化
    useEffect(() => {
        // console.log('resource change', resource)
        const { data } = resource
        setDetailFormValues(data)
    }, [resource])

    useEffect(() => {
        // console.log('operateType change', operateType)
        const statusResult: any = { ...formItemDisabledStatus }
        if (['create', 'edit', 'detail'].includes(operateType)) {
            Object.keys(statusResult).forEach(key => {
                statusResult[key] = operateType == 'detail'
            })
        } else if (isPublishEdit) {
            Object.keys(statusResult).forEach(key => {
                statusResult[key] = !['validUntil', 'validityAt'].includes(key)
            })
        }
        setFormItemDisabledStatus(statusResult)
    }, [operateType])

    // 监听编辑模式，发布者选中值是否 存在下拉框选项中
    useEffect(() => {
        if (operateType == 'edit') {
            // console.log('behalfOfOptions change',behalfOfOptions)
            const types = behalfOfOptions?.filter((i: any) => i.visible).map((i: any) => i.value)
            const behalfOfType = details.behalfOfType
            // console.log('details', details)
            if (!types.includes(behalfOfType)) {
                form.setFieldValue('behalfOfType', null)
                setBehalfOfId(null)
            } else {
                form.setFieldValue('behalfOfType', behalfOfType)
                setBehalfOfId(details.behalfOfId)
            }
        }
    }, [operateType, behalfOfOptions, form.getFieldValue('behalfOfType')])

    useEffect(() => {
        // console.log('globalLang',globalLang, behalfOfOptions)
        setBehalfOfOptions(behalfOfOptions.map((item: any) => {
            if (item.value === 'PERSONAL') { // 解决个人发布用户名
                item.label = t(item.label2)
            }
            return item
        }))
    }, [globalLang])

    const init = async () => {
        if (operateType !== 'create') {
            // 获取公告详情
            getDeails()
        }
        if (isReadOnly) return // 只读
        const permissions = await getAnnManagePermission()
        if (!permissions.notificationPermission) {
            // console.log('没有发布权限')
            setBehalfOfOptions([]) // 没有发布权限
            return
        }
        const behalfOfTypeList = permissions?.behalfOfTypeList || []
        const supervisorDepartments = await getSupervisorDepartments()
        // 设置发布代表选项
        const options = behalfOfOptions.map((i: any) => {
            if (i.value === 'PERSONAL') {
                i.visible = behalfOfTypeList.includes('PERSONAL') // 获取是否有个人发布公告的权限
            }
            if (i.value === 'COMPANY') {
                i.visible = isActiveCompanyAdmin || behalfOfTypeList.includes('COMPANY')
            }
            return i
        })
        if (supervisorDepartments && supervisorDepartments.length > 0) {
            supervisorDepartments.forEach((item: any) => {
                options.push(
                    {
                        //部门机器人 仅supervisor创建时显示此选项，选项显示部门名
                        value: `DEPARTMENT_${item.id}`, // 类型+部门id
                        label: item.name, //
                        icon: <SvgIcon svgName="menu_org" />,
                        desc: 'info-hub.depart-tip',
                        visible: true
                    }
                )
            });

        }
        // console.log('options', options)
        setBehalfOfOptions(options)
    }

    // 获取详情
    const getDeails = async () => {
        setIsLoading(true)
        const res: any = await api_get_admin_notification_details(resource?.data?.id).catch(() => {
            setIsLoading(false)
            return false
        })
        setIsLoading(false)
        if (!res) return false
        console.log('res', res)
        setDetailFormValues(res)
        return res
    }

    // 获取当前用户 发布者权限
    const getAnnManagePermission = async () => {
        const res: any = await api_get_ann_manage_permission().catch(() => {
        })
        if (!res) return []
        return res
    }

    // 获取当前用户是supervisor 的部门集合
    const getSupervisorDepartments = async () => {
        setIsLoading(true)
        const res: any = await api_get_my_direct_departments(null, true).catch(() => {
            setIsLoading(false)
        })
        setIsLoading(false)
        if (!res) return []
        const datas = res.data || []
        // console.log('datas', datas)
        setDepartmentList(datas.map((i: any) => ({ value: i.id, label: i.name })))
        return datas
    }

    // 编辑和查看详情，表单赋值
    const setDetailFormValues = (info: any) => {
        // title, content, behalfOfType, behalfOfId, priority, publishType, publishAt, validityAt, validUntil, attachments
        form.setFieldValue('title', info.title || '')
        form.setFieldValue('content', info.content || '')
        form.setFieldValue('publishType', info.publishTiming)
        form.setFieldValue('priority', info.priority)
        form.setFieldValue('validityAt', info.validityAt)
        if (info?.publishTiming === 'PUBLISH_AT') {
            const scheduledTime = info.scheduledTime ? dayjs(info.scheduledTime) : info.scheduledTime
            form.setFieldValue('publishAt', scheduledTime || '')
        }
        if (info?.validityAt === 'TIMED') {
            const expireTime = info.expireTime ? dayjs(info.expireTime) : info.expireTime
            form.setFieldValue('validUntil', expireTime || '')
        }
        let behalfOfType = info.behalfOfType
        if (isReadOnly) { // 只读
            const options = [{
                value: behalfOfType,
                label: info.behalfOfName,
                icon: null,
                desc: '',
                visible: true
            }]
            // console.log('options',options)
            setBehalfOfOptions(options)
        } else {
            if (behalfOfType === 'DEPARTMENT') { //处理部门选项
                behalfOfType = `DEPARTMENT_${info.behalfOfId}`
            }
        }
        // console.log('behalfOfType', behalfOfType)
        form.setFieldValue('behalfOfType', behalfOfType)
        info.behalfOfType = behalfOfType
        setBehalfOfId(info.behalfOfId)
        let attachments = info.attachments || []
        attachments = attachments.map((file: any) => {
            file.isUploaded = true //已经上传过
            return file
        })
        form.setFieldValue('attachments', attachments)
        setUploadedFilelist(attachments)
        // 转换为antd的格式
        const _fileList = attachments.map((file: any) => {
            file.uid = file.id
            file.type = file.fileType
            file.name = file.fileName
            file.size = file.fileSize
            file.isUploaded = true //已经上传过
            return file
        })
        setFileList(_fileList)
        setDetails(info)
    }

    // 表单项 输入改变事件
    const onFormValuesChange = (changedFields: any, allValues: any) => {
        try {
            // console.log('changedFields', changedFields)
            setFormValues({ ...formValues, ...allValues });
            Object.keys(changedFields).forEach((fieldName: any) => {
                const fieldError = form?.getFieldError(fieldName)
                if (fieldError.length > 0) FormUtils.clearValidate(form, fieldName)
            })
        } catch (error) {

        }
    }

    /*上传附件 */
    const fileItemRender = (originNode: any, file: any) => {
        return (
            <List.Item>
                <SvgIcon className="icon_attachment" svgName="attachment" />
                {originNode}
            </List.Item>
        );
    };

    // 附件上传事件
    const onChangeUpload = (e: any) => {
        const file = e.file;
        const fileList = e.fileList;

        setFileList(fileList)
        console.log('fileList', fileList)
        const currentFileList: object[] = fileList.map((item: any) => {
            return {
                "id": item.uid,
                "fileName": item.name,
                "filePath": null, //未请求批量上传，没有path
                "fileSize": item.size,
                "fileType": item.type,
                "isUploaded": item.isUploaded ? true : false
            }
        })
        setUploadedFilelist(currentFileList)
    }

    const onUploadCustomRequest = (e: any) => {
        const file = e.file;
        const isFileFormatValid = validateFileFormat(file)
        if (!isFileFormatValid) {
            e.onError("error");
            return;
        }
        e.onSuccess({ "status": "success" }, e);
    }

    // 文件大小、格式校验
    const validateFileFormat = (file: any): boolean => {
        let fileType = file.name.substring(file.name.lastIndexOf("."));
        fileType = fileType?.toLowerCase()
        const _fileAccept = FileAccept.split(',')
        // 格式
        const isFileFormatValid = _fileAccept.includes(fileType);
        // 大小
        const isfileSizeValid = file.size / 1024 / 1024 <= FileSizeLimit;
        let errorMessage = null;
        if (!isFileFormatValid) {
            errorMessage = t("robot-manage.document.document-type-error") + `${fileType}`
        } else if (!isfileSizeValid) {
            errorMessage = t('info-hub.file-exceeds') //`File Size Exceeds Limit（20M）`
        }
        if (errorMessage) {
            message.error(errorMessage);
        }
        return isFileFormatValid && isfileSizeValid;
    };

    const onBeforeFileUpload = (file: any, uploadFileList: any) => {
        const totalFileList = [...fileList, ...uploadFileList]
        const isFileFormatValid = validateFileFormat(file)
        // console.log('beforeFileUpload isFileFormatValid', isFileFormatValid)
        // 文件格式校验
        if (!isFileFormatValid) {
            return false || Upload.LIST_IGNORE
        }
        // 文件个数校验
        if (totalFileList?.length > FileMaxCountLimit) {
            message.destroy('FileCountExceed')
            message.open({ type: "error", content: t(["robot-manage.document.document-count-exceed"]), key: 'FileCountExceed' }) // 提示
        }
        let result = true
        // 已上传的文件个数等于上限，限制当前上传
        if (fileList?.length === FileMaxCountLimit) {
            result = false
        }
        return result || Upload.LIST_IGNORE
    }

    const onUpdateContent = (content: any, charCount: any, filesInfo: any) => {
        // console.log('content', content)
        // console.log('byteCount', byteCount)
        if ((charCount && charCount != 0) || (filesInfo && filesInfo.length > 0)) {
            form.setFieldValue('content', content)
        } else {
            form.setFieldValue('content', null)
        }
    }

    // 标题输入框 规则：限制200字符（1个中文字=2个字符）
    const titleFormRules = ([
        { required: !isPublishEdit, whitespace: true, message: t('common.input-required') as string },
        {
            whitespace: true,
            validator: async (_: any, value: any) => {
                // console.log('value',value)
                if (value != null && value !== undefined && value.length === 0) return Promise.resolve() // 有值才校验
                const length = CalculateLength(value);
                // console.log('length', length)
                if (length != 0 && (length > 200)) {
                    return Promise.reject(t('info-hub.rule.title'));
                }
                return Promise.resolve();
            }
        }
    ])

    // 表单校验
    const validForm = async () => {
        return new Promise<any>((resolve, reject) => {
            form.validateFields({ validateOnly: false })
                .then(() => {
                    // 必填校验通过后，
                    // 1.校验 点击发布的实际时间＞设置的发布时间
                    const { publishType, publishAt, validityAt, validUntil } = form.getFieldsValue()
                    const nowTime = Date.now()
                    // console.log('nowTime', nowTime)
                    if (!formItemDisabledStatus['publishType']&& publishType === 'PUBLISH_AT') { //设置了发布时间
                        let publishTime = dayjs(publishAt).valueOf()
                        // console.log('publishTime', publishTime)
                        if (nowTime > publishTime) {
                            message.error(t('info-hub.time-error'))
                            form.setFields([{ name: 'publishAt', errors: [''] }]) // 表单标红
                            return resolve(false)
                        }
                    }
                    // 2.校验 点击发布的实际时间＞设置的公告有效期
                    if (!formItemDisabledStatus['validityAt'] && validityAt === 'TIMED') { //设置了公告有效期
                        let validUntilTime = dayjs(validUntil).valueOf()
                        // console.log('validUntilTime', validUntilTime)
                        if (nowTime > validUntilTime) {
                            message.error(t('info-hub.time-error'))
                            form.setFields([{ name: 'validUntil', errors: [''] }]) // 表单标红
                            return resolve(false)
                        }
                    }

                    resolve(true)
                })
                .catch(() => {
                    resolve(false)
                });
        })
    }

    // 获取公告参数
    const getCommonParams = async (type = 'SAVE', audiences?: null) => {
        try {
            const values: any = form.getFieldsValue()
            const { title, content, behalfOfType, priority, publishType, publishAt, validityAt, validUntil, attachments } = values
            let p: any = {}
            if (isPublishEdit) { // 已发布状态只能修改 公告有效期
                p.validityAt = validityAt
                p.audiences = audiences
                if (validityAt === 'TIMED') {
                    const expireTime = dayjs(validUntil).valueOf()
                    p.expireTime = expireTime
                }
            } else {
                p = {
                    title,
                    content,
                    publishTiming: publishType,
                    behalfOfId,
                    priority,
                    validityAt, // 有效期类型
                }
                p.behalfOfType = behalfOfType
                if (behalfOfType?.indexOf('DEPARTMENT') !== -1) {
                    p.behalfOfType = 'DEPARTMENT'
                }
                if (publishType === 'PUBLISH_AT') {
                    const scheduledTime = dayjs(publishAt).valueOf()
                    p.scheduledTime = scheduledTime
                }
                if (validityAt === 'TIMED') {
                    const expireTime = dayjs(validUntil).valueOf()
                    p.expireTime = expireTime
                }
                if (type === 'PUBLISH') {
                    p.audiences = audiences
                }
            }
            if (operateType !== 'create') {
                p.id = resource?.data?.id
            }
            //  若有附件，先请求 上传附件
            if (!isPublishEdit) {
                let successRes: any = await batchUploadAttachments()
                // console.log('上传 isSuccess', successRes)
                if (successRes === false) return null
                p.attachments = [...uploadedFilelist.filter((item: any) => item.isUploaded), ...successRes]
            }
            console.log('----p', p)
            return p
        } catch (error) {
            console.log('error', error)
            return null
        }
    }

    // 保存
    const onSave = async () => {
        const isPass = await validForm().catch(() => { })
        // console.log('isPass', isPass)
        if (!isPass) return
        const p = await getCommonParams('SAVE')
        // console.log('p',p)
        if (!p) return
        // return
        setIsSubmiting(true)
        // return
        //  发起保存公告请求
        const res: any = await api_save_notification(p).catch(() => {
            setIsSubmiting(false)
        })
        setIsSubmiting(false)
        // console.log('res', res)
        if (res && res.code === 0) {
            const msg = operateType === 'create' ? t('company.create-success') : t('common.edit-success')
            message.success(msg)
            onRefresh && onRefresh('SAVE')
        }
    }

    // 发布
    const onPublish = async () => {
        const isPass = await validForm().catch(() => { })
        // console.log('isPass', isPass)
        if (!isPass) return
        if (isPublishEdit) {
            //  直接发布 已发布状态的公告，重新发布
            publishRequest()
        } else {
            // 选择可见人员->发布
            setPublishResource({ visible: true, id: null })
        }

    }

    // 发布公告前保存附件
    const batchUploadAttachments = async () => {
        // console.log('attachments', attachments)
        if (fileList && fileList.length > 0) {
            const attachments: any = fileList.filter((item: any) => !item.isUploaded).map((item: any) => item.originFileObj)
            // console.log('attachments', attachments)
            if (attachments.length == 0) return []
            const formData = new FormData();
            attachments.forEach((file: any) => {
                formData.append('files', file);
            });
            // const formData:any = [...attachments]
            setIsUploading(true)
            const res = await api_upload_attachments(formData).catch(() => {
                setIsUploading(false)
                return false
            })
            setIsUploading(false)
            if (!res) {
                message.error(t('info-hub.upload-error'))
                return false
            }
            return res
        } else {
            return []
        }
    }

    const publishRequest = async (audiences?: any) => {
        const p = await getCommonParams('PUBLISH', audiences)
        if (!p) return
        setIsSubmiting(true)
        const res: any = await api_publish_notification(p).catch(() => {
            setIsSubmiting(false)
        })
        setIsSubmiting(false)
        // console.log('res', res)
        if (res && res.code === 0) {
            const msg = t('info-hub.publish-success')
            message.success(msg)
            onRefresh && onRefresh('PUBLISH')
        }
    }

    // 返回
    const onBackPage = async () => {
        onBack && onBack()
    }

    // 取消发布
    const onPublishCancel = () => {
        setPublishResource({ visible: false, id: null })

    }

    //确认发布
    const onPublishSubmit = async(audiences: any) => {
        const isPass = await validForm().catch(() => { })
        // console.log('isPass', isPass)
        if (!isPass) return
        setPublishResource({ visible: false, id: null })
        publishRequest(audiences)
    }

    // 操作日志打开
    const onOpenOperationLog = () => {
        setOperationLogVisible(true)
    }

    // 操作日志关闭
    const onOperationLogClose = () => {
        setOperationLogVisible(false)
    }

    // 发布者选择
    const onChangeBehalfOfType = (type: any) => {
        // console.log('type', type)
        let behalfOfType = type
        let deptId = null
        if (type.indexOf("DEPARTMENT") !== -1) {
            const [_type, _deptId] = behalfOfType.split('_')
            behalfOfType = _type
            deptId = _deptId
        }
        // PERSONAL DEPARTMENT COMPANY
        let behalfOfId = null
        if (behalfOfType === 'PERSONAL') {
            behalfOfId = userInfo.userId  // 用户ID
        } else if (behalfOfType === 'DEPARTMENT') {
            behalfOfId = Number(deptId) // 部门ID
        } else if (behalfOfType === 'COMPANY') {
            behalfOfId = activeCompanyId // 公司ID
        }
        // console.log('behalfOfId', behalfOfId)
        setBehalfOfId(behalfOfId)
    }

    const getTitle = () => {
        return isReadOnly ? t('info-hub.ann-details') : t('info-hub.publish-ann')
    }

    // 删除附件
    const onDeleteAttachments = (file: any, fileIndex: number) => {
        const newUploadedFileList = [...uploadedFilelist]
        newUploadedFileList.splice(fileIndex, 1)
        setUploadedFilelist(newUploadedFileList)
        // console.log('fileList', fileList)
        const newFileList = fileList.filter((item: any) => item.id != file.id && item.uid != file.id)
        // console.log('newFileList', newFileList)
        setFileList(newFileList)
    };

    return (
        <Flex className="ann-operate-page common-wrap" vertical>
            <Flex className="wrap-nav" align="center" gap={4}>
                <div onClick={onBackPage} className="icon-back" ><SvgIcon svgName="icon_back" /></div>
                <span className="wn-title">{getTitle()}</span>
                <Flex className="wn-btns" gap={10}>
                    {
                        operateType === 'detail' ?
                            <Button
                                icon={<SvgIcon svgName="icon_upload_files" />}
                                color="default"
                                variant="filled" onClick={() => onOpenOperationLog()}>{t('info-hub.operation-log')}</Button>
                            :
                            <>
                                {
                                    isPublishEdit ?
                                        null :
                                        <Button
                                            color="default"
                                            icon={<SvgIcon svgName="save" />}
                                            variant="filled" onClick={() => onSave()}
                                            disabled={isSubmiting || isLoading}>{t('modal.save-ok')}</Button>
                                }

                                <Button type="primary" onClick={() => onPublish()}
                                    icon={<SvgIcon svgName="icon_publish" />}
                                    disabled={isSubmiting || isLoading}>{t('info-hub.publish')}</Button>
                            </>
                    }
                </Flex>
            </Flex>
            <div className="wrap-content">
                {/* {
                    isLoading ? <Spin className="full-spin" size="large" />
                        : */}
                <Form
                    name="basic"
                    layout="vertical"
                    form={form}
                    onValuesChange={onFormValuesChange}
                    validateTrigger="onBlur"
                    autoComplete="off"
                    requiredMark={true}
                    initialValues={
                        {
                            title: '',
                            behalfOfType: null,
                            priority: null,
                            content: null,
                            publishType: null,
                            publishAt: '',
                            validityAt: null,
                            validUntil: '',
                            attachments: []
                        }
                    }
                >
                    <Row gutter={RowGutter}>
                        <Col span={RowSpanFull}>
                            {/* Title标题 */}
                            <Form.Item label={t('info-hub.ann-title')} name={"title"} rules={titleFormRules}>
                                <Input disabled={formItemDisabledStatus['title']} placeholder={t('common.please-input') as string} variant="filled" allowClear maxLength={200} />
                            </Form.Item>
                        </Col>
                    </Row>
                    <Row gutter={RowGutter}>
                        <Col span={RowSpanHalf}>
                            {/* Behalf of */}
                            <Form.Item label={t('info-hub.behalf-of')} name="behalfOfType" rules={[{ required: !isPublishEdit, message: t('common.select-required') as string }]}>
                                <Select
                                    disabled={formItemDisabledStatus['behalfOfType']}
                                    options={behalfOfOptions.filter((i: any) => i.visible).map((i: any) => {
                                        // i.label = i.value == 'PERSONAL' ? t(i.label) : i.label
                                        return i
                                    })}
                                    optionRender={(option) => (
                                        <Flex className="behalf-of-selector-text" vertical gap={8}>
                                            <Flex gap={8}>
                                                {option.data.icon || null}
                                                {/* {option.data.label} */}
                                                <div>{option.data.value == 'PERSONAL' ? t(option.data.label as string) : option.data.label}</div>
                                                {/* <div>{option.data.label}</div> */}
                                            </Flex>
                                            {option.data.desc ? <div className="desc">{t(option.data.desc)}</div> : null}
                                        </Flex>
                                    )}
                                    onChange={onChangeBehalfOfType}
                                    placeholder={t('common.please-select') as string} variant="filled" suffixIcon={<SvgIcon svgName="icon_select_suffix" />}>
                                </Select>
                            </Form.Item>
                        </Col>
                        <Col span={RowSpanHalf}>
                            {/* Priority */}
                            <Form.Item label={t('info-hub.priority')} name="priority" rules={[{ required: !isPublishEdit, message: t('common.select-required') as string }]}>
                                <Select disabled={formItemDisabledStatus['priority']}
                                    options={priorityOptions.map(i => {
                                        i.label = t(i.label)
                                        return i
                                    })} placeholder={t('common.please-select') as string} variant="filled"
                                    suffixIcon={<SvgIcon svgName="icon_select_suffix" />}
                                >
                                </Select>
                            </Form.Item>
                        </Col>
                    </Row>
                    <Row gutter={RowGutter}>
                        <Col span={24}>
                            {/* Content */}
                            <Form.Item label={t('info-hub.content')} name="content" rules={[{ required: !isPublishEdit, message: t('common.input-required') as string }]}>
                                {/* <RichTextEditor2 onUpdate={onUpdateContent}/> */}
                                <RichTextEditor
                                    // ref={editorRef}
                                    // contents={operateType !== 'create' ? resource.content : ''}
                                    disabled={formItemDisabledStatus['content']}
                                    contents={form.getFieldValue('content')}
                                    onUpdate={onUpdateContent} />

                            </Form.Item>
                        </Col>
                    </Row>

                    <Row gutter={RowGutter}>
                        <Col span={RowSpanHalf}>
                            {/* 发布类型Publish Type */}
                            <Form.Item label={t('info-hub.publish-type')} name="publishType" rules={[{ required: !isPublishEdit, message: t('common.select-required') as string }]}>
                                <Select options={publishTypes.map(i => {
                                    i.label = t(i.label)
                                    return i
                                })}
                                    disabled={formItemDisabledStatus['publishType']} placeholder={t('common.please-select') as string}
                                    variant="filled" suffixIcon={<SvgIcon svgName="icon_select_suffix" />}
                                >
                                </Select>
                            </Form.Item>
                        </Col>
                        <Col span={RowSpanHalf}>
                            {/* 发布时间 Publish At */}
                            {/* 发布的实际时间＞设置的发布时间 */}
                            {
                                form.getFieldValue('publishType') != 'PUBLISH_NOW' ?

                                    <Form.Item
                                        label={t('info-hub.publish-time')}
                                        name="publishAt"
                                        // getValueProps={(value) => ({ value: value && dayjs(Number(value)) })}
                                        // normalize={(value) => value && `${dayjs(value).valueOf()}`}
                                        // getValueFromEvent={(date) => date ? date.format('YYYY-MM-DD') : null}
                                        // getValueProps={(value) => ({ value: value ? dayjs(value) : null })}
                                        rules={[{ required: !isPublishEdit, message: t('common.select-required') as string }]}>
                                        <DatePicker
                                            showTime
                                            format={DateFormat}
                                            variant="filled"
                                            onChange={(value, dateString) => {
                                                // console.log('Selected Time: ', value);
                                                console.log('Formatted Selected Time: ', dateString);
                                            }}
                                            disabled={formItemDisabledStatus['publishAt']}
                                            suffixIcon={<SvgIcon svgName="icon_datepicker" />}
                                        />
                                    </Form.Item>
                                    : null
                            }
                        </Col>
                    </Row>
                    <Row gutter={RowGutter}>
                        <Col span={RowSpanHalf}>
                            {/* 公告有效期 Validity Period */}
                            <Form.Item label={t('info-hub.validity-period')} name="validityAt" rules={[{ required: true, message: t('common.select-required') as string }]}>
                                <Select disabled={formItemDisabledStatus['validityAt']}
                                    options={validityPeriodOptions.map(i => {
                                        i.label = t(i.label)
                                        return i
                                    })}
                                    placeholder={t('common.please-select') as string}
                                    variant={isPublishEdit ? 'outlined' : "filled"} suffixIcon={<SvgIcon svgName="icon_select_suffix" />}
                                >
                                </Select>
                            </Form.Item>
                        </Col>
                        <Col span={RowSpanHalf}>
                            {/* 有效期至 Valid Until */}
                            {
                                form.getFieldValue('validityAt') != 'PERMANENT' ?

                                    <Form.Item
                                        label={t('info-hub.validity-until')}
                                        name="validUntil"
                                        shouldUpdate={true}
                                        // getValueProps={(value) => ({ value: value && dayjs(Number(value)) })}
                                        // normalize={(value) => value && `${dayjs(value).valueOf()}`}
                                        // getValueProps={(value) => ({
                                        //     value: value ? dayjs(value) : null,
                                        // })}
                                        rules={[{ required: true, message: t('common.select-required') as string }]}>
                                        <DatePicker
                                            showTime
                                            format={DateFormat}
                                            variant={isPublishEdit ? 'outlined' : "filled"}
                                            onChange={(value, dateString) => {
                                                // console.log('Selected Time: ', value.format(DateFormat));
                                                console.log('Formatted Selected Time: ', dateString);
                                                // form.setFieldValue('validUntil', dateString)
                                            }}
                                            disabled={formItemDisabledStatus['validUntil']}
                                            suffixIcon={<SvgIcon svgName="icon_datepicker" />}
                                        />
                                    </Form.Item>
                                    : null}
                        </Col>
                    </Row>

                    <Row gutter={RowGutter}>
                        <Col span={24}>
                            {/* Attachment */}
                            <Form.Item className={`${formItemDisabledStatus['attachments'] ? 'disabled' : ''}`} label={t('info-hub.attachments')} name="attachments">
                                <div>
                                    <Upload className={`mutil-upload-wrap`}
                                        multiple={true}
                                        accept={FileAccept}
                                        onChange={onChangeUpload}
                                        customRequest={onUploadCustomRequest}
                                        beforeUpload={onBeforeFileUpload}
                                        fileList={fileList}
                                        itemRender={fileItemRender} // 自定义渲染
                                        // showUploadList={
                                        //     {
                                        //         removeIcon: <img src={fileDeleteIcon} alt='' />
                                        //     }
                                        // }
                                        showUploadList={false}
                                        maxCount={FileMaxCountLimit}
                                        disabled={formItemDisabledStatus['attachments']}
                                    >
                                        {
                                            uploadedFilelist && uploadedFilelist?.length == FileMaxCountLimit ?
                                                null :
                                                (
                                                    !formItemDisabledStatus['attachments'] ? <Button
                                                        disabled={uploadedFilelist && uploadedFilelist?.length == FileMaxCountLimit}
                                                        className="ant-upload-trigger tertiary" color="default" variant="filled"
                                                        icon={<SvgIcon svgName="file_upload" />} >
                                                        {t("upload.upload-file")}
                                                    </Button> : null
                                                )
                                        }
                                    </Upload>
                                    {
                                        uploadedFilelist && uploadedFilelist.length > 0 ?
                                            <Flex className="ant-upload-list" vertical>
                                                {
                                                    uploadedFilelist.map((file: any, fileIndex: number) => (
                                                        <Flex key={file.id || file.uid} className="ant-upload-list-item" gap={8} align="center">
                                                            <SvgIcon className="attachment-item-file-icon" svgName="icon_attachment" />
                                                            <div className="">{file.fileName}</div>
                                                            <img onClick={() => onDeleteAttachments(file, fileIndex)} className="attachment-item-remove-icon" src={fileDeleteIcon} alt='' />
                                                        </Flex>
                                                    ))
                                                }
                                            </Flex>
                                            :
                                            null
                                    }
                                    {
                                        formItemDisabledStatus['attachments'] && uploadedFilelist && uploadedFilelist.length == 0 ?
                                            <div className="no-data">{t('info-hub.no-attachment')}</div>
                                            : null
                                    }
                                </div>
                            </Form.Item>
                        </Col>
                    </Row>

                </Form>
                {/* } */}
            </div>
            {
                // 发布弹窗
                publishResource.visible ?
                    <AnnPublish visible={publishResource.visible} onClose={onPublishCancel} onSumit={onPublishSubmit}
                        resource={publishResource} /> : null
            }
            { // 操作日志弹窗
                operationLogVisible ?
                    <AnnOperateLog nid={resource?.data?.id} onClose={onOperationLogClose} />
                    : null
            }
            <Spin spinning={isUploading || isSubmiting} fullscreen />
        </Flex>
    )
})


const mapStateToProps = (state: any) => ({
    globalLang: state.app.globalLang,
    userInfo: state.app.userInfo,
    companies: state.app.companies,
    activeCompanyId: state.app.activeCompanyId,
    activeCompanyName: state.app.activeCompanyName,
    isActiveCompanyAdmin: state.app.isActiveCompanyAdmin,
})
const mapDispatchToProps = (dispatch: any) => ({
    // dispatch_api_: (data:any) => dispatch(dispatch_api_(data))
})
const ConnectedCounter = connect(mapStateToProps, mapDispatchToProps, null, { forwardRef: true })(AnnOperate);
export default ConnectedCounter;