import { connect } from "react-redux";
import React, { useState, useRef, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Button, Flex, Popover, Select, Spin } from 'antd'
import { useTranslation } from 'react-i18next';
import UserAvatar from "@/components/userAvatar";
import groupDefaultAvatar from '@/icons/png/chats/icon_group_default_avatar.png';
import { useOpenIM } from '@/contexts/OpenIMContext';
import SvgIcon from '@/components/svgicon'
import { api_all_users_profile } from '@/api/chat'

import './index.scss'

const MyGroups = (props: any) => {
    const { t } = useTranslation();
    const { service: ImService } = useOpenIM();
    const navigate = useNavigate();

    const { userInfo, activeCompanyId } = props
    const currentUserId = String(userInfo.imUserId)
    const [groupDatas, setGroupDatas] = useState<any>([]) // 所有群组数据
    const [groupType, setGroupType] = useState<any>('created') // 类型筛选，默认我创建的
    const [loading, setLoading] = useState(false)
    const [selectGroupMembers, setSelectGroupMembers] = useState<any>([]) // 选中群主的群员信息
    const [selectShowGroupMembers, setShowSelectGroupMembers] = useState<any>([]) // 选中群主的群员信息
    const [usersInfoMap, setUsersInfoMap] = useState<any>({}) // 已有的用户信息映射

    useEffect(() => {
        if (ImService) getGroupList()
    }, [ImService])

    const getGroupList = async (_groupType = groupType) => {
        let list = await ImService.getJoinedGroupList()
        // console.log('res',list)
        if (!list) return
        if (groupType) {

        }
        list = list.filter((item: any) => _groupType === 'created' ? item.creatorUserID === currentUserId : item.creatorUserID !== currentUserId)
        // console.log('list',list)
        setGroupDatas(list)
    }

    // 获取用户信息Map
    async function getAllUsersProfile() {
        if (Object.keys(usersInfoMap).length > 0) return usersInfoMap
        const res: any = await api_all_users_profile({ currentTenantId: activeCompanyId }).catch((err: any) => {
            return usersInfoMap
        })
        if (!res) return usersInfoMap
        const infoMap = { ...res }
        setUsersInfoMap(infoMap)
        // console.log('infoMap', infoMap)
        return infoMap
    }


    const onChangeGroupType = (value: any) => {
        setGroupType(value)
        getGroupList(value)
    }

    // 查看详情
    const onOpenDetail = async (isOpen: any, item: any) => {
        // console.log('isOpen', isOpen)
        // console.log('usere', user)
        if (isOpen) {
            setLoading(true)
            // 获取用户信息Map
            const _usersInfoMap = await getAllUsersProfile()
            // 获取群成员
            let list = await ImService.getGroupMemberList(item.groupID, 0, 15).catch(() => {
                setLoading(false)
            })
            setLoading(false)
            list = list.map((item: any) => {
                const target =  _usersInfoMap[item.userID]
                // 获取用户名
                item.userName = target?.userName || item.nickname
                // 获取用户头像
                item.avatar = target?.avatar
                return item
            })
            // list = list.slice(0,14) // test
            // console.log('list', list)
            const showList = list.length>14? list.slice(0,13) :list
            setShowSelectGroupMembers(showList)
            setSelectGroupMembers(list)
        }
    }

    const onSendMessage = async (item:any) => {
        // 跳转至聊天，携带群组ID 参数
        const groupID = item.groupID
        // console.log('groupID',groupID)
        navigate("/chats",{state: { groupID }});
    }

    // 渲染弹窗详情
    const renderDeails = (item: any) => {
        let showMemberList: any = selectShowGroupMembers

        return (
            <Flex className="my-contact-group-details" vertical>
                <Flex className="top" gap={8} align="center">
                    <img className="avatar" src={groupDefaultAvatar} alt="" />
                    <div className="name">{item.groupName}</div>
                </Flex>
                <Flex className="bottom" vertical>
                    <Flex className="title" align="center">
                        <div>{t('chat.group-members')}</div>
                        <div>({selectShowGroupMembers.length || item.memberCount})</div>
                    </Flex>
                    {
                        loading ?
                            <div style={{ minHeight: '156px' }}>
                                <Spin className="full-spin" size="large" />
                            </div>
                            :
                            <Flex wrap gap={'12px 10px'}>
                                {
                                    showMemberList && showMemberList.map((item: any,index:number) =>
                                        <UserAvatar key={item.userID+''+index} name={item.userName} src={item?.avatar} userId={item.userID}/>
                                )}
                                {
                                    selectGroupMembers?.length> 14?
                                    <Flex className="more" justify="center" align="center">
                                        <SvgIcon svgName="more_dot" />
                                    </Flex>
                                    :null
                                }
                            </Flex>
                    }
                    <Button onClick={()=>onSendMessage(item)} className="tertiary" color="default" variant="filled" icon={<SvgIcon svgName="icon_send_message" />}>{t('chat.send-messages')}</Button>
                </Flex>
            </Flex>
        )
    }

    return (
        <Flex className='my-contact-area contacts-area' vertical>
            <Flex className="area-title" gap={20} justify="space-between">
                <div>{t('contacts.my-groups')}</div>
                <Select value={groupType} onChange={onChangeGroupType} placeholder={t('common.please-select') as string} variant="filled" suffixIcon={<SvgIcon svgName="icon_select_suffix" />}
                    options={[
                        {
                            value: 'created',
                            label: t('contacts.i-created')
                        },
                        {
                            value: 'joined',
                            label: t('contacts.i-joined')
                        }
                    ]}
                >

                </Select>
            </Flex>
            <Flex className="lists-con" vertical gap={12}>
                {groupDatas && groupDatas.map((item: any, index: number) => (
                    <Flex key={item.groupID + '' + index} >
                        <Popover onOpenChange={(e) => onOpenDetail(e, item)}
                            placement="right" arrow={false} overlayClassName="contacts-group-info-popover"
                            trigger='click'
                            content={renderDeails(item)}>
                            <Flex className="list-item" align="center" gap={8}>
                                <img className="ui-avatar" src={groupDefaultAvatar} alt="" />
                                <div className="ui-name-count">
                                    <div className="ui-name">{item.groupName}</div>
                                    <div className="ui-count">{t('contacts.group-members', { count: item.memberCount, unit: item.memberCount && item.memberCount > 1 ? 's' : '' })}</div>
                                </div>
                            </Flex>
                        </Popover>
                    </Flex>
                ))}
            </Flex>
        </Flex>
    )
}

const mapStateToProps = (state: any) => ({
    userInfo: state.app.userInfo,
    activeCompanyId: state.app.activeCompanyId,
});

const mapDispatchToProps = (dispatch: any) => ({
})

export default connect(mapStateToProps, mapDispatchToProps)(MyGroups);
