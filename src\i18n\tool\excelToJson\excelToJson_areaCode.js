/**
 * Excel文件生成JSON文件
 * 1、生成执行命令：node excelToJson_areaCode.js
 */
 const exceljs = require('exceljs');
 const fs = require('fs');
 const path = require('path');
 
 const workbook = new exceljs.Workbook();
 workbook.xlsx.readFile('../jsonToExcel/excels/areaCode.xlsx')
   .then(function () {
     var worksheet = workbook.getWorksheet("Sheet1");
     let zhJson = {}
     let hkJson = {}
     let enJson = {}
 
     // 遍历所有行
     try {
       const zhData = []
       const hkData = []
       const enData = []
       worksheet.eachRow((row, rowNumber) => {
         if (rowNumber == 1) return
         const key = getCellValue(row.getCell(1))// 获取特定单元格的内容作为key, 如 app.cacel
         const zhvalue = getCellValue(row.getCell(2))// 获取简体中文
         const hkvalue = getCellValue(row.getCell(3))// 获取繁体中文
         const envalue = getCellValue(row.getCell(4))// 获取英文
        //  const envalueUpdate = getCellValue(row.getCell(5))// 获取英文调整
        //  const zhvalueUpdate = getCellValue(row.getCell(6))// 获取简体中文调整
        //  const hkvalueUpdate = getCellValue(row.getCell(7))// 获取繁体中文调整
 
         if (key) {
           zhData.push({ key: key, value: zhvalue})
           hkData.push({ key: key, value: hkvalue })
           enData.push({ key: key, value: envalue })
         }
       });
    //    console.log('enData', enData)
       zhJson = generateObject(zhData)
       hkJson = generateObject(hkData)
       enJson = generateObject(enData)
       // console.log("---zhData:", zhData)
       // console.log("---zhJson:", zhJson)
       // console.log("---hkJson:", hkJson)
       // console.log("---enJson:", enJson)
       writeJsonFile(zhJson, "zh.json")
       writeJsonFile(hkJson, "hk.json")
       writeJsonFile(enJson, "en.json")
     } catch (error) {
       console.log("happened error", error)
     }
   });
 
 function getCellValue(cell){
   let value = cell.value ? cell.value.toString() : ''; // 确保 value 是字符串;
   if (cell.type === exceljs.ValueType.RichText) {
     // 如果是富文本类型，提取纯文本
     value = cell.value.richText.map(part => part.text).join('');
   }  
   // 去除前后空格符号和换行符
   const cleanedValue = value.replace(/[\s\n\r]+/g, ' ').trim();
   return cleanedValue
 }
 
 // 根据给定的路径字符串比如"app.cancel"，可以使用JavaScript生成对应的JSON对象
 function generateObject(data) {
 
   const result = data.reduce((acc, curr) => {
     const keys = curr.key.split('.');
     let obj = acc;
 
     for (let i = 0; i < keys.length; i++) {
       const key = keys[i];
       if (i === keys.length - 1) {
         obj[key] = curr.value;
       } else {
         if (!obj[key]) {
           obj[key] = {};
         }
         obj = obj[key];
       }
     }
 
     return acc;
   }, {});
   return result
 }
 
 // 定义一个JSON字符串
 function writeJsonFile(string, filename) {
   const jsonString = JSON.stringify(string, null, 2); // null, 2用于格式化输出（缩进）
 
   // 指定要保存的文件名
   const fileName = filename || 'output.json';
   // 指定上上上一层目录的路径
   const grandparentDirPath = path.join(__dirname, '..', '..'); // '..' 表示上一级目录
   const filePath = path.join(grandparentDirPath, 'locales','areaCode', fileName); // 在上上一级目录的 locales 文件夹中创建文件
 
   // 使用fs模块将JSON字符串写入文件
   fs.writeFile(filePath, jsonString, (err) => {
     if (err) {
       console.error('Error writing to file', err);
     } else {
       console.log(`${filePath} has been saved successfully!`);
     }
   });
 }