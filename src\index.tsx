import React from 'react';
import ReactDOM from 'react-dom/client';
import "./i18n";
import "antd/dist/reset.css";
import "./assets/fontcss/iconfont.css";
import "./styles/index.scss";
import './icons'; // 导入所有 SVG 图标
import App from './App';
import reportWebVitals from './reportWebVitals';
import { disableReactDevTools } from '@fvilers/disable-react-devtools';
import { OpenIMProvider } from './contexts/OpenIMContext';
import store from "./store";

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);
// 生产环境禁用react developer tools
if (process.env.NODE_ENV === 'production') {
  // try {
  //   // 手动覆盖全局钩子
  //   Object.defineProperty(window, '__REACT_DEVTOOLS_GLOBAL_HOOK__', {
  //     get: () => null,
  //     set: () => {
  //       // 忽略设置操作
  //     },
  //     configurable: false,
  //   });
  // } catch (error) {
  //   console.warn('Could not disable React DevTools:', error);
  // }
  disableReactDevTools()
}
window.addEventListener('error', (event) => {
  console.error('捕获到全局错误:', event.error);
  // 这里可以调用错误上报服务
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('捕获到未处理的 Promise 拒绝:', event.reason);
  // 这里可以调用错误上报服务
});
// root.render(
//   <React.StrictMode>
//     <App />
//   </React.StrictMode>
// );
const noOpenImPath = ['/widget-bot']
const currentPath = window.location.pathname
// console.log('widget-bot', currentPath)
const noOpenImPaths = ['/widget-bot']
if (noOpenImPaths.includes(currentPath)){
  // console.log('000000000000000')
  root.render(
    <App />
  );
} else{
  // console.log('111111')
  root.render(
    <OpenIMProvider store={store}>
      <App />
    </OpenIMProvider>
  );
}



// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
