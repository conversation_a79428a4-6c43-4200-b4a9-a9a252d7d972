@import '../../../styles/mixin.scss';
.my-contact-area{
    .area-title{
        margin-bottom: 16px;
    }
    .lists-con{
        padding: 16px;
        flex: 1;
        overflow: auto;
        .user-list{
            flex: 1;
            // overflow: hidden;
        }
    }
    .list-item{
        margin-right: 20px;
        max-width: calc(100% - 20px);
        cursor: pointer;
        .ui-avatar{
            flex-shrink: 0;
            width: 32px;
            height: 32px;
            border-radius: 10px;
            border: 1px solid rgba(0, 0, 0, 0.02);
        }
        .ui-name-count{
            flex:1;
            overflow: hidden;
            // max-width: 700px;
            @include font($line-height:22px,$font-weight:500);
        }
        .ui-name{
            max-width: 100%;
            @include ellipsis-multiline();
        }
        .ui-count{
            @include font(12px,18px,400,var(--colorTextSecondary));
        }
    }
    .ant-select{
        width: 200px;
    }
}
.contacts-group-info-popover{
    .ant-popover-inner{
        padding: 0;
    }
    
    .my-contact-group-details{
        width: 330px;
        height: 330px;
        padding: 20px;
        .avatar{
            width: 48px;
            height: auto;
            border-radius: 10px;
            border: 1px solid rgba(0, 0, 0, 0.02);
        }
        .ant-btn{
            margin-top: auto;
        }
        .top{
            @include font($line-height:22px,$font-weight:500);
            width: 100%;
            overflow: hidden;
            padding-bottom: 16px;
            border-bottom: 1px solid var(--colorBorderSecondary);
            margin-bottom: 16px;
            .name{
                max-width: 100%;
                @include ellipsis();
            }
        }
        .bottom{
            flex: 1;
            .title{
                @include font(12px,18px,600);
                word-wrap: break-word;
                margin-bottom: 16px;
            }
        }
        .more{
            font-size: 16px;
            border-radius: 50%;
            background: var(--colorFillTertiary);
            width: 32px;
            height: 32px;
            border: 1px solid var(--colorBorderSecondary);
        }
    }
}