@import "../../styles/mixin.scss";
.invite-container {
    width: 100%;
    height: 100%;
    .invite-error {
        width: 100%;
        height: 100%;
    }
    .invite-error-text {
        padding: 32px 16px;
        @include font(16px, 24px, 500);
        border-radius: 12px;
        background: var(--colorFillTertiary);
    }
    .invite-error-desc {
        @include font(14px, 22px, 400);
        text-align: center;
    }
    &.has-error {
        .login-center {
            width: 446px;
            height: auto;
            padding: 0;
        }
        .right {
            padding: 40px;
        }
    }
    .login-wrapper .public-login.content{
       justify-content: flex-start;
    }
}

.invite-info {
    padding-top: 46px;
    max-width: 370px;
    overflow: hidden;
    .join-content,
    .join-info {
        max-width: 100%;
        @include ellipsis();
    }
    .join-content {
        @include font(16px, 24px, 500);
    }
    .join-info {
        @include font(14px, 22px, 400, var(--colorTextSecondary));
        @include ellipsis();
    }
}
.logined-user-info {
    width: 370px;
    padding-top: 64px;
    .lu-title {
        @include font(14px, 22px, 400);
    }
    .lu-user {
        border-radius: 8px;
        background: var(--colorFillQuaternary);
        padding: 17px 24px;
        .lu-user-text {
            @include font(18px, 24px, 600);
            > div:last-child {
                @include font(14px, 22px, 400, var(--colorTextTertiary));
            }
        }
    }
}
