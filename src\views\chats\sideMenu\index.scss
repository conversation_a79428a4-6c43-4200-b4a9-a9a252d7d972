@import "../../../styles/mixin.scss";
@mixin flex($flex-direction:row,$align-items:center, $justify-content: normal){
    display: flex;
    flex-direction: $flex-direction;
    align-items: $align-items;
    justify-content: $justify-content;
}
.side-menu{
    @include flex($flex-direction:column);
    width: 280px;
    background: var(--colorBgNav);
    height: 100%;
    .no-data{
        @include font(12px,18px,400,var(--colorTextQuaternary));
        height: 60px;
    }
}
$align-space:16px;
.side-menu-title{
    width: 100%;
    flex-shrink: 0;
    @include flex($justify-content:space-between);
    @include font(18px,24px,600);
    padding: 19px $align-space;
    span{
        margin-right: 4px;
    }
    .anticon{
        font-size: 16px;
        cursor: pointer;
    }
}
.menu-item-container{
    position: relative;
    padding: 0 $align-space;
    width: 100%;
    flex: 1;
    overflow-y: auto;
}
.menu-item-title{
    position: sticky;
    height: 36px;
    bottom: 0;
    z-index: 555;
    @include flex($justify-content:space-between);
    @include font(12px,18px,600);
    padding: 10px 0;
    // margin-top:10px;
    cursor: pointer;
    background: var(--colorBgNav);

    .toggle-icon{
        font-size: 16px;
        transition: transform 0.2s ease-in-out;
        &.close{
            transform: rotate(180deg);
        }
    }
    &.user{
        bottom: auto;
        top: 0;
    }
    
    &.active.no-toggle{
        background: var(--colorPrimary);
        .menu-item-title-text,.menu-item-title-text .svg-icon{
            color: var(--colorPrimaryContrasting);
        }
        border-radius: 12px;
    }
    
    &.no-toggle{
        padding-left: 8px;
        .menu-item-title-text{
            display: flex;
            align-items: center;
            gap: 8px;
            @include font(14px,22px,500);
        }
    }
}
.menu-item-title-text{
    .svg-icon{
        font-size: 16px;
        color:var(--colorIcon)
    }
}
.menut-item-content{
    @include flex;
    padding: 6px 8px;
    @include font(14px,22px,500);
    margin-bottom: 6px;
    cursor: pointer;
    &.home-bot{
        width: calc(100% - $align-space * 2);
        flex-shrink: 0;
        font-weight: 600;
        font-size: 14px;
        line-height: 22px;
        padding: 4px 8px;
        margin: 0 auto 12px;
    }
    &:hover{
        background: var(--colorFillQuaternary);
        border-radius: 12px;
    }
    &.active{
        background: var(--colorPrimary);
        color: var(--colorPrimaryContrasting);
        border-radius: 12px;
    }
    .svg-icon{
        font-size: 16px;
        margin-right: 10px;
        flex-shrink: 0;
    }
    span{
        flex: 1;
        @include ellipsis-multiline(1)
    }
}
.menut-item-list{
    height: auto;
    display: block;
    transition: all 0.5s ease;
    max-height: auto; 
    &.is-close{
        display: none;
        height: 0;
        max-height: 0; 
    }
}
.user-menu{
    flex-shrink: 0;
}