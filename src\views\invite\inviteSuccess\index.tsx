import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { <PERSON>lex, <PERSON><PERSON>, Spin } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';
import { connect } from "react-redux";
import { loginedDefaultPath } from '@/routes'
import { api_get_dept_invitation } from '@/api/user'
import Avatar from '@/components/avatar'
import Layout from '@/views/login/layout'
import {refreshTheme} from '@/theme'

import SvgIcon from '@components/svgicon'; // 根据实际路径引入
import './index.scss'


/* 邀请成功*/
const InviteSuccess = (props: any) => {
    const navigate = useNavigate();
    const { t, i18n } = useTranslation();
    const location = useLocation();
    const searchParams = new URLSearchParams(location.search);
    const inviteCode = searchParams.get('code') || ''; // 获取查询参数  

    const { isLogin } = props;

    const [inviteData, setInviteData] = useState<any>({}); // 邀请详情
    const [isLoading, setIsLoading] = useState(true) // 加载中

    // 初始化
    useEffect(() => {
        // 根据邀请code获取详情
        if (inviteCode) {
            getInvitorInfo()
        }
    }, [])

    // 获取邀请详情
    const getInvitorInfo = async () => {
        setIsLoading(true)
        const res: any = await api_get_dept_invitation(inviteCode).catch((err: any) => {
            setIsLoading(false)
            // setIsQuerySuccess(false)
        })
        if (!res) {
            getThemeConfig(true)
            return false
        }
        getThemeConfig(false, res.data?.tenantId)
        setIsLoading(false)
        setInviteData(res?.data)
        return true
    }

    // 获取邀请企业主题配置
    const getThemeConfig = async (isDefault = false, tenantId?: any) => {
        refreshTheme(isDefault, false, tenantId)
    }

    const onToVama = () => {
        if (isLogin) {
            navigate(loginedDefaultPath, { replace: true })
        }

    }

    // 渲染 邀请信息
    const renderInviteInfo = () => {
        return (
            <Flex className='invite-info' vertical align='center' style={{ margin: '0 auto' }} gap={4}>
                <Avatar name={inviteData?.invitorName} src={inviteData?.avatar} />
                <div className='join-content'>{t('login.joinContent', { userName: inviteData?.invitorName })}</div>
                <div className='join-info'>{inviteData?.companyName}</div>
                <div className='join-info'>{inviteData?.deptNames}</div>
            </Flex>
        )
    }

    return (
        <Flex vertical className={`invite-success-container`} align="center" justify="center">
            <Layout isInvite={true} hideCarousel={true}>
                {
                    !isLoading ?
                        <>
                            <Flex className="invite-detail" vertical gap={40}>
                                {renderInviteInfo()}
                                <Flex className="invite-detail-text" vertical gap={8} align="center">
                                    <Flex gap={12} align="center">
                                        <SvgIcon className="icon" svgName="icon_success" />
                                        <div>{t('company.apply-submitted')}</div>
                                    </Flex>
                                    <div className="invite-detail-desc">{t('company.apply-submitted-tip')}</div>
                                    {isLogin ? <Button type="primary" onClick={onToVama}>{t('invite.to-vama')}</Button> : null}
                                </Flex>
                            </Flex>
                        </> : <Spin className="modal-spin" size="large" indicator={<LoadingOutlined style={{ fontSize: 48 }} spin />} />
                }
            </Layout>
        </Flex>
    )
}

const mapStateToProps = (state: any) => ({
    isLogin: state.app.isLogin,
})

const mapDispatchToProps = (dispatch: any) => ({
})

export default connect(mapStateToProps, mapDispatchToProps)(InviteSuccess);
// export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(Login));
