import React from "react";
import { connect } from "react-redux";
import i18next from "i18next";
import classNames from "classnames";
import Svgicon from "@/components/svgicon"
import { Flex, Button, Spin, message, Empty, Dropdown, Input } from "antd";
import type { MenuProps } from 'antd';
import Detail from './modal/detail'

import { api_query_embedding_chunk_List_params } from "@/api/robotManage";
import { dispatch_api_query_embedding_chunk_List } from "@/store/api"

import './index.scss'
import '../common/style/index.scss'
import { AnyMxRecord } from "dns";

interface embeddingChunkListFace {
  chunkId: string,
  documentId: string,
  chunkText: string,
  documentName: string,
  documentPath: string,
  length?: any,
  tokenCount?: AnyMxRecord
}

interface ContextState {
  robotId: string | null,
  keyWord: string,
  embeddingChunkList: embeddingChunkListFace[],
  detailModal: {
    visible: boolean,
    resource: embeddingChunkListFace
  },
  isLoading: boolean
}

// 基本信息-机器人知识
class RobotSettingEmbedding extends React.Component<any, ContextState> {
  constructor(props: any) {
    super(props);
    this.state = {
      robotId: "",
      keyWord: '',
      embeddingChunkList: [],
      detailModal: { // 编辑文档
        visible: false,
        resource: {
          chunkId: "",
          documentId: "",
          chunkText: "",
          documentName: "",
          documentPath: ""
        }
      },
      isLoading: false
    }
  };

  // 显示详情弹窗
  handleIsShowDetailModal = (record: any) => {
    const { detailModal } = this.state
    this.setState({ detailModal: { ...detailModal, resource: record, visible: true } })
  }
  // 关闭 详情弹窗
  handleCloseDetailModal = () => {
    this.setState({ detailModal: { ...this.state.detailModal, visible: false } })
  }

  // 关键字输入框
  handlekeywordInputChange = (e: any) => {
    console.log('e.target.value', e.target.value)
    this.setState({ keyWord: e.target.value })
  }

  // 重置
  handleResetParams = () => {
    this.setState({ embeddingChunkList: [],keyWord: "" })
  }

  // 查询
  handleQueryEmbeddingChunk = () => {
    const { keyWord, robotId } = this.state;
    const { userInfo } = this.props;

    if (!keyWord) {
      return message.open({ type: "error", content: i18next.t(["robot-manage.chat-debug.search-notice"]) || "" })
    }


    const data = {
      ...api_query_embedding_chunk_List_params,
      account: userInfo.account,
      robotId,
      text: keyWord
    }

    this.setState({ isLoading: true })
    this.props.dispatch_api_query_embedding_chunk_List(data).then((result: any) => {
      this.setState({ isLoading: false, embeddingChunkList: result.embeddingChunkList })
    }).catch((err: any) => {
      this.setState({ isLoading: false })
      console.log(err)
    })
  }

  componentDidMount() {
    const params = new URLSearchParams(window.location.search);
    const robotId = params.get("robotid");
    this.setState({ robotId })
  }

  componentWillUnmount() {

  }

  // 更多菜单
  renderMoreIcon(d: embeddingChunkListFace) {
    const items: MenuProps['items'] = [
      {
        key: '1',
        label: `${i18next.t("robot-manage.chat-debug.content-count")} ${d.length}`,
        icon: <Svgicon svgName="text" />
      },
      {
        key: '2',
        label: `${i18next.t("robot-manage.chat-debug.hit-count")} ${d.tokenCount}`,
        icon: <Svgicon svgName="hit" />
      },
      // {
      //   key: '3',
      //   label: i18next.t("robot-manage.chat-debug.token"),
      //   icon: <Svgicon svgName="token" />
      // },
    ];

    return (
      <Dropdown menu={{ items }} placement="bottom" overlayClassName="robot-embedding-more-dropdown">
        <Flex className="ct-more" align="center" justify="center">
          <Svgicon svgName="more_dot" />
        </Flex>
      </Dropdown>
    )
  }

  render() {
    const { isLoading, keyWord, embeddingChunkList, detailModal } = this.state;

    return (
      // common-wrap gray-bg
      <Flex className={"robot-embedding-wrap common-wrap gray-bg"} vertical>
        <Flex className="wrap-nav" align="center">
          <span className="wn-title">{i18next.t('robot-manage.embedding-search')}</span>
        </Flex>
        {/* 筛选 */}
        <Flex className="wrap-filters" gap={16}>
          <Input className="filter-input" value={keyWord} variant="filled"
            placeholder={i18next.t("robot-manage.chat-debug.search-hint") || ""}
            onChange={this.handlekeywordInputChange}
            onPressEnter={this.handleQueryEmbeddingChunk}
            prefix={<Svgicon svgName="search" />}
          />
          <Button type="primary" disabled={isLoading}onClick={this.handleQueryEmbeddingChunk}>{i18next.t(["common.query"]) || ""}</Button>
          <Button color="default" variant="filled" onClick={this.handleResetParams}>{i18next.t(["common.reset"]) || ""}</Button>
        </Flex>
        <div className="wrap-content">
          {
            isLoading?
            <Spin className="full-spin" size="large"/>:
            <>
              {
                embeddingChunkList?.length > 0 ?
                  <Flex className="cards" wrap gap={20}>
                    {
                      embeddingChunkList.map((item: any,index) => {
                        return (
                          <div className="card common-card no-hover" key={item.documentId+''+index} onClick={() => this.handleIsShowDetailModal(item)}>
                            <Flex className="card-top" gap={10} align="center">
                              <div className="ct-text card-title">{item.documentName}</div>
                              {this.renderMoreIcon(item)}
                            </Flex>
                            <div className="card-content">{item.chunkText}</div>
                          </div>
                        )
                      })
                    }
                  </Flex>
                  :
                  // 空数据
                  <Empty className="page-empty" image={null} description={
                    <><div>{i18next.t('common.no-result')}</div>
                      <div>{i18next.t('robot-manage.embedding-search-empty')}</div>
                    </>} />
              }
            </>
          }
        </div>
        {// 向量详情
          detailModal.visible ?
            <Detail visible={detailModal.visible} resource={detailModal.resource} onClose={this.handleCloseDetailModal} /> : null
        }
      </Flex>
    )
  }
}

const mapStateToProps = (state: any) => ({
  userInfo: state.app.userInfo,
  globalLang: state.app.globalLang,
  globalRegion: state.app.globalRegion,
  themeInfo: state.app.themeInfo
});

const mapDispatchToProps = (dispatch: (e: any) => void) => ({
  dispatch_api_query_embedding_chunk_List: (data = api_query_embedding_chunk_List_params) => dispatch(dispatch_api_query_embedding_chunk_List(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(RobotSettingEmbedding);
