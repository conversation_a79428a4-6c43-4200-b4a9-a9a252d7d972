
import { connect } from "react-redux";
import { useEffect, useState, forwardRef, useImperativeHandle } from "react";
import { Modal, message, Flex, Button, Input, Empty, Checkbox, Drawer, Spin, Divider } from "antd"
import { LoadingOutlined } from "@ant-design/icons";
import { useTranslation } from 'react-i18next';
import { useOpenIM } from '@/contexts/OpenIMContext';
import { $trim, $isNull } from '@/utils/common'
import UserAvatar from "@/components/userAvatar";
import UserInfoCard from '@/components/userInfoCard'

import SvgIcon from '@components/svgicon';
import './index.scss'
import classNames from "classnames";

const appendParent = false

const SettingGroup = forwardRef((props: any, ref: React.LegacyRef<HTMLDivElement> | any) => {
    /**自定义暴露给父组件的实例值，接收父组件通知调用 */
    useImperativeHandle(ref, () => ({
        emit_refresh: () => {
            getMembers()
        }
    }))

    const { t } = useTranslation()
    const [modal, contextHolder] = Modal.useModal();
    const { userInfo, resource, usersInfoMap, onInviteUser, onSendMessageToUser,
        userProfile: loginUserProfile, userProfilePersonal: loginUserProfilePersonal,
        activeCompanyId
    } = props
    const { groupId, groupName, groupAvatar } = resource
    const currentUserId = userInfo.imUserId
    const [isLoading, setLoading] = useState(false)
    const { service: ImService, isConnected: isConnectedIm } = useOpenIM();
    const [memberList, setMemberList] = useState([])
    const [showMemberList, setShowMemberList] = useState([])
    const [groupAdmin, setgroupAdmin] = useState<any>({}) // 群主信息
    const [showViewMore, setShowViewMore] = useState(false)
    const [isEditGroupName, setIsEditGroupName] = useState(false)
    const [currentGroupName, setCurrentGroupName] = useState('')
    const [groupNameInput, setGroupNameInput] = useState('')

    const [loginUserName, setLoginUserName] = useState<any>('')// 登录账号 用户名

    useEffect(() => {
        setCurrentGroupName(groupName)
    }, [groupName])

    // 初始化
    useEffect(() => {
        if (ImService) getMembers()
    }, [ImService])

    useEffect(() => {
        // 显示前17个
        const showList = memberList.slice(0, 17)
        setShowMemberList(showList)
    }, [memberList])

    // 初始化用户名
    useEffect(() => {
        const profile = String(activeCompanyId) === '0' ? loginUserProfilePersonal : loginUserProfile
        const name = profile && profile?.userName ? profile.userName : ''
        // console.log('用户名 变了profileprofileprofile  ', name)
        setLoginUserName(name)
    }, [activeCompanyId, loginUserProfile, loginUserProfilePersonal])

    const getMembers = async () => {
        setLoading(true)
        let memberList: any = await ImService.getGroupMemberList(groupId, 0, 1000).catch(() => {
            setLoading(false)
        });
        setLoading(false)
        if (!memberList) return
        // memberList = [...memberList, ...memberList, ...memberList, ...memberList, ...memberList, ...memberList, ...memberList] // 调试
        memberList.forEach((item: any) => {
            item.userName = usersInfoMap[item.userID]?.userName || item.nickname || item.userID
        });
        setMemberList(memberList)
        // 获取管理员信息
        const admin = memberList.filter((i: any) => i.roleLevel === 100)
        setgroupAdmin(admin.length > 0 ? admin[0] : {})
        // // 显示前17个
        // const showList = memberList.slice(0, 17)
        // setShowMemberList(showList)
        console.log(`---------onClickSettingGroup memberList,`, memberList);
        // console.log(`---------onClickSettingGroup admin,`, admin);
        // console.log(`---------onClickSettingGroup currentUserId,`, currentUserId);
    }

    // 取消
    const onClose = () => {
        if (showViewMore) {
            onCloseViewMore()
            return
        }
        props.onClose()
    }

    // 修改群头像
    const onClickChangeGroupAvatar = () => {

    }

    // 查看所有成员
    const onViewMore = () => {
        setShowViewMore(true)
    }

    // 关闭查看所有成员
    const onCloseViewMore = () => {
        setShowViewMore(false)
    }

    // 删除成员
    const onDeleteGroupUser = (target: any, index: number) => {
        modal.confirm({
            width: 460,
            title: t('chat.kick-out-member'),
            icon: <SvgIcon className="modal-icon" svgName="icon_warning" />,
            content: t('chat.kick-out-member-confirm', { user: target.userName }),
            okText: t('chat.yes'),
            cancelText: t('app.cancel'),
            centered: true,
            closable: true,
            onCancel: () => {
                // onCancel()
            },
            onOk: async () => {
                // onCancel()
                const list = [target.userID]
                const res = await ImService.kickGroupMember(groupId, list).catch()
                if (!res) return
                const _memberList = memberList
                _memberList.splice(index, 1)
                // setShowMemberList(previtem=> previtem.filter((i:any)=>i.userID!==target.userID))
                const _showList = _memberList.slice(0, 17)
                setShowMemberList(_showList)
                console.log('_memberList', _memberList)
                setMemberList(_memberList) // 删除
            }
        });
    }

    const onChangeGroupNameInput = (e: any) => {
        const value = e.target.value
        setGroupNameInput(value)
    }

    const onEditGroupName = () => {
        setGroupNameInput(currentGroupName)
        setIsEditGroupName(true)
    }

    // 提交群名修改
    const onEnterGroupNameInput = async () => {
        setIsEditGroupName(false)
        const oldName = $trim(currentGroupName)
        const newName = $trim(groupNameInput)
        if (oldName !== newName && newName !== '') {
            // console.log('groupNameInput', groupNameInput)
            const res = await ImService.setGroupInfo(groupId, groupNameInput);
            if (!res) return
            // console.log('res', res)
            setCurrentGroupName(groupNameInput)
        }
    }

    const onBlurGroupNameInput = () => {
        setIsEditGroupName(false)
    }

    const onAddUser = () => {
        const userIds = memberList.map((i: any) => i.userID)
        onInviteUser && onInviteUser(groupId, userIds)
    }

    const getUserAvatar = (user: any) => {
        const target = usersInfoMap?.[user?.userID]
        const avatarUrl = target && target?.avatar ? target.avatar : null
        // console.log('avatarProfile',avatarProfile)
        return <UserAvatar className="pointer" name={user.userName} src={avatarUrl} userId={user?.userID} />
    }

    const getUserName = (user: any) => {
        // console.log('user', user)
        let name = user?.userName
        if (user?.userID == currentUserId) {
            // console.log('当前用户', loginUserName)
            name = !$isNull(loginUserName) ? loginUserName : name
        }
        return name
    }

    return (
        // , "drawer-append-parent"
        <div className={classNames(["chat-setting-group-modal", { "drawer-append-parent": appendParent }])}>
            <Drawer open={true}
                width={460}
                title={
                    showViewMore ?
                        <Flex gap={8} className="ml-title" align="center">
                            <Flex className="icon-operate icon-back" align="center" justify="center">
                                <SvgIcon onClick={onCloseViewMore} svgName="icon_back" />
                            </Flex>
                            <span>{t('chat.group-member-list')}</span>

                            <Flex className="icon-operate" align="center" justify="center">
                                <SvgIcon onClick={onAddUser} svgName="icon_new_user" />
                            </Flex>
                        </Flex> :
                        t('chat.settings')
                }
                className={classNames(["chat-setting-group-drawer", 'common-antd-drawer'])}
                onClose={onClose}
                footer={null}
                mask={true}
                rootStyle={!appendParent ? {} : { position: "relative", flex: 1, height: '100%', boxShadow: 'none' }}
                getContainer={appendParent ? false : 'body'}
            >
                <div>
                    {
                        isLoading ? <Spin className="modal-spin" size="large" indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />} /> :
                            (
                                !showViewMore ?
                                    <>
                                        <Flex className="group-info" gap={8} align="center">
                                            <div onClick={onClickChangeGroupAvatar} className="group-avatar">
                                                {groupAvatar}
                                                {/* <img className="change-icon" src={ChangeAvatarIcon} alt="" /> */}
                                            </div>
                                            {
                                                isEditGroupName ?
                                                    <Input autoFocus={true} value={groupNameInput} onChange={onChangeGroupNameInput}
                                                        onBlur={onBlurGroupNameInput}
                                                        onPressEnter={onEnterGroupNameInput}
                                                        maxLength={16}
                                                        suffix={
                                                            <SvgIcon svgName="icon_chat_enter" />
                                                        }
                                                        variant="filled"
                                                        className="group-name-input"
                                                    />
                                                    :
                                                    <>
                                                        <div className="group-name">{currentGroupName}</div>
                                                        {
                                                            // 仅群主可编辑
                                                            groupAdmin.userID === String(currentUserId) ? <SvgIcon onClick={onEditGroupName} className="edit-group-icon" svgName="icon_group_edit" />
                                                                : null
                                                        }
                                                    </>
                                            }

                                        </Flex>
                                        <Divider />
                                        <Flex className="group-members" vertical gap={12}>
                                            <div className="gm-title">{t('chat.group-members')} ({memberList.length}/1000)</div>
                                            <Flex className="gm-users" gap={8} wrap>
                                                {
                                                    showMemberList && showMemberList.map((item: any, index: number) =>
                                                        <Flex key={item.userID + '' + index} vertical gap={4} justify="center" align="center">
                                                            <UserInfoCard key={item.userID + '' + index} userInfo={item} onSendMessageToUser={onSendMessageToUser}>
                                                                <div className="pointer">
                                                                    {
                                                                        getUserAvatar(item)
                                                                    }
                                                                </div>
                                                            </UserInfoCard>
                                                            <div className="gm-name">
                                                                {
                                                                    getUserName(item)
                                                                }
                                                            </div>
                                                        </Flex>
                                                    )
                                                }
                                                {
                                                    // groupAdmin.userID === String(currentUserId) ?
                                                    <Flex onClick={onAddUser} className="add-user" vertical align="center" justify="center">
                                                        <Flex className="icon" align="center" justify="center">
                                                            <SvgIcon svgName="icon_plus" />
                                                        </Flex>
                                                        <span className="text">{t('robot-manage.document.add-document')}</span>
                                                    </Flex>
                                                    // : null
                                                }

                                            </Flex>
                                            <Flex onClick={onViewMore} className="view-more" gap={4} align="center" justify="center">
                                                <span>{t('chat.view-more')}</span>
                                                <SvgIcon className="view-more-icon" svgName="arrow_right_line" />
                                            </Flex>
                                        </Flex>
                                    </> :
                                    <Flex vertical className="all-member-list" gap={12}>
                                        <Flex vertical gap={12} className="ml-list">
                                            {
                                                memberList && memberList.map((item: any, index: number) =>
                                                    <Flex className="ml-list-item" key={item.userID + '' + index} gap={8} align="center">
                                                        <UserInfoCard userInfo={item} onSendMessageToUser={onSendMessageToUser}>
                                                            <Flex className="ml-list-item-left" gap={8} align="center">
                                                                <div>
                                                                    {
                                                                        getUserAvatar(item)
                                                                    }
                                                                </div>
                                                                <div className="name">{getUserName(item)}</div>
                                                            </Flex>
                                                        </UserInfoCard>
                                                        {
                                                            groupAdmin.userID === String(item.userID) ?
                                                                <span className="group-admin">{t('chat.group-owner')}</span>
                                                                : null
                                                        }
                                                        {
                                                            groupAdmin.userID !== String(item.userID) && groupAdmin.userID === String(currentUserId) ? <Button className="delete-btn" color="danger" variant="filled" size="small"
                                                                icon={<SvgIcon onClick={() => onDeleteGroupUser(item, index)} className="icon-delete" svgName="icon_delete" />}>
                                                            </Button> : null
                                                        }

                                                    </Flex>
                                                )
                                            }
                                        </Flex>
                                    </Flex>
                            )

                    }
                </div>
            </Drawer>
            {contextHolder}
        </div>
    )
})

const mapStateToProps = (state: any) => ({
    activeCompanyId: state.app.activeCompanyId,
    userInfo: state.app.userInfo,
    userProfile: state.app.userProfile,
    userProfilePersonal: state.app.userProfilePersonal,
})
const mapDispatchToProps = (dispatch: any) => ({
    // dispatch_api_: (data:any) => dispatch(dispatch_api_(data))
})
const ConnectedCounter = connect(mapStateToProps, mapDispatchToProps, null, { forwardRef: true })(SettingGroup);
export default ConnectedCounter;