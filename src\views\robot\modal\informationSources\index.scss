@import "../../../../styles/mixin.scss";
.information-sources-drawer {
  .ant-drawer-body {
    overflow-y: hidden;
    padding-right: 0;
  }
  .ant-tabs {
    height: 100%;
    .ant-tabs-nav{
      flex-shrink: 0;
      // margin-left: 16px;
      margin-bottom: 20px;
      margin-right: 24px;
    }
    .ant-tabs-content-holder{
      flex: 1;
      overflow: auto;
      padding-right: 24px;
    }
    
    &.common-tab .ant-tabs-tab+.ant-tabs-tab{
      margin-left: 24px;
    }
  }
  .common-antd-drawer-card {
    width: 502px;
  }
  .common-antd-drawer-card:hover {
    background: var(--colorFillTertiary);
    box-shadow: none;
  }

  .card-title{
    >div {
      flex: 1;
      @include ellipsis-multiline;
    }
  }

  .attachments {
    border-radius: 12px;
    padding: 10px;
    margin-bottom: 12px;

    .card-title {
      margin-bottom: 4px;
      
      font-weight: 600;
      font-size: 14px;
      color: var(--colorText);
      line-height: 24px;
    }

    .file-type-size {
      max-width: 300px;
      display: flex;
      align-items: center;

      img {
        margin-top: 2px;
        width: 16px;
        height: 16px;
      }

      .type-text {
        margin-left: 8px;
        height: 20px;
        
        font-weight: 400;
        font-size: 12px;
        color: var(--colorTextSecondary);
        line-height: 20px;
      }

      .size {
        margin-left: 8px;
        height: 20px;
        
        font-weight: 400;
        font-size: 12px;
        color: var(--colorTextSecondary);
        line-height: 20px;
      }
    }
  }

  .historyChat {
    border-radius: 12px;
    padding: 10px;
    margin-bottom: 12px;

    .card-title2 {
      margin-bottom: 4px;
      span {
        font-weight: 400;
        font-size: 12px;
        color: var(--colorTextSecondary);
        line-height: 20px;
        &:first-child{
          flex: 1;
          display: block;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        &:last-child{
          flex-shrink: 0;
        }
      }
    }

    .card-text {
      p {
        margin-top: 4px;
        margin-bottom: 4px;
      }
    }
  }

  .reference {
    border-radius: 12px;
    padding: 10px;
    margin-bottom: 12px;

    .card-title {
      margin-bottom: 4px;

      img {
        width: 16px;
        height: 16px;
        margin: 0px;
      }

      >div {
        margin-left: 4px;
        font-weight: 600;
        font-size: 14px;
        color: var(--colorText);
        line-height: 24px;
      }
    }

    .card-msg {
      margin-top: 12px;

      span {
        
        font-weight: 400;
        font-size: 12px;
        color: var(--colorTextTertiary);
        line-height: 16px;
      }
    }
  }

  img {
    user-select: none;
  }

  .googleSearch {
    border-radius: 12px;
    padding: 10px;
    margin-bottom: 12px;

    .internet-search-user {
      font-size: 14px;
      color: var(--colorText);
      line-height: 20px;
      margin-bottom: 15px;
    }

    .card-title {
      
      font-weight: 600;
      font-size: 14px;
      color: var(--colorText);
      line-height: 24px;
      margin-bottom: 4px;
    }

    .internet-card-msg {
      display: flex;
      margin-bottom: 4px;
      font-size: 12px;
      color: var(--colorTextSecondary);
      line-height: 16px;

      img {
        width: 16px;
        height: 16px;
        margin-right: 4px;
        flex-shrink: 0;
        border-radius: 50%;
        user-select: none;
      }

      span {
        font-weight: 400;
        font-size: 12px;
        color: var(--colorTextTertiary);
        line-height: 16px;
        // word-break: break-word;
        @include ellipsis-multiline;
      }
    }

    .card-text {
      
      font-weight: 400;
      font-size: 14px;
      color: var(--colorText);
      line-height: 24px;
    }
  }

  .card-textarea {
    padding: 0;
    border-width: 0px;
    font-size: 14px;
    color: var(--colorText);
    line-height: 24px;
    border: 0;
    background: transparent;
    cursor: pointer;

    &:focus, &:focus-within {
      box-shadow: none;
      outline: none;
    }
  }
  .ai-mark-down img{
    max-width: 100%;
    border-radius: 12px;
  }
}
.logicalReasoning{
  &.common-antd-drawer-card{
    padding-top: 0;
    padding-bottom: 0;
  }
  .card-text {
    font-size: 14px;
    line-height: 24px;
  }
}

.no-bg-card.common-antd-drawer-card{
  background: transparent;
  cursor: text;
  &:hover{
    background: transparent;
  }
}