import React, { memo, useState } from 'react'
import { Tooltip } from 'antd'
import './index.scss'

type SvgIconProps = {
    // 图标名称
    svgName: string,
    // 图标尺寸，不传时为图标默认24px
    iconSize?: number,
    className?: string,
    /**
    * 若该图标需要hover时改变颜色，请先把该图标内置fill属性删除，然后传入两个值，
    * iconColor：原始颜色，hoverColor：hover时的颜色
    */
    hasHover?: boolean,
    // 图标默认颜色
    iconColor?: string,
    // 图标hover时的颜色
    hoverColor?: string,
    // 是否需要显示小手
    needPointer?: boolean,
    // 图标提示文案
    toolTipValue?: string
}

function SvgIcon(props: any) {
    const {
        svgName,
        iconSize,
        className,
        hasHover,
        iconColor,
        hoverColor,
        needPointer,
        toolTipValue,
        ...otherProps
    } = props

    const [svgColor, setSvgColor] = useState(iconColor)

    const handleMouseEnter = () => {
        if (hasHover) {
            setSvgColor(hoverColor)
        }
    }

    const handleMouseLeave = () => {
        if (hasHover) {
            setSvgColor(iconColor)
        }
    }

    return (
        // <Tooltip title={toolTipValue} arrow={false}>
        //     <div
        //         className="svg-icon-wrap"
        //         style={{
        //             width: iconSize,
        //             height: iconSize,
        //             cursor: needPointer ? 'pointer' : 'normal'
        //         }}
        //         onMouseEnter={handleMouseEnter}
        //         onMouseLeave={handleMouseLeave}
        //     >
            <svg aria-hidden="true" className={className? className +" svg-icon" :"svg-icon"} {...otherProps}>
                <use xlinkHref={`#svg-${svgName}`} />
                {/* <use href={`#svg-${svgName}`} /> */}
            </svg>
        // </Tooltip>
    )
}

// SvgIcon.defaultProps = {
//     iconSize: 24,
//     hasHover: false,
//     iconColor: '#000000',
//     hoverColor: '#000000',
//     needPointer: false,
//     toolTipValue: ''
// }

export default memo(SvgIcon)