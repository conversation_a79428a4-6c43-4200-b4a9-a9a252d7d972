import { connect } from 'react-redux';
import { Button, Flex, Input, message } from 'antd';
import { useState, useEffect } from "react";
import { useTranslation } from 'react-i18next';
import './index.scss';
import { dispatch_api_account_feed_back } from '@/api/platform'

const { TextArea } = Input;

// 反馈
const Feedback = (props: any) => {
  const { t } = useTranslation()
  const [feedbackText, setFeedbackText] = useState('') // 意见反馈输入框
  const [isSubmit, setIsSubmit] = useState(false) // 提交中

  useEffect(() => {
  }, [])

  // 确认提交反馈
  const confirmFeedback = () => {
    const data = { content: feedbackText };
    setIsSubmit(true)
    props.dispatch_api_account_feed_back(data).then((result: any) => {
      setIsSubmit(false)
      setFeedbackText('')
      message.success(t("success.submit"));
    }).catch((err: any) => {
      setIsSubmit(false)
    })

  }
  // 意见反馈输入框
  const onChangeFeedbackText = (e: any) => {
    setFeedbackText(e.target.value)

  }
  // 意见反馈 提交按钮启用状态判断
  const getConfirmBtnEnable = () => {
    const enable = (feedbackText && feedbackText.trim() != '') && !isSubmit
    return enable
  }

  return (
    <Flex vertical className="feedback-wrapper">
      <div className='fw-top'>
        <div className='title'>{t('setting.feedback')}</div>
        <div className='desc'>
          {t('setting.feedback-desc')}
        </div>
        <TextArea value={feedbackText}
          maxLength={1000}
          variant="filled"
          autoSize={{ minRows: 5, maxRows: 26 }}
          onChange={(e) => onChangeFeedbackText(e)}
          placeholder={t(["placeholder.opinion"]) || ""}
        />
      </div>
      <Flex justify="flex-end" className='bottom-operate'>
        <Button loading={isSubmit} type="primary" onClick={() => confirmFeedback()} disabled={!getConfirmBtnEnable()}>
          {t("submit")}
        </Button>
      </Flex>
    </Flex>
  );
};

const mapStateToProps = (state: any) => ({
});
const mapDispatchToProps = (dispatch: any) => ({
  dispatch_api_account_feed_back: (data:any) => dispatch(dispatch_api_account_feed_back(data)),
});
export default connect(mapStateToProps, mapDispatchToProps)(Feedback);
