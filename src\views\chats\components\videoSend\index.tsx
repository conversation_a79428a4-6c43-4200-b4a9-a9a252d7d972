
import { useState } from "react";
import { <PERSON><PERSON>, Flex, Upload } from "antd"
import VideoUtils from "../../utils/video";
import type { UploadProps } from 'antd';
import SvgIcon from '@components/svgicon';
import './index.scss'

const VideoSend = (props: any) => {
    const { onUpdate, onSubmiting } = props
    const [thumbnail, setThumbnail] = useState<any>(null);

    const getFileType = (file: any) => {
        let fileType = file.name.substring(file.name.lastIndexOf(".") + 1);
        fileType = fileType.toLowerCase()
        return fileType
    }
    // 视频上传
    const videoUploadprops: UploadProps = {
        name: 'chatVideo',
        action: '#',
        accept: '.mp4',
        showUploadList: false,
        // 上传前检验
        beforeUpload: async (file: any, uploadFileList: any) => {
            let fileType = getFileType(file)
            onSubmiting && onSubmiting(true)
            if (fileType === 'mp4') {
                await VideoUtils.getVideoDuration(file) // 获取视频时长
            }
            return true
        },
        customRequest: async (options: any) => {
            // console.log('customRequest uploadingFileTotal', uploadingFileTotal)
            const { onSuccess, onError, file, onProgress } = options;

            let fileType = getFileType(file)
            // 上传
            // console.log('file', file)
            // 获取视频元数据-缩略图
            // const thumbnail = await VideoUtils.generateThumbnail(file);
            let thumbnailFile = null
            if (fileType === 'mp4') {
                const data: any = await VideoUtils.generateThumbnail(file);
                thumbnailFile = data.thumbnailFile
                onSubmiting && onSubmiting(true, data.thumbnailUrl)
            }
            // 通知上传视频
            onUpdate && onUpdate(file, thumbnailFile)
        }
    };

    return (
        <Upload className="chat-video-upload" {...videoUploadprops}>
            <Flex  className="operate-item" align="center" justify="center">
                <SvgIcon svgName="send_video" />
            </Flex>
        </Upload>
    )
}

export default VideoSend