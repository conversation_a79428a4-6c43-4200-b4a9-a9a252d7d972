import HTTP from "@/utils/request";
import HTTP2 from "@/utils/userRequest";
import * as actions from "@/store/action";
import { AxiosRequestConfig } from 'axios';

// 意见反馈
export const api_account_feed_back_params = { content:'' };
export const api_account_feed_back = (data = api_account_feed_back_params) => HTTP2.post("/system/user/profile/feedback", data);
export const dispatch_api_account_feed_back = (params: any) => (dispatch: any) => new Promise((resolve, reject) => {
  const data = { ...api_account_feed_back_params, ...params }
  api_account_feed_back(data).then(result => {
    resolve(result.data);
  }).catch(err => reject(err))
})

// 获取全局配置
export const dispatch_api_get_configurations = () =>(dispatch: any) => new Promise((resolve, reject) => {
  HTTP2({
    method: 'get',
    url:  "/infra/configuration/public",
    noTenant: true,
  }as AxiosRequestConfig).then(async(result) => {
    const config = result?.data?.data ||{}
    config.isSaaS = config.isSaaS =='true'
    dispatch(actions.change_app_config(config));
    if (!config.isSaaS){ // 私有化部署，无企业概念数据
      await  dispatch(actions.change_app_active_company({ id: '0', name: '个人空间' }))
    }
    resolve(config);
  }).catch(err => reject(err))
})

// 获取当前IP的地区代码
export const dispatch_api_get_ip_areacode= () => (dispatch: any) => new Promise((resolve, reject) => {
  HTTP2({
      method: 'get',
      url:  "/system/auth/ip-area-code",
      noTenant: true,
    } as AxiosRequestConfig).then(result => {
    const res=result?.data || {}
    resolve(res);
    // res.data = 'aa' // 调试
    dispatch(actions.change_app_ip_areacode(res.data));
  }).catch(err => reject(err))
})