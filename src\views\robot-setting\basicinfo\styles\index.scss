@import "../../../../styles/mixin.scss";
.robot-basicinfo-wrap{
    height: 100%;
    width: 100%;
    .setting-area{
        flex: 1;
        min-width: 400px;
        background: var(--colorBgLayout);
        overflow: auto;
        padding-bottom: 40px;
        .common-form{
            // padding: 0 40px 40px;
            // flex: 1;
            // overflow: auto;
            width: 805px;
            margin: 0 auto;
            .half-width{
                width: 50%;
            }
            .single-row{
                width: calc(50% - 10px);
            }
        }
        .field-desc{
            font-weight: 400;
            font-size: 12px;
            color: var(--colorTextTertiary);
            line-height: 16px;
            margin-bottom: 8px;
            &.min-height32{
                min-height: 32px;
            }
        }
        .field-tooltip-trigger{
            font-size: 16px;
            line-height: 16px;
            color: var(--colorIconQuaternary);
            cursor: pointer;
            // margin-left: 4px;
        }
        // 折叠面板
        .ant-collapse{
            background: transparent;
        }
        .ant-collapse-item{
            background: var(--colorBgBase);
            padding: 0 10px;
            &:last-child{
                margin-bottom: 0!important;
            }
        }
        .ant-collapse-header{
            display: flex;
            align-items: center;
            padding: 10px;
            padding-right: 0;
            .card-title{
                padding: 0;
                font-weight: 600;
                font-size: 14px;
                color: var(--colorText);
                line-height: 22px;
                cursor: pointer;
                .svg-icon{
                    font-size: 16px;   
                }
                .toggle-icon{
                    margin-left: auto;
                }
                >span{
                    margin-left: 8px;
                }
            }
        }
        
        .ant-form-item:last-of-type{
            margin-bottom: 0;
        }
        .ant-collapse {
            .ant-collapse-content>.ant-collapse-content-box{
                padding: 24px 10px;
                border-top: 1px solid var(--colorBorderSecondary);
            }
        }
    }
    .area-title{
        // padding: 0 40px;
        // height: var(--page-nav-height);
        // font-weight: 600;
        // font-size: 18px;
        // color: var(--colorText);
        // line-height: 24px;
    }
   .debug-area{
       width: 800px;
       box-shadow: -1px 0px 0px 0px var(--colorBorderSecondary);
       background: var(--colorBgBase);
       .area-title{
           flex-shrink: 0;
           width: 100%;
           padding: 0 20px;
       }
       .debug-main{
           flex: 1;
       }
   }
}

.en .konwledge-count-fomr-item{
    .ant-form-item-label{
        min-height: 52px;
    }
}