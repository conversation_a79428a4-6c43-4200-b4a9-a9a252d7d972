import { Modal, Checkbox, Card, Input, Button, Radio } from "antd";
import classnames from "classnames";
import i18next from "i18next";
import React,{ useState, useEffect, forwardRef, useImperativeHandle } from "react";
import {getFileType} from "./utils"
import './index.scss'
import Svgicon from '@/components/svgicon'

const { TextArea } = Input;
interface Props{
  visible: boolean,
  resource: any,
  updateVisible:Function,
  onOk: Function
  onCancel: Function
}
// 增强弹框
const EnhanceModalComponent = forwardRef((props: Props, ref) => {
  /** 自定义暴露给父组件的实例值 **/
  // useImperativeHandle(ref, () => ({
  //   init() {
  //   }
  // }))

  /* props */
  const resource = props.resource;
  const isVisible = props.visible
  /* state */
  const [checkAgree, setCheckAgree] = useState(false)
  const [dataList, setDataList] = useState<any[]>([])
  const enhanceRef:React.LegacyRef<HTMLDivElement> | any = React.createRef()

  /* methods */
  // 增强确定
  const handleEnhanceModalOk = async () => {
    if (checkAgree === false) {
      return;
    }
    let embeddingChunkList:any[] = [];
    dataList.forEach((d: any, index: number) => {
      if (d.checked === true) {
        d.chunkText = d.isEdited ? d.enhanceText : d.chunkText;
        delete d.isEidt;
        delete d.checked;
        delete d.enhanceText;
        delete d.lastestEnhanceText
        delete d.isEdited;
        delete d.fileType
        // delete d.ref
        embeddingChunkList.push(deepClone(d));
      }
    });
    props.onOk(resource.isEnhance, embeddingChunkList)
    handleEnhanceModalCancel(true)
  }

  // 增强取消
  const handleEnhanceModalCancel = (ignoreEnhance?: boolean) => {
    if (!ignoreEnhance && !resource.isEnhance){// 若是普通对话取消，需要删除最新的对话
      props.onCancel(false)
    }
    props.updateVisible(false) // 关闭弹框
    setDataList([])
    setCheckAgree(false)
  }
  
  const updateDataListTargetProp =(arr:any[], index:number)=>{
    const updateProps:any = {} 
    arr.forEach((item:any)=>{
      updateProps[item.prop] = item.value
    })
    setDataList(prevItems => prevItems.map((item, i) => 
      i === index ? { ...item, ...updateProps } : item
    ))
  }

  // 增强片段输入框change
  const handleChangeTextEnhance = (e:any, d: any, index: any) => {
    updateDataListTargetProp([{prop:"enhanceText", value: e.target.value}], index)
  }

  // 增强-编辑
  const handleIsEditEnhance = (d: any, index: any) => {
    updateDataListTargetProp([{prop:"isEidt", value: true}], index)
    // setTimeout(()=>{
    //   // 自动获取焦点
    //   d.ref?.current!.focus({
    //     cursor: 'start',
    //   })
    // })
  }

  // 增强-保存
  const handleSaveEnhance=(d: any, index: number)=>{
   // 对比原字符串，判断是否已编辑，并切换为未编辑状态
    const isEdited = dataList[index]['enhanceText'] !== dataList[index]['chunkText'] 
    updateDataListTargetProp([{prop:"lastestEnhanceText", value: d.enhanceText},{prop:"isEidt", value: false},{prop:"isEdited", value: isEdited}], index)
  }

  // 增强片段勾选
  const handleChangeCheckTextEnhance = (e: any, d: any, index: any) => {
    updateDataListTargetProp([{prop:"checked", value: e.target.checked}], index)
  }

  // 增强-还原
  const handleRebackTextEnhance = (d: any, index: number) => {
    updateDataListTargetProp([{prop:"isEdited", value: false},{prop:"enhanceText", value: d.chunkText},{prop:"lastestEnhanceText", value: d.chunkText}], index)
  }

  // 增强-编辑状态取消
  const handleCancelSaveEnhance=(d: any, index: number)=>{
    const isEdited = dataList[index]['lastestEnhanceText'] !== dataList[index]['chunkText'] 
    updateDataListTargetProp([{prop:"isEidt", value: false},{prop:"isEdited", value: isEdited},{prop:"enhanceText", value: d.lastestEnhanceText}], index)
  }

  // 增强 同意协议勾选
  const handleChangeCheckAgreeEnhance = (e: any) => {
    setCheckAgree(e.target.checked);
  }

  // 查看详情
  const openDocument = (d: any) => {
    if (d.internetSearch === false) {
      window.open(d.documentPath,d.documentPath)
    } else {
      window.open(d.url,d.url)
    }
  };
  // 深拷贝
  const deepClone=(origin:any)=>{
    try {
      return JSON.parse(JSON.stringify(origin))
    } catch (error) {
      console.log('deepClone error', error)
      return origin
    }
  }
  
  useEffect(() => {
    if (!isVisible) return
    setCheckAgree(false)
    if (resource?.dataList){
      const result = resource.dataList?.map((i:any) => {
        i.isEidt=false
        i.checked=true
        i.enhanceText = i.chunkText // 输入框绑定值
        i.lastestEnhanceText = i.chunkText // 输入框最新修改值
        // i.ref = React.createRef()
        return i
      })
      setDataList(result)
    } 
  }, [isVisible, resource])

  useEffect(()=>{
    let timerId:any;
    if (dataList.length> 0 && enhanceRef?.current){
      // 打开弹窗，滚动条滑至顶部
      timerId = setTimeout(()=>{
        if ( enhanceRef?.current?.scrollTop)enhanceRef.current.scrollTop = 0;
      },100)
      // 清理副作用
      return () => {
        clearTimeout(timerId);
      };
    }
  },[dataList.length])

  /** render **/
  const renderEnhanceModal = () => {
    return (
      <div className={"enhance-modal-wrap"} style={{ pointerEvents: "auto" }}>
        <div className={"enhance-modal-title"}>
          <span className='title-pre'>{i18next.t(["app.enhance-notice"])}</span>
          {resource.llmModelImage ? <img draggable="false" src={resource.llmModelImage} className="title-icon" alt="" /> : null}
          <span className='title-gpt'>
            {resource.llmCategory && resource.llmModel ? (resource.llmCategory + ' /  ' + resource.llmModel) : 'Chat GPT'}
          </span>
          <span className='title-end'>{" ?"}</span>
          <Svgicon svgName="icon_close" className='title-close' onClick={() => handleEnhanceModalCancel()} />
        </div>
        <div className="enhance-wraps" ref={enhanceRef}>
          {
            dataList.map((d: any, index: any) => {
              return (
                d.internetSearch === false ?
                  <div className={"enhance-wrap"} key={d.chunkId + '' + index}>
                    <div className={"checkbox-wrap"}>
                      <Checkbox
                        checked={d.checked}
                        onChange={(e) => handleChangeCheckTextEnhance(e, d, index)}
                      />
                    </div>

                    <div className={"Card-wrap"}>
                      <Card className={classnames([{ "nochecked": !d.checked, 'edit': d.isEidt }, "enhance-ard-wrap"])} hoverable>
                        <div className="file-info">
                          <img src={getFileType(d.documentType)} alt="" />
                          <span>{d.documentName + '.' +d.documentType}</span>
                        </div>
                        <TextArea
                          // ref={d.ref}
                          className={"TextArea-wrap"}
                          size={"large"}
                          value={d.enhanceText}
                          onChange={(e:any) => handleChangeTextEnhance(e,d, index)}
                          autoSize={true}
                          disabled={!d.isEidt}
                        >
                        </TextArea>
                        <div className={"operate"} >
                          <div className="ol">
                            <div>
                              <span>{i18next.t(["home.upload-time"])}：</span>
                              <span>{d.createTime || "-"}</span>
                            </div>
                            <div>
                              <span>{i18next.t(["home.relevance"])}：</span>
                              {
                                d.score!=undefined && d.score!=null && d.score!=''?
                                <span>{d.score.toFixed(2)}</span>:'-'
                              }
                            </div>
                            <div>
                              <span>{i18next.t(["home.size"])}：</span>
                              {
                                d.tokenCount!=undefined && d.tokenCount!=null && d.tokenCount!=''?
                                <span>{d.tokenCount} token{d.tokenCount>1?`s`:''}</span>:'-'
                              }
                            </div>
                            <div>{d.isEdited ? i18next.t(["app.edited"]) : ''}</div>
                          </div>
                          <div className="or">
                            {!d.isEidt ?
                              <>
                                {/* 未编辑状态 */}
                                <div className={"a-item edit"}
                                  onClick={() => handleIsEditEnhance(d, index)}>
                                  <span>{i18next.t(["app.edit"])}</span>
                                </div>

                                {d.isEdited && (
                                  <div className={"a-item rollback"}
                                    onClick={() => handleRebackTextEnhance(d, index)}>
                                    <span>{i18next.t(["app.rollback"])}</span>
                                  </div>
                                )}
                                <div className={"a-item document"}
                                  onClick={() => openDocument(d)}>
                                  <span>{i18next.t(["app.document"])}</span>
                                </div>
                              </>
                              :
                              <>
                                {/* 编辑状态 */}
                                <div className={"a-item cancel"}
                                  onClick={() => handleCancelSaveEnhance(d, index)}>
                                  <span>{i18next.t(["app.cancel"])}</span>
                                </div>
                                <div className={"a-item save"}
                                  onClick={() => handleSaveEnhance(d, index)}>
                                  <span>{i18next.t(["modal.save-ok"])}</span>
                                </div>
                              </>
                            }
                          </div>
                        </div>
                      </Card>
                    </div>

                  </div> : null
              )
            })
          }
        </div>
        <div className={"check-agree-enhance-btn"}>
          <Checkbox className={"check-agree-enhance radio-style"}
            checked={checkAgree}
            onChange={(e) => handleChangeCheckAgreeEnhance(e)}>
            {i18next.t(["app.check-agree-enhance"])}
          </Checkbox>
          {/* <Radio value={checkAgree} onChange={(e) => handleChangeCheckAgreeEnhance(e)}>{i18next.t(["app.check-agree-enhance"])}</Radio> */}
          <div className={"btn-wrap"}>
            <Button className={"btn-cancel"} color="default" autoInsertSpace={false} variant="filled" onClick={() => handleEnhanceModalCancel()}>{i18next.t(["app.cancel"])}</Button>
            <Button className={"btn-ok"} disabled={!checkAgree} type="primary" onClick={() => handleEnhanceModalOk()}> {i18next.t(["app.ok"])}</Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="enhance-modal">
      <Modal className={"enhance-modal"}
        open={isVisible}
        maskClosable={true}
        width={1000}
        modalRender={() => renderEnhanceModal()}
      >
      </Modal>
    </div>
  );
});

export default EnhanceModalComponent;
