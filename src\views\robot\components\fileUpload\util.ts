import docIcon from "./style/doc.png";
import pdfIcon from "./style/pdf.png";
import txtIcon from "./style/txt.png";
import i18next from "i18next";

// 根据文件类型获取文件图标
const getFileTypeInfo = (fileType: any) => {
  let image = undefined;
  let text = undefined;
  let type = fileType;
  switch (type) {
    case "pdf":
      image = pdfIcon;
      text = i18next.t("upload.pdf");
      break;
    // case "doc":
    case "docx":
      image = docIcon;
      text = i18next.t("upload.docx");
      break;
    case "txt":
      image = txtIcon;
      text = i18next.t("upload.txt");
      break;
    default:
      break;
  }
  return { image, text };
};

// 文件大小格式化
function formatFileSize(bytes: number) {
  if (bytes < 1024) return "1KB"; // 小于1KB的文件显示为1KB

  const units = ["KB", "MB", "GB", "TB"];
  let size = bytes / 1024; // 先转换为KB
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  const unit = units[unitIndex];
  // 四舍五入到1位小数
  const result =
    unit === "KB"
      ? `${Math.ceil(Number(size))}${unit}`
      : `${Number(size.toFixed(1))}${unit}`;
  return result;
}
export { getFileTypeInfo, formatFileSize };
