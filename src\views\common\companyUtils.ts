import store from '@/store'
export const getCompanyName = (id: any) => {
    try {
        let companyName = id
        const companies = store.getState().app.companies
        const target = companies.filter((i: any) => i.id.toString() === id.toString())
        companyName = target.length > 0 ? target[0].companyName : companyName
        return companyName
    } catch (error) {
        return id
    }
}