

import { useState } from 'react';
import {  Button, Popover, Flex } from 'antd';
import SvgIcon from '@components/svgicon'; 

// 常用表情列表
const EMOJI_LIST = [
    '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣',
    '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰',
    '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜',
    '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳', '😏',
];

const StickerSelector = (props: any) => {
    const {onUpdate} =  props
    const [emojiVisible, setEmojiVisible] = useState(false);

    // 插入表情
    const insertEmoji = (emoji:any) => {
        onUpdate && onUpdate(emoji)
        setEmojiVisible(false);
    }
    // 表情选择面板
    const emojiPanel = (
        <Flex gap={8} wrap style={{ width: 360, maxHeight: 200, overflowY: 'auto' }}>
            {EMOJI_LIST.map((emoji, index) => (
                <Flex key={index}>
                    <Button
                        type="text"
                        style={{ fontSize: 20, padding: 4 }}
                        onClick={() => insertEmoji(emoji)}
                    >
                        {emoji}
                    </Button>
                </Flex>
            ))}
        </Flex>
    )
    return(
            <Popover
                content={emojiPanel}
                trigger="click"
                open={emojiVisible}
                onOpenChange={setEmojiVisible}
                placement="topLeft"
                arrow={false}
            >
                <Flex  className="operate-item" align="center" justify="center">
                    <SvgIcon svgName="send_sticker"/>
                </Flex>
        </Popover>
    )
}
export default StickerSelector