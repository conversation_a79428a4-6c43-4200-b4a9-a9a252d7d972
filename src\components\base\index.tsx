import React from "react";
import { useNavigate, useLocation, useParams, useSearchParams } from "react-router-dom";
import type { MenuProps } from 'antd';

export const withRouter = (WrapperComponent: any) => {
  return function (props: any) {
    const navigate = useNavigate();
    const location = useLocation();
    const params = useParams();
    const searchParams = useSearchParams();

    const router = { navigate, location, params, searchParams }
    return <WrapperComponent {...props} router={router} />
  }
}

type MenuItem = Required<MenuProps>['items'][number];

export const getMenuItem = ( label: React.ReactNode, key: React.Key, icon?: React.ReactNode, children?: MenuItem[]):MenuItem => {
  return {
    label,
    key,
    icon,
    children,
  } as MenuItem
}

