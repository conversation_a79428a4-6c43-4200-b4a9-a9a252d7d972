@import "@/styles/mixin.scss";
.robot-iframe-wrap {
    width: 100%;
    height: 100%;
    background: var(--colorBgLayout);
    overflow: auto;
    .robot-iframe-left {
        width: 798px;
        padding: 10px 16px;
        // flex-shrink: 0;
    }
    .iframe-title {
        @include font(18px, 24px, 600);
    }
    .ifram-nav {
        min-height: 34px;
    }
    .iframe-card {
        margin-top: 12px;
        padding: 16px;
        border-radius: 10px;
        background: var(--colorBgBase);
        .ic-title {
            @include font(14px, 22px, 500);
            padding-bottom: 12px;
            &.border {
                border-bottom: 1px solid var(--colorBorderTertiary);
            }
        }
    }
    .ic-html {
        border-radius: 12px;
        background: var(--colorFillQuaternary);
        @include font(12px, 18px, 400);
        padding: 4px 16px;
    }
    .html-copy {
        cursor: pointer;
        padding: 4px 8px;
    }
    .iframe-code {
        padding: 16px;
        @include font(14px, 24px, 400);
        >div{
            padding: 0!important;
            margin: 0!important;
        }
        code{
            white-space: normal!important;
            @include font(14px, 24px, 400);
            width: 100%;
            display: block;
            word-wrap: break-word;
            background: transparent!important;
        }
    }
    .ant-form {
        padding-top: 12px;
        .ant-form-item {
            margin-bottom: 12px;
        }
    }

    .robot-iframe-right {
        width: 450px;
        padding: 10px 16px;
        flex-shrink: 0;
    }
    .preview-con {
        // light 色值
        --colorBorderSecondary: #E8E8E8;
        --colorBgBase: #FFFFFF;
        --colorText: rgba(0, 0, 0, 0.88);
        --colorTextTertiary: rgba(0, 0, 0, 0.45);

        margin-top: 12px;
        min-height: 700px;
        border-radius: 10px;
        border-left: 1px solid var(--colorBorderSecondary);
        box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.1);
        background: var(--colorBgBase);
        padding: 12px;
        .preview-text {
            padding: 12px;
        }
        .preview-icon{
            text-align: center;
            img{
                width: 48px;
                border-radius: 50%;
            }
        }
        .preview-title{
            @include font(18px, 24px, 600); //
            text-align: center;
        }
        .preview-desc{
            @include font(14px, 22px, 400);
            text-align: center;
        }
        .preview-input {
            padding: 8px 12px;
            margin-top: auto;
            border-radius: 12px;
            border: 1px solid var(--colorBorderSecondary);
            height: 100px;
            .preview-input-placeholder{
                @include font(14px, 24px, 400, var(--colorTextTertiary));
            }
            .preview-input-enter-img {
                margin-top: auto;
                margin-left: auto;
                width: 36px;
                height: 36px;
                background: var(--colorPrimarySecondary);
                border-radius: 50%;
                opacity: 0.2;
                .svg-icon {
                    color: var(--colorPrimaryContrasting);
                }
            }
        }
        // 预览暗色模式
        &.dark{
            // dark 色值
            --colorBorderSecondary: #404040;
            --colorBgBase: #101010;
            --colorText: rgba(255,255,255,0.85);
            --colorTextTertiary: rgba(255, 255, 255, 0.45);
        }
    }
}
