@import "../../../../styles/mixin.scss";
.bot-details-modal {
    .bot-info {
        padding-top: 52px;
    }
    .bot-icon {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        margin: 0 auto;
        border: 1px solid var(--colorBorderSecondary);
    }
    .bot-name {
        text-align: center;
        @include font(18px, 24px, 600);
    }
    .bot-user {
        @include font(14px, 22px, 400, var(--colorTextSecondary));
        .svg-icon {
            font-size: 14px;
            flex-shrink: 0;
        }
        .divide {
            flex-shrink: 0;
            width: 2px;
            height: 14px;
            line-height: 14px;
            margin: 0 6px;
            text-align: center;
            background: var(--colorIconTertiary);
            border-radius: 1px;
        }
        .usage {
            flex-shrink: 0;
        }
    }
    .bot-describe {
        text-align: center;
        @include font(14px, 22px, 400);
    }
    .bot-model {
        padding: 12px;
        border-radius: 12px;
        background: var(--colorFillTertiary);
    }
    .prop{
        flex-shrink: 0;
        min-width: 70px;
    }
    .value{
        @include font(14px, 22px, 500);
    }
    .bot-others{
        padding: 12px;
    }
}
