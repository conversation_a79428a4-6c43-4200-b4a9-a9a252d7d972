import { connect } from "react-redux";
import { Switch, Input, Toolt<PERSON>, Popover, Flex } from "antd";
import type { InputRef } from 'antd';
import { useTranslation } from 'react-i18next';
import { useState, useRef, useEffect, forwardRef, useImperativeHandle } from "react";
import Svgicon from "@/components/svgicon";
import classNames from "classnames";
const { TextArea } = Input;

const QuestionInputComponent = forwardRef((props: any, ref) => {
    const { t } = useTranslation();
    const { robotName, onSend,themeInfo } = props

    // console.log('ref', ref)
    // 自定义暴露给父组件的实例值
    useImperativeHandle(ref, () => ({
        // 初始化
        emit_init() {
            inputRef.current?.focus() // 输入框自动聚焦
        },
        // 清空输入
        emit_clear_input_value() {
            setPrompt("");
            setHasInputValue(false)
        }
    }))

    const inputRef = useRef<InputRef>(null);
    const [prompt, setPrompt] = useState("");
    const [isGoogleSearch, setIsGoogleSearch] = useState(false);
    const [hasInputValue, setHasInputValue] = useState(false); // 输入框是否有输入值
    const [hasEnter, setHasEnter] = useState(false); // 输入框是否有输入
    const [maxLength, setMaxLength] = useState(3000);

    /**method **/
    const handleOnChangePrompt = (e: any) => {
        // e.preventDefault();
        const value = e.target.value
        setPrompt(value);
        if (value?.trim() !== "") {
            setHasInputValue(true)
        } else {
            setHasInputValue(false)
        }
    };

    const handleKeyDownEnter = (e: any) => {
        if (e.shiftKey && e.keyCode === 13) {
            return;
        } else if (e.keyCode === 13) {
            e.preventDefault();
            if (!hasInputValue) return
            handleSend();
        }
    };


    // 发送请求
    const handleSend = async () => {
        if (prompt?.length > maxLength || !hasInputValue) return
        onSend && await onSend(prompt)
        // 清空输入框
    };

    useEffect(() => {
        inputRef.current?.focus() // 输入框自动聚焦
    }, [])

    // 监听输入框是否有输入
    useEffect(() => {
        const bool = prompt != '' && prompt != null && prompt != undefined
        setHasEnter(bool)
    }, [prompt])

    return (
        <div className="chat-input">
            <div className="text-area">
                <TextArea
                    key="chat-question-textarea"
                    ref={inputRef}
                    onChange={(e: any) => handleOnChangePrompt(e)}
                    onKeyDown={(e: any) => handleKeyDownEnter(e)}
                    value={prompt}
                    autoSize={{ minRows: 1, maxRows: 7 }}
                    maxLength={maxLength}
                    variant="borderless"
                />
                <span className={classNames(["placeholder", { 'hidden': hasEnter }])}>
                    {t('robot-iframe.talk-to', { robot: robotName })}
                </span>
                {
                    prompt?.length >= maxLength ?
                        <div className="max-count-error">{t('app.max-input-count', { n: maxLength })}</div>
                        : null
                }
            </div>
            <Flex onClick={handleSend} className={classNames(["enter-img", { "enter-img-disabled": !hasInputValue }])} align="center" justify={"center"}>
                <Svgicon draggable="false" svgName="icon_send" />
            </Flex>
        </div>
    );
});
const mapStateToProps = (state: any) => ({
    themeInfo: state.app.themeInfo
});
// 使用 connect 连接 Redux store，并启用 forwardRef
const ConnectedCounter = connect(mapStateToProps, null, null, { forwardRef: true })(QuestionInputComponent);
export default ConnectedCounter;
