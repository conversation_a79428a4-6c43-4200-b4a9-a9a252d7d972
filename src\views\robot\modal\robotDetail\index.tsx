
import { useState, useEffect } from "react";
import { useTranslation } from 'react-i18next';
import classnames from "classnames";
import { LoadingOutlined } from "@ant-design/icons";
import { Drawer,Spin } from "antd";
import Svgicon from '@/components/svgicon'

import "./index.scss"
import {connect} from "react-redux";
import { parseTime } from "../../../../utils/common"
import {api_query_robot} from "@/api/robotManage2";

// interface Props{
//   resource: any,
//   visible:boolean,
//   updateVisible: Function,
//   dispatch_api_get_robot_detail: Function
// }
// const RobotDetailModalComponent:React.FC<Props> =({resource, visible, updateVisible})=>{}
// 机器人详情弹窗
const RobotDetailModalComponent = (props: any) => {
  const { t } = useTranslation();
  /* props */
  const resource = props.resource;
  const isVisible = props.visible
  const appendParent = props.appendParent
  /* state */
  const [details, setDetails] = useState<any>({}) // 详情
  const [loading, setLoading] = useState(false);
  const nameArrs = [
    {
      name: t("robot.robot-id"),
      prop: "robot_id"
    },
    {
      name: t("robot.create-time"),
      prop: "createTime"
    },
    {
      name: t("robot.ai-model-creativity"),
      prop: "temperature"
    },
    {
      name: t("robot.relevance"),
      prop: "relevance_score"
    },
    {
      name: t("robot.knowlege-count"),
      prop: "knowledge_count"
    }
  ]

  /* methods */
  // 关闭弹框
  const handleClose = () => {
    // setVisible(false);
    props.updateVisible(false)
    setDetails({})
  };

  useEffect(() => {
    if (!isVisible) return
    const data ={
      seq: 0,
      robot_id:resource.robotId
    }
    setLoading(true)
    api_query_robot(data).then((res:any)=>{
      setLoading(false)
      const detail = res.data||{}
      // if (detail.createdAt){
      //   detail.createdAt = parseTime(detail.createdAt, "{y}-{m}-{d}")
      // }
      setDetails(detail)
    }).catch(()=>{
      setLoading(false)
    })
  }, [isVisible, resource])

  return (
    <div className={classnames(["robot-detail-modal",{"drawer-append-parent":appendParent}])}>
      <Drawer open={isVisible}
        width={560}
        title={t(["robot.details"])}
        className={classnames(["robot-detail-drawer", 'common-antd-drawer'])}
        onClose={handleClose}
        footer={null}
        mask={!appendParent}
        rootStyle={!appendParent?{}:{position: "relative",flex: 1, width: '560px',height: '100%',boxShadow:'none'}}
        getContainer={appendParent? false:'body'}
      >
        <div>
          {
            loading ? <Spin className="modal-spin" size="large" indicator={<LoadingOutlined style={{fontSize: 24}} spin/>}/>: 
          <>
            <div className="info">
              <img src={resource.robotLogo} alt="robot icon" />
              <div className="text">
                <span>{details.name}</span>
                <span>{t("robot.create-by", { user: details.username })}</span>
              </div>
            </div>
            <div className="describe">{details.introduction_message}</div>
            <div className="model">
              <div className="model-item">
                <span>{t("robot.llm-model")}</span>
                <span>{details?.default_LLM_display_name?.length>0?details.default_LLM_display_name.join("/"):'-'}</span>
              </div>
              <div className="model-item">
                <span>{t("robot.llm-model-enhance")}</span>
                <span>{details?.enhance_LLM_display_name?.length>0?details.enhance_LLM_display_name.join("/"):'-'}</span>
              </div>
            </div>
            <div className="props">
              {
                nameArrs.map((item: any, index: number) => (
                  <div className="prop-item" key={index}>
                    <div className="name">{item.name}</div>
                    <div className="value">{
                      item.prop=='createTime'? (details[item.prop]+'').split(" ")[0] : details[item.prop]
                    }</div>
                  </div>
                ))
              }
            </div>
          </>
          }
        </div>
      </Drawer>
    </div>
  );
};
const mapStateToProps = (state: any) => ({
  userInfo: state.app.userInfo,
  globalRegion: state.app.globalRegion
});
const mapDispatchToProps = (dispatch: any) => ({
})
const ConnectedCounter = connect(mapStateToProps, mapDispatchToProps)(RobotDetailModalComponent);
export default ConnectedCounter;
