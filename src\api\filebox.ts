/* filebox模块*/
import HTTP from "@/utils/request";

// 上传文件
export const api_upload_document_params = { seq: 0,robotId: "" };
export const api_upload_document = (data = api_upload_document_params) => HTTP.post("/filebox/uploadAttachment", data, {
  headers: {
    'Content-Type': 'multipart/form-data',
  }
})
export const dispatch_api_upload_document = (data = api_upload_document_params) => (dispatch: any) => new Promise((resolve, reject) => {
  api_upload_document(data).then(result => {
    resolve(result?.data?.data);
  }).catch(err => reject(err))
})

// 查询filebox当前上传的文件
export const api_get_user_uploaded_files_params = { seq: 0,robotId: "" };
export const api_get_user_uploaded_files = (data = api_get_user_uploaded_files_params) => HTTP.post("/filebox/userCurrentFilebox", data);
export const dispatch_api_get_user_uploaded_files = (params: any) => (dispatch: any) => new Promise((resolve, reject) => {
  const data = { ...api_get_user_uploaded_files_params, ...params }
  api_get_user_uploaded_files(data).then(result => {
    resolve(result?.data?.data);
  }).catch(err => reject(err))
})

//  查询filebox上传过的所有文件
export const api_get_user_filebox_params = { seq: 0, robotId: "" };
export const api_get_user_filebox = (data = api_get_user_filebox_params) => HTTP.post("/filebox/userFilebox", data);
export const dispatch_api_get_user_filebox = (params: any) => (dispatch: any) => new Promise((resolve, reject) => {
  const data = { ...api_get_user_filebox_params, ...params }
  api_get_user_filebox(data).then(result => {
    resolve(result?.data?.data);
  }).catch(err => reject(err))
})

// 删除已上传的文件
export const api_delete_attachment_params = { seq: 0, robotId: "" };
export const api_delete_attachment = (data = api_delete_attachment_params) => HTTP.post("/filebox/deleteAttachment", data);
export const dispatch_api_delete_attachment = (data = api_delete_attachment_params) => (dispatch: any) => new Promise((resolve, reject) => {
  api_delete_attachment(data).then(result => {
    resolve(result?.data?.data);
  }).catch(err => reject(err))
})

// 清空filebox
export const api_clear_current_filebox_params = { seq: 0, robotId: "" };
export const api_clear_current_filebox = (data = api_clear_current_filebox_params) => HTTP.post("/filebox/clearUserCurrentFilebox", data);
export const dispatch_api_clear_current_filebox = (data = api_clear_current_filebox_params) => (dispatch: any) => new Promise((resolve, reject) => {
  api_clear_current_filebox(data).then(result => {
    resolve(result?.data?.data);
  }).catch(err => reject(err))
})