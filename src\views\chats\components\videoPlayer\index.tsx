import Svgicon from '@/components/svgicon';
import { Button, Flex, Modal } from 'antd';
import { DownloadOutlined} from "@ant-design/icons";
import React, { useState } from 'react';
import { downloadVideo } from '@/utils/download'
import './index.scss'

const VideoPlayer = (props: any) => {
  const { url,onClose } = props
  const [visible, setVisible] = useState(true);
  // 关闭弹窗
  const onCloseView=(e:any)=>{
    e?.stopPropagation()
    // setVisible(false)
    onClose()
  }

  const onDownload = ()=>{
    const name =  url.slice(url.lastIndexOf(-1)+1)
    downloadVideo(url, name)
  }

  return (
    <Modal
      width="100%"
      title=""
      maskClosable={false}
      open={visible}
      footer={null}
      closeIcon={null}
      centered
      className="view-video-modal"
      wrapClassName="view-video-modalwrap"
    >

      <Flex vertical align="center">
        <Flex className="vi-top" gap={24} justify="flex-end" align="center">
          <Svgicon className="icon_close_view" svgName="icon_close" onClick={(e: any) => onCloseView(e)} />
        </Flex>
        <video width="640" height="360" controls>
          <source src={url} type="video/mp4" />
          您的浏览器不支持HTML5视频标签。
        </video>
        <div onClick={onDownload} className='download-icon'><DownloadOutlined /></div>
      </Flex>
    </Modal>
  );
}

export default VideoPlayer;