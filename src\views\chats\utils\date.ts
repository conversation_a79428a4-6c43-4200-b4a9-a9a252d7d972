
// 当日显示“今天HH:MM”；昨天显示“昨天HH:MM”，昨天以前今年以内显示“MM/DD HH:MM”；去年显示“YYYY/MM/DD HH:MM”
import i18next from "i18next";
export function FormatTimestamp(timestamp: number, t:any): string {
    interface DateYMD {
        y: number;
        m: number;
        d: number;
    }
    const date = new Date(timestamp);
    const now = new Date();

    // 获取本地时间关键值（类型明确）
    const getYMD = (d: Date): DateYMD => ({
        y: d.getFullYear(),
        m: d.getMonth(),    // 0-11
        d: d.getDate()      // 1-31
    });

    // 当前时间和目标时间（类型推断）
    const current = getYMD(now);
    const target = getYMD(date);

    // 时间格式化辅助函数
    const formatTime = (d: Date): string =>
        `${String(d.getHours()).padStart(2, '0')}:${String(d.getMinutes()).padStart(2, '0')}`;

    // 计算本地日期差异（类型安全）
    const currentDate = new Date(current.y, current.m, current.d).getTime();
    const targetDate = new Date(target.y, target.m, target.d).getTime();
    const diffDays = Math.round((currentDate - targetDate) / 86400000);

    // 生成日期字符串（类型保护）
    let dateStr: string;
    if (diffDays === 0) {
        dateStr = t('chat.today');
    } else if (diffDays === 1) {
        dateStr = t('chat.yesterday');
    } else if (target.y === current.y) {
        dateStr = `${String(target.m + 1).padStart(2, '0')}/${String(target.d).padStart(2, '0')}`;
    } else {
        dateStr = `${target.y}/${String(target.m + 1).padStart(2, '0')}/${String(target.d).padStart(2, '0')}`;
    }

    return `${dateStr} ${formatTime(date)}`;
}