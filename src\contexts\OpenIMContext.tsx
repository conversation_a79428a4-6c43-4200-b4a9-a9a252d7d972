// src/contexts/OpenIMContext.tsx
import React, { createContext, useContext, useEffect, useRef, useState } from 'react';
import ImChatService from '@/services/ImChatService';

type OpenIMContextType = {
  service: any;
  isReady: boolean;
  error: Error | null;
  isConnected: boolean,
  isConnectFailed: boolean,
  unreadMsgCount: any,
  newContacts: any,
  notifyRoot: any
};

const OpenIMContext = createContext<OpenIMContextType>({
  service: null,
  isReady: false,
  error: null,
  isConnected: false,
  isConnectFailed: false,
  unreadMsgCount: null,
  newContacts: [],
  notifyRoot: () => { }
});

export const OpenIMProvider: React.FC<{
  children: React.ReactNode;
  store: any
  //   config: { appKey: string; serverUrl: string };
}> = ({ children,store }) => {
  const userInfo = store.getState().app.userInfo
  // const currentUserId = String(userInfo.imUserId)
  const [currentUserId, setCurrentUserId] = useState<any>(null);
  const [service, setService] = useState<any>(null);
  const [isReady, setIsReady] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [isConnectFailed, setIsConnectFailed] = useState(false);
  const [unreadMsgCount, setUnreadMsgCount] = useState<any>(null);
  const [newContacts, setNewContacts] = useState<any>([]); // 收到的好友申请数据

  const newContactsRef = useRef<any>([])

  // 初始化
  useEffect(() => {
    // console.log('ImChat',ImChatService)
    const currentPath = window.location.pathname
    const noOpenImPaths = ['/widget-bot']
    if (noOpenImPaths.includes(currentPath)) return
    // console.log('IM 服务实例初始化')

    ImChatService.getInstance()
      .then((instance: any) => {
        console.log('-----全局 IM 服务实例初始化成功', instance)
        setService(instance);
        setIsReady(true);
      })
      .catch((err: any) => setError(err));
  }, []);
  
  useEffect(() => {
    if (userInfo){
      // console.log('userInfo change',userInfo.imUserId)
      setCurrentUserId(String(userInfo.imUserId))
    }
  }, [userInfo]);

  useEffect(() => {
    // console.log('---***```````service',service)
    if (!service) return
    // service.OnTotalUnreadMessageCountChanged
    // 订阅会话总未读
    service.registerCallBack("OnTotalUnreadMessageCountChanged", async (data: any) => {
      // data = 100
      console.log(`📢全局订阅会话总未读发生变化：`, data);
      setUnreadMsgCount(data)
    });

    // 订阅好友申请更新
    service.registerCallBack("OnFriendApplicationAdded", async (data: any) => {
      try {
        console.log('📢------>收到好友申请：', data)
        let _currentUserId = currentUserId
        if (_currentUserId===undefined){ // 获取不到userId,读取缓存
          const _userInfo = localStorage.getItem("AMA_USER_INFO") ? JSON.parse(localStorage.getItem("AMA_USER_INFO") as string) : {};
          _currentUserId = _userInfo.String(_userInfo.imUserId)
        }
        console.log('store',_currentUserId, data?.fromUserID === _currentUserId)
        // 当前用户 发出好友申请的 不处理
        if (data?.fromUserID === _currentUserId) return
        let result: any = [...newContactsRef.current, data]
        // console.log('旧好友申请通知:', newContactsRef.current)
        // 去重
        result = deduplicateBy('fromUserID', 'createTime', result)
        // console.log('新好友申请通知更新:', result)
        newContactsRef.current = result
        setNewContacts(result)
      } catch (error) {
        
      }
    });

    // 订阅连接状态变化
    const unsubscribe = service.onConnectionChange((connected: boolean) => {
      console.log('📢全局订阅连接状态变化', connected)
      setIsConnected(connected);
    });
    return unsubscribe;
  }, [service])

  // 去重
  const deduplicateBy = (deduplicateProp: string, compareProp: string, arr: any) => {
    try {
      const map = new Map();
      arr.forEach((item: any) => {
        const current = map.get(item[deduplicateProp]);
        if (!current || item[compareProp] > current[compareProp]) {
          map.set(item[deduplicateProp], item);
        }
      });

      return Array.from(map.values());
    } catch (error) {
      return arr
    }
  }

  // 子组件调用此函数来发送通知
  const handleChildNotification = (message: any, resource:any) => {
    console.log("收到子组件通知:", message,resource);
    if (message === 'UpdateNewContacts'){
      const deleteUserID = resource?.userID
      let result: any = newContactsRef.current.filter((i:any)=>{
        return i?.fromUserID!== deleteUserID
      })
      newContactsRef.current = result
      setNewContacts(result)
    }
  }

  return (
    <OpenIMContext.Provider
      value={{ service, isReady, error, isConnected, unreadMsgCount, isConnectFailed, newContacts, notifyRoot: handleChildNotification }}>
      {children}
    </OpenIMContext.Provider>
  );
};

export const useOpenIM = () => useContext(OpenIMContext);