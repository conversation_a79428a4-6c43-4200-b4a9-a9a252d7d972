@import "../../../../../styles/mixin.scss";
.ama-upload {
  margin-left: 10px;
  margin-top: 10px;
  .ant-spin {
    color: var(--colorPrimary);
  }
  img{
    user-select: none;
  }
}
.upload-trigger {
  width: 32px;
  height: 32px;
  font-size: 16px;
  border-radius: 12px;
  background: var(--colorBgElevated);
  border: 1px solid var(--colorBorderSecondary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--colorIcon);
  &.uploaded {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }
  &.document{
    font-size: 20px;
  }
}
$listMaxHeight: 407px;
$listMaxWidth: 370px;
$listSpace:12px;
$listSpaceBottom:8px;
@mixin common-pover-inner(){
  overflow: hidden;
  background: var(--colorBgElevated );
  box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.1);;
  border-radius: 12px;
  margin-bottom: 20px;
  padding: $listSpace $listSpace $listSpaceBottom;
  margin-left: -10px ;
}
.uploaded-list-popover {
  // &.popover-loading .ant-popover-inner {
  //   margin-bottom: 60px;
  // }
  .ant-popover-inner {
    width: $listMaxWidth;
    height: $listMaxHeight;
    @include common-pover-inner();
    padding-right: 4px;
  }
  .ant-popover-inner-content{
    height: calc(100% - $listSpace - $listSpaceBottom);
  }
  .top {
    display: flex;
    align-items: center;
    flex-shrink: 0;
  }
  .title {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    font-weight: 600;
    font-size: 16px;
    color: var(--colorText);
    line-height: 22px;
    .tip {
      font-weight: 400;
      font-size: 12px;
      color: var(--colorTextSecondary);
      line-height: 17px;
      margin-top: 6px;
    }
  }
  
  .upload-trigger {
    color: var(--colorText);
    margin-left: auto;
    &.clear-all{
      margin-left: auto;
      margin-right: 10px;
      font-size: 16px;
      cursor: pointer;
      &+.upload-trigger{
        margin-left: inherit;
      }
    }
  }
  .ant-upload-disabled .noupload-trigger-tooltip{
    cursor: not-allowed;
  }
  .bottom {
    // margin-top: 12px;
    flex: 1;
    @include  webkitScrollStyle($width:4px);
    overflow: auto;
    padding-right: 8px;
    .operate {
      position: absolute;
      right: 10px;
      width: 16px;
      height: 16px;
      top: 50%;
      transform: translateY(-50%);
      z-index: 20;
      &.delete{
        display: none;
      }
    }
    .file-item {
      padding: 10px;
      padding-right: 36px;
      border-radius: 10px;
      position: relative;
      cursor: pointer;
      border: 1px solid transparent;
      &:hover {
        background: var(--colorFillQuaternary);
        border: 1px solid var(--colorBorderSecondary);
        border-radius: 12px;
        .delete {
          display: inline-block;
        }
      }
    }
    .file-name {
      max-width: 300px;
      font-size: 12px;
      color: var(--colorText);
      line-height: 16px;
      margin-bottom: 6px;
      // display: flex;
      // align-items: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      // .prefix{
      //   overflow: hidden;
      //   text-overflow: ellipsis;
      //   white-space: nowrap;
      // }
      // .suffix{
      //   flex-shrink: 0;
      // }
    }

    .file-type-size {
      max-width: 300px;
      display: flex;
      align-items: center;
      margin-top: 6px;
      .type-image {
        width: 16px;
        height: 16px;
        font-size: 16px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      > span {
        margin-right: 5px;
        font-weight: 400;
        font-size: 12px;
        color: var(--colorTextSecondary);
        line-height: 16px;
      }
    }
    .chat-item{
      font-size: 12px;
      color: var(--colorText);
      line-height: 16px;
      // padding-top: 9px;
      // padding-bottom: 9px;
      max-height: 76px;
      box-sizing: border-box;
    }
    .chat-q{
      @include ellipsis-multiline(1);
    }
    .chat-a{
      @include ellipsis-multiline(2);
      margin-top: 8px;
    }
  }
  .bottom-operate{
    flex-shrink: 0;
    padding: 8px 8px 0 0;
    button{
      border: 0;
      font-weight: 500;
      font-size: 14px;
      line-height: 22px;
    }
    .ant-upload-wrapper{
      flex: 1;
      .ant-upload{
        width: 100%;
        display: inline-block;
      }
      .plus-btn{
        width: 100%;
      }
    }
    .plus-btn,.plus-btn:hover{
      flex: 1;
    }
  }
  .empty-tip{
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: var(--colorTextSecondary);
    line-height: 22px;
    padding: 0 9px;
    text-align: center;
  }
}
.uploaded-list{
  height: calc($listMaxHeight - $listSpace - $listSpaceBottom);
  display: flex;
  flex-direction: column;
  .ant-tabs.common-tab{
    .ant-tabs-nav{
      margin-bottom: 6px;
    }
    .ant-tabs-tab{
      padding-top: 0;
      +.ant-tabs-tab{
        margin-left: 24px;
      }
    }
  }
}
.upload-operate-popover{
  width: $listMaxWidth;
  &.popover-loading .ant-popover-inner {
    margin-bottom: 60px;
  }
  .ant-popover-inner {
    @include common-pover-inner();
    padding: 16px;
  }
  .ope-item{
    display: flex;
    align-items: center;
    cursor: pointer;
  }
  .ope-icon{
    font-size: 24px;
    padding: 12px;
    display: flex;
    align-items: center;
    background: var(--colorFillSecondary);
    border-radius: 12px;
    color: var(--colorIconSecondary);
    margin-right: 15px;
    img{
      width: 24px;
      height: 24px;
      user-select: none;
    }
  }
  .ant-upload-wrapper {
    margin-bottom: 16px;
    display: block;
  }
  .ope-text{
    display: flex;
    flex-direction: column;
    gap:5px 0;
    span{
      font-weight: 500;
      font-size: 13px;
      color: var(--colorText);
      line-height: 16px;
      &:last-child{
        font-weight: 400;
        font-size: 12px;
        color: var(--colorTextSecondary);
        line-height: 16px;
      }
    }
  }
}

.ant-notification-notice-wrapper .upload-notification-error {
  // width: auto!important;
  width: 470px !important;

  .ant-notification-notice-message {
    max-width: 390px !important;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}