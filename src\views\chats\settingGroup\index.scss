@import "../../../styles/mixin.scss";
// .chat-setting-group-modal{
    .chat-setting-group-drawer {
        .ant-drawer-header{
            padding: 12px 16px;
        }
        .ant-drawer-body{
            padding: 0;
        }
        
        // .ant-drawer-close .svg-icon{
        //     font-size: 20px;   
        //     width: 20px;
        //     height: 20px;
        // }
        .group-info{
            margin-top: 18px;
            padding: 0 16px;
        }
        .group-avatar{
            position: relative;
            flex-shrink: 0;
            cursor: pointer;
            border-radius: 12px;
            .change-icon{
                position: absolute;
                bottom: 0;
                right: 0;
                width: 16px;
                z-index: 200;
            }
        }
        .group-name{
            flex: 1;
            min-height: 20px;
            @include ellipsis();
        }
        .edit-group-icon{
            font-size: 16px;
            cursor: pointer;
            color: var(--colorIconTertiary);
            margin-left: auto;
            flex-shrink: 0;
        }
        .group-members{
            padding: 0 16px;
            margin-top: 20px;
            .gm-title{
                @include font(16px,24px,600);
                padding: 6px 0;
            }
            .gm-users{
                .gm-name{
                    width: 56px;
                    @include font(10px,18px,400,var(--colorTextSecondary));
                    @include ellipsis();
                    text-align: center;
                }
            }
            .view-more{
                width: 100%;
                cursor: pointer;
                padding: 4px 0;
                @include font(12px,18px,400,var(--colorPrimary));
            }
            .add-user{
                cursor: pointer;
                width: 56px;
                .icon{
                    height: 32px;
                    width: 32px;
                    font-size: 16px;
                    border-radius: 50%;
                    background: var(--colorFillTertiary);
                    border: 1px solid var(--colorBorderSecondary);
                    color: var(--colorIconSecondary);
                }
                >.text{
                    @include font(10px,18px,400);
                }
            }
        }
        .icon-operate{
            margin-left: auto;
            font-size: 20px;
            padding: 8px;
            cursor: pointer;
            color: var(--Icon/colorIcon);
            &.icon-back{
                margin-left: 0;
            }
        }
        .all-member-list{
            padding: 0 8px;
            .ml-list-item{
                padding: 10px 8px;
                cursor: pointer;
                &:hover{
                    background: var(--colorFillTertiary);
                    border-radius: 12px;
                    .delete-btn{
                        display: block;
                    }
                }
            }
            .ml-list-item-left{
                max-width: 100%;
                overflow: hidden;
                .name{
                    flex: initial;
                    max-width: 100%;
                }
            }
            .name{
                flex: 1;
                @include font(14px,22px,500);
                @include ellipsis();
            }
            .delete-btn{
                margin-left: auto;
                display: none;
                padding: 4px 6px;
                text-align: center;
                height: 26px;
                width: 26px;
                box-sizing: border-box;
                .ant-btn-icon{
                    font-size: 14px;
                }
            }
            .group-admin{
                background: #E8F3FF;
                padding: 2px 6px;
                border-radius: 6px;
                @include font(10px,14px,400,#165DFF);
            }
        }
    }
    
// }