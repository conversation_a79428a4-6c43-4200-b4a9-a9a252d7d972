.robot-wrapper {
  height: 100%;
  width: 100%;
  background: var(--colorBgBase);

  .robot-center {
    padding: 0;
    height: 100%;
    $contentMaxWidth: 670px;
    .robot-content {
      display: flex;
      height: 100%;
      width: 100%;

      .left {
        flex-shrink: 0;
        width: 280px;
        height: 100%;
        overflow: hidden;

        .dialog-list {
          &.is-action {
            .item {
              &.active {
                .desc {
                  .name {
                    color: var(--colorPrimary);
                  }
                }
              }
            }
          }

          &.isScroll {
            max-height: 355px;
          }

          .item {
            padding: 0 30px;
            height: 70px;
            display: flex;
            align-items: center;
            cursor: pointer;
            position: relative;

            &:hover {
              .del {
                display: flex;
              }
            }

            .pic {
              position: relative;
              width: 40px;
              height: 40px;
              min-width: 40px;
              min-height: 40px;

              img {
                width: 100%;
                height: 100%;
                border-radius: 50%;
                user-select: none;

                &.ai-avatar {
                  position: absolute;
                  width: 20px;
                  height: 20px;
                  bottom: -5px;
                  right: -5px;
                  border-radius: 50%;
                }
              }
            }

            .desc {
              margin-left: 15px;

              .name {
                font-weight: 600;
                font-size: var(--font-size-large);
                color: var(--colorText);
                width: 168px;

                span {
                  width: 100%;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                  display: block;
                }
              }

              .api {
                margin-top: 2px;
                font-size: var(--font-size-small);
                color: var(--colorTextSecondary);
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                max-width: 200px;
              }
            }

            .del {
              position: absolute;
              right: 16px;
              margin: 0;
              top: 0;
              bottom: 0;
              height: 100%;
              display: none;
              align-items: center;

              img {
                user-select: none;
                width: 16px;
                height: 16px;
              }
            }

            &.active {
              background: var(--colorFillTertiary);
            }
          }

          .prompt {
            font-size: var(--font-size-small);
            color: var(--colorTextSecondary);
            height: 70px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 0;
          }
        }
      }

      .right {
        background: var(--colorBgBase);
        border-radius: 20px;
        display: flex;
        flex-direction: column;
        align-items: center;
        flex: 1;
        overflow: hidden;
        position: relative;
        &.robotManage, &.botsMarketplace,&.orphaned{
          border-radius:0;
        }

        .header {
          flex-shrink: 0;
          width: 100%;
          height: var(--robot-right-header);
          box-shadow: 0px 1px 0px 0px var(--colorBorderSecondary);
          padding: 0 20px;
          display: flex;
          align-items: center;
          justify-content: initial;

          .check-all {
            margin-right: 12px;
          }

          .name-con {
            flex: 1;
            overflow-x: hidden;
            display: flex;
            .name{
              flex: 0 1 auto;
              display: flex;
              align-items: center;
              cursor: pointer;
            }

            .pic {
              display: flex;
              align-items: center;
              justify-content: center;
              img {
                width: 32px;
                max-height: 32px;
                user-select: none;
                border-radius: 10px;
                border: 1px solid var(--colorBorderSecondary);
              }
            }

            .text {
              margin-left: 8px;
              max-width: 80%;
              display: flex;
              align-items: center;
              font-size: 18px;
              line-height: 24px;
              font-weight: 600;
              @include ellipsis-multiline(1);

              .anticon-edit {
                margin-left: 16px;
                cursor: pointer;
              }
            }
          }

          .operate {
            margin-left: auto;
            display: flex;
            align-items: center;
            flex-shrink: 0;
          }

          .select-conver {
            margin-left: 16px;
            min-width: 104px;
          }
        }

        .right-main{
          flex: 1;
          overflow: hidden;
          overflow-x: auto;
          position: relative;
          // &.auto-size{
          //   .infinite-scroll-component__outerdiv {
          //     width: 100%!important;
          //   }
          //   .submit {
          //     width: 100%!important;
          //   }
          // }
        }

        $full-height: calc(
          100vh - var(--robot-right-header) - var(--footer-height) - var(--robot-footer-space) * 2 - 20px
        );

        .response {
          flex: 1;
          padding: 0 44px;
          overflow-y: auto;
          height: calc(100% - var(--robot-right-header)  - var(--footer-height) - var(--robot-footer-space) * 2);
          width: 100%;

          .prompt {
            padding: 24px 0 21px;
            max-width: 100%;
            overflow: hidden;
            border-bottom: 1px solid var(--colorBorderSecondary);
            color: var(--colorText);
            word-wrap: break-word;
          }

          .desc {
            margin-left: 16px;
          }

          .me-ai {
            position: relative;
            padding: 16px;
            cursor: pointer;
            &:hover{
              .additional-operation-right{
                visibility: visible;
              }
            }

            &.check-status {
              cursor: pointer;
            }
          }

          .item {
            display: flex;
            flex-direction: column;
            margin-bottom: 0;
            position: relative;

            @mixin selected{
              background: var(--colorFillQuaternary);
              border-radius: 10px;
            }

            &.open-check .me-ai {
              padding-left: 74px;
              cursor: pointer;
              &.is-checked{
                @include selected;
              }
              &:hover{
                @include selected;
              }
            }
            
            &.welcome {
              padding: 30px 0 15px;
              font-size: 14px;
              line-height: 24px;
            }

            .ant-checkbox-wrapper {
              margin-right: 40px;
              position: absolute;
              top: 50%;
              left: 16px;
              z-index: 100;
              transform: translateY(-50%);
            }

            @mixin chat-title {
              font-weight: 600;
              font-size: 15px;
              color: var(--colorText);
              line-height: 22px;
            }

            .me-ai {
              position: relative;
              padding: 15px 0;
            }

            .me {
              @include flex($flex-direction: column, $align-items: flex-start);
              margin: 0 0 22px 0;
              
              &.hidden {
                display: none;
                // visibility: hidden;
              }

              .me-info {
                @include flex;
                @include chat-title;

                .ama-user-avatar {
                  margin-right: 10px;
                }
              }

              .me-res {
                display: flex;
                flex-direction: column;
                justify-content: flex-end;
                padding: 8px 30px;
                max-width: 100%;
              }

              p {
                color: var(--colorText);
                font-size: var(--font-size-base);
                line-height: 22px;
                word-wrap: break-word;
                white-space: pre-wrap;
                width: 100%;
                margin-bottom: 0;
              }
            }

            .ai {
              display: flex;
              align-items: center;

              .ai-res {
                width: 100%;
                @include flex($flex-direction: column, $align-items: flex-start);
              }

              //pre {
              //  padding: 15px 30px;
              //  background: $base-markdown-color;
              //  code {
              //    color: var(--colorBgBase);
              //  }
              //}
            }
            
            .ai-info {
              width: 100%;
              @include flex();
              margin-bottom: 10px;
              @include chat-title;

              img,svg {
                width: 20px;
                height: 20px;
                margin-right: 10px;
                border-radius: 50%;
                user-select: none;
                font-size: 20px;
              }
            }
            .result {
              padding-left: 30px;
              width: 100%;
              word-wrap: break-word;
            }

            &.last-index {
              min-height: $full-height;
              margin-bottom: 0;
              padding: 0;
            }
          }

          .context-cleared {
            .ant-divider-plain.ant-divider-with-text {
              font-size: 12px;
              color: var(--colorTextSecondary);
              border-color: var(--colorBorderSecondary);
              line-height: 16px;
              margin: 15px 0;
            }

            .anticon-undo {
              font-size: 16px;
              margin-left: 7px;
              cursor: pointer;
              transform: rotate(90deg);
              vertical-align: text-top;
            }
          }

          .infinite-scroll-component__outerdiv {
            width: $contentMaxWidth;
            margin: 0 auto;
          }
        }
      }

      .right-content {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
      }

      .drag-drop-overlay {
        background: var(--colorBgElevated);
        opacity: 0.9;
        border-radius: 12px;
        position: absolute;
        top: calc(var(--robot-right-header) - 1px);
        right: -1px;
        left: -1px;
        bottom: -1px;
        z-index: 5000;
        // border: 2px dashed var(--colorTextQuaternary);
        top: 0;

        &::after {
          content: attr(data-content);
          text-align: center;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          font-weight: 600;
          font-size: 18px;
          color: var(--colorText);
          line-height: 24px;
          background: url("../../icons/png/robot/icon_drag.png") no-repeat left center;
          background-size: 24px;
          padding-left: 32px;
        }
      }

      .submit {
        flex-shrink: 0;
        min-height: 50px;
        padding: 0;
        border-radius: 0px 0px 16px 16px;
        margin: var(--robot-footer-space) 0;
        position: relative;
        display: flex;
        width: $contentMaxWidth;

        &.hidden {
          display: none;
        }
      }

      .clear-current-chat {
        flex-shrink: 0;
        margin-right: 10px;
        cursor: pointer;
        width: 50px;
        height: 50px;
        user-select: none;
        font-size: 26px;
        background: var(--colorBgElevated);
        border-radius: 12px;
        border: 1px solid var(--colorBorderSecondary);
        color: var(--colorIcon);
        &.disabled {
          opacity: 0.4;
          cursor: not-allowed;
        }
      }

      .submit-right {
        flex: 1;
        border: 1px solid var(--colorBorderSecondary);
        border-radius: 16px;
        max-width: calc(100% - 40px);
      }

      .input-area {
        width: 100%;
        display: flex;
        // overflow-x: hidden;
      }

      .additional-operation {
        @include flex($justify-content: space-between);
        gap: 0 20px;

        .additional-operation-left {
          @include flex($justify-content: flex-start);
          font-size: 12px;
          color: var(--colorTextTertiary);
          line-height: 16px;
          gap: 0 20px;
          flex-wrap: wrap;

          cursor: pointer;

          > span {
            @include flex;
            padding-top: 8px;
          }

          .anticon {
            margin-right: 4px;
            font-size: 14px;
          }

          .svg-icon {
            width: 18px;
            height: 18px;
            margin-right: 4px;
          }
        }

        .additional-operation-right {
          margin-left: auto;
          @include flex();
          color: var(--colorTextTertiary);
          font-size: 14px;
          font-weight: 600;
          visibility: hidden;

          .ant-spin {
            padding: 8px;
          }

          > span {
            display: inline-block;
            width: 32px;
            padding: 8px;
            cursor: pointer;
            &:hover {
              background: var(--colorPrimaryBg);
              border-radius: 8px;
              color: var(--colorText);
            }
            &.enhance{
              padding: 0;
              >span{
                display: inline-block;
                width: 100%;
                padding: 8px;
              }
            }
          }

          .svg-icon {
            font-size: 16px;
          }
        }

        &.hidden {
          display: none;
        }
      }
    }

    .select-chat-status {
      width: $contentMaxWidth;
      margin: 0 auto;
      display: flex;
      align-items: center;

      > span {
        flex-shrink: 0;
        font-weight: 600;
        font-size: 18px;
        color: var(--colorText);
        line-height: 24px;
      }

      .select-chat-btns {
        margin-left: auto;
      }

      button {
        box-shadow: none;
      }
    }
  }
}

.robot-wrapper {
  .ant-collapse {
    border: 0;
    background-color: transparent;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    overflow-x: hidden;
    height: 100%;
  }

  .ant-collapse .ant-collapse-content > .ant-collapse-content-box {
    padding: 0;
  }

  .ant-collapse > .ant-collapse-item > .ant-collapse-header {
    padding: 14px 30px;
  }

  .ant-collapse > .ant-collapse-item {
    border-bottom: 0;
  }

  .ant-collapse > .ant-collapse-item > .ant-collapse-header .ant-collapse-header-text {
    font-weight: 600;
    font-size: var(--font-size-large);
    color: var(--colorText);
  }

  .ant-collapse .ant-collapse-content {
    border-top: 0;
  }
}

.lang-box {
  min-width: 86px;
  background: var(--colorFillQuaternary);
  border-radius: 10px;
  padding: 10px 15px;

  .item {
    padding: 10px 0;
    cursor: pointer;
    border-bottom: 1px var(--colorBorderSecondary) solid;

    &:last-child {
      border-bottom: 0;
    }

    .desc {
      font-size: var(--font-size-base);
      color: var(--colorText);
    }
  }
}

.create-dialog-modal {
  background: var(--colorBgBase);
  padding: 24px 20px;
  box-shadow: 0 0 8px 0 rgba(28, 33, 42, 0.1);
  border-radius: 15px;
  position: relative;

  .close {
    position: absolute;
    right: 15px;
    top: 5px;
    cursor: pointer;
  }

  .title {
    font-size: 17px;
    font-weight: 600;
    color: var(--colorText);
    text-align: center;
  }

  .prompt {
    font-size: var(--font-size-base);
    font-weight: 400;
    color: var(--colorText);
    margin-top: 18px;
    text-align: center;
  }

  .dialog-item {
    margin-top: 18px;

    &.last {
      margin-bottom: 32px;
    }

    .ant-checkbox-group {
      flex-direction: column;
    }

    .ant-checkbox-wrapper {
      margin-bottom: 8px;
    }

    .ant-checkbox-checked:after {
      border: 2px solid var(--colorPrimary);
    }

    .ant-checkbox-wrapper:not(.ant-checkbox-wrapper-disabled):hover
      .ant-checkbox-checked:not(.ant-checkbox-disabled):after,
    .ant-checkbox-wrapper:not(.ant-checkbox-wrapper-disabled):hover .ant-checkbox-inner,
    .ant-checkbox:not(.ant-checkbox-disabled):hover .ant-checkbox-inner,
    .ant-checkbox:not(.ant-checkbox-disabled):hover .ant-checkbox-inner,
    .ant-checkbox-checked:not(.ant-checkbox-disabled):hover .ant-checkbox-inner {
      border-color: var(--colorPrimary);
    }

    .ant-checkbox-wrapper:not(.ant-checkbox-wrapper-disabled):hover
      .ant-checkbox-checked:not(.ant-checkbox-disabled)
      .ant-checkbox-inner,
    .ant-checkbox-checked .ant-checkbox-inner {
      background-color: var(--colorPrimary);
      border-color: var(--colorPrimary);
    }

    .ant-checkbox .ant-checkbox-input:focus-visible + .ant-checkbox-inner {
      outline: 4px solid var(--colorPrimary);
    }

    .center-text {
      text-align: center;
      font-size: var(--font-size-base);
      font-weight: 400;
      color: var(--colorText);
    }

    .textarea-wrap {
      position: relative;

      textarea {
        background: var(--colorFillQuaternary);
        height: 260px;
        width: 100%;
        padding: 15px;

        &.assist {
          height: auto;
        }
      }

      .length {
        position: absolute;
        right: 0;
        bottom: -20px;
        font-size: var(--font-size-base);
        color: var(--colorTextSecondary);
      }
    }

    .tips {
      color: var(--colorTextSecondary);
      font-size: var(--font-size-small);
    }

    .dialog-title {
      margin-top: 5px;
      border-radius: 10px;
      color: var(--colorText);

      .ant-input {
        background: var(--colorFillQuaternary);

        &:hover {
          border-color: #d9d9d9;
        }

        &:focus {
          border-color: #d9d9d9;
        }

        &outline {
        }
      }
    }
  }

  .btn-wrap {
    margin-top: 18px;
    display: flex;
    align-items: center;
    height: 40px;
    justify-content: center;

    .btn {
      border-radius: 20px;
      flex: 1;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      &.cancel {
        border: 1px solid #c8cdd5;
        margin-right: 15px;
        color: var(--colorText);
      }

      &.confirm {
        color: var(--colorBgBase);
        background: var(--colorPrimary);
      }
    }
  }
}

.homebot-describe-popover {
  max-width: 400px;
}

.ant-divider-plain.ant-divider-with-text.no-more-data {
  color: var(--colorTextSecondary);
  text-align: center;
  font-size: 12px;
  line-height: 16px;
  margin-bottom: 0;
}

.loading-more {
  text-align: center;
  padding-top: 20px;
}

.robot-detail {
  color: var(--colorIconSecondary);
  font-size: 16px;
  margin-left: 8px;
  cursor: pointer;
}

.modal-spin {
  width: 100%;
  height: 50vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.copy-tooltip {
  .copyed .anticon {
    color: var(--colorSuccess);
    margin-right: 4px;
  }
  .ant-tooltip-inner {
    padding: 8px 10px;
  }
}

.loading-area{
  margin-top:0;
  margin-left:30px;
  .loading-text{
    font-weight: 400;
    font-size: 14px;
    color: var(--colorText);
    line-height: 24px;
    margin-right: 13px;
  }
  &.is-think{
    border-radius: 8px;
    background: var(--colorFillTertiary);
    padding: 4px 8px;
    color: var(--colorText);
    .loading-text{
      margin-left: 4px;
      margin-right: 0;
      display: flex;
      align-items: center;
      gap: 4px;
    }
    svg{
      width: 16px;
      height: 16px;
      font-size: 16px;
    }
  }
}
// 暗色
.dark{
  .robot-wrapper .robot-center .robot-content .drag-drop-overlay::after {
    background-image: url("../../icons/png/robot/icon_drag_dark.png");
  }
}


