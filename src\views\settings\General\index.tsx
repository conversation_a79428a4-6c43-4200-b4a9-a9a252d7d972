import { connect } from 'react-redux';
import { Flex, Segmented, Select } from 'antd';
import { useState, useEffect } from "react";
import { useTranslation } from 'react-i18next';
import './index.scss';
import { changeTheme, changeFaviconTitle } from "@/theme"
import { change_app_themeInfo, change_app_globalLang } from "@/store/action";
import SvgIcon from '@components/svgicon';

const langeItems = [
  {
    value: 'zh',
    label: '简体中文'
  },
  {
    value: 'hk',
    label: '繁體中文'
  },
  {
    value: 'en',
    label: 'English'
  }
]

// 快捷键
const General = (props: any) => {
  const { t } = useTranslation();

  const { globalLang } = props;
  const [themeActive, setThemeActive] = useState("auto") // 外观选中
  const [langeActive, setLangeActive] = useState(globalLang || 'en') // 语言选中

  // 外观主题设置项
  const themeItems: any[] = [
    { label: t('automatic'), value: "auto", key: "auto" }, // 跟随操作系统颜色
    { label: t('light'), value: "light", key: "light" },
    { label: t('dark'), value: "dark", key: "dark" },
  ];

  // 初始化
  useEffect(() => {
    const storageTheme = localStorage.getItem("AMA_APPEARANCE")
    // console.log('storageTheme', storageTheme)
    if (storageTheme) {
      setThemeActive(storageTheme)
    }
    
  }, [])

  // 切换外观
  const onChangeTheme = async (theme: any) => {
    try {
      setThemeActive(theme)
      // 记录用户所选外观
      localStorage.setItem("AMA_APPEARANCE", theme)
      // saveSetting(theme)
      const base = {
        colorPrimarySecondary: props.themeInfo.colorPrimarySecondary,
        colorPrimaryContrasting: props.themeInfo.colorPrimaryContrasting
      }
      const result = await changeTheme(base, theme)
      const dynamicColors = { ...props.themeInfo, ...result?.themeColors, theme: result?.activeTheme }
      if (theme !== 'auto') dynamicColors.theme = theme
      await props.change_app_themeInfo(dynamicColors)
      // 修改浏览器标签页角标
      changeFaviconTitle()
    } catch (error) {

    }
  }

  function getKeyByValue(object:any, value:any) {
    return Object.keys(object).find(key => object[key] === value);
  }
  
  const onChangeLange = (lang: string) => {
    setLangeActive(lang)
    // saveSetting(themeActive, lang)
    props.change_app_globalLang(lang)
  }

  return (
    <Flex vertical className="general-wrapper wrapper">
      <div className="title">{t('setting.general')}</div>
      <Flex className='general-list' vertical gap={20}>
        <Flex gap={20} align="center">
          <div className='name'>{t("appearance")}</div>
          <div className='config'>
            <Segmented<string>
              className="common-segmented"
              value={themeActive}
              options={themeItems.map(i => {
                i.label = t(i.label)
                return i
              })}
              onChange={onChangeTheme}
            />
          </div>
        </Flex>
        <Flex gap={20} align="center">
          <div className='name'>{t("system-language")}</div>
          <div className='config'>
            <Select value={langeActive} onChange={onChangeLange}
              className="lang-selector"
              placeholder={t('common.please-select') as string}
              variant="filled"
              suffixIcon={<SvgIcon svgName="icon_select_suffix" />}
              options={langeItems}
            >
            </Select>
          </div>
        </Flex>
      </Flex>
    </Flex>
  );
};

const mapStateToProps = (state: any) => ({
  userInfo: state.app.userInfo,
  themeInfo: state.app.themeInfo,
  globalLang: state.app.globalLang,
});
const mapDispatchToProps = (dispatch: any) => ({
  change_app_themeInfo: (data: any) => dispatch(change_app_themeInfo(data)),
  change_app_globalLang: (globalLang: string) => dispatch(change_app_globalLang(globalLang)),
});
export default connect(mapStateToProps, mapDispatchToProps)(General);
