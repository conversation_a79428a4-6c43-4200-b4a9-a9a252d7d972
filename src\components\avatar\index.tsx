// import pinyin from 'pinyin';
import './index.scss'
import { pinyin } from 'pinyin-pro';
// const initials = require('initials');
// const addPx = require('add-px');
// const contrast = require('contrast');

const defaultColors = [
  '#E6E6E6',
  '#BEDAFF',
  '#FFF1B8',
  '#CFF7D3',
  '#FFCCC7',
  '#FFD8BF'
];
const defaultTextColors: any = {
  '#E6E6E6': '#1E1E1E',
  '#BEDAFF': '#031A79',
  '#FFF1B8': '#874D00',
  '#CFF7D3': '#024023',
  '#FFCCC7': '#820014',
  '#FFD8BF': '#871400'
}

export function sumChars(str: string) {
  let sum = 0;
  for (let i = 0; i < str.length; i++) {
    sum += str.charCodeAt(i);
  }

  return sum;
}

export const getInitials = (name: any) => {
  try {
    // 中文转拼音首字母（如 "张三" → "ZS"）
    // const pinyinLetters = pinyin(name, { style: 'firstLetter' });
    // return pinyinLetters.slice(0, 2).join('').toUpperCase();
    // return pinyinPro.pinyin(name, { pattern: 'first' })
    // return name
    // 判断是否包含中文字符
    const isChinese = /[\u4E00-\u9FFF]/.test(name);

    if (isChinese) {
      // 处理中文名：取名字部分（去掉姓氏）的每个字首字母
      const chars = Array.from(name);
      if (chars.length < 2) return ''; // 单字姓返回空
      const givenNameChars = chars.slice(1); // 去掉姓氏

      return givenNameChars.map((char: any) => {
        const firstLetter = pinyin(char, {
          pattern: 'first',
          toneType: 'none'
        }).toUpperCase();
        return firstLetter || '';
      }).join('');
    } else {
      // 处理英文名：取第一个和最后一个单词的首字母
      const parts = name.trim().split(/\s+/).filter((part: any) => part.length > 0);
      if (parts.length === 0) return '';

      const firstInitial = parts[0][0].toUpperCase();
      const lastInitial = parts.length > 1
        ? parts[parts.length - 1][0].toUpperCase()
        : '';
      return firstInitial + lastInitial;
    }
  } catch (error) {
    // console.log('error',error)
    return ''
  }
};

// 获取首字母
export const getInitialChar = (name: any) => {
  return getInitials(name).charAt(0)
}

// 按首字母分组
export const groupByInitial = (users: any[]) => {
  try {
    const grouped: any = {};

    users.forEach((user: any) => {
      // 获取首字母集合
      let firstLetters = pinyin(user.name, {
        pattern: 'first',
        toneType: 'none'
      }).toUpperCase();
      let initial = firstLetters ? firstLetters[0] : '' // 获取首字母
      initial = initial.toUpperCase(); // 统一转为大写

      if (!grouped[initial]) {
        grouped[initial] = [];
      }
      grouped[initial].push(user);
    });

    return grouped;
  } catch (error) {
    console.log('error', error)
  }
};

const getColor = (name: any, colorList: any) => {
  const hash = name.split('').reduce((acc: any, char: any) => char.charCodeAt(0) + acc, 0);
  return colorList[hash % colorList.length];
};

// 用户头像，优先去用户上传的profile头像地址,其次使用用户名头像
const Avatar = (props: any) => {
  let {
    borderRadius = '50%',
    src, // 头像路径，优先级高
    srcset,
    name,
    color,
    colors = defaultColors,
    size,
    style,
    onClick,
    className,
  } = props;
  // if (!name) throw new Error('UserAvatar requires a name');
  if (!name) name = '';
  
  let abbr = getInitials(name);
  if (abbr.length > 2) {
    abbr = abbr.slice(0, 2)
  }
  // console.log('getInitials', abbr)
  size = size ? size + 'px' : size;

  const imageStyle = {
    display: 'block',
    borderRadius,
    width: '32px',
    height: '32px'
  };

  const innerStyle: any = {
    lineHeight: size,
    textAlign: 'center',
    borderRadius: borderRadius,
    backgroundColor: '',
    width: '32px',
    height: '32px'
  };

  if (size) {
    imageStyle.width = innerStyle.width = innerStyle.maxWidth = size;
    imageStyle.height = innerStyle.height = innerStyle.maxHeight = size;
  }

  let inner, classes = [className, 'UserAvatar', 'size-' + size];
  let background, textColor;
  if (color) {
    background = color;
  } else {
    // pick a deterministic color from the list
    let i = sumChars(name) % colors.length;
    // console.log('sss',sumChars(name))
    // console.log('i',i)
    background = colors[i];
    textColor = defaultTextColors[background]
  }

  innerStyle.backgroundColor = background;
  innerStyle.color = textColor;

  inner = abbr;


  return (
    <div aria-label={name} className={classes.join(' ')} style={style}>
      {
        src ? <img src={src} className="UserAvatar--img" style={{...imageStyle}}/> :
          <div className="UserAvatar--inner" style={innerStyle}>
            {inner}
          </div>
      }
    </div>
  )
}

export default Avatar