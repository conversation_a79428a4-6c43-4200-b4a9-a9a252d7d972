import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Input, message, Form, Space, Select,Flex } from 'antd';
import Svgicon from '@/components/svgicon'
import './index.scss'

// 可筛选下拉框
const FilterSelect =(props:any)=>{
    const { id, value, children,onChange,options,popupClassName,filterable,
        filterOption,optionRender,optionTextRender,labelRender,labelInValue,validateStatus,
        ...otherProps} = props;
    const [searchValue, setSearchValue] = useState('') // 搜索value
    const { t } = useTranslation();

    const _onChange =(e:any)=>{
        // setSearchValue('')
        onChange&&onChange(e)
    }

    const onSearchValueChange =(e:any)=>{
        setSearchValue(e.target.value)
    }

    const onFocus=()=>{
        setSearchValue('')
    }

    const onBlur=()=>{
    }

    const true_value = labelInValue ? value.value : value

    // 选项渲染
    const _optionRender = 
    optionRender ? optionRender: 
    optionTextRender? (
        (option:any,index:number) => {
            // console.log('optionTextRender(option,index)',optionTextRender(option,index))
            return (<Flex className='select-option'align="center">
            <Flex className='select-option-text' gap={8}>
                {
                    typeof optionTextRender =='function'?
                        optionTextRender(option,index)
                    :<div className='select-option-text-inner'>{option.data.label}</div>
                }
            </Flex>
            {true_value == option.data.value?<Svgicon className='select-option-check' svgName="icon_check"/>:null}
        </Flex>)
        }
    ):
    ( // 默认
        (option:any,index:number) => (
            <Flex className='select-option'align="center">
                <Flex className='select-option-text'>
                    <div className='select-option-text-inner'>{option.data.label}</div>
                </Flex>
                {true_value == option.data.value?<Svgicon className='select-option-check' svgName="icon_check"/>:null}
            </Flex>
        )
    )

    // 自定义筛选方法
    const _filterOption = filterOption || ((input:any, option:any) =>{
        try {
            let label= (option?.label?? '')
            label=label.toString()
            return (label)?.toLowerCase().includes(input.toLowerCase())
        } catch (error) {
            return true
        }
    })

    // filterable 区分是否需要筛选
    const selectProps = filterable ? {
        searchValue:searchValue,
        autoClearSearchValue: true,
        filterOption:_filterOption,
        dropdownRender:(menu:any) => (
            <>
                <Form.Item noStyle validateStatus="success">
                    <Input
                        className='select-search-input search'
                        suffix={<Svgicon svgName="icon_search"/>}
                        placeholder={t('search') as string}
                        value={searchValue}
                        onChange={onSearchValueChange}
                        onKeyDown={(e) => e.stopPropagation()}
                        onBlur={onBlur}
                    />
                </Form.Item>
                {menu}
            </>
        )
    } : {

    }

    const selectRender = (
        <Select 
                value={value} 
                onChange={_onChange} 
                variant="filled"
                suffixIcon={<Svgicon svgName="icon_select_suffix"/>}
                options={options}
                popupClassName={`${popupClassName} a-select`}
                labelInValue={labelInValue}
                labelRender={labelRender}
                optionRender={_optionRender}
                {...selectProps}
                onFocus={onFocus}
                {...otherProps}
            >
                {children}
        </Select>
    )

    return validateStatus ?(
        <Form.Item noStyle validateStatus={validateStatus}>
            {selectRender}
        </Form.Item>
    ):(selectRender)
}
export default FilterSelect