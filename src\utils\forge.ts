const forge = require('node-forge');

const a = ['b','e','g','i','n',' ',"p","u","b","l","i","c"," ","k","e","y"] 
const b = ['e','n','d',' ',"p","u","b","l","i","c"," ","k","e","y"]
const _ = '-----'

const pbk = `${_}${a.join('').toLocaleUpperCase()}${_}
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCQUJuEu99nYaVtMMKbC4kABFSS1DULR/zWV1MzaL32cIrdPF4BhFZRoPDiEPvKmjC42eQ9pZIPOnKjKPafDneDPdNXvHLJkrqNpItBUjrBXvd9NcGoocTlKQi1ByJIVG5xvsIs9aBqhlZBervXo4Hk4DFvgzcG+fy15rcdfYqfmwIDAQAB
${_}${b.join('').toLocaleUpperCase()}${_}`

// 加密
export function Encrypt(plaintext:any) {
  // 将 PEM 格式公钥转换为 forge 公钥对象
  const publicKey = forge.pki.publicKeyFromPem(pbk);
  
  // 执行加密（使用 PKCS#1 v1.5 填充）
  const encryptedBytes = publicKey.encrypt(plaintext, 'RSAES-PKCS1-V1_5');
  
  // 转换为 Base64 编码
  return forge.util.encode64(encryptedBytes);
}