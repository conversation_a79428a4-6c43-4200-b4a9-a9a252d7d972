import { theme, colorPrimary, themeDefaultConfig } from './config'
import { generate } from '@arco-design/color';
import store from "@/store";
import * as actions from "@/store/action";
import { api_get_app_default_theme, api_get_theme_of_company, api_domain_theme } from "@/api/theme";
import { staticRoutes } from '@/routes'
import { message } from "antd";

// 查询操作系统 主题
const getSystemTheme = () => {
    // 查询操作系统是否为暗色配色
    const prefersDarkScheme: boolean = window.matchMedia('(prefers-color-scheme:dark)').matches
    // console.log('--prefersDarkScheme', prefersDarkScheme)
    const systemTheme = prefersDarkScheme ? "dark" : 'light'
    return systemTheme
}

// 获取主题
const getTheme = () => {
    try {
        /* theme 取值顺序：浏览器缓存（用户上次选择）> 操作系统设置，默认亮白*/
        let activeTheme: string = theme// 默认
        // 查询 用户上次选择 的主题外观
        let storageTheme = localStorage.getItem("AMA_APPEARANCE")
        // 查询操作系统 主题
        const systemTheme = getSystemTheme()
        if (storageTheme) {
            storageTheme = storageTheme === 'auto' ? systemTheme : storageTheme
        }
        // console.log('***themeInfo', themeInfo)

        if (storageTheme && ["light", "dark"].includes(storageTheme)) {
            activeTheme = storageTheme
        } else {
            activeTheme = systemTheme || activeTheme
        }
        // console.log('******activeTheme', activeTheme)
        return activeTheme
    } catch (error) {
        return theme
    }
}

// 初始化主题静态变量
const initThemeStaticVariables = () => {
    const theme = getTheme()
    themeDefaultConfig && Object.keys(themeDefaultConfig[theme]).forEach((item) => {
        if (item.indexOf('colorPrimary') != -1) return
        document.documentElement.style.removeProperty(item);
        document.documentElement.style.setProperty(item, themeDefaultConfig[theme][item]);
    })
}

// 初始化主题（针对主题动态变量）
const initTheme = (themeConfig?: any, newTheme?: string) => {
    // try {
    // 获取当前主题信息
    const themeInfo = store.getState().app.themeInfo
    let activeTheme = null
    if (newTheme) {
        activeTheme = newTheme === 'auto' ? getSystemTheme() : newTheme
    } else {
        activeTheme = getTheme()
    }
    const colorPrimary = themeConfig && themeConfig.colorPrimary ? themeConfig.colorPrimary : themeInfo.brandColor
    // console.log('***最终activeTheme', activeTheme)
    // console.log('+++themeConfig', themeConfig)

    const result = generateThemeColorsByTheme(colorPrimary, activeTheme, themeConfig)
    const themeConfigFinal = result.themeConfig
    // const themeColors = result.themeColors
    // console.log('---themeConfigFinal', themeConfigFinal)
    // 设置主题动态变量
    themeConfigFinal && Object.keys(themeConfigFinal).forEach((item) => {
        document.documentElement.style.removeProperty(item);
        document.documentElement.style.setProperty(item, themeConfigFinal[item]);
    })
    // document.documentElement.setAttribute('theme', activeTheme)
    document.body.className = activeTheme
    result.themeConfig = { ...themeConfig, ...result.themeConfig }
    return { ...result, activeTheme }
    // } catch (error) {
    //     console.log('error', error)
    //     return { }
    // }
}

// 刷新主题 
const refreshTheme = (isGetDefault?: boolean, isToLogin?: boolean, tenantId?: any) => {
    return new Promise<any>((resolve, reject) => {
        /*主题取值逻辑：
        *1.非登录状态： 
        *  1）登录页面：根据“域名”获取域名对应的主题配置
        *  2）邀请、邀请成功、插件页面：根据发出邀请的企业，获取其企业配置
        *  3）其他 获取默认配置
        *2.登录状态：
        *  1）个人空间 获取默认配置
        *  2）根据选中企业的 获取 企业配置
        */
        let requestApi = api_get_app_default_theme
        let isDefault = true // 是否获取默认配置
        const pathname = window.location.pathname // 路由名称
        const activeCompanyId = store.getState().app.activeCompanyId
        const isLoginStatus = store.getState().app.isLogin
        const defaultThemePage = staticRoutes.map((i: any) => i.path) // 默认配置的页面，非登录状态路由
        const isNoDefault = defaultThemePage.filter((p: string) => pathname.indexOf(p) !== -1).length === 0

        const isDomainConfig = pathname.indexOf('/login') !== -1
        const isTenantConfig = pathname.indexOf('/invite') !== -1 || pathname.indexOf('/widget-bot') !== -1

        if (!isGetDefault && (isNoDefault || pathname === '/') && activeCompanyId && activeCompanyId !== '0') {
            // 获取企业配置
            requestApi = api_get_theme_of_company
            isDefault = false
            if (isGetDefault !== false) return resolve(0) // 刷新页面，避免重复调用
        }

        if (!isGetDefault && isTenantConfig) {
            requestApi = api_domain_theme
            isDefault = false
        }
        // console.log(isGetDefault, 'isTenantConfig',isTenantConfig)
        if (isToLogin || isDomainConfig) {
            requestApi = api_domain_theme
        }
        // /invite /inviteSuccess /login
        // console.log('isDefault',isDefault)
        requestApi(tenantId).then(async (res: any) => {
            res = res?.data || {}
            const { brandColor, brandSubColor, contrastColor, ...resConfig } = res
            // 修改浏览器标签页角标
            changeFaviconTitle({ light: res.faviconLight, dark: res.faviconDark }, res?.platformName)
            // 初始化主题
            let base = {}
            if (brandColor) {
                base = {
                    colorPrimary: brandColor,
                    colorPrimarySecondary: brandSubColor,
                    colorPrimaryContrasting: contrastColor
                }
            }
            const result = initTheme(base)
            const dynamicColors = { ...base, theme: result.activeTheme, ...result?.themeColors, brandColor: res?.brandColor, ...resConfig }
            if (isDefault && !isTenantConfig) {
                store.dispatch(actions.change_app_default_themeInfo(dynamicColors))
            } else {
                // 更新 当前企业管理员id数据
                const adminIds: any[] = res.admin ? [res.admin.toString()] : []
                // 更新 登录用户是否是当前企业管理员标识
                let _isAdmin = false
                const currentUserId = store.getState().app.userInfo?.userId
                if (adminIds && adminIds.includes(currentUserId?.toString())) {
                    _isAdmin = true
                }
                // console.log('_isAdmin',_isAdmin)
                await store.dispatch(actions.change_is_active_company_admin(_isAdmin))
            }
            store.dispatch(actions.change_app_themeInfo(dynamicColors))
            resolve(res)
        }).catch((err: any) => {
            console.log('----err', err)
            resolve(false)
            initTheme()
            if (!isLoginStatus && [1002015000].includes(err?.code)){ // 公司不存在提示
                // console.log('公司不存在')
                const key = 'CompanyNoExist'
                message.destroy(key)
                message.open({
                  type: 'error',
                  content: err.msg,
                  key: key
                })
            }
        })
    })
}

// 切换主题
const changeTheme = (baseThemeInfo: any, newtheme?: string) => {
    return initTheme(baseThemeInfo, newtheme)
}

// 切换自动外观 主题
const changeAutoThemeBySystem = (baseThemeInfo: any, systemTheme: string) => {
    // 查询 用户上次选择 的主题外观
    let storageTheme = localStorage.getItem("AMA_APPEARANCE")
    if (storageTheme === 'auto') {
        return initTheme(baseThemeInfo, systemTheme)
    }
    return null
}

// 生成主题色 派生色
const generatePrimaryColorsByTheme = (colorPrimary: string, theme: string) => {
    /* 
    *1、ama 品牌色(#252526) 使用系统默认主题
    *2、其他用户自定义颜色，使用字节算法生成的主题
    */
    const isAma = colorPrimary === '#252526'
    const derivationColorsByPrimary = generate(colorPrimary, { list: true, dark: theme === "dark" }) // 主题色派生色集合，有十个
    // console.log('derivationColorsByPrimary', derivationColorsByPrimary)
    const colorObject: any = {
        "colorPrimary": derivationColorsByPrimary[5], // 主色 colorPrimary 
        "colorPrimaryBg": derivationColorsByPrimary[0], // 主色淺色背景色 colorPrimaryBg
        "colorPrimaryBgHover": derivationColorsByPrimary[1], //主色淺色背景懸浮態 colorPrimaryBgHover
        "colorPrimaryHover": derivationColorsByPrimary[4], //主色懸浮態 colorPrimaryHover
        "colorPrimaryActive": derivationColorsByPrimary[6],  // 主色激活態 colorPrimaryActive
        "colorPrimaryBorder": derivationColorsByPrimary[2],  // 主色描邊色 colorPrimaryBorder
        "colorPrimaryBorderHover": derivationColorsByPrimary[3],  // 主色描邊色懸浮態 colorPrimaryBorderHover
        "colorPrimaryTextHover": derivationColorsByPrimary[4],  // 主色文本懸浮態 colorPrimaryTextHover
        "colorPrimaryText": derivationColorsByPrimary[5],// 主色文本色 colorPrimaryText
        "colorPrimaryTextActive": derivationColorsByPrimary[6],// 主色文本激活態 colorPrimaryTextActive
    }
    if (isAma) {
        // console.log('isAmaisAmaisAma')
        Object.keys(colorObject).forEach((key: any) => {
            colorObject[key] = themeDefaultConfig[theme]["--" + key] || colorObject[key]
        })
    }
    return colorObject
}

// 生成所有主题色 派生色（暗黑、亮白）
const generateAllThemeColors = (colorPrimary: string, activeTheme?: string) => {
    const lightThemeColors: any = generatePrimaryColorsByTheme(colorPrimary, "light")
    const darkThemeColors: any = generatePrimaryColorsByTheme(colorPrimary, "dark")
    // console.log('darkThemeColors', darkThemeColors)
    const themeConfigFinal: any = { ...themeDefaultConfig }
    Object.keys(lightThemeColors).forEach(key => {
        if (themeConfigFinal.light["--" + key]) themeConfigFinal.light["--" + key] = lightThemeColors[key]
        if (themeConfigFinal.dark["--" + key]) themeConfigFinal.dark["--" + key] = darkThemeColors[key]
    })
    // console.log('themeConfigFinal',  themeConfigFinal)
    return themeConfigFinal
}


// 根据主题模式 生成 主题色 派生色（暗黑or亮白）
const generateThemeColorsByTheme = (colorPrimary: string, activeTheme: string, themeConfig?: any) => {
    const themeColors: any = generatePrimaryColorsByTheme(colorPrimary, activeTheme)
    const themeConfigFinal: any = { ...JSON.parse(JSON.stringify(themeDefaultConfig)) }
    if (themeConfig) {
        Object.keys(themeConfig).forEach(key => {
            themeConfigFinal[activeTheme]["--" + key] = themeConfig[key]
        })
    }
    Object.keys(themeColors).forEach(key => {
        if (themeConfigFinal[activeTheme]["--" + key]) themeConfigFinal[activeTheme]["--" + key] = themeColors[key]
    })
    // console.log('themeConfigFinal',  themeConfigFinal)
    return { themeConfig: themeConfigFinal[activeTheme], themeColors }
}

// 动态修改 Favicon和title
function changeFaviconTitle(_themeImages?: any, _title?: any) {
    try {
        // 获取当前主题信息
        const themeInfo = store.getState().app.themeInfo
        const themeImages: any = _themeImages || { light: themeInfo.faviconLight, dark: themeInfo.faviconDark }
        const title = _title || themeInfo.platformName || 'vama'
        document.title = title
        const systemTheme = getTheme()
        const base64Image = themeImages[systemTheme]
        // 查找现有的 favicon 链接
        const favicon = document.getElementById("favicon") as HTMLLinkElement;
        // const base64Image = `/api/logo/static/${systemTheme}/favicon.ico`

        // 如果不存在，则创建一个新的链接元素
        if (!favicon) {
            const newFavicon = document.createElement('link');
            newFavicon.id = 'favicon';
            newFavicon.rel = 'icon';
            newFavicon.href = base64Image;
            document.head.appendChild(newFavicon);
        } else {
            // 更新 favicon 的 href 属性为 Base64 编码的图像
            favicon.href = base64Image;
        }
    } catch (error) {
        console.log('error', error)
        // 发生错误 使用默认图片
        const favicon = document.getElementById("favicon") as HTMLLinkElement;
        favicon.href = `${process.env.PUBLIC_URL}/favicon.ico`;

    }
}

function getCSSVariablesValue(propName: string) {
    const root = document.documentElement; // 获取根元素
    return getComputedStyle(root).getPropertyValue(propName).trim()
}

export {
    theme, colorPrimary, themeDefaultConfig,
    getTheme, initTheme, refreshTheme, changeTheme, generatePrimaryColorsByTheme, generateAllThemeColors, generateThemeColorsByTheme, initThemeStaticVariables, changeFaviconTitle, changeAutoThemeBySystem, getCSSVariablesValue
}