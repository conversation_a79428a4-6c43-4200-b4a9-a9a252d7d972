<svg width="32" height="42" viewBox="0 0 32 42" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Mask group" filter="url(#filter0_d_7065_4659)">
<mask id="mask0_7065_4659" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="1" y="0" width="30" height="40">
<path id="Rectangle 1" d="M1 2C1 1.17157 1.6715 0.5 2.49993 0.5C9.62529 0.5 16.7985 0.5 19.1774 0.5C19.7079 0.5 20.2107 0.710714 20.5858 1.08579L25.5 6L30.4142 10.9142C30.7893 11.2893 31 11.798 31 12.3284V38C31 38.8284 30.3284 39.5 29.5 39.5H2.5C1.67157 39.5 1 38.8284 1 38V2Z" fill="url(#paint0_linear_7065_4659)"/>
</mask>
<g mask="url(#mask0_7065_4659)">
<path id="Rectangle 1_2" d="M1 0.5C10.1754 0.5 20 0.5 20 0.5L25.5 6L31 11.5V39.5H1V0.5Z" fill="url(#paint1_linear_7065_4659)"/>
<g id="Vector 7" filter="url(#filter1_d_7065_4659)">
<path d="M31 11.5L20 0.5V9.5C20 10.6046 20.8954 11.5 22 11.5H31Z" fill="url(#paint2_linear_7065_4659)"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_7065_4659" x="0" y="0.5" width="32" height="41" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.31 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_7065_4659"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_7065_4659" result="shape"/>
</filter>
<filter id="filter1_d_7065_4659" x="16" y="-3.5" width="19" height="19" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_7065_4659"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_7065_4659" result="shape"/>
</filter>
<linearGradient id="paint0_linear_7065_4659" x1="16" y1="0.5" x2="16" y2="39.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#F0F0F0"/>
<stop offset="1" stop-color="#FCFCFC"/>
</linearGradient>
<linearGradient id="paint1_linear_7065_4659" x1="16" y1="0.5" x2="16" y2="39.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#F0F0F0"/>
<stop offset="1" stop-color="#FCFCFC"/>
</linearGradient>
<linearGradient id="paint2_linear_7065_4659" x1="25.5" y1="6" x2="20" y2="11.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#E9E9E9"/>
<stop offset="0.307692" stop-color="#F7F7F7"/>
<stop offset="1" stop-color="#FDFDFD"/>
</linearGradient>
</defs>
</svg>
