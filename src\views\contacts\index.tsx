import { connect } from "react-redux";
import React, { useState, useRef, useEffect } from "react";
import { Flex } from 'antd'
import { useTranslation } from 'react-i18next';
// import SvgIcon from '@components/svgicon';
import SideMenu from './components/sideMenu';
import Organization from './organization';
import MyGroups from './myGroups';
import NewContacts from './newContacts';
import MyContacts from './myContacts';

import './index.scss'
import classNames from "classnames";

const Contacts = (props: any) => {
    const { t } = useTranslation();
    const sideMenuRef = useRef<any>(null)
    const organizationRef = useRef<any>(null)
    const newContactsRef = useRef<any>(null)

    const [activeMenu, setActiveMenu] = useState<any>(null); // 当前选中的菜单
    const [resources, setResources] = useState<any>(null);

    // 菜单切换
    const onChangeMenu = (key: any) => {
        setActiveMenu(key)
    }

    // 组织架构页面 通知刷新sidemenu部门
    const onUpdateSideMenuDepartments = (resources: any) => {
        sideMenuRef?.current?.emit_update_organization_children && sideMenuRef?.current?.emit_update_organization_children(resources)
    }


    // sidemenu页面 通知刷新部门信息
    const onUpdateOrganizationDepartment = (resources: any) => {
        // console.log('organizationRef?.current',organizationRef?.current,resources)
        organizationRef?.current?.emit_update_organization && organizationRef?.current?.emit_update_organization(resources)
    }

    // sidemenu页面 通知刷新 新的好友页面
    const onUpdateNewCount = (resources: any) => {
        // console.log('organizationRef?.current',organizationRef?.current,resources)
        newContactsRef?.current?.emit_update_new_contact && newContactsRef?.current?.emit_update_new_contact(resources)
    }

    return (
        <Flex className="contacts-page">
            <SideMenu ref={sideMenuRef} activeMenu={activeMenu}
                onChangeMenu={onChangeMenu}
                onUpdateDepartment={onUpdateOrganizationDepartment}
                onUpdateNewCount={onUpdateNewCount}
                />
            { // 组织架构
                // activeMenu==='Organization'?
                <div className={classNames(['con', activeMenu === 'Organization' ? '' : 'hidden'])}>
                    <Organization ref={organizationRef} onUpdateDepartments={onUpdateSideMenuDepartments} />
                </div>
                // :null
            }
            { // 我的通讯录
                activeMenu === 'NewContacts' ?
                    <NewContacts ref={newContactsRef} />
                    : null
            }
            { // 我的通讯录
                activeMenu === 'MyContacts' ?
                    <MyContacts />
                    : null
            }
            { // 我的群组
                activeMenu === 'MyGroups' ?
                    <MyGroups />
                    // <div></div>
                    : null
            }
        </Flex>
    )
}
const mapStateToProps = (state: any) => ({
    companies: state.app.companies,
    activeCompanyId: state.app.activeCompanyId
})
const mapDispatchToProps = (dispatch: any) => ({
    // dispatch_api_: (data:any) => dispatch(dispatch_api_(data))
})
export default connect(mapStateToProps, mapDispatchToProps)(Contacts);