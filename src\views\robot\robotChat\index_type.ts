export interface EmbeddingChunkFace {
    chunkId: string,
    chunkText: string,
    documentId: string,
    documentName: string,
    documentPath: string,
    isEidt?:boolean,
    enhanceText?: string,
    checked?:boolean,
    internetSearch?:boolean,
    url?: string,
    title?: string,
    displayLink?: string,
    displaySnippet?: string,
    image?: string,
    isEdited?:boolean,
  }
  
export  interface StackConfig {
    messageId?: number | string, // messageId
    user: string, // 问题
    assistant: string, // 回答
    index: number,
    searchResults: EmbeddingChunkFace[], // 谷歌搜索
    referenceChunkList: EmbeddingChunkFace[], // 引用知识
    u_includedMessages?:any[] | null // 问题历史会话
    a_includedMessages?:any[] | null // 回答历史会话
    u_attachments?: any[] | null, // 问题附加文件
    a_attachments?: any[] | null, // 回答附加文件
    u_messageToken?:any, // 问题 token数
    a_messageToken?:any, // 回答 token数
    llmModel?: string | null, // homebot 提问所选 llm名称
    llmImage?: string | null, // homebot 提问所选 llm图标 
    seq?: number,
    robotId?: string,
    loading: boolean,
    isToggleAi: boolean,
    canEnhance?: boolean,
    isEnhance?: boolean,
    enhanceLoading?:boolean, // 增强请求loading
    isChecked?:boolean // 是否勾选
    meta_contextClear?:boolean, // 是否为 上下文清除 状态
    inPairs?: boolean // 问题答案是否成对
    isImage?: boolean // 回答是否是图片
    isThinking?: boolean, // 回答是否在 思考中
    isThinkingEnd?: any, // 思考结束 null:无推理 true：推理结束 false: 推理中
    thinkingText?: string // 思考过程文字
    thinkingTime?:any, // 思考时间
    sourceTypes?: any // 信息来源 类型集合
  }
  
export  interface DialogObject {
    robotId: string,
    robot_type?: string | number, // 0为Home Bot
    robotName: string,
    describe: string,
    robotLlmModel?: string[],
    welcome_message?:string,
    escalateLlmModel?: string[],
    can_show_citations?: boolean,
    llmModelImage?: string | null // LLM图标，不区分本地和增强
    llmCategory?: string | null,// LLM分类，不区分本地和增强
    llmModel?: string | null// LLM名称，不区分本地和增强
    companyId? :any
  }
  
  export  interface ContextState {
    currentDialog: DialogObject,
    stack: StackConfig[],
    currentInformationSourcesModal: {// 信息来源弹框
      type: string,
      visible: boolean,
      resource: any,
      seq?:any
    },
    isMaxMsgModal: boolean,
    isGoogleSearch: boolean,
    currentEnhanceModal: { // 增强弹框
      visible: boolean,
      resource: any
    },
    currentStack: StackConfig,
    llmModelList: any[],
    llmModelSelected : string[],
    isDragingFileToPanel: boolean, // 是否处于文件拖拽进入右侧版面中
    currentFilebox: {
      isRequesting: boolean, // fileupload 组件请求中
      attachments: any,
      attachmentTokens: number,
    //   isTokenOverflow: boolean
    },
    changeLlmConfirmModal: { // 切换LLM模型-有上传附件时，确认弹框
      visible: boolean,
      llm: any
    },
    deleteChatConfirmModal: { // 删除会话弹窗确认
        visible: boolean,
        messageId: any
    }
    tokenExceedModal: { // token超出限制 提示弹框
      visible: boolean,
      overflow: any
    },
    currentRobotDetail: { // 机器人详情
      visible: boolean,
      resource: any
    },
    viewImage: {
      visible: boolean,
      url: any
    },
    currentInputTokenLimit: any, // 当前机器人-输入框最大字符数（token）
    currentRemainTokenLimit: any, // 当前机器人-除去输入框剩余的token
    currentPageSize: number, // 分页size
    currentLastMessageId: any, // 分页lastMessageId 
    isLoadingMore: boolean,
    loadAllFinished: boolean,
    openSelectConversation: boolean, // 是否开启选择对话
    historyConversationSelected: any[], // 历史会话勾选
    historyConversationMemory: any[], // 历史会话勾选 临时存储
    copyFinished: boolean, // 已复制
    copyFinishedTimer: any,
    containerHeight: number,
    scrollTopMemory: number | null,
    keyDownEventListener: any,
    loadingLLm: boolean,
    pageOriginId: string // 来源 robot 机器人列表 、market机器人市场发起的聊天
  }