
import { APP_HTTP } from "@/utils/userRequest";
import * as actions from "@/store/action";
import { AxiosRequestConfig } from 'axios';

// 获取账号信息
// export const api_get_my_account_info = (): Promise<unknown> => new Promise((resolve, reject) => {
//     APP_HTTP({
//         method: 'get',
//         url: '/system/user/profile/myself',
//         // noTenant: true
//     } as AxiosRequestConfig).then(result => {
//         const res = result?.data?.data || {}
//         resolve(res)
//     }).catch(err => reject(err))
// })


//  验证旧邮箱和验证码 / 验证旧手机和验证码
export const api_check_verify_code = (data: any): Promise<unknown> => new Promise((resolve, reject) => {
    APP_HTTP({
        method: 'post',
        data,
        url: '/system/user/profile/check-verify-code',
        handleCode: true,
    } as AxiosRequestConfig).then(result => {
        const res = result?.data || {}
        resolve(res)
    }).catch(err => reject(err))
})

//  验证新邮件和新的验证码 / 验证新手机和新验证码
export const api_change_contact = (data: any): Promise<unknown> => new Promise((resolve, reject) => {
    APP_HTTP({
        method: 'post',
        data,
        url: '/system/user/profile/change-contact',
        handleCode: true,
        noTenant: true
    } as AxiosRequestConfig).then(result => {
        const res = result?.data || {}
        resolve(res)
    }).catch(err => reject(err))
})

//  验证密码是否正确
export const api_verify_password = (data: any): Promise<unknown> => new Promise((resolve, reject) => {
    APP_HTTP({
        method: 'post',
        data,
        url: '/system/user/profile/verify-password',
        handleCode: true,
    } as AxiosRequestConfig).then(result => {
        const res = result?.data || {}
        resolve(res)
    }).catch(err => reject(err))
})
//  更改密码
export const api_change_password = (data: any): Promise<unknown> => new Promise((resolve, reject) => {
    APP_HTTP({
        method: 'put',
        data,
        url: '/system/user/profile/change-password',
        handleCode: true,
    } as AxiosRequestConfig).then(result => {
        const res = result?.data || {}
        resolve(res)
    }).catch(err => reject(err))
})

//  注销用户
export const api_delete_account = (data: any): Promise<unknown> => new Promise((resolve, reject) => {
    APP_HTTP({
        method: 'delete',
        data,
        url: '/system/user/profile/myself',
        handleCode: true,
    } as AxiosRequestConfig).then(result => {
        const res = result?.data || {}
        resolve(res)
    }).catch(err => reject(err))
})

// 检查用户是否存在任何公司中
export const api_is_exists_in_tenant = (): Promise<unknown> => new Promise((resolve, reject) => {
    APP_HTTP({
        method: 'get',
        url: 'system/user/profile/exists-in-tenant',
        noTenant: true,
    } as AxiosRequestConfig).then(result => {
        const res = result?.data?.data || {}
        resolve(res)
    }).catch(err => reject(err))
})

// 获取基本设置
export const api_get_general_setting = (): Promise<unknown> => new Promise((resolve, reject) => {
    APP_HTTP({
        method: 'get',
        url: '/system/user/profile/general-settings',
    } as AxiosRequestConfig).then(result => {
        const res = result?.data?.data || {}
        resolve(res)
    }).catch(err => reject(err))
})

// 保存基本设置
export const api_update_general_setting = (data: any): Promise<unknown> => new Promise((resolve, reject) => {
    APP_HTTP({
        method: 'put',
        data,
        url: '/system/user/profile/general-settings',
    } as AxiosRequestConfig).then(result => {
        const res = result?.data?.data || {}
        resolve(res)
    }).catch(err => reject(err))
})

// 显示个人所属公司profile
export const api_get_my_profile = (tenantId: any, hideTip?:boolean): Promise<unknown> => new Promise((resolve, reject) => {
    APP_HTTP({
        method: 'get',
        url: `/system/user/profile/my-profile`,
        tenantId,
        hideTip
    } as AxiosRequestConfig).then(result => {
        const res = result?.data?.data || {}
        resolve(res)
    }).catch(err => reject(err))
})


// 更新个人所属公司profile
export const api_update_my_profile = (tenantId: any, data: any): Promise<unknown> => new Promise((resolve, reject) => {
    APP_HTTP({
        method: 'put',
        data,
        url: `/system/user/profile/my-profile`,
        tenantId
    } as AxiosRequestConfig).then(result => {
        const res = result?.data?.data || {}
        resolve(res)
    }).catch(err => reject(err))
})

// 更新用户头像/所在公司的用户头像
export const api_update_avatar = (data: any): Promise<unknown> => new Promise((resolve, reject) => {
    APP_HTTP({
        method: 'put',
        data,
        url: '/system/user/profile/avatar',
        noTenant: true,
        headers: {
            'Content-Type': 'multipart/form-data',
        }
    } as AxiosRequestConfig).then(result => {
        const res = result?.data?.data || {}
        resolve(res)
    }).catch(err => reject(err))
})

export const api_reset_avatar = (tenantId: any): Promise<unknown> => new Promise((resolve, reject) => {
    APP_HTTP({
        method: 'delete',
        data: {},
        url: '/system/user/profile/avatar',
        tenantId: tenantId
    } as AxiosRequestConfig).then(result => {
        const res = result?.data?.data || {}
        resolve(res)
    }).catch(err => reject(err))
})