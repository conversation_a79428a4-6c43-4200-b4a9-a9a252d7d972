
//import { getSDK, <PERSON>bEvents, CallbackEvent, MessageItem} from '@openim/client-sdk';
import { getSDK, CbEvents, MessageItem, SessionType, ViewType } from '@openim/wasm-client-sdk';
import { message } from 'antd'
import { t } from 'i18next';
import store from "../store";
import { dispatch_api_logout } from "@/api/user";
import router from "../routes";

class ImChatService {
    static instance: ImChatService;
    // static get Instance() {
    //   if (!this.instance) {
    //     this.instance = new ImChatService();
    //   }
    //   return this.instance;
    // }

    public static async getInstance(): Promise<any> {
        if (!ImChatService.instance) {
            ImChatService.instance = new ImChatService();
            await ImChatService.instance.initialize();
        }
        return ImChatService.instance;
    }

    private isInitialized = false;
    // 连接状态（初始为未连接）
    private _isConnected = false;
    // // 连接中
    // private isConnecting = false;
    // 状态变更监听器列表
    private connectionListeners: Array<(connected: boolean) => void> = [];
    IMSDK: any;
    apiAddr: string;   // IM api 地址，一般为`http://your-server-ip:10002`
    wsAddr: string;   // IM ws 地址，一般为`ws://your-server-ip:10001`
    isLogin: boolean;
    isLogouting: boolean;
    isConnectFailed: boolean;

    callBackMapping: Record<string, any>;

    constructor(url: string = "") {
        this.apiAddr = `${window.location.origin}/im`;   // IM api 地址，一般为`http://your-server-ip:10002`
        const wshost = `${window.location.protocol === 'http:' ? 'ws' : 'wss'}://${window.location.host}`
        this.wsAddr = `${wshost}/imws`;   // IM ws 地址，一般为`ws://your-server-ip:10001`
        this.isLogin = false;
        this.callBackMapping = {};
        this.isLogouting = false // 是否正在退出登录,避免多次退出登录
        this.isConnectFailed = false
        //this.IMSDK = getWithRenderProcess({
        //  wasmConfig: {
        //    coreWasmPath: "./openIM.wasm",
        //    sqlWasmPath: `/sql-wasm.wasm`,
        //  },
        //});
        //this.IMSDK = getSDK();
        this.IMSDK = getSDK({
            coreWasmPath: "./openIM.wasm",
            sqlWasmPath: "/sql-wasm.wasm",
            debug: false // false不打印日志
        });
        // console.log('this.IMSDK', this.IMSDK)

        this.IMSDK.on(CbEvents.OnConnecting, () => {
            console.log("🐾IM连接中...");
        });

        this.IMSDK.on(CbEvents.OnConnectFailed, ({ errCode, errMsg }: any) => {
            console.log(`😰IM连接失败, Error Code: ${errCode}, errMsg: ${errMsg}.`);
            this.isConnectFailed = true
        });

        this.IMSDK.on(CbEvents.OnConnectSuccess, () => {
            console.log("🍀🍀IM登录并且连接成功✔✔");
            this.isLogin = true;
            this._isConnected = true;
            this.notifyConnectionChange();
            // this.getAllConversationList();
        });

        this.IMSDK.on('OnDisconnect', () => {
            console.log("😰IM连接断开");
            this._isConnected = false;
            this.notifyConnectionChange();
        });

        // 登录凭证过期，需要重新登录
        this.IMSDK.on(CbEvents.OnUserTokenExpired, () => {
            console.log("😰登录凭证过期，需要重新登录");
            this._isConnected = false;
            this.isLogin = false;
            // 退出登录
            this.$_handleLoginExpired()
            message.error(t('error.logo-timeout'))
        });

        // 账号已在其他地方登录，当前设备被踢下线
        this.IMSDK.on(CbEvents.OnKickedOffline, () => {
            console.log("----------账号已在其他地方登录，当前设备被踢下线");
            this._isConnected = false;
            this.isLogin = false;
            // 退出登录
            this.$_handleLoginExpired()
            message.error(t('error.logo-timeout'))
        });

        // 登录Token无效
        this.IMSDK.on(CbEvents.OnUserTokenInvalid, () => {
            console.log("😰登录Token无效");
        });

        this.IMSDK.on(CbEvents.OnFriendApplicationAdded, (data: any) => { this.callBackMapping.OnFriendApplicationAdded?.call(this, data?.data); }); // 用户发起好友申请后，申请发起者和接收者都会收到此回调
        this.IMSDK.on(CbEvents.OnFriendApplicationAccepted, (data: any) => { this.callBackMapping.OnFriendApplicationAccepted?.call(this, data?.data); }); // 好友申请被同意
        this.IMSDK.on(CbEvents.OnFriendApplicationRejected, (data: any) => { this.callBackMapping.OnFriendApplicationRejected?.call(this, data?.data); }); // 好友申请被拒绝
        this.IMSDK.on(CbEvents.OnFriendAdded, (data: any) => { this.callBackMapping.OnFriendAdded?.call(this, data?.data); }); // 两个用户成功建立好友关系后双方都会收到该回调
        this.IMSDK.on(CbEvents.OnConversationChanged, (data: any) => { this.callBackMapping.OnConversationChanged?.call(this, data?.data); });
        this.IMSDK.on(CbEvents.OnNewConversation, (data: any) => { this.callBackMapping.OnNewConversation?.call(this, data?.data); });
        this.IMSDK.on(CbEvents.OnRecvNewMessages, (data: any) => { this.callBackMapping.OnRecvNewMessages?.call(this, data?.data); });
        // 会话总未读发生变化时的回调
        this.IMSDK.on(CbEvents.OnTotalUnreadMessageCountChanged, (data: any) => {
            // data 消息未读数
            this.callBackMapping.OnTotalUnreadMessageCountChanged?.call(this, data?.data);
        });

    }

    private async initialize() {
        if (this.isInitialized) return;
        // console.log("----IMSDK.init");
        // 初始化 WASM
        this.isInitialized = true;
    }


    // 登录状态判断
    $_isLoginedStatus() {
        if (!this.isLogin === true) {
            // const key= 'imunlogin'
            // message.destroy(key)
            // message.open({
            //     key:key,type:'error',content: '登录失效，请尝试重新登录'
            // })
            return false;
        }
        return true
    }

    // 登录失效处理
    $_handleLoginExpired() {
        const logout = dispatch_api_logout({}, this.IMSDK)
        logout(store.dispatch).then(() => {
            router.navigate("/login");
        }).catch((err: any) => {
            console.log(err)
        })
    }

    // $_handleErrorCode()

    // 注册状态监听
    public onConnectionChange(listener: (connected: boolean) => void) {
        this.connectionListeners.push(listener);
        // 返回取消订阅函数
        return () => {
            this.connectionListeners = this.connectionListeners.filter(l => l !== listener);
        };
    }

    // 触发所有监听器
    private notifyConnectionChange() {
        this.connectionListeners.forEach(listener => listener(this._isConnected));
    }

    // 暴露当前状态
    get isConnected() {
        return this._isConnected;
    }

    //注册回调函数,可能有多个scoket
    registerCallBack(socketType: string, callBack: (data: Record<string, any>) => void) {
        // console.log(`registerCallBack, socketType: ${socketType}.`);
        this.callBackMapping[socketType] = callBack;
    }

    unRegisterCallBack(socketType: string) {
        this.callBackMapping[socketType] = null;
    }

    // 登录
    login(userID: string, token: string) {
        if (this.isLogin === true) {
            console.log("---------login already");
            return;
        }
        // console.log('this.IMSDK',this.IMSDK)
        this.IMSDK.login({
            userID: userID,       // IM 用户 userID
            token: token,        // IM 用户令牌
            platformID: 5,   // 当前登录平台号，web端为5
            apiAddr: this.apiAddr,   // IM api 地址，一般为`http://your-server-ip:10002`
            wsAddr: this.wsAddr    // IM ws 地址，一般为`ws://your-server-ip:10001`
        })
            .then(() => {
                // 登录完成
                // 注意:登录回调成功可能不代表IM真的登陆成功，需要观察IM的连接状态回调，收到onConnectSuccess 时才表示登录并且连接IM成功。
                console.log("😄IM登录回调成功");
                // this.isLogin = true;
                // console.log("---------login getLoginStatus", this.IMSDK.getLoginStatus());
            })
            .catch((err: any) => {
                // 登录失败
                console.log("😡IM登录接口调用失败✖", err);
                message.error(t('error.request-failed'))
            });
    }

    // 登出
    logout() {
        if (!this.isLogin === true) {
            return true;
        }
        this.isLogouting = true
        return new Promise<any>((resolve, reject) => {

            this.IMSDK.logout().then(() => {
                this.isLogouting = false
                console.log("🖐IM 登出成功");
                this.isLogin = false;
                resolve(true)
            }).catch((err: any) => {
                resolve(false)
                this.isLogouting = false
                // 登出失败
                console.log("😡IM 登出失败: ", err);
                message.error(t('error.request-failed'))
            })
        })
    }

    // 获取所有会话总未读数
    getTotalUnreadMsgCount() {
        return new Promise<any>((resolve, reject) => {
            const isLogin = this.$_isLoginedStatus()
            if (!isLogin) return resolve(false)

            this.IMSDK.getTotalUnreadMsgCount().then((res: any) => {
                console.log("🖐IM 获取所有会话总未读数成功", res);
                resolve(res?.data)
            }).catch((err: any) => {
                resolve(false)
                // 登出失败
                console.log("😡IM 获取所有会话总未读数失败: ", err);
            })
        })
    }

    // 标记会话已读: 1、会话类型为单聊，该接口用于消除未读数以及单聊的已读回执发送，调用该接口后 对方发送的消息的已读状态会更新。 2、会话类型为群聊或通知，该接口仅仅用于消除未读数。
    async markConversationMessageAsRead(conversationID: string) {
        const isLogin = this.$_isLoginedStatus()
        if (!isLogin) return
        // console.log('conversationID',conversationID)
        const res = await this.IMSDK.markConversationMessageAsRead(conversationID).catch(({ errCode, errMsg }: any) => {
            console.log('😡markConversationMessageAsRead 调用失败', errCode, errMsg)
        });
        if (!res) return
        console.log('🍀markConversationMessageAsRead 调用成功', res)
        return res
    }

    // 获取所有消息列表
    async getAllConversationList() {
        const isLogin = this.$_isLoginedStatus()
        if (!isLogin) return
        //const data = await this.IMSDK.getConversationListSplit({offset: 0, count: 20})
        const data: any = await this.IMSDK.getAllConversationList().catch(({ errCode, errMsg }: any) => {
            console.log('---------getAllConversationList 调用失败', errCode, errMsg);
            message.error(t('error.request-failed') + ' Error Code: ' + errCode)
            return false;
        });
        if (!data) return false
        return data
    }

    // 获取单个消息列表
    async getOneConversation(sourceID: string, sessionType: SessionType) {
        const isLogin = this.$_isLoginedStatus()
        if (!isLogin) return

        const { data } = await this.IMSDK.getOneConversation({
            sourceID: sourceID,
            sessionType: sessionType,
        })
            //const data = await this.IMSDK.getConversationListSplit({offset: 0, count: 20})
            .catch(({ errCode, errMsg }: any) => {
                console.log('---------getOneConversation 调用失败', errCode, errMsg);
                return;
            });

        console.log(`---------getOneConversation 调用成功}`);
        return data;
    }

    // 单聊发送文字消息
    async sendTextMessageToUser(recvID: string, text: string) {
        const msg: any = await this.IMSDK.createTextMessage(text)
            .catch(({ errCode, errMsg }: any) => {
                console.log('-----createTextMessage 调用失败', errCode)
            });

        if (!msg) return;

        // 调用成功
        const { data } = msg
        const res = await this.IMSDK.sendMessage({
            recvID: recvID,
            message: {
                ...data
            }
        }).catch(({ errCode, errMsg }: any) => {
            console.log('----sendTextMessageToUser 调用失败', errCode, errMsg)
        });

        if (!res) return;
    }

    // 群聊发送文字消息
    async sendTextMessageToGroup(groupID: string, text: string) {
        const msg: any = await this.IMSDK.createTextMessage(text)
            .catch(({ errCode, errMsg }: any) => {
                console.log('-----createTextMessage 调用失败', errCode)
            });
        console.log('-----createTextMessage 调用成功')
        if (!msg) return;

        // 调用成功
        const { data } = msg;
        const res = await this.IMSDK.sendMessage({
            groupID: groupID,
            recvID: "",
            message: {
                ...data
            }
        }).catch(({ errCode, errMsg }: any) => {
            console.log('----sendTextMessageToGroup 调用失败', errCode, errMsg)
        });
        if (!res) return
        console.log('---------sendTextMessageToGroup 调用成功', res)
    }

    // 单聊发送视频消息
    async sendVideoMessage(conType: string, targetId: string, file: any, snapshotFile: any) {
        const fileType = file.type.split('/')[0]
        const shotFileType = snapshotFile.type.split('/')[0]

        // let baseUrl = process.env.REACT_APP_IM_HTTPSERVER
        // baseUrl = baseUrl?.split('//')[1]
        // 上传视频缩略图文件
        const shotFileResource: any = await this.IMSDK.uploadFile({
            name: snapshotFile.name, //文件名
            contentType: shotFileType,// 文件类型
            uuid: snapshotFile.uid,
            file: snapshotFile,
            filepath: ''
        }).catch(({ errCode, errMsg }: any) => {
            console.log('-----uploadFile缩略图 调用失败', errCode, errMsg)
        });
        if (!shotFileResource) return;
        // console.log('---缩略图', shotFileResource)
        let { url: shortFilePath } = shotFileResource.data
        // shortFilePath = shortFilePath.replace(/\b127\.0\.0\.1\b/g, baseUrl); // 转换地址

        // 上传视频文件
        const fileResource: any = await this.IMSDK.uploadFile({
            name: file.name, //文件名
            contentType: fileType,// 文件类型
            uuid: file.uid,
            file: file,
            filepath: ''
        }).catch(({ errCode, errMsg }: any) => {
            console.log('-----uploadFile 调用失败', errCode, errMsg)
        });
        if (!fileResource) return;
        let { url: videoPath } = fileResource.data
        console.log('---fileResource', fileResource)

        // videoPath = videoPath.replace(/\b127\.0\.0\.1\b/g, baseUrl); // 转换地址
        console.log('+++videoPath', videoPath)
        const params = {
            videoPath: '', //文件绝对路径，如果没有传空字符即可
            duration: file.duration,
            videoType: fileType,
            snapshotPath: '',
            videoUUID: file.uid,
            videoUrl: videoPath,
            videoSize: file.size,
            snapshotUUID: snapshotFile.uid,
            snapshotSize: snapshotFile.size,
            snapshotUrl: shortFilePath,
            snapshotWidth: 200,
            snapshotHeight: 118,
            // snapShotType?: string
            videoFile: file,
            snapshotFile: snapshotFile
        }
        console.log('+++params', params)

        try {
            //创建视频消息
            const videoMessage: any = await this.IMSDK.createVideoMessageByFile(params).catch((err: any) => {
                console.log('-----createVideoMessageByFile 调用失败', err)
            });
            if (!videoMessage) return;
            console.log('createVideoMessageByFile 调用成功 data', videoMessage)
            const param1 = {
                recvID: conType === 'user' ? targetId : '',
                groupID: conType === 'group' ? targetId : '',
                message: {
                    ...videoMessage.data
                }
            }
            console.log('+++param1', param1)
            console.log('+++videoMessage', videoMessage)

            // 发送视频消息
            const res = await this.IMSDK.sendMessageNotOss(param1).catch(({ errCode, errMsg }: any) => {
                console.log('----sendVideoMessageToUser 调用失败', errCode, errMsg)
            });
            if (!res) return;
            console.log('---------sendVideoMessageToUser 调用成功', res)
        } catch (error) {
            console.log('报错了', error)
        }
    }

    // 发送图片消息
    async sendPitcture(conType: string, targetId: string, imageFile: any) {
        const picBaseInfo = {
            uuid: "uuid",
            type: imageFile.type,
            size: imageFile.size,
            width: 1024,
            height: 1024,
        }

        let msg: any = await this.IMSDK.createImageMessageByFile({
            sourcePicture: picBaseInfo,
            bigPicture: picBaseInfo,
            snapshotPicture: picBaseInfo,
            sourcePath: imageFile.path,
            file: imageFile
        })
            .catch(({ errCode, errMsg }: any) => {
                console.log('-----createImageMessageByFile 调用失败', errCode)
            });
        console.log(`-----createImageMessageByFile 调用成功 ${JSON.stringify(msg)}`)
        if (!msg) return;


        // 调用成功
        const { data } = msg;
        console.log('data', data)
        // return
        const res = await this.IMSDK.sendMessage({
            recvID: conType === 'user' ? targetId : '',
            groupID: conType === 'group' ? targetId : '',
            message: {
                ...data
            }
        }).catch(({ errCode, errMsg }: any) => {
            console.log('----sendPitctureToGroup 调用失败', errCode, errMsg)
        });
        if (!res) return
        console.log('---------sendPitctureToGroup 调用成功', res)
    }

    // 发送文件消息
    async sendFile(conType: string, targetId: string, file: any) {
        let msg: any = await this.IMSDK.createFileMessageByFile({
            filePath: file.path || '',
            fileName: file.name,
            uuid: 'uuid',
            sourceUrl: '',
            fileSize: file.size,
            fileType: file.type,
            file: file,
        })
            .catch(({ errCode, errMsg }: any) => {
                console.log('-----createFileMessageByFile 调用失败', errCode)
            });
        console.log(`-----createFileMessageByFile 调用成功 ${JSON.stringify(msg)}`)
        if (!msg) return;

        // 调用成功
        const { data } = msg;

        const res = await this.IMSDK.sendMessage({
            recvID: conType === 'user' ? targetId : '',
            groupID: conType === 'group' ? targetId : '',
            message: {
                ...data
            }
        }).catch(({ errCode, errMsg }: any) => {
            console.log('----sendFile 调用失败', errCode, errMsg)
        });
        if (!res) return
        console.log('---------sendFile 调用成功', res)
    }

    // 发送自定信息
    async sendCustomMessage(conType: string, targetId: string, customObject: any) {
        // createCustomMessage 创建自定义消息，其中的字段都由开发者自己定义，SDK 只负责透传。
        const msg: any = await this.IMSDK.createCustomMessage({
            data: customObject.data || '自定义消息', // 文本内容
            extension: customObject.extension || '',//拓展内容
            description: customObject.description || '',
        })
            .catch(({ errCode, errMsg }: any) => {
                console.log('-----createTextMessage 调用失败', errCode)
            });
        console.log('-----createTextMessage 调用成功', msg)
        if (!msg) return;

        // 调用成功
        const { data } = msg;
        const res = await this.IMSDK.sendMessage({
            recvID: conType === 'user' ? targetId : '',
            groupID: conType === 'group' ? targetId : '',
            message: {
                ...data
            }
        }).catch(({ errCode, errMsg }: any) => {
            console.log('----sendCustomMessage 调用失败', errCode, errMsg)
        });
        if (!res) return
        console.log('---------sendCustomMessage 调用成功', res)
    }

    // 设置会话草稿
    async setConversationDraft(conversationID: string, draftText: string) {
        const res = await this.IMSDK.setConversationDraft({
            conversationID,
            draftText
        }).catch(({ errCode, errMsg }: any) => {
            console.log('----setConversationDraft 调用失败', errCode, errMsg)
        });
        if (!res) return
        console.log('---------setConversationDraft 调用成功', res)
        return res
    }

    // 添加好友
    async addFriend(toUserID: string, reqMsg: string = '') {
        const isLogin = this.$_isLoginedStatus()
        if (!isLogin) return

        const res = await this.IMSDK.addFriend({
            toUserID: toUserID,
            reqMsg: reqMsg,
        }).catch(({ errCode, errMsg }: any) => {
            console.log('----addFriend 调用失败', errCode, errMsg)
            message.error(t('error.request-failed') + ' Error Code: ' + errCode)
        });
        if (!res) return
        console.log('---------addFriend 调用成功', res)
        return res
    }

    // 检验好友关系 1是好友 0非好友
    async checkFriend(userIDList: any[]) {
        const isLogin = this.$_isLoginedStatus()
        if (!isLogin) return

        const res = await this.IMSDK.checkFriend(userIDList).catch(({ errCode, errMsg }: any) => {
            console.log('----checkFriend 调用失败', errCode, errMsg)
            message.error(t('error.request-failed') + ' Error Code: ' + errCode)
        });
        if (!res) return
        console.log('---------checkFriend 调用成功', res)
        return res?.data || {}
    }

    // 按照时间从新到老，获取会话中的历史聊天记录
    async getAdvancedHistoryMessageList(conversationID: string, startClientMsgID: string) {
        const isLogin = this.$_isLoginedStatus()
        if (!isLogin) return

        const { data } = await this.IMSDK.getAdvancedHistoryMessageList({
            viewType: ViewType.History,
            count: 1000,
            startClientMsgID: startClientMsgID,
            conversationID: conversationID,
        })
            .catch(({ errCode, errMsg }: any) => {
                console.log('---------getAdvancedHistoryMessageList 调用失败', errCode, errMsg);
                return;
            });

        return data;
    }

    // 创建群聊
    async createGroup(groupName: string, memberUserIDs: string[]) {
        // const isLogin = this.$_isLoginedStatus()
        // if (!isLogin) return
        // // return
        const data = await this.IMSDK.createGroup({
            groupInfo: {
                groupName: groupName,
                groupType: 2,
            },
            memberUserIDs: memberUserIDs,
        })
            .catch(({ errCode, errMsg }: any) => {
                console.log('---------createGroup 调用失败', errCode, errMsg);
                message.error(t('error.request-failed') + ' Error Code: ' + errCode)
                return false;
            });
        if (!data) return false
        return data?.data;
    }

    // 获取指定群的群成员列表
    async getGroupMemberList(groupID: string, offset: number, count: number) {
        const isLogin = this.$_isLoginedStatus()
        if (!isLogin) return

        const data = await this.IMSDK.getGroupMemberList({
            groupID: groupID,
            filter: 0,
            offset: offset,
            count: count,
        })
            .catch(({ errCode, errMsg }: any) => {
                console.log('---------getGroupMemberList 调用失败', errCode, errMsg);
                message.error(t('error.request-failed') + ' Error Code: ' + errCode)
                return;
            });

        return data?.data;
    }

    //获取指定群信息
    async getSpecifiedGroupsInfo(groupID: any) {
        const isLogin = this.$_isLoginedStatus()
        if (!isLogin) return
        const groupIDList = [groupID]
        const data = await this.IMSDK.getSpecifiedGroupsInfo(groupIDList).catch(({ errCode, errMsg }: any) => {
            console.log('---------getSpecifiedGroupsInfo 调用失败', errCode, errMsg);
            return;
        });

        return data?.data;
    }


    // 设置群信息，包括群头像、名称、公告、简介、扩展字段等
    async setGroupInfo(groupID: string, groupName: string) {
        const isLogin = this.$_isLoginedStatus()
        if (!isLogin) return

        const data = await this.IMSDK.setGroupInfo({ groupID: groupID, groupName: groupName }).catch(({ errCode, errMsg }: any) => {
            console.log('---------setGroupInfo 调用失败', errCode, errMsg);
            message.error(t('error.request-failed') + ' Error Code: ' + errCode)
            return;
        });
        if (!data) return
        return data;
    }

    // 把群成员从群组中移除。
    async kickGroupMember(groupID: string, userIDList: string[]) {
        const isLogin = this.$_isLoginedStatus()
        if (!isLogin) return

        const data = await this.IMSDK.kickGroupMember({
            groupID: groupID,
            reason: '',
            userIDList: userIDList
        })
            .catch(({ errCode, errMsg }: any) => {
                console.log('---------kickGroupMember 调用失败', errCode, errMsg);
                message.error(t('error.request-failed') + ' Error Code: ' + errCode)
                return;
            });

        return data;
    }

    // 邀请用户进群。
    async inviteUserToGroup(groupID: string, userIDList: string[]) {
        const isLogin = this.$_isLoginedStatus()
        if (!isLogin) return false


        const data = await this.IMSDK.inviteUserToGroup({
            groupID: groupID,
            reason: '',
            userIDList: userIDList
        })
            .catch(({ errCode, errMsg }: any) => {
                console.log('---------inviteUserToGroup 调用失败', errCode, errMsg);
                message.error(t('error.request-failed') + ' Error Code: ' + errCode)
                return false;
            });
        if (!data) return false
        return data;
    }

    // 订阅指定用户的在线状态，并返回其在线状态
    async subscribeUsersStatus(userIDList: any[]) {
        const res = await this.IMSDK.subscribeUsersStatus(userIDList).catch(({ errCode, errMsg }: any) => {
            console.log('----subscribeUsersStatus 调用失败', errCode, errMsg)
        });
        if (!res) return
        console.log('---------subscribeUsersStatus 调用成功', res)
        return res?.data || []
    }

    // 获取已加入的群列表
    async getJoinedGroupList() {
        const res = await this.IMSDK.getJoinedGroupList().catch(({ errCode, errMsg }: any) => {
            console.log('----getJoinedGroupList 调用失败', errCode, errMsg)
        });
        if (!res) return
        console.log('---------getJoinedGroupList 调用成功', res)
        return res?.data || []
    }

    // 获取好友申请列表（发出的）。
    async getFriendApplicationListAsApplicant() {
        const res = await this.IMSDK.getFriendApplicationListAsApplicant().catch(({ errCode, errMsg }: any) => {
            console.log('----getFriendApplicationListAsApplicant 调用失败', errCode, errMsg)
        });
        if (!res) return
        console.log('---------getFriendApplicationListAsApplicant 调用成功', res?.data)
        return res?.data || []
    }

    // 获取好友申请列表（收到的）。
    async getFriendApplicationListAsRecipient() {
        const res = await this.IMSDK.getFriendApplicationListAsRecipient().catch(({ errCode, errMsg }: any) => {
            console.log('----getFriendApplicationListAsRecipient 调用失败', errCode, errMsg)
        });
        if (!res) return
        console.log('---------getFriendApplicationListAsRecipient 调用成功', res?.data)
        return res?.data || []
    }

    // 同意对方添加自己为好友
    async acceptFriendApplication(toUserID: string, reqMsg: string = '') {
        const res = await this.IMSDK.acceptFriendApplication({ toUserID, reqMsg }).catch(({ errCode, errMsg }: any) => {
            console.log('----acceptFriendApplication 调用失败', errCode, errMsg)
            message.error(t('error.request-failed') + ' Error Code: ' + errCode)
        });
        if (!res) return
        console.log('---------acceptFriendApplication 调用成功', res)
        return res?.data || []
    }

    // 拒绝对方添加自己为好友
    async refuseFriendApplication(toUserID: string, handleMsg: string = '') {
        const res = await this.IMSDK.refuseFriendApplication({ toUserID, handleMsg }).catch(({ errCode, errMsg }: any) => {
            console.log('----refuseFriendApplication 调用失败', errCode, errMsg)
            message.error(t('error.request-failed') + ' Error Code: ' + errCode)
        });
        if (!res) return
        console.log('---------refuseFriendApplication 调用成功', res)
        return res?.data || []
    }

    // 一次性获取自己所有的好友列表
    async getFriendList() {
        const res = await this.IMSDK.getFriendList().catch(({ errCode, errMsg }: any) => {
            console.log('----getFriendList 调用失败', errCode, errMsg)
            message.error(t('error.request-failed') + ' Error Code: ' + errCode)
        });
        if (!res) return
        console.log('---------getFriendList 调用成功', res)
        return res?.data || []
    }

    // 获取非好友之间的公开信息
    async getUsersInfo(userIDList: any) {
        const res = await this.IMSDK.getUsersInfo(userIDList).catch(({ errCode, errMsg }: any) => {
            console.log('----getUsersInfo 调用失败', errCode, errMsg)
        });
        if (!res) return
        console.log('---------getUsersInfo 调用成功', res.data)
        return res?.data || []
    }

}

// 导出单例引用
export const openIMService = ImChatService.getInstance();
export default ImChatService;





