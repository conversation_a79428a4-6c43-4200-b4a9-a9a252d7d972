export interface TableColumnsFace {
    key?: string,
    documentId: string | number,
    documentName: string,
    status:  number,
    updateTime: string,
    hitCount: string,
    characters: number
  }
  
  
  export interface ContextState {
    robotId: string | null,
    apiName: string,
    selectedRowKeys: React.Key[],
    selectedRows: object[],
    dataSource: TableColumnsFace[],
    tableLoading: boolean,
    operateModal: {
      visible: boolean,
      type: string,
      robotId: any,
      keyId?: any,
      apiName?: any
    },
    deleteModal: {
      isBatch: boolean,
      visible: boolean,
      ids: any
    }
  }