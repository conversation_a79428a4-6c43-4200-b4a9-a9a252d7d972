import thunk from "redux-thunk";
import { createLogger } from "redux-logger";
import { legacy_createStore as createStore, applyMiddleware } from "redux";
import { composeWithDevTools } from "redux-devtools-extension";
import rootReducer from "./reducers";

// const devTools = process.env.NODE_ENV === "development" ? composeWithDevTools(applyMiddleware(thunk, createLogger())) : composeWithDevTools(applyMiddleware(thunk));
const devTools = composeWithDevTools(applyMiddleware(thunk));
export default createStore(
  rootReducer,
  devTools
);
