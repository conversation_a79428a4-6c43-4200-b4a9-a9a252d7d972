import { connect } from "react-redux";
import React, { useState, useRef, useEffect } from "react";
import { Badge, Button, Empty, Flex, Input, message, Modal, Pagination, Popover, Select, Spin, Tabs, TabsProps, Tag, Tooltip } from 'antd'
import { useTranslation } from 'react-i18next';
import SvgIcon from '@components/svgicon';
import './index.scss'
import { $trim, $isNull } from '@/utils/common'
import Svgicon from "@components/svgicon";

import { api_get_notify_tags, api_create_notify_tag, api_update_notify_tag, api_delete_notify_tag, api_tag_notify, api_delete_tag_notify } from '@/api/information'
import { modalGlobalConfig } from "antd/es/modal/confirm";

const tagNameLengthLimit = 20
const taLengthLimit = 10

// 标签管理页面，限制10个
const TagManage = (props: any) => {
    const { t } = useTranslation();
    const { children, options = [], notificationId, onUpdate, onRefreshListItemTag, onRefreshListTargetTag, onRefreshTags,onDeleteListTargetTag } = props
    const InputRef = useRef<any>(null)

    const [allTagOptions, setAllTagOptions] = useState<any>([]); // 所有标签
    const [filterTagOptions, setFilterTagOptions] = useState<any>([]); // 筛选标签结果
    const [activeTagOptions, setActiveTagOptions] = useState<any>([]); // 当前选中的标签列表 
    const [searchValue, setSearchValue] = useState<any>(null); // 输入框
    const [openPopover, setOpenPopover] = useState(false); // 是否显示弹窗
    const [createTagObj, setCreateTagObj] = useState<any>(null);
    const [activeTag, setActiveTag] = useState<any>(null);
    const [inputKey, setInputKey] = useState<any>(0);

    const tagColors = [
        // default默认 orange橙色 yellow黄色 green绿色 red红色  purple紫色 cyan 青色 gray灰色 pink粉色 lime草绿
        { color: 'default', name: t('info-hub.default') },
        { color: 'orange', name: t('info-hub.orange') },
        { color: 'yellow', name: t('info-hub.yellow') },
        { color: 'lime', name: t('info-hub.lime') },
        { color: 'green', name: t('info-hub.green') },
        { color: 'cyan', name: t('info-hub.cyan') },
        { color: 'purple', name: t('info-hub.purple') },
        { color: 'pink', name: t('info-hub.pink') },
        { color: 'red', name: t('info-hub.red') },
        { color: 'gray', name: t('info-hub.gray') }
    ] // tag 颜色选项

    // 初始化
    useEffect(() => {
        // console.log('currentOptions',currentOptions)

    }, [])

    useEffect(() => {
        // console.log('currentOptions',currentOptions)
        if (options && options.length > 0) {
            const _options = options.map((o: any) => {
                return {
                    id: o.id,
                    name: o.tagName,
                    color: o.tagColor
                }
            })
            // console.log('options change', options)
            // console.log('_options change', _options)
            setActiveTagOptions([..._options])
        } else {
            setActiveTagOptions([])
        }
    }, [options])

    useEffect(() => {
        if (openPopover) init()
    }, [openPopover])

    const init = async () => {
        setSearchValue('')
        setCreateTagObj(null)
        getTags(true)

        // setActiveTagOptions([...currentOptions])
        // 输入框聚焦
        setInputKey(inputKey+1)
        InputRef?.current?.focus()
    }

    // 获取所有标签
    const getTags = async (isInit?: boolean) => {
        const res: any = await api_get_notify_tags().catch(() => {
        })
        if (!res) return
        let datas = res.data || []
        // console.log('datas', datas)
        datas = datas.map((i: any) => {
            return {
                id: i.id,
                name: i.tagName,
                color: i.tagColor
            }
        })
        setAllTagOptions([...datas])
        setFilterTagOptions([...datas])
        if (!isInit) {
            // 通知刷新
            onRefreshTags && onRefreshTags([...datas])
        }
    }

    // 输入框输入
    const onSearchValueChange = (e: any) => {
        const keyWord = $trim(e.target.value)
        setSearchValue(keyWord)
        if (keyWord !== '') {
            // 筛选Option
            const filterResult = allTagOptions.filter((i: any) => i.name.indexOf(keyWord) !== -1)
            // console.log('filterResult', filterResult)
            setFilterTagOptions(filterResult)
            // 创建
            if (filterResult.length === 0) {
                if (allTagOptions.length < taLengthLimit) {
                    setCreateTagObj({ name: keyWord, color: tagColors[0].color })
                }
            } else {
                setCreateTagObj(null)
            }
        } else {
            setFilterTagOptions([...allTagOptions])
        }
    }

    // 确定新增标签
    const onConfirmCreateTag = async () => {
        const tagName = searchValue
        setSearchValue('') // 清空输入框
        const data = {
            "tagName": searchValue,
            "tagColor": "default"
        }
        // 1. 新增标签
        const res: any = await api_create_notify_tag(data).catch(() => {
        })
        if (!res) return
        const newTagId = res.data
        // 2.获取所有标签
        getTags()
        // console.log('newTagId', newTagId)
        // 3. 为通知分配刚新增的标签
        await onSelectTagToNotify({ id: newTagId, name: tagName, color: data.tagColor })
        const newObj = {
            id: newTagId,
            ...createTagObj
        }
        const newActiveTags = [...activeTagOptions, newObj]
        // console.log('newActiveTags', newActiveTags)
        setActiveTagOptions(newActiveTags)
        setCreateTagObj(null)

    }

    // 为当前通知 选择标签
    const onSelectTagToNotify = async (tag: any) => {
        const isExist = activeTagOptions.filter((i: any) => i.id === tag.id).length > 0
        if (isExist) return
        const data2 = {
            id: notificationId,
            tags: [tag.id]
        }
        const res: any = await api_tag_notify(data2).catch(() => {
            return false
        })
        if (!res) return false
        setSearchValue('') // 清空输入框
        // console.log('res2', res)
        const newActiveTags = [...activeTagOptions, tag]
        setActiveTagOptions(newActiveTags)
        let emitTags = getEmitTags(newActiveTags)
        onRefreshListItemTag && onRefreshListItemTag(notificationId, emitTags) // 通知列表刷新通知标签数据
        return res
    }

    // 获取通知列表 标签数据格式
    const getEmitTags = (tags: any) => {
        let emitTags = tags.map((i: any) => {
            return {
                id: i.id,
                "tagName": i.name,
                "tagColor": i.color
            }
        })
        return emitTags
    }

    // 弹窗显隐
    const onOpenChange = (open: boolean) => {
        setOpenPopover(open)
        if (!open) {
            // console.log('关闭弹窗')
            // todo 保存 tag修改
        } else {
            // console.log('open弹窗')
        }
    }

    // 选择标签编辑
    const onSelectEditTag = (e: any, tag: any) => {
        // e.stopPropagation()
        // console.log('tag', tag)
        setActiveTag(tag.id)
    }

    // 移出通知 标签
    const onRemoveNotifyTag = async (tag?: any) => {
        // console.log('tag', tag)
        const data = [tag.id]
        const res: any = await api_delete_tag_notify(notificationId, data).catch(() => {
            return false
        })
        if (!res) return false
        return true
    }

    // 为通知删除标签
    const onDeleteNotifyTag = async (e: any, tag: any, index: number) => {
        // console.log('tag', tag)
        const isOk = await onRemoveNotifyTag(tag)
        if (!isOk) return
        // 刷新 当前弹窗-已选标签
        const newActiveTags = [...activeTagOptions]
        newActiveTags.splice(index, 1)
        let emitTags = getEmitTags(newActiveTags)
        setActiveTagOptions(emitTags)
        // 刷新 列表通知标签
        // console.log('emitTags', emitTags)
        onRefreshListItemTag && onRefreshListItemTag(notificationId, emitTags)
    }

    // 编辑弹窗 - 删除标签
    const onDeleteTag = async (tag?: any) => {
        const isOk = await onRemoveNotifyTag(tag)
        if (!isOk) return
        // 刷新 当前弹窗-已选标签
        const newActiveTags = activeTagOptions.filter((t:any)=>t.id!=tag.id)
        let emitTags = getEmitTags(newActiveTags)
        setActiveTagOptions(emitTags)
        // 通知 列表移除标签
        onDeleteListTargetTag(tag)
        getTags()
         
    }

    // // 编辑弹窗 - 修改标签 或标签名
    // const onUpdateTag = async (tag?: any) => {
         
    // }


    // 编辑弹窗隐藏
    const onCloseEditPopover = (tag: any) => {
        setActiveTag(null)
    }

    // 编辑tag弹窗 通知刷新tag列表
    const onRefreshEditPopover = (tag: any) => {
        // setActiveTag(null)
        // 更新activeTagOptions
        setActiveTagOptions(activeTagOptions.map((item: any) => {
            return tag.id == item.id ? { ...tag } : item
        }))
        // 通知 刷新通知列表，更新 打了该标签的通知
        onRefreshListTargetTag(tag)
        // 刷新标签列表
        getTags()
    }

    const renderPopover = (_options: any) => {
        return (
            <div className="ann-tag-operate">
                {/* 已添加tag集合+输入框 */}
                <Flex className="to-top" align="center" wrap={true} gap={4}>
                    {
                        _options.map((tag: any, index: number) => (
                            <Tag className={`to-top-tag ${tag.color}`} key={tag.id} bordered={false}
                            >
                                <Flex align="center" gap={8}>
                                    <div className="to-top-tag-name">{tag.name}</div>
                                    <SvgIcon onClick={(e: any) => onDeleteNotifyTag(e, tag, index)} className="to-top-tag-delete" svgName="icon_close" />
                                </Flex>
                            </Tag>
                        ))
                    }
                    <div className='to-top-input'>
                        <Input
                            key={inputKey}
                            ref={InputRef}
                            value={searchValue}
                            onChange={onSearchValueChange}
                            onKeyDown={(e) => e.stopPropagation()}
                            variant="borderless"
                            size="small"
                            placeholder={t('info-hub.search-tag') as string}
                            maxLength={tagNameLengthLimit}
                            autoFocus={true}
                        />
                    </div>
                </Flex>
                <div className="to-tip">{
                    !$isNull(searchValue) && filterTagOptions.length == 0 && allTagOptions.length == taLengthLimit
                        ? t('info-hub.tag-limit',{count:taLengthLimit}):
                        t('info-hub.add-select-tag')
                }</div>
                <Flex className="to-list" gap={4} vertical>
                    {
                        filterTagOptions.map((tag: any) => (
                            <Flex
                                onClick={() => onSelectTagToNotify(tag)}
                                className={`to-list-item ${activeTag === tag.id ? 'active' : ''}`}
                                key={tag.id} align="center" gap={10} justify="space-between">
                                <Tag className={`to-list-tag ${tag.color}`} key={tag.id} bordered={false}>
                                    {tag.name}
                                </Tag>
                                <EditPopover
                                    tagData={tag}
                                    tagColors={tagColors}
                                    // onUpdateTag={onUpdateTag}
                                    onDelete={onDeleteTag}
                                    onClose={onCloseEditPopover}
                                    onRefresh={onRefreshEditPopover}
                                >
                                    <Flex align="center" justify="center" className="to-list-operate" >
                                        <SvgIcon onClick={(e: any) => onSelectEditTag(e, tag)} svgName="more_dot" />
                                    </Flex>
                                </EditPopover>
                            </Flex>
                        ))
                    }
                    {
                        createTagObj ?
                            <Flex onClick={onConfirmCreateTag} align="center" className="to-list-item create" gap={10}>
                                <div className="create-title">{t('company.create')}</div>
                                <div className="">{ }</div>
                                <Tag className={`create-tag ${createTagObj.color}`} bordered={false}>
                                    {createTagObj.name}
                                </Tag>
                            </Flex>
                            : null
                    }
                </Flex>
            </div>
        )
    }

    return (
        <div onClick={(e) => { //console.log(e);
            e.stopPropagation()
        }}>

            {openPopover ? <div className="popver-mask" onClick={(e: any) => {
                e.stopPropagation()
                setOpenPopover(false)
            }}></div> 
            : null}
            <Popover
                placement="bottomLeft"
                arrow={false} overlayClassName="ann-tag-operate-popover"
                trigger='click'
                open={openPopover}
                onOpenChange={onOpenChange}
                content={renderPopover(activeTagOptions)}
                // destroyTooltipOnHide={true}
            // getPopupContainer={(triggerNode: any) => { return props.popupContainer || triggerNode.parentNode }}
            // getPopupContainer={
            //     (triggerNode: any) => {
            //         return triggerNode.parentNode;
            //     }
            // }
            >
                <Flex onClick={(e: any) => e.stopPropagation()} gap={8} align="center" wrap={true}>
                    <Button className="quaternary add-tag" icon={<SvgIcon svgName="icon_muti_plus" />}
                        color="default" size="small" variant="filled">{t('info-hub.add-tag')}</Button>
                    {children}
                </Flex>
            </Popover>
        </div>

    )
}

const EditPopover = (props: any) => {

    const { t } = useTranslation();
    const [modal, contextHolder] = Modal.useModal();

    const { children, tagData, tagColors, onUpdateColor, onDelete, onUpdateTag, onClose, onRefresh } = props

    const [openTagEditPopover, setOpenColorPopover] = useState(false); // 是否显示颜色操作弹窗
    const [originTagName, setOriginTagName] = useState<any>(''); // 标签名
    const [tagName, setTagName] = useState<any>(''); // tagName输入框
    const [tagSelected, setTagSelected] = useState<any>({}); // 选择编辑的tag

    // const tagColors = [
    //     // default默认 orange橙色 yellow黄色 green绿色 red红色  purple紫色 cyan 青色 gray灰色 pink粉色 lime草绿
    //     { color: 'default', name: '默认' },
    //     { color: 'orange', name: '橙色' },
    //     { color: 'yellow', name: '黄色' },
    //     { color: 'lime', name: '草绿' },
    //     { color: 'green', name: '绿色' },
    //     { color: 'cyan', name: '青色' },
    //     { color: 'purple', name: '紫色' },
    //     { color: 'pink', name: '粉色' },
    //     { color: 'red', name: '红色' },
    //     { color: 'gray', name: '灰色' }
    // ] // tag 颜色选项

    useEffect(() => {
        if (openTagEditPopover) {
            setOriginTagName(tagData.name)
            setTagName(tagData.name)
            setTagSelected(tagData)
        } else {
            setTagSelected({})
        }
    }, [openTagEditPopover])

    // 弹窗显隐
    const onOpenTagEditChange = (open: boolean) => {
        setOpenColorPopover(open)
        if (!open) {
            onClose && onClose()
            updateTagName()
        }
    }

    const onTagNameChange = (e: any) => {
        const value = $trim(e.target.value)
        setTagName(value)
    }

    // 编辑标签名输入框，enter事件
    const onTagNameEnter = (e: any) => {
        // onSaveTag({ name: tagName })
        updateTagName()
    }

    // 修改标签名
    const updateTagName = async () => {
        if ($isNull(tagName)){
            // 输入为空
            setTagName(originTagName)
            return 
        }
        const name = tagName
        const isSucess = await onSaveTag({ name })  // 请求保存标签名称
        // console.log('isSucess', isSucess)
        if (isSucess) {
            onUpdateTag && onUpdateTag({ ...tagData, name })
            setOriginTagName(tagName)
        }
    }

    // 切换颜色
    const onSelectTagColor = async (target: any) => {
        const isSucess = await onSaveTag({ color: target.color })
        if (isSucess) onUpdateTag && onUpdateTag({ ...tagData, color: target.color })
        // setOpenColorPopover(false)
    }

    // 删除tag、移除通知标签
    const onDeleteTag = async (e: any) => {
        e.stopPropagation();
        // modal.confirm({
        //     width: 272,
        //     title: t('app.confirm-del'),
        //     icon: null,
        //     content: '',
        //     okText: t('app.ok'),
        //     okButtonProps: { color: "danger", variant: "filled" },
        //     centered: true,
        //     className: 'no-icon',
        //     closable: true,
        //     onCancel: () => {
        //     },
        //     onOk: async() => {
        const data = { id: tagData.id }
        const res: any = await api_delete_notify_tag(data).catch(() => {
        })
        if (!res) return
        onDelete && onDelete(tagData)

        // }
        // });

    }

    // 修改标签
    const onSaveTag = async (newTag: any) => {
        // console.log('newTag', newTag)
        const r = {
            ...tagData,
            ...newTag
        }
        const data = {
            id: tagData.id,
            "tagName": r.name,
            "tagColor": r.color
        }
        const res: any = await api_update_notify_tag(data).catch(() => {
            return false
        })
        if (!res) return false
        onClose && onClose() // 关闭弹窗
        setOpenColorPopover(false)
        onRefresh && onRefresh(r) // 通知刷新标签列表
        return true
    }

    const renderTagEditPopover = () => {
        // console.log('tagData', tagData)
        return (
            <div className="ann-tag-color-operate" onClick={(e) => { e.stopPropagation() }}>
                <Input
                    className=""
                    value={tagName}
                    onChange={onTagNameChange}
                    // onKeyDown={(e) => e.stopPropagation()}
                    onPressEnter={onTagNameEnter}
                    variant="filled"
                    maxLength={tagNameLengthLimit}
                />
                <Flex onClick={(e) => onDeleteTag(e)} className="ann-tag-delete" align="center" gap={8}>
                    <SvgIcon className="" svgName="icon_delete" />
                    <div>{t('common.delete')}</div>
                </Flex>
                <div className="color-picker">
                    <div className="cp-title">{t('info-hub.tag-color')}</div>
                    <Flex gap={4} vertical>
                        {
                            tagColors.map((item: any) => (
                                <Flex onClick={() => onSelectTagColor(item)} className="cp-tag-item" key={item.color} align="center">
                                    <Tag className={`cp-color ${item.color}`}>
                                    </Tag>
                                    <div className="cp-color-name">{item.name}</div>
                                    {
                                        tagData.color === item.color ? <SvgIcon className="icon_selected" svgName="icon_check" /> : null
                                    }
                                </Flex>
                            ))
                        }
                    </Flex>
                </div>
            </div>
        )
    }
    return <div className="ann-tag-edit" onClick={(e) => { e.stopPropagation() }}>
        {openTagEditPopover ? <div className="popver-mask"></div> : null}
        <Popover
            key="edit"
            placement="bottom" arrow={false} overlayClassName="ann-tag-operate-color-popover"
            trigger='click'
            open={openTagEditPopover}
            onOpenChange={onOpenTagEditChange}
            content={renderTagEditPopover()}
        >
            {children}
        </Popover>
        {contextHolder}
    </div>
}

const mapStateToProps = (state: any) => ({
    // activeCompanyId: state.app.activeCompanyId
})
const mapDispatchToProps = (dispatch: any) => ({
    // dispatch_api_: (data:any) => dispatch(dispatch_api_(data))
})
export default connect(mapStateToProps, mapDispatchToProps)(TagManage);