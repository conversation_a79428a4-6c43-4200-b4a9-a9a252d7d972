@import "../../../../styles/mixin.scss";
@mixin flex($flex-direction: row, $align-items: center, $justify-content: normal) {
    display: flex;
    flex-direction: $flex-direction;
    align-items: $align-items;
    justify-content: $justify-content;
}
$sideMenuBg: var(--colorBgNav);
.robot-wrapper {
    .side-menu {
        @include flex($flex-direction: column);
        width: 280px;
        background: $sideMenuBg;
        height: 100%;
        .no-data {
            @include font(12px, 18px, 400, var(--colorTextQuaternary));
            height: 60px;
        }
        border-right: 1px solid var(--colorBorderSecondary);
        .robot-logo {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            border: 1px solid var(--colorBorderImage);
        }
    }
    $align-space: 6px;
    $padding: 8px;
    .side-menu-title {
        width: 100%;
        flex-shrink: 0;
        @include flex($justify-content: space-between);
        @include font(18px, 24px, 600);
        padding: 19px $align-space;
        padding-left: 16px;
        span {
            margin-right: 4px;
        }
        .anticon {
            font-size: 16px;
            cursor: pointer;
        }
    }
    .menu-item-container {
        position: relative;
        padding: 0 $align-space;
        width: 100%;
        flex: 1;
        overflow-y: auto;
    }
    $h: 34px;
    .menu-item-title {
        position: sticky;
        height: $h;
        bottom: 0;
        z-index: 1000;
        @include flex($justify-content: space-between);
        @include font(12px, 18px, 600);
        padding: 8px $padding;
        // margin-top:10px;
        cursor: pointer;
        background: $sideMenuBg;
        .toggle-icon {
            font-size: 16px;
            transition: transform 0.2s ease-in-out;
            &.close {
                transform: rotate(180deg);
            }
        }
        &.share {
            bottom: 0;
        }
        &.user {
            top: 0;
        }
    }
    .menu-item-title-text {
        .svg-icon,
        .anticon {
            font-size: 16px;
            color: var(--colorIcon);
        }
    }
    .menut-item-content {
        @include flex;
        padding: 7px $padding;
        @include font(14px, 22px, 500);
        margin-bottom: 6px;
        cursor: pointer;
        &.home-bot {
            width: calc(100% - $align-space * 2);
            flex-shrink: 0;
            font-weight: 500;
            font-size: 14px;
            line-height: 22px;
            padding: 6px $padding;
            margin: 0 auto 16px;
        }
        &:hover {
            background: var(--colorFillQuaternary);
            border-radius: 12px;
        }
        &.active {
            background: var(--colorPrimary);
            color: var(--colorPrimaryContrasting);
            border-radius: 12px;
        }
        .svg-icon {
            font-size: 16px;
            margin-right: 10px;
            flex-shrink: 0;
        }
        span {
            flex: 1;
            @include ellipsis-multiline(1);
        }
    }
    .menut-item-list {
        height: auto;
        display: block;
        transition: all 0.5s ease;
        max-height: auto;
        &.is-close {
            display: none;
            height: 0;
            max-height: 0;
        }
    }
    .user-menu {
        flex-shrink: 0;
    }

    .robot-side-menu.side-menu {
        .function-area {
            margin-bottom: 16px;
            flex-shrink: 0;
            width: 100%;
            padding: 0 6px;
            .functioin-title {
                padding: 6px 8px;
                @include font(12px, 18px, 600);
            }
            .functioin-item {
                cursor: pointer;
                padding: 6px 8px;
                @include font(14px, 22px, 600);
                &.active {
                    color: var(--colorPrimaryContrasting);
                    background: var(--colorPrimary);
                    border-radius: 12px;
                    .svg-icon {
                        color: var(--colorPrimaryContrasting);
                    }
                    &:hover {
                        background: var(--colorPrimary);
                    }
                }
                .svg-icon {
                    font-size: 16px;
                    color: var(--colorIcon);
                }
                &:hover {
                    background: var(--colorFillQuaternary); // 会有 看到下层 机器人名称的问题
                    border-radius: 12px;
                }
            }
        }
    }
}
