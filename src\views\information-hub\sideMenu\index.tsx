import { connect } from "react-redux";
import { useTranslation } from 'react-i18next';
import { useNavigate } from "react-router-dom";
import React, { useState, useRef, useEffect, forwardRef, useImperativeHandle } from "react";
import { Flex, Modal, message, Badge } from 'antd'
import SvgIcon from '@components/svgicon'; // 根据实际路径引入
import classnames from "classnames";
import { change_app_is_refresh_companies, change_app_active_company } from '@/store/action'

import classNames from "classnames";
import './index.scss'
import { api_get_ann_manage_permission } from "@/api/information";

const defaultmenuItemResource: any[] = [
    {
        index: 1,
        key: "Ann",
        label: 'info-hub.ann',
        icon: <SvgIcon svgName="nav_ann" />
    }
]
// 对admin用户、部门supervisor、在admin平台配置了有发布公告权限的个人
const authMenuItemResource: any[] = [
    {
        index: 2,
        key: 'ManageAnn',
        label: 'info-hub.ann-manage',
        icon: <SvgIcon svgName="icon_robot_manage" />
    }
]


// 侧边菜单
const SideMenuComponent = forwardRef((props: any, ref) => {
    // 自定义暴露给父组件的实例值
    useImperativeHandle(ref, () => ({
        // 初始化
        emit_init() {
        }
    }))
    const { t } = useTranslation();
    const navigate = useNavigate();
    const [modal, contextHolder] = Modal.useModal();
    const containerRef = useRef(null)
    const tabWindow = useRef<any>(null)

    const { activeCompanyId, isActiveCompanyAdmin, userInfo, onChangeMenu, activeCompanyName } = props

    const [menuItems, setMenuItems] = useState<any>(defaultmenuItemResource); // 菜单选项
    const [activeMenu, setActiveMenu] = useState<any>(null); // 当前选中的菜单
    const [isAdmin, setIsAdmin] = useState<any>(false); // 是否是当前企业的管理员


    useEffect(() => {
    }, [])

    useEffect(() => {
        init()
    }, [isActiveCompanyAdmin])

    // 初始化
    const init = async () => {
        // 根据权限初始化菜单
        let items = [...defaultmenuItemResource]
        // console.log('_isAdmin',_isAdmin)
        // 有“公告管理”菜单必须满足条件之一： admin用户、至少是一个部门的supervisor、在admin平台配置了有发布公告权限的个人
        // if (isActiveCompanyAdmin) {
        //     items = [...items, ...authMenuItemResource]
        // } else{
            
        // }
        const res = await getAnnManagePermission()
        // console.log('res',res)
        if (res?.notificationPermission){
            items = [...items, ...authMenuItemResource]
        }

        items = items.sort((a: any, b: any) => a.index - b.index) // 排序
        const _activeMenu = items[0]?.key // 选中菜单
        setActiveMenu(_activeMenu)
        setMenuItems(items)
        onChangeMenu && onChangeMenu(_activeMenu)
    }

     // 获取当前用户是否有 公告管理权限
     const getAnnManagePermission = async () => {
        const res: any = await api_get_ann_manage_permission().catch(() => {
        })
        if (!res) return []
        return res
    }

    // 选择菜单
    const handleSelectMenu = async (target?: any, e?: any) => {
        e && e.stopPropagation()
        setActiveMenu(target.key)
        // console.log(target.key)
        onChangeMenu && onChangeMenu(target.key)
        return
    }


    return (
        <div className="information-side-menu">
            <div className="side-menu-title">
                <span>{t('info-hub.title')}</span>
            </div>
            {
                menuItems ?
                    <div className="information-menu-container" ref={containerRef}>
                        {menuItems.map((item: any, hIndex: number) => {
                            return (
                                <React.Fragment key={item.key}>
                                    <Flex align="center" onClick={(e) => handleSelectMenu(item, e)}
                                        className={classNames(["information-menu-item-title", item.key, { active: activeMenu === item.key }])} gap={8}>
                                        {item.icon && item.icon}
                                        <span className="menu_label">{t(item.label)}</span>
                                    </Flex>

                                </React.Fragment>
                            )
                        })}
                    </div>
                    : null
            }
            {contextHolder}
        </div>
    );
});
const mapStateToProps = (state: any) => ({
    isPrivate: state.app.isPrivate,
    userInfo: state.app.userInfo,
    isActiveCompanyAdmin: state.app.isActiveCompanyAdmin,
    activeCompanyId: state.app.activeCompanyId,
    activeCompanyName: state.app.activeCompanyName
})
const mapDispatchToProps = (dispatch: any) => ({
    dispatch_change_app_active_company: (data: any) => dispatch(change_app_active_company(data)),
    dispatch_change_app_is_refresh_companies: (data: any) => dispatch(change_app_is_refresh_companies(data))
})
const ConnectedCounter = connect(mapStateToProps, mapDispatchToProps, null, { forwardRef: true })(SideMenuComponent);
export default ConnectedCounter;
