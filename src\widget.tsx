import React from 'react';
import ReactDOM from 'react-dom/client';
import "./i18n";
import "antd/dist/reset.css";
import "./assets/fontcss/iconfont.css";
import "./styles/index.scss";
import './icons'; // 导入所有 SVG 图标
import WidgetApp from './widgetApp';
import reportWebVitals from './reportWebVitals';
import { disableReactDevTools } from '@fvilers/disable-react-devtools';

const root = ReactDOM.createRoot(
  document.getElementById('widget') as HTMLElement
);
// 生产环境禁用react developer tools
if (process.env.NODE_ENV === 'production') {
  disableReactDevTools()
}
window.addEventListener('error', (event) => {
  console.error('捕获到全局错误:', event.error);
  // 这里可以调用错误上报服务
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('捕获到未处理的 Promise 拒绝:', event.reason);
  // 这里可以调用错误上报服务
});

root.render(
    <WidgetApp />
  );
reportWebVitals();
