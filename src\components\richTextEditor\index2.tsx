import React, { Component, forwardRef, useEffect, useRef, useState } from 'react';
import { connect } from 'react-redux';
import SunEditor from 'suneditor-react';
import 'suneditor/dist/css/suneditor.min.css';

import { en } from "suneditor/src/lang";
import zh_hk from "./lang/zh_hk";
import zh_cn from "./lang/zh_cn";
import "katex/dist/katex.min.css";
import "./index.scss"
import { getFileTypeByName } from '@/utils/file';
import { message } from 'antd';
import { api_upload_image } from '@/api/information';
import { useTranslation } from 'react-i18next';

const langMap: any = {
    en: en,
    zh: zh_cn,
    hk: zh_hk
  }

const imageAccept = '.jpg,.jpeg,.png,.gif'
const imageSizeLimit = 10
const maxCharCount = 5000

const Editor = forwardRef((props: any, ref: any) => {
    const { t } = useTranslation();
    const { contents: initialContent, onContentChange, disabled, onUpdate, readOnly,
        activeCompanyId, globalLang } = props

    // 使用 getSunEditorInstance 而不是 ref
    const editorInstance = useRef<any>(null);

    const [content, setContent] = useState(initialContent);
    const [isDisabled, setIsDisabled] = useState(false);
    const [activeLang, setActiveLang] = useState(globalLang);
    const [languagePack, setLanguagePack] = useState<any>(langMap[props.globalLang]);
    const [editorKey, setEditorKey] = useState(0);


    // 初始化
    useEffect(() => {
    })

    // useEffect(() => {
    //     // return()=>{
    //     //     if (editorInstance?.current) editorInstance?.current.destroy();
    //     // }
    //     console.log('editorInstance', editorInstance)
    //     // return () => {
    //     //     if (editorInstance && editorInstance?.current) editorInstance?.current.destroy();
    //     // };
    // }, [editorInstance])

    // 监听内容输入改变
    useEffect(() => {
        // console.log('initialContent change', initialContent)
        setContent(initialContent)
        // editorInstance?.current.core.history.reset(true);
    }, [initialContent])

    // 监听 禁用状态改变
    useEffect(() => {
        // console.log('disabled change', disabled)
        setIsDisabled(disabled)
        // setEditorStatus(disabled)
    }, [disabled])

    // 监听 语言改变
    useEffect(() => {
        if (globalLang != activeLang) {
            setActiveLang(globalLang)
            // console.log('globalLang change', disabled)
            changeLanguage(globalLang)
        }

    }, [globalLang])

    // 加载语言包并重置编辑器
    const changeLanguage = async (langCode: any) => {
        try {
            let pack;
            switch (langCode) {
                case 'zh':
                    pack = (await import('./lang/zh_cn')).default;
                    break;
                case 'hk':
                    pack = (await import('./lang/zh_hk')).default;
                    break;
                case 'en':
                    pack = (await import('suneditor/src/lang/en')).default;
                    break;
            }

            setLanguagePack(pack);
            setEditorKey(prev => prev + 1); // 改变 key 强制重新渲染
        } catch (error) {
            console.error('切换语言失败:', error);
        }
    };

    const getEditorInstance = (editor: any) => {
        editorInstance.current = editor;
        // console.log('编辑器实例:', editor);
    };

    // 设置编辑器状态：可编辑 or 只读
    const setEditorStatus = (isDisabled: boolean) => {
        // 会导致 link 编辑弹窗出不来,还有字体格式无法active 
        const editor = editorInstance.current
        // console.log('editor',editor, isDisabled)
        // if (isDisabled) {
        //     // 编辑器只读
        //     editor.disable()
        //     editor.readOnly(true)
        //     editor.toolbar.hide()

        // } else {
        //     editor.enable()
        //     editor.readOnly(false)
        //     editor.toolbar.show()
        // }
        if (isDisabled) {
            editor.setOptions({
                // ...this.state.options,
                resizingBar: !isDisabled
            })
            
        }

        // console.log('this.editor.', this.editor)
    }

    // 内容改变
    const handleChange = (newContent: string) => {
        setContent(newContent);
        onContentChange?.(newContent);
        // console.log('newContent', newContent)
        // console.log('editorInstance', editorInstance, isDisabled)
        try {
            if (isDisabled || !editorInstance?.current) return
            // console.log('newContent22', newContent)
            const editor = editorInstance.current
            // 获取字符数
            const charCount = editor && editor?.getCharCount();
            const images = editor.getFilesInfo('image') || [];
            const videos = editor.getFilesInfo('video') || [];
            const filesInfo = [...images, ...videos]
            // console.log('----onChangeContent filedInfo', filesInfo)
            // console.log('----onChangeContent charCount', charCount)

            // const byteCount= 0
            // console.log('this.editor?.getCharCount', this.editor?.getCharCount) 
            // console.log('----charCount2', charCount2)
            onUpdate && onUpdate(newContent, charCount, filesInfo)
            const isFullChar = charCount && charCount == maxCharCount
            if (isFullChar) {
                // console.log('到达上限')
                editor.toolbar.disable()
            } else {
                editor.toolbar.enable()
            }
        } catch (error) {
            console.log('error', error)
        }

    };

    // 检验图片 格式和大小
    const validateImage = (file: any) => {
        // 验证文件类型和大小
        const fileType = getFileTypeByName(file.name)
        const typeAccept = imageAccept.split(',')
        if (!typeAccept.includes(`.${fileType}`)) {
            message.error(t('info-hub.unsurpport-image-format'));
            return false
        }
        // console.log('file.size', file.size)
        if (file.size > imageSizeLimit * 1024 * 1024) {
            message.error({ content: t('info-hub.image-size-limit', { num: imageSizeLimit }) });
            return false
        }
        return true
    }

    const onImageUploadBefore = (files: any, info: any, uploadHandler: any) => {
        // console.log('onImageUploadBefore files', files)
        if (files[0]) handleImageUpload(files[0], info)
        return false
    }

    // 图片上传
    const handleImageUpload = async (file: any, info: any) => {
        // console.log('file', file)

        const isValid = validateImage(file)
        // console.log('isValid',isValid)
        if (!isValid) return

        // 上传到服务器
        const formData = new FormData();
        formData.append('file', file);

        const response: any = await api_upload_image(formData).catch(() => {})
        // const response = {
        //     "id": null,
        //     "fileName": "icon_drap.png",
        //     "filePath": "/attachments/notifications/24d56af10fac01446642d04ce645f2fbd973f04f5dde26fdc046517af19ccdd7.png",
        //     "fileSize": 959,
        //     "fileType": "image/png"
        // }
        // console.log('response', response)
        // console.log('info', info)
        // console.log('core', core)
        if (response) {
            // const imageUrl = URL.createObjectURL(file);
            const align = info.align
            const baseUrl = "/gateway/app-api/system/notification/image?url=" // 后端要求的
            // console.log('align', align)
            const imageObj = `<img src="${baseUrl + response.filePath}" alt="${info.alt}" data-align="${align}"  data-file-name="${file.name}">`
            // console.log('imageObj', imageObj)
            // console.log('editorRef?.current',editorInstance?.current)
            editorInstance?.current?.insertHTML(imageObj, true, true);
            // this.editor.insertImage(response.data, {
            //   name: file.name,
            //   size: file.size,
            //   element: null,
            //   alt: info.alt || '示例图片',       // 替代文本
            //   dataCustom: 'value'    // 自定义数据属性
            // });
        } else {
            // 上传失败
        }
    }

    // 视频上传
    const onVideoUpload = (state: any, info: any) => {
        // const contents = this.editor && this.editor?.getContents();
        // console.log('contents',contents)
        // const videos = this.editor.getFilesInfo('video');
        // console.log('videos',videos)
    }

    return (
        <div className={`ama-richtext-editor ${props.themeInfo?.theme} ${isDisabled ? 'disabled' : ''} ${readOnly ? 'read-only' : ''}`}>
            <SunEditor
                key={`editor-${editorKey}`}
                getSunEditorInstance={getEditorInstance}
                onChange={handleChange}
                setContents={content}
                // setOptions={{
                //     height: '300px',
                //     buttonList: [
                //         ['undo', 'redo'],
                //         ['font', 'fontSize', 'formatBlock'],
                //         ['bold', 'underline', 'italic', 'strike', 'subscript', 'superscript'],
                //         ['fontColor', 'hiliteColor', 'removeFormat'],
                //         ['align', 'list'],//, 'horizontalRule'
                //         ['table', 'link',
                //             'image'
                //             , 'video'],
                //     ]
                // }}
                disable={isDisabled}
                readOnly={isDisabled}
                setOptions={{
                    placeholder: readOnly ? '' : t('common.please-input') as string,
                    lang: languagePack,
                    width: '100%',
                    height: 'auto',
                    maxHeight: readOnly ? 'auto' : '500px',
                    /* 图片配置 */
                    imageUploadUrl: '', // 禁用默认上传URL
                    // imageUploadSizeLimit: 1 * 1024 * 1024, // 图片大小限制1MB
                    imageAccept: imageAccept, // 允许的文件类型
                    imageMultipleFile: false,
                    imageUploadHeader: {
                        'Authorization': localStorage.getItem('AMA_USER_TOKEN') as string || '',
                        'Tenant-id': activeCompanyId,
                    },
                    imageUrlInput: false,

                    /* 视频配置 */
                    // videoUploadSizeLimit: 20 * 1024 * 1024, // 图片大小限制20MB
                    videoRatio: 0.4285,
                    videoTagAttrs: { controls: false, loop: true, autoplay: true, }, // loop:true, autoplay:true,
                    // mediaAutoSelect: true,
                    videoRatioList: [
                        { name: t('app.default'), value: 0.4285 },
                    ],
                    // 链接配置
                    linkTargetNewWindow: true,
                    // linkNoPrefix: true,

                    /* toolbar配置 */
                    formats: ['p', 'div', 'blockquote', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'],
                    buttonList: [
                        // default
                        ['undo', 'redo'],
                        ['font', 'fontSize', 'formatBlock'],
                        ['bold', 'underline', 'italic', 'strike', 'subscript', 'superscript'],
                        ['fontColor', 'hiliteColor', 'removeFormat'],
                        ['align', 'list'],//, 'horizontalRule'
                        ['table', 'link',
                            'image', 'video']
                    ],
                    resizingBar: !isDisabled,
                    charCounter: !isDisabled,
                    charCounterLabel: t('info-hub.char-count') as string,
                    maxCharCount: maxCharCount,
                }}
                hideToolbar={isDisabled}
                onImageUploadBefore={onImageUploadBefore}
            />

        </div>
    );
});

const mapStateToProps = (state: any) => ({
    globalLang: state.app.globalLang,
    activeCompanyId: state.app.activeCompanyId,
    themeInfo: state.app.themeInfo,
})
const mapDispatchToProps = (dispatch: any) => ({
})
// export default connect(mapStateToProps, mapDispatchToProps)(Editor);


// const RichTextEditor = forwardRef((props: any, ref: any) => {
//     return <Editor ref={ref}  {...props} />;
// });

// export default connect(mapStateToProps, mapDispatchToProps, null, { forwardRef: true })(RichTextEditor);
export default connect(mapStateToProps, mapDispatchToProps)(Editor);

// export default Editor;