import { connect } from "react-redux";
import { useState, useEffect,useCallback } from "react";
import { Flex, Button, Modal, Input, message } from "antd";
import { useTranslation } from 'react-i18next';
import SvgIcon from '@components/svgicon'; // 根据实际路径引入
import debounce from 'lodash/debounce';
import { api_search_company, api_join_company } from '@/api/company'
import areaDatas from '@/components/form/mobileInput/areaCode.json'
import {RequestFailedMessage} from '@/utils/userRequest'
import './index.scss'
let countryOptions:any = JSON.parse(JSON.stringify(areaDatas))
// console.log('countryOptions',countryOptions)
// 加入 弹窗
const JoinTeamModal = (props: any) => {
    const { visible, themeInfo,globalLang:lang } = props
    const { touchIcon } = themeInfo
    const { t } = useTranslation()
    const [modal, contextHolder] = Modal.useModal();

    const [searchValue, setSearchValue] = useState('')
    const [searchResults, setSearchResults] = useState([])
    const [teamSelected, setTeamSelected] = useState<any[]>([])
    const [isSubmiting, setIsSubmiting] = useState(false) // 是否提交中
    const [submittable, setSubmittable] = useState<boolean>(false);
    const [searchResultNull,setSearchResultNull]=useState(false)

    useEffect(() => {
        setSubmittable(teamSelected.length !== 0)
    }, [teamSelected]);

    // 初始化
    useEffect(() => {
        // try {
        //     // 模拟数据
        //     const data: any = [...searchResults]
        //     for (let i = 0; i < 6; i++) {
        //         data.push({
        //             id: i,
        //             name: 'Eddid vama' + i,
        //             desc: "Eddid Innovation Technology Ltd." + i,
        //             logo: ''
        //         })
        //     }
        //     setSearchResults(data)
        // } catch (error) {

        // }
    }, [])

    const onCancel = () => {
        props.onClose()
    }

    const onApply = async() => {
        if (teamSelected.length===0) return
        // console.log('teamSelected',teamSelected)
        setIsSubmiting(true)
        const res:any = await api_join_company({tenantId: teamSelected[0]}).catch(err=>{
            setIsSubmiting(false)
            console.log(err)
        })
        // console.log('res',res)
        setIsSubmiting(false)
        if (!res) return
        const code = res.code
        if (code===0){
            // 成功
            modal.success({
                width:400,
                title: t('company.apply-submitted'),
                icon: <SvgIcon className="modal-icon" svgName="icon_success" />,
                content: t('company.apply-submitted-tip'),
                okText: t('common.ok2'),
                centered: true,
                className: '',
                closable: true,
                onCancel:()=>{
                    onCancel()
                },
                onOk:()=>{
                    onCancel()
                }
            });
        }else{
            // 失败
            const failedCode = [1002015007] // 该用户已经在该公司内
            if (!failedCode.includes(code)){
                const msg = RequestFailedMessage(res?.msg, code)
                message.error(msg)
                return
            }
            modal.error({
                width:400,
                title: t('company.apply-failed'),
                icon: <SvgIcon className="modal-icon" svgName="icon_error" />,
                content: t('company.apply-failed-reason'),
                okText: t('common.ok2'),
                centered: true,
                className: '',
                closable: true,
                onCancel:()=>{
                    // onCancel()
                },
                onOk:()=>{
                    // onCancel()
                }
            });
        }
    }

    const initSearchResult = ()=>{
        setSearchResults([])
        setSearchResultNull(false)
        setTeamSelected([])
    }

    // 选择公司，单选
    const onToggleSelectTeam = (team: any) => {
        // console.log('teamSelected', teamSelected, team.id)
        if (teamSelected.includes(team.id)) {
            setTeamSelected([])
        } else {
            setTeamSelected([team.id])
        }
    }

    // logo
    const getCompanyLogo = (item:any) => {
        // return logoUserCenterLight
        // const prop = theme === 'dark'? 'darkLogo':'lightLogo'
        const prop = 'touchIcon'
        return item[prop] || touchIcon
    }

    const onChangeSearch =(e:any)=>{
        const value =e.target.value
        setSearchValue(value)
        debouncedOnChange(value)
        setTeamSelected([])
    }

    // 防抖输入框
    const debouncedOnChange = useCallback(
        debounce(async(newValue: string) => {
            // console.log('ssss',newValue)
            if (newValue===''|| newValue ===null || newValue===undefined){
                initSearchResult()
                return
            }
            // 搜索
            const res:any = await api_search_company({name: newValue}).catch(err=>{
                console.log(err)
            })
            console.log("res", res,res && res?.data?.length===0)
            if (!res) return
            setSearchResultNull(res && res?.data?.length===0) // 未有匹配公司
            setSearchResults(res?.data||[])
        }, 200), // 设置延迟
    []);

    // // 防抖输入框
    // const onDebouncedChangeSearch = async(value:any ) => {
    //     console.log(' 防抖value',searchValue)
    //     if (searchValue===''|| searchValue ===null || searchValue===undefined){
    //         // setSearchResults([])
    //         return
    //     }
    //     // // 搜索
    //     // const res:any = await api_search_company({name: value}).catch(err=>{
    //     //     console.log(err)
    //     // })
    //     // console.log("res", res)
    //     // if (!res) return
    //     // setSearchResults(res?.data||[])
    // }

    // const searchRequest = ()=>{
        
    // }

    // const onClearSearch =()=>{
    //     setSearchValue('')
    // }
    
    const getAreaName = (code:any)=>{
        const target:any = countryOptions.filter((i:any) => i.value===code)
        return target.length>0? target[0][`name_${lang}`] : ''
    }

    return (
        <div>
            <Modal
                className={"join-team-modal no-padding"}
                title={t('company.join-title')}
                open={visible}
                onCancel={onCancel}
                maskClosable={false}
                centered={true}
                width={400}
                footer={
                    <Flex className="modal-bottom" justify={"flex-end"} align="center" gap={12}>
                        <Button color="default" variant="filled" onClick={() => onCancel()}>{t("app.cancel")}</Button>
                        <Button type="primary" onClick={() => onApply()} disabled={!submittable || isSubmiting}>{t('company.apply')}</Button>
                    </Flex>
                }
            >
                <Flex vertical>
                    <div className="modal-tip">{t('company.join-tip')}</div>
                    <Flex className="modal-middle" vertical>
                        <div className="search-input">
                            <Input className="search" value={searchValue} onChange={onChangeSearch} placeholder={t('search') as string} variant="filled" 
                                allowClear={{clearIcon:<SvgIcon svgName="icon_input_clear"/>}}
                                suffix={searchValue===''?<SvgIcon svgName="icon_search"
                                className={"icon_input_search"}/>:null} />
                            </div>
                        <Flex className="result-area" vertical gap={4}>
                            {
                                // 搜索结果不为空
                                searchResults.length > 0 ?
                                    searchResults.map((item: any, index: number) => {
                                        return (
                                            <Flex onClick={() => onToggleSelectTeam(item)} className="result-item" key={index} gap={8} align="center">
                                                <img className="team-logo" src={getCompanyLogo(item)} alt=""></img>
                                                <div className="team-name">
                                                    <div>{item.companyName}</div>
                                                    <div>{getAreaName(item.regionCode)}</div>
                                                </div>
                                                {
                                                    teamSelected.includes(item?.id) ? <SvgIcon className="team-selected" svgName="icon_check" /> : null
                                                }
                                            </Flex>
                                        )
                                    })
                                    :
                                    // 搜索结果为空
                                    <Flex className="no-result" vertical gap={8} align="center" justify="center">
                                        <div>{t('company.no-data')}</div>
                                        {
                                            searchResultNull?
                                            <div>{t('company.search-no-result')}</div>:
                                            <div>{t('common.please-search')}</div>
                                        }
                                    </Flex>
                            }
                        </Flex>
                    </Flex>
                </Flex>
            </Modal>
            {contextHolder}
        </div>
    )
}

const mapStateToProps = (state: any) => ({
    globalLang: state.app.globalLang,
    themeInfo: state.app.themeInfo
})
const mapDispatchToProps = (dispatch: any) => ({
})
export default connect(mapStateToProps, mapDispatchToProps)(JoinTeamModal);