.loading-animation{
    width: 24px;
    height:24px;
    &.image{
      width: 500px;
      height:500px;
      background: var(--colorFillSecondary);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      svg{
        width: 60px!important;
        height: 60px!important;
      }
    }
}
  
.error-color {
  color: var(--colorError);
}

.pointer{
  cursor: pointer;
}

a{
  color: var(--colorPrimary);
  &:hover{
    color: var(--colorPrimary);
  }
}

.privacy-term{
  color: var(--colorTextTertiary);
  font-size: 12px;
  line-height: 18px;
  a{
    text-decoration: underline;
    color: var(--colorText);
  }
}

.modal-privacy-content{
  margin-top: 4px;
  line-height: 22px;
  color: var(--colorText);
  a{
      text-decoration: underline;
      color: var(--colorText);
  }
}

/* 隐藏数字输入框的上下箭头 */
input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type="number"] {
  -moz-appearance: textfield;
}

// 
.common-wrap{
  width: 100%;
  height: 100%;
  background: var(--colorBgBase);
  $space: 0 40px;
  .wrap-nav{
    width: 100%;
      height: var(--page-nav-height);
      flex-shrink: 0;
      padding: 0 20px;
      .wn-title{
          font-weight: 600;
          font-size: 18px;
          color: var(--colorText);
          line-height: 24px;
      }
      .wn-btns{
          margin-left: auto;
      }
  }
  .wrap-filters{
      padding: $space;
      margin-top: 20px;
      flex-shrink: 0;
      .filter-input{
          width: 300px;
      }
      .ant-select{
          width: 150px;
      }
  }
  .wrap-content{
      flex: 1;
      padding: $space;
      overflow-x: auto;
      min-width: 1000px;
      padding-top: 20px;
  }
}

// 公共卡片样式
.common-card {
  width: 380px;
  background: var(--colorBgContainer);
  border-radius: 10px;
  padding: 15px;
  cursor: pointer;
  &:not(.no-hover):hover {
      box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
  }
  .card-title {
      font-weight: 600;
      font-size: 14px;
      color: var(--colorText);
      line-height: 22px;
  }
}
.padding-bottom24 {
  padding-bottom: 24px;
}

// 区域加载中
.full-spin{
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}