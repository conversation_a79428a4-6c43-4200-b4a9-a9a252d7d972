import { connect } from "react-redux";
import {  Input } from "antd";
import { useTranslation } from 'react-i18next';
import { useState, useRef, useEffect, useCallback, forwardRef } from "react";
import './index.scss'
import Svgicon from "@/components/svgicon";
import debounce from 'lodash/debounce';

const InputComponent = forwardRef((props: any, ref) => {
    const { t } = useTranslation();
    const [value, setValue] = useState('')

    const emitChange=(newValue:any)=>{
        props.onChange(newValue)
    }

    const debouncedOnChange = useCallback(
        debounce((newValue: string) => {
        emitChange(newValue);
        }, 200), // 设置延迟
    []);
    

    const onChange = (e: any) => {
        const newValue = e.target.value;
        setValue(newValue)
        debouncedOnChange(newValue)
    }

    const onClear = () => {
        setValue("")
    }
    return (
        <Input className="keyword-input" value={value} onChange={onChange} onClear={onClear}
            allowClear placeholder={t('common.search-keyword') as string} size="large" prefix={<Svgicon svgName="search" />}></Input>
    );
});
const mapStateToProps = (state: any) => ({
    themeInfo: state.app.themeInfo
});
// 使用 connect 连接 Redux store，并启用 forwardRef
const ConnectedCounter = connect(mapStateToProps, null, null, { forwardRef: true })(InputComponent);
export default ConnectedCounter;
