
import { Flex, Button } from 'antd'
import i18next from "i18next";
import Svgicon from '@components/svgicon'
import './common.scss'

const TermsConditions = () => {
    return (
        <div className='legal-document'>
            <Flex className='ld-title' justify="center" align="center" style={{ padding: '20px' }}><h3>{i18next.t("login.terms-conditions")}</h3></Flex>
            {/* <Flex vertical justify={"flex-start"}>
                <h3>1. Primary - solid</h3>
                <Flex gap={10} style={{ margin: "10px" }} align="flex-start" wrap>
                    <Button disabled={false} type="primary">Button Text</Button>
                    <Button disabled={false} size="small" type="primary">Button Text</Button>
                    <Button disabled={true} size="large" type="primary">Button Text</Button>
                    <Button disabled={false} icon={<Svgicon svgName="icon_save" />} type="primary">Button Text</Button>
                </Flex>
                <h3>2. Secondary - filled</h3>
                <Flex gap={10} style={{ margin: "10px" }} wrap>
                    <Button color="default" variant="filled">Button Text</Button>
                    <Button color="default" size="small" variant="filled">Button Text</Button>
                    <Button color="default" disabled={true} size="large" variant="filled">Button Text</Button>
                    <Button color="default" disabled={true} icon={<Svgicon svgName="icon_save" />} variant="filled">Button Text</Button>
                </Flex>
                <h3>3. Tertiary - filled</h3>
                <Flex gap={10} style={{ margin: "10px" }} wrap>
                    <Button className="tertiary" color="default" variant="filled">Button Text</Button>
                    <Button className="tertiary" color="default" size="small" variant="filled">Button Text</Button>
                    <Button className="tertiary" color="default" disabled={true} size="large" variant="filled">Button Text</Button>
                    <Button className="tertiary" color="default" disabled={true} icon={<Svgicon svgName="icon_save" />} variant="filled">Button Text</Button>
                </Flex>
                <h3>4. Quaternary - filled </h3>
                <Flex gap={10} style={{ margin: "10px" }} wrap>
                    <Button className="quaternary" color="default" variant="filled">Button Text</Button>
                    <Button className="quaternary" color="default" size="small" variant="filled">Button Text</Button>
                    <Button className="quaternary" color="default" disabled={true} size="large" variant="filled">Button Text</Button>
                    <Button className="quaternary" color="default" disabled={true} icon={<Svgicon svgName="icon_save" />} variant="filled">Button Text</Button>
                </Flex>

                <h3>5. Danger - Primary solid </h3>
                <Flex gap={10} style={{ margin: "10px" }} wrap>
                    <Button color="danger" variant="solid">Button Text</Button>
                    <Button color="danger" variant="solid" size="small">Button Text</Button>
                    <Button color="danger" variant="solid" disabled={true} size="large">Button Text</Button>
                    <Button color="danger" variant="solid" disabled={true} icon={<Svgicon svgName="icon_save" />}>Button Text</Button>
                </Flex>

                <h3>6.Danger - Secondary filled </h3>
                <Flex gap={10} style={{ margin: "10px" }} wrap>
                    <Button color="danger" variant="filled">Button Text</Button>
                    <Button color="danger" size="small" variant="filled">Button Text</Button>
                    <Button color="danger" disabled={true} size="large" variant="filled">Button Text</Button>
                    <Button color="danger" disabled={true} icon={<Svgicon svgName="icon_save" />} variant="filled">Button Text</Button>
                </Flex>
            </Flex> */}
        </div>
    )
}
export default TermsConditions