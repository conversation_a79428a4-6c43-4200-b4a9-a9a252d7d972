@import "../../../styles/mixin.scss";
.new-contact-area {
    .area-title {
        margin-bottom: 0;
    }
    .list-con {
        padding: 16px;
        flex: 1;
        overflow: auto;
        .list {
            width: 100%;
        }
        .list-item{
            padding: 6px 8px;
        }
        .li-user-info {
            flex: 1;
            overflow: hidden;
            .li-avatar {
                flex-shrink: 0;
            }
            .li-user {
                flex: 1;
                overflow: hidden;
                > div {
                    max-width: 100%;
                    &:first-child {
                        @include font(14px, 22px, 500);
                        @include ellipsis();
                    }
                    &:last-child {
                        @include font(12px, 18px, 400, var(--colorTextSecondary));
                        @include ellipsis-multiline();
                    }
                }
            }
        }
        .li-status {
            margin-left: auto;
            flex-shrink: 0;
            @include font(14px, 22px, 400);
        }
        .send-status{
            .svg-icon{
                color: var(--colorText);
                font-size: 16px;
            }
        }
    }
}
