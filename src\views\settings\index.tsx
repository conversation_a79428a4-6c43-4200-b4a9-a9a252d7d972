
import { connect } from "react-redux";
import { useState, useRef, useEffect, forwardRef, useImperativeHandle, createContext } from "react";
import { Space, Flex, Button, Modal, Checkbox, Form, Input, Select, Menu } from "antd";
import Svgicon from "@/components/svgicon";
import MyAccount from "./MyAccount";
import Profiles from "./Profiles";
import Feedback from "./Feedback";
import Keybinds from "./Keybinds";
import General from "./General";

import type { MenuProps } from 'antd';
import "./index.scss"
import { CountdownProvider } from '@/components/form/verifyCode/CountdownProvider';
import { t } from "i18next";
import { api_get_my_profile } from '@/api/setting'

type MenuItem = Required<MenuProps>['items'][number];

const defaultSelectedKey = 'account'

// 设置页面
const Settings = (props: any) => {
    const { onClose } = props
    const [visible, setVisible] = useState(true)
    const [activeMenu, setActiveMenu] = useState(defaultSelectedKey)

    // 左侧菜单选项
    const menuItems: MenuItem[] = [
        {
            key: '1',
            label: 'setting.user-setting',
            type: 'group',
            children: [
                { key: 'account', label: 'setting.my-account', icon: <Svgicon svgName="icon_setting1" /> },
                { key: 'profiles', label: 'setting.profiles', icon: <Svgicon svgName="icon_setting2" /> },
            ]
        },
        {
            key: '2',
            label: 'setting.system-setting',
            type: 'group',
            children: [
                { key: 'general', label: 'setting.general', icon: <Svgicon svgName="icon_setting3" /> },
                { key: 'keybinds', label: 'setting.keybinds', icon: <Svgicon svgName="icon_setting4" /> },
            ]
        },
        {
            key: '3',
            label: 'setting.other',
            type: 'group',
            children: [
                { key: 'feedback', label: 'setting.feedback', icon: <Svgicon svgName="icon_setting5" /> },
            ]
        }
    ]

    useEffect(() => {
        setVisible(true)
    }, [])

    // 关闭
    const onCloseModal = () => {
        setVisible(false)
        if (onClose) onClose()
    }
    // 菜单点击
    const onClickMenu = ({ key }: any) => {
        // console.log('111', key)
        setActiveMenu(key)
    }

    return (
        <CountdownProvider>
            <Modal
                className={"settings-modal no-padding border"}
                title={t('setting.title')}
                open={visible}
                maskClosable={false}
                onCancel={onCloseModal}
                centered={true}
                width={960}
                footer={null}
            >
                <Flex className="settings-content">
                    <Menu
                        onClick={onClickMenu}
                        style={{ width: 256 }}
                        defaultSelectedKeys={[defaultSelectedKey]}
                        defaultOpenKeys={['1']}
                        mode="inline"
                        inlineIndent={8}
                        items={menuItems.map((i: any) => {
                            i.label = t(i.label)
                            if (i.children) {
                                i.children = i.children.map((ci: any) => {
                                    ci.label = t(ci.label)
                                    return ci
                                })
                            }
                            return i
                        })}
                    />
                    <div className="settings-ringht-wrapper">
                        {
                            activeMenu === 'account' ? <MyAccount /> : null
                        }
                        {
                            activeMenu === 'profiles' ? <Profiles /> : null
                        }
                        {
                            activeMenu === 'feedback' ? <Feedback /> : null
                        }
                        {
                            activeMenu === 'keybinds' ? <Keybinds /> : null
                        }
                        {
                            activeMenu === 'general' ? <General /> : null
                        }
                    </div>
                </Flex>
            </Modal>
        </CountdownProvider>
    )
}
export default Settings 