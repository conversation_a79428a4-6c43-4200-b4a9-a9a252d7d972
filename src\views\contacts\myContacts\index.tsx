import { connect } from "react-redux";
import React, { useState, useRef, useEffect } from "react";
import { Flex, Anchor, Empty, Spin } from 'antd'
import { useTranslation } from 'react-i18next';
import { groupByInitial } from '@components/avatar/index';
import UserAvatar from "@/components/userAvatar";
import { useOpenIM } from '@/contexts/OpenIMContext';
import UserInfoCard from '@/components/userInfoCard';
import './index.scss'
import { useNavigate } from "react-router-dom";
import { api_all_users_profile } from '@/api/chat'

// 我的好友
const MyContacts = (props: any) => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const { service: ImService, isConnected: isConnectedIm } = useOpenIM();
    const { activeCompanyId } = props

    const [isLoading, setIsLoading] = useState<any>(null)
    const [userDatas, setUserDatas] = useState<any>([]) // 所有用户数据
    const [sortedUserGroups, setSortedUserGroups] = useState<any>([]) // 根据首字母排序的用户数据
    const [anchorItems, setAnchorItems] = useState<any>(null) // 字母锚点导航数据
    const [usersInfoMap, setUsersInfoMap] = useState<any>({}) // 已有的用户信息映射

    useEffect(() => {
        query()

    }, [])

    const query = async () => {
        setIsLoading(true)
        // 获取用户信息Map
        const _usersInfoMap = await getAllUsersProfile()
        let list = await ImService.getFriendList().catch(() => {
            setIsLoading(false)
        })
        console.log('getFriendList', list)
        setIsLoading(false)
        if (!list) return
        const _datas = list.map((i: any) => {
            const target =  _usersInfoMap[i.userID]
            i.name = target?.userName || i.nickname || ''
            i.avatar = target?.avatar
            return i
        })

        // const _datas =[]
        // const names = [
        //     '张晓霞', '黄西斯', '黄呃呃', '啊啊啊', 'Adrs', '叶问', 'cff', '谢佳佳', '高老师', '郑老师', 'Christopher Carter',
        //     'aa fasf', 'asasfas d','cc fa', 'uurqt','ww as','试试f','yy ui','ii o','pp dfg','tts s','ee tt','ww r','dd as','ff as','ga sd',
        //     'bobs as','HS SD','kats sd','marry ssd', 'NUO SJ','oearta','rafas sd','vaasf','zsdfasf','qsdf fd'
        // ]
        // for (let i = 0; i < names.length; i++) {
        //     _datas.push(
        //         {
        //             "id": i,
        //             "name": names[i],
        //             "type": "user",
        //         })
        // }

        // console.log('_datas', _datas)
        setUserDatas(_datas)
        const groupedUsers = groupByInitial(_datas);
        const sortedGroups = sortGroups(groupedUsers);
        console.log('sortedGroups', sortedGroups)
        setSortedUserGroups(sortedGroups)
        InitAnchorItems(sortedGroups)
    }

    // 获取用户信息Map
    async function getAllUsersProfile() {
        if (Object.keys(usersInfoMap).length > 0) return usersInfoMap
        const res: any = await api_all_users_profile({ currentTenantId: activeCompanyId }).catch((err: any) => {
            return usersInfoMap
        })
        if (!res) return usersInfoMap
        const infoMap = { ...res }
        setUsersInfoMap(infoMap)
        // console.log('infoMap', infoMap)
        return infoMap
    }

    const InitAnchorItems = (groups: any) => {
        const _items = Object.keys(groups).map((char: any) => {
            return {
                key: 'ContactsInitial' + char,
                href: '#ContactsInitial' + char,
                title: char,
            }
        })
        // console.log(_items)
        setAnchorItems(_items)
    }

    // 对分组进行排序
    const sortGroups = (grouped: any) => {
        const sortedKeys = Object.keys(grouped).sort();
        const sortedGroups: any = {};

        sortedKeys.forEach(key => {
            sortedGroups[key] = grouped[key].sort((a: any, b: any) =>
                a.name.localeCompare(b.name)
            );
        });

        return sortedGroups;
    };

    // 发送信息给用户
    const onSendMessageToUser = (userID: any) => {
        // 跳转至聊天，携带用户ID 参数
        //  console.log('userID',userID)
        if (userID) navigate("/chats", { state: { userID } });
    }

    return (
        <Flex className='my-contact-area contacts-area' vertical>
            <div className="area-title">{t('contacts.my-contacts')}</div>
            <Flex className="user-list-con" id="my-contacts-scroll" gap={4}>
                {
                    isLoading ?
                        <Spin className="full-spin" size="large" />
                        :
                        <Flex className="user-list" vertical gap={12}>
                            {Object.entries(sortedUserGroups).map(([initial, users]: any) => (
                                <Flex key={initial} className="user-group" vertical gap={12}>
                                    <div id={`ContactsInitial` + initial} className="initial-header">{initial}</div>
                                    <Flex className="user-items" vertical gap={16}>
                                        {users.map((user: any, index: number) => (
                                            <Flex key={user.userID + '' + index} className="user-item">
                                                <UserInfoCard onSendMessageToUser={onSendMessageToUser} placement="right"
                                                    userInfo={{ userID: user.userID, userName: user.name }} popupContainer={document.body}>
                                                    <Flex key={user.userID} className="user-item-detail" gap={8} align="center">
                                                        <UserAvatar className="ui-avatar" name={user.name} src={user?.avatar} userId={user?.userID} />
                                                        <div className="ui-name">{user.name}</div>
                                                    </Flex>
                                                </UserInfoCard>
                                            </Flex>
                                        ))}
                                    </Flex>
                                </Flex>
                            ))}
                        </Flex>
                }

                {
                    isLoading !== null && isLoading === false && (!anchorItems || anchorItems.length === 0) ?
                        <Empty className="page-empty full" image={null} description={
                            <>
                                <div>{t('common.no-data')}</div>
                                <div></div>
                            </>}
                        /> : null
                }
                {
                    anchorItems && anchorItems.length > 0 ?
                        <div className="anchor">
                            <Anchor replace
                                offsetTop={16}
                                getContainer={() => document.getElementById('my-contacts-scroll') as HTMLAnchorElement}
                                items={anchorItems}
                            />
                        </div>
                        : null}
            </Flex>
        </Flex>
    )
}
const mapStateToProps = (state: any) => ({
    activeCompanyId: state.app.activeCompanyId,
})
const mapDispatchToProps = (dispatch: any) => ({
})
const ConnectedCounter = connect(mapStateToProps, mapDispatchToProps, null, { forwardRef: true })(MyContacts);
export default ConnectedCounter;