import React, { forwardRef } from "react";
import type { CSSProperties } from 'react';
import { connect } from "react-redux";
import i18next from "i18next";
import classNames from "classnames";
import Svgicon from "@/components/svgicon"
import {
    Flex, Button, Form, Input, Collapse, Cascader, Slider, InputNumber,
    Select, Switch, Modal, message, Tooltip
} from "antd";
import type { CollapseProps } from 'antd';
// import RobotDebug from "./debug/index";
import { $trim } from '@/utils/common'
import { getCSSVariablesValue } from '@/theme'
import UploadImage from "@/views/robot-manage/component/uploadImage";

import './styles/llmModel.scss'
import './styles/index.scss'
import '../common/style/index.scss'

import { api_update_robot,api_get_admin_robot_llmmodel} from "@/api/robotManage2";

const { TextArea } = Input;

interface ContextState {
    robotId: any,
    robotName: any,
    robotLogo: any,
    robotInfo: any,
    settingForm: any,
    isSubmiting: boolean,
    llmSearchValue1: any,
    llmSearchValue2: any,
    isDirty: boolean, // 用于跟踪表单是否已修改
    robotLlmModelList: any[]
}

// 接口响应字段对应表
const fieldMap:any = {
    robotId: "robot_id",
    robotIdentityPrompt: "identity_prompt",
    robotIntroduction: "introduction_message",
    robotLlmModel: "default_LLM",
    escalateLlmModel: "enhance_LLM",
    robotLogo: "logo",
    robotName: "name",
    // embeddingModel: "",
    robotWelcomeMsg: "welcome_message",
    konwledgeCount: "knowledge_count",
    relevance: "relevance_score",
    intelligence: "temperature",
    isIrrelevantQuestionsBoolean: "answer_irrelevant_question",
    irrelevantRespond: 'irrelevant_respond',
    // maxResponseLength: "",
    showKnowledge: "can_show_citations",
    enableConversationMemory: "enable_chat_memory"
}

// 基本信息-设置和调试
class RobotSettingBasicInfo extends React.Component<any, ContextState> {

    // 创建表单引用
    private formRef: React.LegacyRef<HTMLDivElement> | any;
    // private robotDebug: React.LegacyRef<HTMLDivElement> | any;
    constructor(props: any) {
        super(props);
        this.formRef = React.createRef();
        // this.robotDebug = React.createRef();
        this.state = {
            robotId: null,
            robotName: "",
            robotLogo: null, //robot图标
            robotInfo: {}, // 机器人信息
            robotLlmModelList: [],// llm数据
            // 设置表单字段
            settingForm: {
                robotId: "",
                robotIdentityPrompt: "",
                robotIntroduction: "",
                robotLlmModel: [],
                escalateLlmModel: [],
                robotLogo: "",
                robotName: "",
                // robotType: "",
                // embeddingModel: "",
                robotWelcomeMsg: "",
                konwledgeCount: "",
                relevance:"", // 知识相关性
                intelligence: "",
                isIrrelevantQuestionsBoolean: false,
                irrelevantRespond:'',
                // maxResponseLength: 2048,
                showKnowledge: true,
                enableConversationMemory: false
            },
            isSubmiting: false,
            llmSearchValue1: '',
            llmSearchValue2: '',
            isDirty: false // 用于跟踪表单是否已修改
        }
    };
    
    // 初始化
    emit_init(robotInfo: any,noRefresh?:boolean) {
        // console.log('init robotInfo', robotInfo)
        if (robotInfo) {
            let info:any = {}
            Object.keys(this.state.settingForm).forEach((key:any)=>{
                let value = robotInfo[fieldMap[key]]
                info[key]=value
                // delete robotInfo[fieldMap[key]]
            })
            // info = {...robotInfo,...info}
            console.log('info',info)
            this.formRef.current.setFieldsValue(info)
            this.setState({
                settingForm: info,
                robotInfo: {...info} 
            })
            // if (!noRefresh) this.robotDebug?.current?.emitInit(robotInfo)
        }
    }

    on_form_is_dirty(){
        return this.state.isDirty
    }

    // nav 操作按钮
    renderNavButton=(isLoading?:boolean)=>{
        // if (!this.props.permissionMap.robotManageVectorRobotBtn) return null // 权限控制
        return <Button  icon={<Svgicon svgName="save" />} type="primary"
        onClick={() => this.onSave().catch(err=>console.log(err))} disabled={isLoading||this.state.isSubmiting}>{i18next.t('robot-manage.save-changes')}</Button>
    }

    // 处理表单字段变化
    handleFieldChange = (changedFields: any) => {
        try {
            // console.log('changedFieldschangedFieldschangedFields',  changedFields)
            const { settingForm, robotInfo } = this.state
            const initialValues = JSON.stringify(robotInfo)
            const trimFields = ['robotName'] // 需要 去除前后空字符串的字段
            Object.keys(changedFields).forEach((field: any) => {
                let value = changedFields[field]
                if (trimFields.includes(field) && value !== undefined) {
                    const trimValue = $trim(value)
                    value = trimValue
                }
                settingForm[field] = value
                // this.formRef.current.setFieldsValue({
                //     [field]: value
                // });
            })
            const currentValues = JSON.stringify(settingForm)
            const hasChanges = initialValues!==currentValues
            this.setState({isDirty: hasChanges})
            // console.log('hasChanges',hasChanges)
            // 更新调试页面 机器人信息
            // this.robotDebug?.current?.emitUpdateRobotInfo({
            //     robotName:settingForm.robotName,
            //     robotLogo: settingForm.robotLogo,
            //     robotWelcomeMsg: settingForm.robotWelcomeMsg,
            //     robotIntroduction: settingForm.robotIntroduction})
            // this.setState({ settingForm: settingForm })
        } catch (error) {
            console.log('error', error)
        }
    };

    // 表单必填校验
    formRequiredValidator = ({ }) => ({
        validator(_: any, value: any) {
            const _value = $trim(value)
            if (_value != '' && _value != null && _value != undefined) {
                return Promise.resolve();
            }
            return Promise.reject(new Error(i18next.t('common.input-required') as string));
        },
    })

    // Cascader 置灰选项，点击提示信息
    handleCascaderConainerClick(disabled?: boolean | undefined) {
        if (disabled) {
            message.open({ type: "error", content: i18next.t(["robot-manage.llm-disabled-tip"]) })
        }
    }

    // 切换AI 默认模型
    handleChangeRobotLlmModel = (value: any, selectedOptions: any) => {
        const { settingForm } = this.state;
        let robotLlmModel = value;
        this.setState({ settingForm: { ...settingForm, robotLlmModel } ,llmSearchValue1:''})
    }

    // 切换AI 增强模型
    handleChangeEscalateLlmModel = (value: any, selectedOptions: any) => {
        const { settingForm } = this.state;
        let escalateLlmModel = value;
        this.setState({ settingForm: { ...settingForm, escalateLlmModel },llmSearchValue2:'' })
    }

    // 更新表单字段值
    handleChangeFormFieldValue = (propName: any, propValue: any) => {
        this.formRef.current.setFieldsValue({
            [propName]: propValue
        })
        // if (propName =='robotLogo'){
        //     // 更新调试页面 机器人信息
        //     this.robotDebug?.current?.emitUpdateRobotInfo({robotLogo:propValue})
        // }
    }
    
    changeSearchValue1 =(e:any)=>{
        this.setState({llmSearchValue1:e.target.value})
    }
    changeSearchValue2 =(e:any)=>{
        this.setState({llmSearchValue2:e.target.value})
    }
    handleRobotLlmModelFocus =()=>{
        this.setState({llmSearchValue1:''})
    }
    handleEscalatelmModelFocus=()=>{
        this.setState({llmSearchValue2:''})
    }

    // 保存修改
    onSave = () => {
        return new Promise((resolve, reject) => {
            this.formRef?.current?.validateFields().then(async (values:any) => {
                const { settingForm } = this.state;
                // console.log('settingForm', settingForm)
                // console.log('values', values)
                let values_final:any = {...settingForm,...values} // 目的处理折叠面板的表单字段值
                const params:any = {}
                Object.keys(fieldMap).forEach((key:any)=>{
                    let value = values_final[key]
                    params[fieldMap[key]] = value
                })
                const data = { ...params, seq: 0}

                console.log('data', data)
                // return
                this.setState({ isSubmiting: true })
                const res = await api_update_robot(data).catch(() => {
                    this.setState({ isSubmiting: false })
                })
                // console.log('res', res)
                if (!res) return resolve(false)
                this.setState({ isSubmiting: false,isDirty: false })
                resolve(true)
                message.success(i18next.t('common.update-success'))
                this.props.onQueryRobotInfo() // 通知父组件获取最新机器人信息
            }).catch((errorInfo: any) => {
                reject(errorInfo)
                console.log('errorInfo', errorInfo)
            })
        })
    }

    // 获取llm数据
    getLlm = async()=>{
        const res:any = await api_get_admin_robot_llmmodel().catch(()=>{
        })
        if (!res) return
        const llmModelList = res?.llmModelList || []
        this.setState({robotLlmModelList: llmModelList})
    }

    componentDidMount() {
        this.getLlm()
        window.addEventListener('beforeunload', this.handleBeforeUnload);
        // 添加一个新的历史记录
        // window.history.pushState(null, '', window.location.href);
        // 监听 popstate 事件
        // window.addEventListener('popstate', this.handlePopState);
    }

    componentWillUnmount() {
        window.removeEventListener('beforeunload', this.handleBeforeUnload);
        
        // window.removeEventListener('popstate', this.handlePopState);
    }

    // 监听浏览器刷新事件处理
    handleBeforeUnload = (event:any) => {
        if (this.state.isDirty) {
            // console.log('表单修改了')
            event.preventDefault();
            event.returnValue = '您有未保存的更改，确定要离开吗？'; // 显示默认的提示消息
        }
    };

    // 监听浏览器前进或后退事件处理
    handlePopState = (event:any) => {
        console.log('Navigated to:', window.location.pathname);
        // 处理前进或后退事件
        event.preventDefault()
        if (this.state.isDirty) {

            // window.history.replaceState(null, '', window.location.href);
            if (window.confirm('Are you sure you want to go back?')) {
                // 允许后退
            } else {
                // 阻止后退
                // window.history.pushState(null, '', `/admin/robotSetting?pid=basicinfo&robotid=${this.state.settingForm.robotId}`);
                // 如果用户取消，阻止导航并恢复历史记录
                window.history.replaceState(null, '', window.location.href);
            }
        }
        
    };

    renderFiledLabelTooltip(label: any, title: any, placement?: any) {
        return <Flex align={"center"} gap={4}>
            <span>{label}</span>
            <Tooltip placement={placement ? placement : "topLeft"} className="field-tooltip" title={title} arrow={false}>
                <span className="field-tooltip-trigger"><Svgicon svgName="info" /></span>
            </Tooltip>
        </Flex>
    }

    // 设置表单集合
    cardFormItems = (panelStyle: CSSProperties): CollapseProps['items'] => {
        const { text } = this.props; // 从 props 中获取 text
        return [
            {// 基本信息
                key: '1',
                label: (
                    <Flex className="card-title" align={"center"}>
                        <Svgicon svgName="basicinfo" />
                        <span>{i18next.t('robot-manage.basic-info')}</span>
                    </Flex>
                ),
                children: this.renderBasicInfo(),
                style: panelStyle,
            },
            {//AI模型
                key: '2',
                label: (
                    <Flex className="card-title" align={"center"}>
                        <Svgicon svgName="ai" />
                        <span>{i18next.t('robot-manage.ai-model')}</span>
                    </Flex>
                ),
                children: this.renderAIModel(),
                style: panelStyle,
            },
            {// 標準問候語
                key: '3',
                label: (
                    <Flex className="card-title" align={"center"}>
                        <Svgicon svgName="welcome" />
                        <span>{i18next.t('robot-manage.create-robot.is-irrelevant-questions')}</span>
                    </Flex>
                ),
                children: this.renderWelcome(),
                style: panelStyle,
            },
            {// 知識庫
                key: '4',
                label: (
                    <Flex className="card-title" align={"center"}>
                        <Svgicon svgName="knowledge" />
                        <span>{i18next.t('robot-manage.create-robot.knowlege-document-count')}</span>
                    </Flex>
                ),
                children: this.renderKnowledge(),
                style: panelStyle,
            },
            { //記憶
                key: '5',
                label: (
                    <Flex className="card-title" align={"center"}>
                        <Svgicon svgName="memory" />
                        <span>{i18next.t('robot-manage.create-robot.enable-conversation-memory')}</span>
                    </Flex>
                ),
                children: this.renderMemory(),
                style: panelStyle,
            }
        ];
    };

    // 基本信息表单
    renderBasicInfo() {
        const { settingForm } = this.state
        return <div className="card-content">
            {/* 机器人名称 */}
            <Form.Item className="single-row"  name="robotName" label={i18next.t('robot-manage.create-robot.robot-name')} rules={[this.formRequiredValidator]}>
                <Input 
                // value={settingForm.robotName}
                 variant="filled" maxLength={40} placeholder={i18next.t('robot-manage.create-robot.robot-name-hint') as string} />
            </Form.Item>
            {/* 机器人头像 */}
            <Form.Item name="robotLogo" label={i18next.t("robot-manage.create-robot.robot-logo")}>
                <UploadImage 
                value={settingForm.robotLogo} 
                onUpdate={(e: any) => this.handleChangeFormFieldValue('robotLogo', e)} 
                showTriggerText={true} isSquare={true} />
            </Form.Item>
            {/* 机器人简介 */}
            <Form.Item name="robotIntroduction" label={i18next.t("robot-manage.create-robot.robot-introduction")}>
                <TextArea
                    name="robotIntroduction"
                    // value={settingForm.robotIntroduction}
                    placeholder={i18next.t("robot-manage.create-robot.robot-introduction-hint2") as string}
                    autoSize={{ minRows: 4, maxRows: 8 }} showCount
                    variant="filled" maxLength={500} />
            </Form.Item>
            {/* 身份描述 */}
            <Form.Item name="robotIdentityPrompt" label={
                this.renderFiledLabelTooltip(i18next.t("robot-manage.create-robot.robot-identity-prompt"), 
                i18next.t("robot-manage.create-robot.robot-identity-prompt-notice"))}>
                <TextArea
                    // value={settingForm.robotIdentityPrompt}
                    placeholder={i18next.t("robot-manage.create-robot.robot-identity-prompt-hint") as string}
                    autoSize={{ minRows: 4, maxRows: 8 }} variant="filled" />
            </Form.Item>
        </div>
    }

    // AI模型表单
    renderAIModel() {
        const { settingForm,llmSearchValue1,llmSearchValue2,robotLlmModelList } = this.state
        // const { robotLlmModel, themeInfo } = this.props;

        let llmModelOptions: any[] = [];
        for (let key in robotLlmModelList) {
            let categoryModel = robotLlmModelList[key];
            let category = categoryModel["Category"];
            let modelList = categoryModel["ModelList"];

            let childrenList = [];
            for (let key1 in modelList) {
                let model = modelList[key1];
                let llmId = model.llmId;
                let name = model.name;
                const image_output= model.image_output
                const text_output= model.text_output
                const image = model.image
                let disabled = false;

                if (model.enabled === false) {
                    disabled = true;
                }

                childrenList.push({
                    label: name,
                    value: llmId,
                    disabled: disabled,
                    image: image,
                    image_output,
                    text_output
                })
            }

            let Level1 = {
                label: category,
                value: category,
                children: childrenList
            }

            llmModelOptions.push(Level1);
        }

        // 自定义Cascader组件选项，显示图标
        const OptionRender: React.FC<{ label: string, value: string | number, image: any, disabled?: boolean, image_output: any, text_output: any }> = ({ label, value, image, disabled,text_output,image_output }) => {
            const tags = []
            if(text_output){
                tags.push(i18next.t('text-generation'))
            }
            if(image_output){
                tags.push(i18next.t('image-generation'))
            }
            return (
                <Flex gap={10} className="custom-cascader-option" onClick={() => this.handleCascaderConainerClick(disabled)}>
                    {
                        image ? <img className="cascader-option-image" src={image} /> : null
                    }
                    <Flex className="cascader-option-texts" vertical gap={4}>
                        <span className="cascader-option-label">{label}</span>
                        { 
                            tags.length>0 ?
                            <div className="cascader-option-tag">{tags.join("、")}</div>
                            :null
                        }
                    </Flex>
                </Flex>
            );
        };
        const dropdownRender1 = (menus: React.ReactNode) => (
            <div className="cascader-custom-search" style={{width:'100%'}}>
              <Input className="cascader-search-input custom-input" variant="filled" value={llmSearchValue1} onChange={this.changeSearchValue1} placeholder={i18next.t("search") as string}/>
              {menus}
            </div>
        );

        const dropdownRender2 = (menus: React.ReactNode) => (
            <div className="cascader-custom-search" style={{width:'100%'}}>
              <Input className="cascader-search-input custom-input" variant="filled" value={llmSearchValue2} onChange={this.changeSearchValue2} placeholder={i18next.t("search") as string}/>
              {menus}
            </div>
        );
        const { SHOW_CHILD } = Cascader;
        
        return <div className="card-content">
            <Flex gap={20}>
                {/* AI模型 (默認) */}
                <Form.Item className="half-width"  name="robotLlmModel" label={i18next.t(["robot-manage.create-robot.llm-model"])}>
                    <Cascader 
                        className={"model-cascader " + (llmSearchValue1!=''? 'searching':'')}
                        placeholder={i18next.t(["robot-manage.create-robot.llm-model-hint"]) || ""}
                        options={llmModelOptions}
                        variant="filled"
                        // value={settingForm.robotLlmModel}
                        onChange={this.handleChangeRobotLlmModel}
                        onFocus={this.handleRobotLlmModelFocus}
                        allowClear={false}
                        popupClassName={'pointer-cascader-dropdown model-cascader-dropdown ' + (llmSearchValue1!=''? 'searching':'')}
                        optionRender={OptionRender}
                        dropdownRender={dropdownRender1}
                        searchValue={llmSearchValue1}
                        showCheckedStrategy={SHOW_CHILD}
                        showSearch={false}
                        suffixIcon={<Svgicon svgName="icon_select_suffix"/>}
                        expandIcon={<Svgicon svgName="arrow_right_line"/>}
                    />
                </Form.Item>
                {/* AI模型 (增强) */}
                <Form.Item className="half-width"  name="escalateLlmModel" label={i18next.t(["robot-manage.create-robot.escalate-llm-model"])}>
                    <Cascader
                        className={"model-cascader " + (llmSearchValue2!=''? 'searching':'')}
                        placeholder={i18next.t(["robot-manage.create-robot.escalate-llm-model-hint"]) || ""}
                        options={llmModelOptions}
                        variant="filled"
                        // value={settingForm.escalateLlmModel}
                        onChange={this.handleChangeEscalateLlmModel}
                        onFocus={this.handleEscalatelmModelFocus}
                        allowClear={false}
                        popupClassName={'pointer-cascader-dropdown model-cascader-dropdown ' + (llmSearchValue2!=''? 'searching':'')}
                        optionRender={OptionRender}
                        dropdownRender={dropdownRender2}
                        searchValue={llmSearchValue2}
                        showCheckedStrategy={SHOW_CHILD}
                        suffixIcon={<Svgicon svgName="icon_select_suffix"/>}
                        expandIcon={<Svgicon svgName="arrow_right_line"/>}
                    />
                </Form.Item>
            </Flex>
            {/* AI模型創造力 */}
            <Form.Item className="single-row"  label={i18next.t(["robot-manage.create-robot.intelligence"])}>
                <div className="field-desc">{i18next.t("robot-manage.create-robot.intelligence-hint")}</div>
                <Flex gap={15}>
                    <Form.Item noStyle name="intelligence">
                        <Slider style={{ flex: 1 }} min={0} max={1} step={0.01}/>
                    </Form.Item>
                    <Form.Item noStyle name="intelligence">
                        <InputNumber style={{ flexShrink: 0 }} min={0} max={1} step={0.01} variant="filled" />
                    </Form.Item>
                </Flex>
            </Form.Item>
        </div>
    }

    //標準問候語表单
    renderWelcome() {
        return <div className="card-content">
            {/* 機器人歡迎語 */}
            <Form.Item name="robotWelcomeMsg" label={i18next.t("robot-manage.create-robot.robot-welcome-msg")}>
                <TextArea
                    // value={settingForm.robotWelcomeMsg}
                    placeholder={i18next.t("robot-manage.create-robot.robot-welcome-msg-hint") as string}
                    autoSize={{ minRows: 4, maxRows: 8 }} showCount
                    variant="filled" maxLength={500} />
            </Form.Item>
        </div>
    }

    // 知識庫表单
    renderKnowledge() {
        return <div className="card-content">
            <Flex gap={20} wrap>
                {/* 知识相关性 */}
                <Form.Item className="half-width" style={{ flex: 1 }} label={i18next.t(["robot-manage.create-robot.relevance"])}>
                    <div className="field-desc">{i18next.t("robot-manage.create-robot.relevance-hint")}</div>
                        <Flex gap={15}>
                            <Form.Item noStyle name="relevance" >
                                <Slider style={{ flex: 1 }} min={0} max={0.95} step={0.01}/>
                            </Form.Item>
                            <Form.Item noStyle name="relevance" >
                                <InputNumber style={{ flexShrink: 0 }} min={0} max={0.95} step={0.01} variant="filled"/>
                            </Form.Item>
                        </Flex>
                </Form.Item>
                
                {/* 允许引用访问 */}
                <Form.Item style={{ flex: 1 }}  label={i18next.t(["robot-manage.create-robot.show-knowledge"])}>
                    <div className="field-desc min-height32">{i18next.t("robot-manage.create-robot.show-knowledge-notice")}</div>
                    <Form.Item noStyle  name="showKnowledge">
                        <Select 
                            variant="filled" suffixIcon={<Svgicon svgName="icon_select_suffix"/>}
                            options={[
                                { label: i18next.t("robot-manage.create-robot.allow"), value: true },
                                { label: i18next.t("robot-manage.create-robot.not-allowed"), value: false }]
                        } />
                    </Form.Item>
                </Form.Item>
            </Flex>
            <Flex gap={20} wrap style={{ width: '100%' }}>
                {/* 數據塊數量 */}
                <Form.Item style={{ flex: 1 }}  name="konwledgeCount" className="konwledge-count-fomr-item" label={i18next.t("robot.knowlege-count")}>
                    <InputNumber style={{ width: '100%' }} min={0} max={10} step={1} variant="filled" />
                </Form.Item>
                {/* 知識檢索為空 */}
                <Form.Item style={{ flex: 1 }}  name="isIrrelevantQuestionsBoolean" label={i18next.t("robot-manage.create-robot.empty-knowlwdge")}>
                    <Select style={{ width: '100%' }} 
                        variant="filled" suffixIcon={<Svgicon svgName="icon_select_suffix"/>}
                        onChange={(e) => this.handleChangeFormFieldValue('isIrrelevantQuestionsBoolean', e)} 
                        options={[
                            { label: i18next.t("robot-manage.create-robot.llm-response"), value: false },
                            { label: i18next.t("robot-manage.create-robot.canned-response"), value: true }]
                        } />
                </Form.Item>
            </Flex>
            {/* 預置響應 */}
            {
                // settingForm.isIrrelevantQuestionsBoolean ?
                    <Form.Item 
                        noStyle
                        shouldUpdate={(prevValues:any, currentValues:any) => prevValues.isIrrelevantQuestionsBoolean !== currentValues.isIrrelevantQuestionsBoolean}
                        >
                             {({ getFieldValue }) =>
                                getFieldValue('isIrrelevantQuestionsBoolean')? (
                                    <Form.Item name="irrelevantRespond" label={i18next.t("robot-manage.create-robot.canned-response")}>
                                         <TextArea
                                            // value={settingForm.irrelevantRespond}
                                            placeholder={i18next.t("robot-manage.create-robot.canned-response-hit") || ''}
                                            autoSize={{ minRows: 3, maxRows: 5 }} showCount
                                            variant="filled" maxLength={200} />
                                    </Form.Item>
                                ) : null
                            }
                    </Form.Item>
                    // : null
            }
        </div>
    }

    //记忆
    renderMemory() {
        return <div className="card-content">
            {/* 機器人歡迎語 */}
            <Form.Item noStyle>
                <div className="field-desc">{i18next.t('robot-manage.create-robot.long-term-memory-desc')}</div>
                <Form.Item  name="enableConversationMemory" label={this.renderFiledLabelTooltip(
                    i18next.t('robot-manage.create-robot.long-term-memory'),
                    i18next.t('robot-manage.create-robot.long-term-memory-tip'))}>
                        <Switch style={{ width: '30px' }} />
                </Form.Item>
            </Form.Item>
        </div>
    }

    render() {
        const { settingForm, isSubmiting, robotInfo } = this.state;
        const panelStyle: React.CSSProperties = {
            marginBottom: 16,
            border: 'none',
            borderRadius: "10px",
            background: getCSSVariablesValue("--colorBgBase"),
        };
        return (
            <Flex className={"robot-basicinfo-wrap common-wrap gray-bg"} vertical>
                {/* <Flex className="area-title wrap-nav" align="center">
                    <div className="wn-title">{i18next.t("robot-manage.setting-robot")}</div>
                </Flex> */}
                {/* 设置 */}
                <Flex className="setting-area wrap-content" vertical>
                    <Form ref={this.formRef} className="common-form" name="validateOnly" layout="vertical" autoComplete="off"
                        initialValues={settingForm} requiredMark={false} disabled={isSubmiting}
                        onValuesChange={(changedValues) => this.handleFieldChange(changedValues)} // 监听字段变化
                    >
                        <Collapse
                            bordered={false}
                            destroyInactivePanel={false}
                            defaultActiveKey={['1', '2']}
                            items={this.cardFormItems(panelStyle)}
                            expandIcon={({ isActive }) => <Svgicon svgName="arrow_down" className={isActive?"":'roate'} />}
                            expandIconPosition="end"
                        />
                    </Form>
                </Flex>
                {/* 调试 */}
                {/* <Flex className="debug-area" vertical>
                    <RobotDebug ref={this.robotDebug} robotInfo={robotInfo} className="debug-main" />
                </Flex> */}
            </Flex>

        )
    }
}

const mapStateToProps = (state: any) => ({
    userInfo: state.app.userInfo,
    globalLang: state.app.globalLang,
    globalRegion: state.app.globalRegion,
    permissionMap: state.app.permissionMap,
    themeInfo: state.app.themeInfo
});

const mapDispatchToProps = (dispatch: (e: any) => void) => ({
});

const ForwardedChildComponent = forwardRef((props: any, ref: any) => {
    return<RobotSettingBasicInfo ref={ref}  {...props} />;
});

export default connect(mapStateToProps, mapDispatchToProps, null, { forwardRef: true })(ForwardedChildComponent);
