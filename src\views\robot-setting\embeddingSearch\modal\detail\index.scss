@import '../../../../../styles/mixin.scss';
.embedding-detail-modal {
    max-height: 100%;
    .ed-con{
        padding-top: 16px;
    }
    .ed-title{
        border-top: 1px solid var(--colorBorderSecondary );
        padding: 10px 0;
        font-weight: 600;
        font-size: 14px;
        color: var(--colorText);
        line-height: 22px;
        flex-shrink: 0;
    }
    .ed-content{
        font-weight: 400;
        font-size: 14px;
        color: var(--colorText);
        line-height: 22px;
        padding-bottom: 10px;
        // flex: 1;
        // overflow: auto;
    }
    .ed-tag{
        flex-shrink: 0;
        font-weight: 500;
        font-size: 14px;
        color: var(--colorTextTertiary);
        line-height: 20px;
        .svg-icon{
            font-size: 16px;
        }
    }
}
