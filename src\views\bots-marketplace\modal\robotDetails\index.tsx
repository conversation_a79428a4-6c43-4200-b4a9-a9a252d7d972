import React, { useState, useEffect, forwardRef, useRef, useImperativeHandle } from "react";
import { connect } from "react-redux";
import { Modal, message, Flex, Spin } from "antd";
import { LoadingOutlined } from "@ant-design/icons";
import { useTranslation } from 'react-i18next';
import { $trim } from '@/utils/common'
import Svgicon from "@/components/svgicon";

import { api_query_robot } from "@/api/robotManage2";
import './index.scss'
import defaultLogo from "@/assets/bot_default_avatar.png";

// 机器人详情
const RobotDetailsModal = forwardRef((props: any, ref) => {
  const { t } = useTranslation();
  /** 自定义暴露给父组件的实例值 **/
  // useImperativeHandle(ref, () => ({
  //   init() {
  //   }
  // }))

  /* props */
  const { visible: isVisible, resource, activeCompanyName } = props

  /* state */
  const [details, setDetails] = useState<any>({}) // 详情
  const [loading, setLoading] = useState(false);


  useEffect(() => {
  }, []);

  useEffect(() => {
    if (!isVisible) return
    const data = {
      seq: 0,
      robot_id: resource.robotId
    }
    setLoading(true)
    api_query_robot(data).then((res: any) => {
      setLoading(false)
      const detail = res.data || {}
      setDetails(detail)
    }).catch(() => {
      setLoading(false)
    })
  }, [isVisible, resource])

  /* method */
  // 获取详情

  // 取消
  const onCancel = () => {
    props.onClose()
  }

  // 获取名称
  const getName = (robot:any)=>{
    let name = robot.username
    if (robot.robot_type ==='DEPARTMENT'){
      name = robot.department_name
    }
    if (robot.robot_type ==='COMPANY'){
      name = activeCompanyName
    }
    return name
  }

  return (
    <>
      <Modal title={''}
        wrapClassName="bot-details-modal"
        open={isVisible}
        onCancel={onCancel}
        footer={null}
        centered
      >
        <div className="bot-info">
          {
            loading ? <Spin className="modal-spin" size="large" indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />} /> :
              <Flex vertical gap={12}>
                <img className="bot-icon" src={details.logo || defaultLogo} alt="robot icon" />
                <div className="bot-name">{details.name}</div>
                {/* <Flex className="bot-user" gap={8} align="center" justify="center">
                  <Svgicon svgName="icon_user" />
                  <span>{details.username}</span>
                  <div className="divide"></div>
                  <span className="usage">10.3k usage</span>
                </Flex> */}
                <div className="bot-describe">{details.introduction_message}</div>
                <Flex className="bot-model" vertical gap={12}>
                  <Flex gap={12} justify="space-between">
                    <span className="prop">{t('bots-marketplace.owner')}</span>
                    <span className="value">{getName(details)}</span>
                  </Flex>
                  <Flex gap={12} justify="space-between">
                    <span className="prop">{t('bots-marketplace.usage')}</span>
                    <span className="value">{details?.chat_count}</span>
                  </Flex>
                </Flex>
                <Flex className="bot-others" vertical gap={12}>
                  <Flex gap={12} justify="space-between">
                    <span className="prop">{t('bots-marketplace.default-model')}</span>
                    <span className="value">{details?.default_LLM_display_name?.length > 0 ? details.default_LLM_display_name.join("/") : '-'}</span>
                  </Flex>
                  <Flex gap={12} justify="space-between">
                    <span className="prop">{t('bots-marketplace.enhanced-model')}</span>
                    <span className="value">{details?.enhance_LLM_display_name?.length > 0 ? details.enhance_LLM_display_name.join("/") : '-'}</span>
                  </Flex>
                  <Flex gap={12} justify="space-between">
                    <span className="prop">{t('bots-marketplace.ai-creativity')}</span>
                    <span className="value">{details?.temperature}</span>
                  </Flex>
                  <Flex gap={12} justify="space-between">
                    <span className="prop">{t('bots-marketplace.relevance')}</span>
                    <span className="value">{details?.relevance_score}</span>
                  </Flex>
                  <Flex gap={12} justify="space-between">
                    <span className="prop">{t("robot.knowlege-count")}</span>
                    <span className="value">{details?.knowledge_count}</span>
                  </Flex>
                  <Flex gap={12} justify="space-between">
                    <span className="prop">{t('robot-manage.create-robot.robot-id')}</span>
                    <span className="value">{details?.robot_id}</span>
                  </Flex>
                </Flex>
              </Flex>
          }
        </div>
      </Modal>
    </>
  );
});
const mapStateToProps = (state: any) => ({
  userInfo: state.app.userInfo,
  activeCompanyName: state.app.activeCompanyName,
});
const mapDispatchToProps = (dispatch: (e: any) => void) => ({
});
export default connect(mapStateToProps, mapDispatchToProps)(RobotDetailsModal);