// 区号字数限制
const areaCodeLimit: { [key: string]: any } = {
  1: {
    bit: [10],
  },
  7: {
    bit: [10],
  },
  20: {
    bit: [10],
  },
  27: {
    bit: [9],
  },
  30: {
    bit: [10],
  },
  31: {
    bit: [9],
  },
  32: {
    bit: [9],
  },
  33: {
    bit: [9],
  },
  34: {
    bit: [9],
  },
  36: {
    bit: [9],
  },
  39: {
    bit: [10],
  },
  40: {
    bit: [9],
  },
  41: {
    bit: [9],
  },
  43: {
    bit: [10],
  },
  44: {
    bit: [10],
  },
  45: {
    bit: [8],
  },
  46: {
    bit: [9],
  },
  47: {
    bit: [8],
  },
  48: {
    bit: [9],
  },
  49: {
    bit: [11],
  },
  51: {
    bit: [9],
  },
  52: {
    bit: [10],
  },
  54: {
    bit: [10],
  },
  55: {
    bit: [9],
  },
  56: {
    bit: [9],
  },
  57: {
    bit: [10],
  },
  58: {
    bit: [10],
  },
  60: {
    bit: [9, 10],
  },
  61: {
    bit: [9],
  },
  62: {
    bit: [10, 11],
  },
  63: {
    bit: [10],
  },
  64: {
    bit: [8, 9, 10],
  },
  65: {
    bit: [8],
  },
  66: {
    bit: [9],
  },
  81: {
    bit: [10],
  },
  82: {
    bit: [10],
  },
  84: {
    bit: [9],
  },
  86: {
    bit: [11],
  },
  90: {
    bit: [10],
  },
  91: {
    bit: [10],
  },
  92: {
    bit: [10],
  },
  93: {
    bit: [9],
  },
  94: {
    bit: [9],
  },
  95: {
    bit: [10],
  },
  212: {
    bit: [9],
  },
  213: {
    bit: [9],
  },
  216: {
    bit: [8],
  },
  218: {
    bit: [8],
  },
  220: {
    bit: [7],
  },
  221: {
    bit: [7],
  },
  222: {
    bit: [8],
  },
  223: {
    bit: [8],
  },
  224: {
    bit: [9],
  },
  225: {
    bit: [8],
  },
  226: {
    bit: [8],
  },
  227: {
    bit: [8],
  },
  228: {
    bit: [8],
  },
  229: {
    bit: [8],
  },
  230: {
    bit: [8],
  },
  231: {
    bit: [9],
  },
  232: {
    bit: [8],
  },
  233: {
    bit: [9],
  },
  234: {
    bit: [10],
  },
  235: {
    bit: [8],
  },
  236: {
    bit: [8],
  },
  237: {
    bit: [9],
  },
  238: {
    bit: [7],
  },
  239: {
    bit: [7],
  },
  240: {
    bit: [9],
  },
  241: {
    bit: [8],
  },
  242: {
    bit: [9],
  },
  244: {
    bit: [9],
  },
  245: {
    bit: [9],
  },
  248: {
    bit: [7],
  },
  250: {
    bit: [9],
  },
  251: {
    bit: [9],
  },
  252: {
    bit: [9],
  },
  253: {
    bit: [8],
  },
  254: {
    bit: [9],
  },
  255: {
    bit: [9],
  },
  256: {
    bit: [9],
  },
  257: {
    bit: [8],
  },
  258: {
    bit: [9],
  },
  260: {
    bit: [9],
  },
  261: {
    bit: [9],
  },
  262: {
    bit: [9],
  },
  263: {
    bit: [9],
  },
  264: {
    bit: [9],
  },
  265: {
    bit: [9],
  },
  266: {
    bit: [8],
  },
  267: {
    bit: [8],
  },
  268: {
    bit: [8],
  },
  269: {
    bit: [7],
  },
  291: {
    bit: [7],
  },
  297: {
    bit: [7],
  },
  298: {
    bit: [6],
  },
  299: {
    bit: [6],
  },
  350: {
    bit: [8],
  },
  351: {
    bit: [9],
  },
  352: {
    bit: [9],
  },
  353: {
    bit: [9],
  },
  354: {
    bit: [7],
  },
  355: {
    bit: [9],
  },
  356: {
    bit: [8],
  },
  357: {
    bit: [8],
  },
  358: {
    bit: [9],
  },
  359: {
    bit: [9],
  },
  370: {
    bit: [8],
  },
  371: {
    bit: [8],
  },
  372: {
    bit: [7, 8],
  },
  373: {
    bit: [8],
  },
  374: {
    bit: [8],
  },
  375: {
    bit: [9],
  },
  376: {
    bit: [9],
  },
  377: {
    bit: [8],
  },
  378: {
    bit: [8],
  },
  381: {
    bit: [9],
  },
  382: {
    bit: [8],
  },
  385: {
    bit: [9],
  },
  386: {
    bit: [8],
  },
  387: {
    bit: [8, 9],
  },
  389: {
    bit: [8],
  },
  420: {
    bit: [9],
  },
  421: {
    bit: [9],
  },
  423: {
    bit: [7],
  },
  501: {
    bit: [7],
  },
  502: {
    bit: [8],
  },
  503: {
    bit: [8],
  },
  504: {
    bit: [8],
  },
  505: {
    bit: [8],
  },
  506: {
    bit: [8],
  },
  507: {
    bit: [8],
  },
  508: {
    bit: [6],
  },
  509: {
    bit: [8],
  },
  590: {
    bit: [9],
  },
  591: {
    bit: [8],
  },
  592: {
    bit: [7],
  },
  593: {
    bit: [7],
  },
  594: {
    bit: [9],
  },
  595: {
    bit: [9],
  },
  596: {
    bit: [9],
  },
  597: {
    bit: [7],
  },
  598: {
    bit: [8],
  },
  599: {
    bit: [7],
  },
  670: {
    bit: [8],
  },
  673: {
    bit: [7],
  },
  675: {
    bit: [8],
  },
  676: {
    bit: [7],
  },
  677: {
    bit: [7],
  },
  678: {
    bit: [7],
  },
  679: {
    bit: [7],
  },
  680: {
    bit: [7],
  },
  682: {
    bit: [5],
  },
  685: {
    bit: [7],
  },
  686: {
    bit: [7],
  },
  687: {
    bit: [6],
  },
  689: {
    bit: [8],
  },
  852: {
    bit: [8],
  },
  853: {
    bit: [8],
  },
  855: {
    bit: [8, 9],
  },
  856: {
    bit: [10],
  },
  880: {
    bit: [8],
  },
  886: {
    bit: [10],
  },
  960: {
    bit: [7],
  },
  961: {
    bit: [8],
  },
  962: {
    bit: [9],
  },
  964: {
    bit: [10],
  },
  965: {
    bit: [8],
  },
  966: {
    bit: [9],
  },
  967: {
    bit: [9],
  },
  968: {
    bit: [8],
  },
  970: {
    bit: [9],
  },
  971: {
    bit: [9],
  },
  972: {
    bit: [9],
  },
  973: {
    bit: [8],
  },
  974: {
    bit: [8],
  },
  975: {
    bit: [8],
  },
  976: {
    bit: [8],
  },
  977: {
    bit: [10],
  },
  992: {
    bit: [9],
  },
  993: {
    bit: [8],
  },
  994: {
    bit: [9],
  },
  995: {
    bit: [9],
  },
  996: {
    bit: [9],
  },
  998: {
    bit: [9],
  },
  1242: {
    bit: [7],
  },
  1246: {
    bit: [7],
  },
  1264: {
    bit: [7],
  },
  1268: {
    bit: [7],
  },
  1284: {
    bit: [7],
  },
  1340: {
    bit: [7],
  },
  1345: {
    bit: [7],
  },
  1441: {
    bit: [7],
  },
  1473: {
    bit: [7],
  },
  1649: {
    bit: [7],
  },
  1664: {
    bit: [7],
  },
  1670: {
    bit: [7],
  },
  1671: {
    bit: [7],
  },
  1684: {
    bit: [7],
  },
  1721: {
    bit: [7],
  },
  1758: {
    bit: [7],
  },
  1767: {
    bit: [7],
  },
  1784: {
    bit: [7],
  },
  1787: {
    bit: [7],
  },
  1809: {
    bit: [7],
  },
  1868: {
    bit: [7],
  },
  1869: {
    bit: [7],
  },
  1876: {
    bit: [7],
  },
};
export default areaCodeLimit