@import '../../styles/mixin.scss';
.UserAvatar {
    display: inline-block;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .size-48px{
    .UserAvatar--inner{
      font-size: 18px;
      line-height: 22px;
      font-weight: 500;
    }
  }

  .size-20px{
    .UserAvatar--inner{
      font-size: 10px;
      line-height: 16px;
      font-weight: 500;
    }
  }
  
  .UserAvatar--inner {
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 12px;
    line-height: 18px;
    font-weight: 600;
    // color: var(--co);
  }
  
  
  .UserAvatar--light .UserAvatar--inner {
    border: 0;
  }
  .UserAvatar--img{
    width: 100%;
    height: 100%;
    object-fit: contain;
    cursor: pointer;
  }