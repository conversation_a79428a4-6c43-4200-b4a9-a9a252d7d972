@import "/src/styles/mixin.scss";
.publish-ann-wrap {
    padding-top: 29px;
    .item {
        width: 100%;
        &:not(:last-of-type),
        .permission:not(:last-of-type) {
            margin-bottom: 24px;
        }
    }
    .label {
        font-weight: 500;
        font-size: 12px;
        color: var(--colorText);
        line-height: 16px;
        margin-bottom: 8px;
    }
    @mixin value-font($fontWeight: 400) {
        font-weight: $fontWeight;
        font-size: 14px;
        color: var(--colorText);
        line-height: 22px;
    }
    .value-read-only {
        height: 32px;
        background: var(--colorFillTertiary);
        border-radius: 8px;
        padding: 0 12px;
        @include value-font;
        @include ellipsis;
        width: 100%;
        display: inline-block;
        line-height: 32px;
    }
    .ant-radio-group {
        padding: 6px 12px;
    }
    .ant-radio-wrapper {
        @include value-font($fontWeight: 500);
    }
    .tree {
        border: 1px solid var(--colorBorderSecondary);
        border-radius: 12px;
        margin-top: 16px;
        .tit {
            @include value-font($fontWeight: 600);
            background: var(--colorFillQuaternary);
            padding: 12px;
            border-bottom: 1px solid var(--colorBorderSecondary);
        }
        .ant-tree {
            // padding: 7px 4px;
            border-radius: 0 0 12px 12px;
            padding: 12px;
            .ant-tree-switcher-icon {
                font-size: 16px;
            }
            .empty-node {
                @include font(12px, 24px, 400, var(--colorTextSecondary));
            }
        }
    }
    // 组织架构样式
    .group-member-con {
        // margin-top: 16px;
        height: 500px;
        .group-member-pick {
            flex: 1;
            // border: 1px solid var(--colorBorderSecondary);
            border-radius: 8px;
            overflow: hidden;
        }
        .picke-options {
            width: 50%;
            padding: 12px 16px 0;
            padding-right: 0;
            border-right: 1px solid var(--colorBorderSecondary);
            height: 100%;
            overflow: hidden;
            .ant-input-affix-wrapper,
            .ant-breadcrumb {
                flex-shrink: 0;
            }
            .orga-list-items {
                flex: 1;
                overflow-y: auto;
                padding-right: 16px;
                padding-bottom: 10px;
            }
        }
        .picker-result {
            width: 50%;
            padding: 12px 16px 0;
            padding-right: 0;
            .pr-title {
                @include font(16px, 24px, 600);
                padding: 6px;
                flex-shrink: 0;
            }
        }
    }
    .contacts {
        overflow: auto;
        .contacts-type {
            @include font(14px, 25px, 500);
            padding: 9px 8px;
        }
        .contact-item {
            width: 100%;
            padding: 6px 8px 6px 32px;
            cursor: pointer;
            .contact-icon {
                flex-shrink: 0;
                img {
                    width: 24px;
                    height: auto;
                    border-radius: 8px;
                }
            }
            .contact-dept-icon {
                font-size: 16px;
            }
            .contact-name {
                flex: 1;
                @include ellipsis();
            }
            .more {
                flex-shrink: 0;
                margin-left: auto;
                font-size: 16px;
            }
            &.child {
            }
        }
    }
    .orga-list-item.dept{
        .ant-checkbox-wrapper{
            width: 100%;
            .ant-checkbox-label{
                flex: 1;
            }
        }
    }
    .orga-list-item-icon.dept {
        padding: 8px;
        border-radius: 10px;
        font-size: 16px;
        background: var(--colorPrimaryBg);
        color: var(--colorPrimary);
    }
    .orga-list-item-icon.user {
        padding: 8px;
        // border-radius: 10px;
        font-size: 16px;
        // background: var(--colorPrimaryBg);
        color: var(--colorPrimary);
    }
    .orga-list-item-arrow {
        margin-left: auto;
        flex-shrink: 0;
        cursor: pointer;
        height: auto;
    }
    .orga-list-item-dept{
        width: 100%;
    }
    .sublevel {
        color: var(--colorIconQuaternary);
    }
    .ant-breadcrumb {
        .ant-breadcrumb-separator {
            font-size: 16px;
            margin: 0 4px;
        }
        li:not(:last-child) {
            &:hover {
                color: var(--colorText);
            }
        }
        .ant-breadcrumb-link {
            cursor: pointer;
            word-break: break-word;
        }
    }
    .pr-items {
        flex: 1;
        overflow-y: auto;
        padding-bottom: 10px;
        padding-right: 16px;
    }
    .pr-item {
        width: 100%;
        padding: 6px 8px;
        padding-right: 0;
        overflow: hidden;
        flex-shrink: 0;
        .UserAvatar {
            flex-shrink: 0;
        }
        .svg-operate {
            margin-left: auto;
            flex-shrink: 0;
        }
        .pr-item-content {
            flex: 1;
            overflow: hidden;
        }
        .name {
            max-width: 100%;
            @include ellipsis();
        }
    }
}
