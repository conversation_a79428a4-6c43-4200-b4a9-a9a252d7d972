// export const requireAll = (requireContext: __WebpackModuleApi.RequireContext) =>
//   requireContext.keys().forEach(requireContext);
// const req = require.context('./svg', false, /\.svg$/);
// console.log('req', req)
// requireAll(req);
// src/icons/index.ts
// const requireAll = (requireContext: __WebpackModuleApi.RequireContext) => 
//   requireContext.keys().forEach(requireContext);

const requireAll = (requireContext:any) => requireContext.keys().map(requireContext);
const req = require.context('./svg', false, /\.svg$/)
const login = require.context('./svg/login', false, /\.svg$/)
const robot = require.context('./svg/robot', false, /\.svg$/)
const common = require.context('./svg/common', false, /\.svg$/)
const nav = require.context('./svg/nav', false, /\.svg$/)
const setting = require.context('./svg/setting', false, /\.svg$/)
const robotmanage = require.context('./svg/robotmanage', false, /\.svg$/)
const contacts = require.context('./svg/contacts', false, /\.svg$/)
const chats = require.context('./svg/chats', false, /\.svg$/)
requireAll(req)
requireAll(login)
requireAll(robot)
requireAll(common)
requireAll(nav)
requireAll(setting)
requireAll(robotmanage)
requireAll(contacts)
requireAll(chats)
export default requireAll
