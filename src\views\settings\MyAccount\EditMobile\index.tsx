
import { connect } from "react-redux";
import { useEffect, useState, useRef, forwardRef } from "react";
import { Modal, message, Flex, Button, Input, Form, Tabs, TabsProps } from "antd"
import { useTranslation } from 'react-i18next';
import { $trim, $isNull } from '@/utils/common'
import './index.scss'
import MobileInput from "@/components/form/mobileInput";
import ErrorCodeMap from '@/views/common/formErrorCodeMap'
import FormUtils from '@/views/common/formUtils'
import classnames from "classnames";
import { EmailRules, MobileRules, VerifyCodeRules } from "@/views/common/formRule";
import { api_get_email_code, api_get_mobile_code, api_bind_user_phone } from '@/api/user'
import { api_check_verify_code, api_change_contact } from '@/api/setting'
import VerifyCode from "@/components/form/verifyCode";
import { RequestFailedMessage } from "@/utils/userRequest";

const TabItems: TabsProps['items'] = [
    {
        key: 'Email',
        label: 'email-verifiy',
        children: null,
    },
    {
        key: 'Mobile',
        label: 'mobile-verifiy',
        children: null,
    }
];

// const scence1 = 'MEMBER_CHANGE_MOBILE'
const scence1:any = {
    Mobile: 'MEMBER_CHANGE_MOBILE',
    Email: 'MEMBER_CHANGE_EMAIL',
}
const scence2 = 'MEMBER_CHANGE_NEW_MOBILE'


// 修改手机号
const EditMobile = forwardRef((props: any, ref: React.LegacyRef<HTMLDivElement> | any) => {
    const { t } = useTranslation()

    const { userInfo, mobile, email, onUpdate } = props
    const currentUserId = userInfo.userId

    const [panel, setPanel] = useState('bind') // bind: 用户未绑定手机号, update: 用户绑定过手机号
    const [step, setStep] = useState(1)// 步骤
    const [accountType, setAccountType] = useState('Mobile') // 邮箱 Email 或  手机号 Mobile
    const [isOkBtnAble, setIsOkBtnAble] = useState(false) // 按钮是否可用
    const [isStep1OkAble, setIsStep1OkAble] = useState(false) // 按钮是否可用
    const [form] = Form.useForm();
    const [formValues, setFormValues] = useState<any>({});
    const [step1CheckCode, setStep1CheckCode] = useState<any>(null);


    // 初始化
    useEffect(() => {
    }, [])

    useEffect(() => {
        // console.log('email', email)
        form.setFieldValue('email', email)
    }, [email])

    useEffect(() => {
        // console.log('mobile change', mobile)
        if (mobile) {
            setPanel('update')
            setMobileFormValue()
        } else {
            setPanel('bind')
        }
    }, [mobile])

    // 表单项输入值监听, 控制提交按钮状态
    const values = Form.useWatch([], form);
    useEffect(() => {
        let fields: any = []
        if (panel === 'bind') {
            fields = ['newMobile', 'newCode']
        } else {
            if (step === 1) {
                fields = accountType == 'Email' ? ['email', "code"] : ['mobile', "code"]
            } else {
                fields = ["newEmail", "newCode"]
            }
        }

        // console.log('fields', fields)
        form.validateFields(fields, { validateOnly: true }).then(() => {
            setIsOkBtnAble(true)
        }).catch((err) => {
            // console.log('SubmitButton1 errerr:', err)
            setIsOkBtnAble(false)
        });
    }, [step, accountType, form, values, panel])

    const setMobileFormValue = () => {
        const { number, codeLabel } = GetMobileFormValue(mobile)
        // console.log('number', number)
        // console.log('codeLabel', codeLabel)
        form.setFieldValue('mobile', {
            number: number,
            code: { label: codeLabel, value: '' }
        })
    }

    // 提交
    const onOk = async () => {
        if (panel === 'bind') {
            bindMobile()
            return
        } else {
            if (step === 1) {
                onSubmitStep1()
                return
            }
            if (step === 2) {
                onSubmitStep2()
                return
            }
        }
    }

    const onSuccess = ()=>{
        message.success(t('setting.change-mobile-ok'))
        onUpdate && onUpdate()
        onCancel()
    }

    const bindMobile = async () => {
        // 绑定手机号
        let { newMobile, newCode } = formValues
        const data: any = {
            userId: currentUserId,
            areaCode: '+' + newMobile.code.label,
            mobile: newMobile.number,
            code: newCode
        }
        // console.log('data',data)
        api_bind_user_phone(data).then(async (res: any) => {
            // console.log('res',res)
            const code = res?.code
            if (code == 0 && res.data.success) { // 成功直接登录
                onSuccess()
            } else {
                const mobileErrors = ErrorCodeMap.mobile // 手机错误码
                const codeErros = ErrorCodeMap.code// 验证码错误码
                const allCodes = [...mobileErrors, ...codeErros]
                if (!allCodes.includes(code)) {
                    const msg = RequestFailedMessage(res?.msg, code)
                    message.error(msg)
                    return
                }
                let errMsg = t(`errorCode.${code}`)
                form.setFields([{ name: mobileErrors.includes(code) ? 'newMobile' : 'newCode', errors: [errMsg], warnings: [`errorCode.${code}`] }])
            }

        }).catch((err: any) => {
            console.log('err', err)
        })
    }

    // 步骤1 提交
    const onSubmitStep1 = async () => {
        // setStep(2)
        // return
        let { email, mobile, code } = formValues
        const data: any = {
            scene: scence1[accountType],
            code,
            email,
            areaCode: '+' + mobile?.code?.label,
            mobile: mobile?.number
        }
        // console.log('data', data)
        setIsOkBtnAble(false)
        const res: any = await api_check_verify_code(data).catch(() => {
            setIsOkBtnAble(true)
        })
        setIsOkBtnAble(true)
        // console.log('res', res)
        if (!res) return
        let _code = res.code
        if (_code === 0) {
            setStep1CheckCode(res?.data?.tempCode)
            setStep(2)
        } else {
            const codeErros = ErrorCodeMap.code// 验证码错误码
            const errors = [...codeErros]
            if (!errors.includes(_code)) {
                const msg = RequestFailedMessage(res?.msg, _code)
                message.error(msg);
                return
            }
            const errMsg = t(`errorCode.${_code}`)
            form.setFields([{ name: "code", errors: [errMsg], warnings: [`errorCode.${_code}`] }]) //warnings 为了解决 setFields 检验信息 国际化问题
        }
    }

    // 步骤2 提交
    const onSubmitStep2 = async () => {
        FormUtils.resetValidate(form)
        let { newMobile, newCode } = formValues
        const data: any = {
            scene: scence2,
            areaCode: '+' + newMobile?.code?.label,
            mobile: newMobile?.number,
            code: newCode,
            tempCode: step1CheckCode
        }
        // console.log('data', data)
        setIsOkBtnAble(false)
        const res: any = await api_change_contact(data).catch(() => {
            setIsOkBtnAble(true)
        })
        setIsOkBtnAble(true)
        // console.log('res', res)
        if (!res) return
        let _code = res.code
        if (_code === 0 && res?.data?.success) {
            onSuccess()
        } else {
            const codeErros = ErrorCodeMap.code// 验证码错误码
            const mobileErrors = ErrorCodeMap.mobile // 手机号错误码
            const errors = [...codeErros,...mobileErrors]
            if (!errors.includes(_code)) {
                const msg = RequestFailedMessage(res?.msg, _code)
                message.error(msg);
                return
            }
            const errMsg = t(`errorCode.${_code}`)
            form.setFields([{
                name: mobileErrors.includes(_code) ? "newMobile" : "newCode",
                errors: [errMsg], warnings: [`errorCode.${_code}`]
            }]) //warnings 为了解决 setFields 检验信息 国际化问题
        }
    }
    // 取消
    const onCancel = () => {
        props.onClose()
    }

    // tab切换
    const onChangeTab = (key: string) => {
        // console.log(key);
        setAccountType(key)
    };

    // 区号切换时需要校验手机号
    const onValidteMobile = () => {
        form.validateFields(["mobile"])
    }

    const onValidteNewMobile = () => {
        form.validateFields(["newMobile"])
    }

    // 表单项 输入改变事件
    const onFiledValueChange = (changedFields: any, allValues: any) => {
        try {
            // console.log('changedFields', changedFields)
            // console.log('allValues',allValues)
            setFormValues({ ...formValues, ...allValues });
            Object.keys(changedFields).forEach((fieldName: any) => {
                const fieldError = form?.getFieldError(fieldName)
                if (fieldError.length > 0) FormUtils.clearValidate(form, fieldName)
            })
        } catch (error) {

        }
    }

    // 处理步骤1  获取验证码失败 对应表单输入框错误提示
    const onVerifyCodeError1 = (err: any) => {
        const _code = err?.code
        const emailErrors = ErrorCodeMap.email
        const mobileErrors = ErrorCodeMap.mobile
        const errMsg = t(`errorCode.${_code}`)
        let targetField: any = null
        if (emailErrors.includes(_code)) targetField = 'email'
        if (mobileErrors.includes(_code)) targetField = 'mobile'
        if (!targetField) return
        form.setFields([{ name: targetField, errors: [errMsg], warnings: [`errorCode.${_code}`] }])
    }

    // 处理步骤2  获取验证码失败 对应表单输入框错误提示
    const onVerifyCodeError2 = (err: any) => {
        const _code = err?.code
        const Errors = ErrorCodeMap.mobile
        if (!Errors.includes(_code)) return
        // const errMsg = _code === 1002003001 ? '手机号已被绑定' : t(`errorCode.${_code}`)
        const errMsg = t(`errorCode.${_code}`)
        form.setFields([{ name: 'newMobile', errors: [errMsg], warnings: [`errorCode.${_code}`] }])
    }

    const MobileDiffRules = {
        whitespace: true,
        validator: async (_: any, value: any) => {
            if (value === null || value === undefined || value?.length === 0) return Promise.resolve() // 有值才校验
            const oldValue = form.getFieldValue('mobile')
            const oldNumberValue = oldValue.number // 区号
            const oldCodeValue = oldValue.code // 手机号
            // console.log('oldValue',oldValue)
            // console.log('value',value)
            // console.log('oldNumberValue',oldNumberValue)
            // console.log('oldCodeValue',oldCodeValue)
            if (oldNumberValue === value?.number && oldCodeValue?.value === value?.code?.value) {
                return Promise.reject('新手机号不能和旧手机号相同');
            }
            return Promise.resolve();
        }
    }


    return (
        <div className="setting-edit-mobile">
            <Modal
                className={`setting-edit-mobile-modal border no-padding step${step} ${panel}`}
                title={panel === 'bind' ? t('setting.bind-mobile') : t('setting.change-mobile')}
                open={true}
                onOk={onOk}
                onCancel={onCancel}
                mask={true}
                maskClosable={true}
                centered={true}
                width={450}
                okText={panel === 'bind' || step === 2 ? t('app.ok') : t('login.continue')}
                cancelText={t("app.cancel")}
                cancelButtonProps={{ variant: "filled", color: "default" }}
                okButtonProps={{ disabled: !isOkBtnAble }}
                footer={undefined}
            >
                <Form
                    form={form}
                    className="setting-edit-mobile-form common-form mediumn"
                    name="basic"
                    autoComplete="off"
                    layout="vertical"
                    onValuesChange={onFiledValueChange}
                    validateTrigger="onBlur"
                    requiredMark={false}
                    initialValues={{
                        email: '',
                        mobile: {
                            number: '', // 手机号
                            code: { label: '', value: '' }// 区号 label: 1, value: 'US'
                        },
                        code: '',
                        newMobile: {
                            number: '', // 手机号
                            code: { label: '', value: '' }// 区号 label: 1, value: 'US'
                        },
                        newCode: ''
                    }}
                >
                    { // 绑定手机号
                        panel === 'bind' ?
                            <>

                                {/*新手机 */}
                                <Form.Item name="newMobile"
                                    shouldUpdate label={t('setting.new-mobile')} help={null} rules={MobileRules(t)}
                                    className={classnames(['mobile-form-item'])}>
                                    <MobileInput onValidte={onValidteNewMobile} height={34} />
                                </Form.Item>

                                {/* 验证码 */}
                                <Form.Item name="newCode"
                                    label={t('login.verify-code')} rules={VerifyCodeRules(t)}>
                                    <VerifyCode
                                        id="edit-mobile-code1"
                                        step={step}
                                        api={api_get_mobile_code}
                                        scene={'MEMBER_UPDATE_MOBILE'}
                                        target={'newMobile'}
                                        mobile={form.getFieldValue("newMobile")}
                                        targetValidate={form.validateFields}
                                        onError={onVerifyCodeError2}
                                    />
                                </Form.Item>
                            </>
                            :
                            <>
                                { // 修改手机号
                                    step === 1 ?
                                        <>
                                            <Tabs className="common-tab without-border" activeKey={accountType} defaultActiveKey="Email"
                                                items={TabItems?.map((item: any) => ({
                                                    key: item.key,
                                                    label: t(`setting.${item.label}`), // 确保这里使用翻译函数
                                                    children: item.children,
                                                }))} onChange={onChangeTab} />
                                            {/* 邮箱 */}
                                            <Form.Item name="email"
                                                label={t('login.email-address')} rules={[{ required: true, whitespace: true, message: '' }, ...EmailRules(t)]}
                                                className={accountType == 'Mobile' ? 'hidden' : ''}>
                                                <Input disabled autoComplete="new-email" variant="filled" maxLength={300} placeholder={t('login-pls.email') as string} />
                                            </Form.Item>
                                            {/* 手机号 */}
                                            <Form.Item shouldUpdate name="mobile" label={t('login.mobile')} help={null} rules={MobileRules(t)}
                                                className={classnames(['mobile-form-item', { hidden: accountType == 'Email' }])}>
                                                <MobileInput disabled onValidte={onValidteMobile} height={34} />
                                            </Form.Item>
                                            {/* 验证码 */}
                                            <Form.Item name="code"
                                                className={classnames(["psd-form-item"])} label={t('login.verify-code')} rules={VerifyCodeRules(t)}>
                                                <VerifyCode
                                                    step={step}
                                                    id="edit-mobile-code2"
                                                    api={accountType == 'Email' ? api_get_email_code : api_get_mobile_code}
                                                    scene={scence1[accountType]}
                                                    target={accountType.toLocaleLowerCase()}
                                                    {
                                                    ...
                                                    (accountType == 'Email' ? { email: form.getFieldValue("email") } : { mobile: form.getFieldValue("mobile") })
                                                    }
                                                    targetValidate={form.validateFields}
                                                    onError={onVerifyCodeError1}
                                                />
                                            </Form.Item>
                                        </> : null
                                }
                                {
                                    step === 2 ? <>
                                        {/*新手机 */}
                                        <Form.Item name="newMobile"
                                            shouldUpdate label={t('setting.new-mobile')} help={null} rules={[...MobileRules(t)]}
                                            className={classnames(['mobile-form-item'])}>
                                            <MobileInput onValidte={onValidteNewMobile} height={34} />
                                        </Form.Item>

                                        {/* 验证码 */}
                                        <Form.Item name="newCode" className={classnames(["psd-form-item"])} label={t('login.verify-code')} rules={VerifyCodeRules(t)}>
                                            <VerifyCode
                                                id="edit-mobile-code3"
                                                step={step}
                                                api={api_get_mobile_code}
                                                scene={scence2}
                                                target={'newMobile'}
                                                mobile={form.getFieldValue("newMobile")}
                                                targetValidate={form.validateFields}
                                                onError={onVerifyCodeError2}
                                            />
                                        </Form.Item>
                                    </> : null
                                }
                            </>
                    }
                </Form>
            </Modal>

        </div>
    )
})

const GetMobileFormValue = (mobileStr: string) => {
    try {
        const [code, number] = mobileStr.split('-')
        const codeLabel: any = code.slice(1)// 去除+号
        // console.log('number', number)
        // console.log('codeLabel', codeLabel)
        return { number: number, codeLabel: parseInt(codeLabel) }
    } catch (error) {
        return {}
    }
}

const mapStateToProps = (state: any) => ({
    userInfo: state.app.userInfo,
})
const mapDispatchToProps = (dispatch: any) => ({
    // dispatch_api_: (data:any) => dispatch(dispatch_api_(data))
})
const ConnectedCounter = connect(mapStateToProps, mapDispatchToProps, null, { forwardRef: true })(EditMobile);
export default ConnectedCounter;
export { GetMobileFormValue };