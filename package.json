{"name": "eddid-gpt-ama", "version": "3.11.0", "releaseVersion": "3.1.5", "private": true, "scripts": {"dev": "webpack serve --mode development", "start": "set PORT=3021 && node scripts/start.js", "start:test": "set PORT=3021 && node scripts/starttest.js", "build:dev": "cross-env NODE_ENV=development node scripts/build.js", "build:test": "cross-env NODE_ENV=test node scripts/build.js", "build:prod": "cross-env NODE_ENV=production node scripts/build.js", "build:main": "cross-env NODE_ENV=production node scripts/build.js webpack --env entry=main", "build:widget": "cross-env NODE_ENV=production node scripts/build.js webpack --env entry=widget", "build:all": "npm run build:main && npm run build:widget", "test": "node scripts/test.js"}, "dependencies": {"@arco-design/color": "^0.4.0", "@babel/core": "^7.16.0", "@fvilers/disable-react-devtools": "^1.3.0", "@openim/client-sdk": "^0.0.12", "@openim/wasm-client-sdk": "^3.8.3-patch.3", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.3", "@svgr/webpack": "^5.5.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/crypto-js": "^4.1.1", "@types/jest": "^27.5.2", "@types/node": "^16.18.31", "@types/react": "^18.2.6", "@types/react-dom": "^18.2.4", "@types/react-router-dom": "^5.3.3", "@types/redux-logger": "^3.0.9", "antd": "^5.22.5", "antd-img-crop": "^4.25.0", "axios": "^1.4.0", "babel-jest": "^27.4.2", "babel-loader": "^8.2.3", "babel-plugin-named-asset-import": "^0.3.8", "babel-preset-react-app": "^10.0.1", "bfj": "^7.0.2", "browserslist": "^4.18.1", "camelcase": "^6.2.1", "case-sensitive-paths-webpack-plugin": "^2.4.0", "classnames": "^2.3.2", "copy-to-clipboard": "^3.3.3", "cross-env": "^7.0.3", "crypto-js": "^4.1.1", "css-loader": "^6.5.1", "css-minimizer-webpack-plugin": "^3.2.0", "dotenv": "^10.0.0", "dotenv-expand": "^5.1.0", "eslint": "^8.3.0", "eslint-config-react-app": "^7.0.1", "eslint-webpack-plugin": "^3.1.1", "exceljs": "^4.4.0", "file-loader": "^6.2.0", "fs-extra": "^10.0.0", "html-webpack-plugin": "^5.5.0", "i18next": "^22.4.15", "i18next-browser-languagedetector": "^7.0.1", "i18next-http-backend": "^2.2.0", "identity-obj-proxy": "^3.0.0", "jest": "^27.4.3", "jest-resolve": "^27.4.2", "jest-watch-typeahead": "^1.0.0", "katex": "^0.16.19", "lottie-react": "^2.4.0", "mathjs": "^11.8.0", "mini-css-extract-plugin": "^2.4.5", "moment": "^2.29.4", "node-forge": "^1.3.1", "pinyin-pro": "^3.26.0", "postcss": "^8.4.4", "postcss-flexbugs-fixes": "^5.0.2", "postcss-loader": "^6.2.1", "postcss-normalize": "^10.0.1", "postcss-preset-env": "^7.0.1", "prompts": "^2.4.2", "react": "^18.2.0", "react-app-polyfill": "^3.0.0", "react-cropper": "^2.3.3", "react-dev-utils": "^12.0.1", "react-dom": "^18.2.0", "react-i18next": "^12.2.2", "react-infinite-scroll-component": "^6.1.0", "react-markdown": "^8.0.7", "react-redux": "^8.0.5", "react-refresh": "^0.11.0", "react-router-dom": "^6.11.1", "react-syntax-highlighter": "^15.5.0", "redux": "^4.2.1", "redux-devtools-extension": "^2.13.9", "redux-logger": "^3.0.6", "redux-thunk": "^2.4.2", "rehype-katex": "^7.0.1", "remark-gfm": "^3.0.1", "remark-math": "^5.1.1", "resolve": "^1.20.0", "resolve-url-loader": "^4.0.0", "sass": "^1.62.1", "sass-loader": "^12.3.0", "semver": "^7.3.5", "source-map-loader": "^3.0.0", "style-loader": "^3.3.1", "suneditor": "^2.47.6", "suneditor-react": "^3.6.1", "tailwindcss": "^3.0.2", "terser-webpack-plugin": "^5.2.5", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "webpack": "^5.64.4", "webpack-cli": "^6.0.1", "webpack-dev-server": "^4.6.0", "webpack-manifest-plugin": "^4.0.2", "workbox-webpack-plugin": "^6.4.1"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "test": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/lodash": "^4.17.16", "@types/react-syntax-highlighter": "^15.5.7", "@types/webpack-env": "^1.18.5", "http-proxy-middleware": "^2.0.6", "svg-sprite-loader": "^6.0.11", "svgo-loader": "^4.0.0"}, "jest": {"roots": ["<rootDir>/src"], "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts"], "setupFiles": ["react-app-polyfill/jsdom"], "setupFilesAfterEnv": ["<rootDir>/src/setupTests.ts"], "testMatch": ["<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}", "<rootDir>/src/**/*.{spec,test}.{js,jsx,ts,tsx}"], "testEnvironment": "jsdom", "transform": {"^.+\\.(js|jsx|mjs|cjs|ts|tsx)$": "<rootDir>/config/jest/babelTransform.js", "^.+\\.css$": "<rootDir>/config/jest/cssTransform.js", "^(?!.*\\.(js|jsx|mjs|cjs|ts|tsx|css|json)$)": "<rootDir>/config/jest/fileTransform.js"}, "transformIgnorePatterns": ["[/\\\\]node_modules[/\\\\].+\\.(js|jsx|mjs|cjs|ts|tsx)$", "^.+\\.module\\.(css|sass|scss)$"], "modulePaths": [], "moduleNameMapper": {"^react-native$": "react-native-web", "^.+\\.module\\.(css|sass|scss)$": "identity-obj-proxy"}, "moduleFileExtensions": ["web.js", "js", "web.ts", "ts", "web.tsx", "tsx", "json", "web.jsx", "jsx", "node"], "watchPlugins": ["jest-watch-typeahead/filename", "jest-watch-typeahead/testname"], "resetMocks": true}, "babel": {"presets": ["react-app"]}}