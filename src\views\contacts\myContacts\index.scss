@import '../../../styles/mixin.scss';
.my-contact-area{
    .area-title{
        margin-bottom: 0;
    }
    .user-list-con{
        padding: 16px;
        flex: 1;
        overflow: auto;
        .user-list{
            flex: 1;
            min-height: 100%;
            // overflow: hidden;
        }
    }
    .initial-header{
        @include font($line-height:22px,$color:var(--colorTextTertiary));
        flex-shrink: 0;
    }
    .user-group{
        width: 100%;
        &:last-of-type{
           padding-bottom: 24px;
        }
    }
    .user-items{
        width: 100%;
        overflow: hidden;
    }
    .user-item{
        // margin-right: 20px;
        max-width: calc(100%);
        padding: 6px 8px;
        cursor: pointer;
        .user-item-detail{
            max-width: 100%;
        }
        .ui-avatar{
            flex-shrink: 0;
            cursor: pointer;
        }
        .ui-name{
            // flex:1;
            cursor: pointer;
            max-width: 700px;
            @include font($line-height:22px);
            @include ellipsis()
        }
        &:hover{
            border-radius: 12px;
            background: var(--colorFillTertiary);
        }
    }
    .anchor {
		width: 13px;
		position: absolute;
		right: 0;
		top: 0;
		z-index: 20;
		position: sticky;
        @include font($line-height:22px);
        padding-inline-start:0;
        .ant-anchor::before{
            border-inline-start:0;
        }
        .ant-anchor-ink{
            height: 0;
            display: none!important;
        }
        .ant-anchor-wrapper .ant-anchor .ant-anchor-link {
            padding-block: 0;
            padding-inline: 0;
        }
        .ant-anchor-link{
            margin-bottom: 8px;
            .ant-anchor-link-title{
                @include font(12px,18px,400,var(--colorTextTertiary));
            }
            &.ant-anchor-link-active .ant-anchor-link-title{
                color: var(--colorPrimary);
            }
        }
	}
}