@import "../../../../styles/mixin.scss";
.enhance-wraps {
  flex: 1;
  overflow: auto;
  padding-right: 5px;
}

.enhance-modal {
  width: 1000px;
  height: 90%;
  overflow: hidden;
  top: 40px;
  margin-top:10px;
  margin-bottom:10px;

  .enhance-modal-wrap {
    background: var(--colorBgElevated);
    padding: 16px 16px 0 16px;
    border-radius: 12px;
    position: relative;
    width: 1000px;
    display: flex;
    flex-direction: column;
    height: 100%;
   
    .enhance-modal-title {
      position: relative;
      display: flex;
      margin-bottom: 18px;
      height: 22px;

      .title-pre {
        font-weight: 600;
        font-size: 16px;
        color: var(--colorText);
        line-height: 22px;
        text-align: left;
      }

      .title-icon {
        width: 20px;
        height: 20px;
        vertical-align: text-bottom;
        margin-top:1px;
        margin-left:8px;
        border-radius: 50%;
      }

      .title-gpt {
        margin-left:8px;
        
        font-weight: 600;
        font-size: 16px;
        color: var(--colorText);
        line-height: 22px;
      }

      .title-end {
        margin-left:8px;
        
        font-weight: 600;
        font-size: 16px;
        color: var(--colorText);
        line-height: 22px;
      }

      .title-close {
        margin-left: auto;
        // margin-right:10px;
        height: 20px;
        width: 20px;
        cursor: pointer;
      }
    }

    .enhance-wrap {
      display: flex;
      margin:10px 0;
      position: relative;

      .TextArea-wrap {
        padding: 0;
        border-width: 0px;
      }

      .ant-input:focus, .ant-input-focused，.ant-input:hover {
        border-color: transparent;
        box-shadow: none;
      }

      .ant-input-disabled, .ant-input[disabled]{
        cursor: auto;
      }

      .checkbox-wrap {
        margin-top: auto;
        margin-bottom: auto;
        margin-right: 13px;
        .ant-checkbox-inner {
          border-width: 2px;
          width: 16px;
          height: 16px;
        }
      }

      .ant-checkbox-wrapper {
        margin-bottom: 8px;
      }

      .Card-wrap {
        flex: 1;

        .ant-card:hover {
          // background: var(--colorPrimaryBg);
        }

        .ant-card {
          border-radius: 10px;
          padding: 10px;
          border: 1px solid var(--colorBorderSecondary);
          box-shadow: none;
          background: var(--colorBgElevated);

          .ant-card-body {
            padding: 0px;
            border-radius: 0 0 0px 0px;
          }

          .file-info {
            display: flex;
            align-items: center;

            img {
              width: 16px;
              height: 16px;
            }

            span {
              margin-left: 8px;
              
              font-weight: 600;
              font-size: 15px;
              color: var(--colorText);
              line-height: 24px;
            }
          }

          .TextArea-wrap {
            margin-top: 8px;
            border: 0;
            background: transparent;

            
            font-weight: 400;
            font-size: 14px;
            color: var(--colorText);
            line-height: 24px;
          }

          &.nochecked {
            .file-info {
              span {
                color: var(--colorTextTertiary);
              }
            }

            .ant-input {
              color: var(--colorTextTertiary);
            }
          }

          &.edit {
            background: var(--colorPrimaryBg);
            .operate .a-item {
              background: var(--colorBgElevated);
            }
          }

          &.nochecked.edit {
            .file-info {
              span {
                color: var(--colorText);
              }
            }

            .ant-input {
              color: var(--colorText);
            }
          }

          .operate {
            display: flex;
            gap:0 20px;
            margin-top: 12px;
            align-items: center;

            .ol {
              display: flex;
              align-items: center;
              gap: 0 16px;
              height: 18px;
              span {
                
                font-weight: 400;
                font-size: 12px;
                color: var(--colorTextTertiary);
                line-height: 18px;
              }
            }

            .or{
              margin-left: auto;
              display: flex;
              justify-content: flex-end;
              gap:12px;
              .a-item {
                height: 32px;
                min-width: 60px;
                padding: 0 16px;
                border-radius: 8px;
                border: 1px solid var(--colorPrimaryBorder);
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                background: var(--colorBgBase);

                span {
                  font-weight: 500;
                  font-size: 14px;
                  color: var(--colorPrimaryText);
                  line-height: 22px;
                }
              }
            }
          }
        }
      }
    }

    .check-agree-enhance-btn {
      display: flex;
      align-items: center;
      padding: 12px 0 20px;
      flex-shrink: 0;
      .check-agree-enhance {
        margin-top: 0;
        margin-bottom: 0;
        // span {
        //   font-weight: 400;
        //   font-size: 12px;
        //   color: var(--colorText);
        //   line-height: 16px;
        // }
      }
      .ant-checkbox-inner{
        // width: 18px;
        // height: 18px;
        // border-radius: 50%;
        // border-color: var(--colorTextSecondary);
        // border-width: 2px;
      }

      .btn-wrap {
        margin-top: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 0;
        margin-right: 0px;
        margin-left: auto;
        gap:12px;
      }
    }

    // .ant-checkbox-checked:after {
    //   border: 2px solid var(--colorPrimary);
    //   border-radius: 50%;
    // }

    // .ant-checkbox-wrapper:not(.ant-checkbox-wrapper-disabled):hover .ant-checkbox-checked:not(.ant-checkbox-disabled):after,
    // .ant-checkbox-wrapper:not(.ant-checkbox-wrapper-disabled):hover .ant-checkbox-inner,.ant-checkbox:not(.ant-checkbox-disabled):hover .ant-checkbox-inner,
    // .ant-checkbox:not(.ant-checkbox-disabled):hover .ant-checkbox-inner,
    // .ant-checkbox-checked:not(.ant-checkbox-disabled):hover .ant-checkbox-inner {
    //   border-color: var(--colorPrimary);
    // }

    // .ant-checkbox-wrapper:not(.ant-checkbox-wrapper-disabled):hover .ant-checkbox-checked:not(.ant-checkbox-disabled) .ant-checkbox-inner,
    // .ant-checkbox-checked .ant-checkbox-inner {
    //   background-color: var(--colorPrimary);
    //   border-color: var(--colorPrimary);
    // }

    // .ant-checkbox .ant-checkbox-input:focus-visible+.ant-checkbox-inner {
    //   outline: 4px solid var(--colorPrimary);
    // }
  }

  // antd升级后
  >div {
    height: 100%;
  }
}