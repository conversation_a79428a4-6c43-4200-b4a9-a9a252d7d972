
@import "./mixin";
button, input, textarea {
  outline: none;
  border: 0;
}

body {
  overflow-y: hidden;
  font-family:var(--font-family);
  @include globalWebkitScrollStyle;
}

#root {
  height: 100%;
  width: 100%;
  min-width: 1150px;
}

@import "./views/common.scss";
@import "./component/nav";
@import "./component/antd";

@import "./views/robot";
@import "./views/landing";
@import "./views/login";
@import "./views/error";


// @media only screen and (max-width: 1601px) {
//   .home-wrapper .home-center {
//     padding: 0 65px;
//   }
// }

// @media only screen and (max-width: 1441px) {
//   .home-wrapper .home-center {
//     padding: 0 18px;
//   }
// }

// @media only screen and (max-width: 1025px) {
//   .home-wrapper .home-center {
//     padding: 0 18px;
//   }
// }
.gray-bg{
  background: var(--colorBgLayout);
}
mark{
  padding: 1px;
  background-color: var(--colorBgMark);
}