import React, { useEffect, useState, forwardRef, useImperativeHandle } from "react";
import { connect } from "react-redux";
import i18next from "i18next";
import classNames from "classnames";
import SvgIcon from "@/components/svgicon"
import { Flex, Button, Table, Modal, message, Empty, Dropdown, Tag, Input, Form, Segmented } from "antd";
import { ColumnProps } from "antd/es/table";
import type { MenuProps } from 'antd';
import { useTranslation } from "react-i18next";
import FormUtils from '@/views/common/formUtils'
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { oneLight, oneDark } from "react-syntax-highlighter/dist/esm/styles/prism";
import defaultLogo from "@/assets/bot_default_avatar.png";
import { $isNull, $trim } from "@/utils/common";
import CopyToClipboard from 'copy-to-clipboard';

import './index.scss'
import '../common/style/index.scss'
const { TextArea } = Input;



// 基本信息-iframe
const RobotIFrame = forwardRef((props: any, ref: any) => {

  /** 自定义暴露给父组件的实例值 **/
  useImperativeHandle(ref, () => ({
    on_form_is_dirty() {
      return isDirty
    }
  }))

  const { robotInfo, themeInfo } = props
  const { theme } = themeInfo

  const { t } = useTranslation()
  const [isSubmit, setIsSubmit] = useState(false) // 提交中
  const [robotLogo, setRobotLogo] = useState('') // 有信息变更
  const [initialValues, setInitialValues] = useState<any>({}); // 表单值原始值
  const [form] = Form.useForm(); // 息表单，可编辑
  const [formValues, setFormValues] = useState<any>({}); // 表单值，可编辑
  const [isDirty, setIsDirty] = useState<any>(false); // 用于跟踪表单是否已修改

  const [integrationCode, setIntegrationCode] = useState(
    '<iframe width="100%" height="100%" allow="microphone *" src="https://www.vama.services/widget/eembtledigcccgflsbuti3i/chat.html"></iframe>')

  // 外观主题设置项
  const appearanceOptions: any[] = [
    { label: t('robot-iframe.light-mode'), value: "light", key: "light" },
    { label: t('robot-iframe.dark-mode'), value: "dark", key: "dark" },
  ];
  const [previewInfo, setPreviewInfo] = useState<any>({}); // 预览信息

  // 初始化
  useEffect(() => {
    console.log('robotInfo', robotInfo)
    if (robotInfo) init()
  }, [robotInfo])

  const init = () => {
    const { name, introduction_message } = robotInfo || {}
    const initValues:any = {
      name, 
      introduction: introduction_message,
      appearance: 'light' //TODO
    }
    Object.keys(initValues).forEach((key:any)=>{
      form.setFieldValue(key, initValues[key])
    })
    setInitialValues({ ...initValues })
    setPreviewInfo({ ...initValues })
    // 获取机器人头像
    const logo = robotInfo?.logo || defaultLogo
    setRobotLogo(logo)
  }

  // 提交保存
  const onSave = async () => {
    const values: any = form.getFieldsValue()
    const { name, introduction, appearance } = values
    console.log('values', values)
  };

  // 表单项 输入改变事件
  const onFiledValueChange = (changedFields: any, allValues: any) => {
    try {
      // console.log('changedFields', changedFields)
      // console.log('allValues',allValues)
      setFormValues({ ...formValues, ...allValues });
      Object.keys(changedFields).forEach((fieldName: any) => {
        const fieldError = form?.getFieldError(fieldName)
        if (fieldError.length > 0) FormUtils.clearValidate(form, fieldName)
      })
      const currentValues = JSON.stringify(allValues)
      const initValues = JSON.stringify(initialValues)
      // console.log('initValues',initValues)
      // console.log('currentValues',currentValues)
      const hasChanges = initValues!==currentValues
      setIsDirty(hasChanges)
    } catch (error) {

    }
  }

  // 修改皮肤
  const onChangeAppearance = (appearance: any) => {
    setPreviewInfo({ ...previewInfo,appearance })
  }
  
  // onBlur
  const onBlur = (prop: any) => {
    console.log('prop',prop)
    let value = form.getFieldValue(prop)
    value = $trim(value)
    // if ($isNull(value)){
    //   form.setFieldValue(prop, initialValues[prop])
    //   return
    // }
    setPreviewInfo({...previewInfo,[prop]:value})
  }
  

  const renderIframeHtml = (code: any) => {
    const html = `
    ${code}
    `
    // console.log('html',html)
    return html
  }

  const onCopy = ()=>{
    CopyToClipboard(integrationCode)
    message.success(t("chat.copy-success"))
  }

  return (
    <Flex className="robot-iframe-wrap">
      <div className="robot-iframe-left">
        <Flex className="ifram-nav" justify="space-between" align="center">
          <div className="iframe-title">{t('system-setting')}</div>
          <Button loading={isSubmit} type="primary" onClick={onSave} disabled={isSubmit || !isDirty}> {t('setting.save-change')}</Button>
        </Flex>
        <div className="iframe-card">
          <div className="ic-title">{t('robot-iframe.inter-code')}</div>
          <Flex className="ic-html" justify="space-between" align="center">
            <div>html</div>
            <Flex onClick={onCopy} className="html-copy" align="center" gap={4}>
              <SvgIcon className="team-selected" svgName="copy" />
              {t('app.copyed')}
            </Flex>
          </Flex>
          <div className="iframe-code">
            {/* {integrationCode} */}
            <SyntaxHighlighter
              style={oneLight}
              customStyle={{ background: "transparent" }}
              language={'html'}
              PreTag="div"
            >
              {integrationCode}
            </SyntaxHighlighter>
          </div>
        </div>
        <div className="iframe-card">
          <div className="ic-title border">{t('appearance')}</div>
          <Form
            form={form}
            className="setting-profile-form common-form mediumn"
            name="basic"
            autoComplete="off"
            layout="vertical"
            onValuesChange={onFiledValueChange}
            validateTrigger="onBlur"
            requiredMark={false}
            initialValues={{
              name: '',
              introduction: '',
              appearance: "light" // 外观 默认亮色
            }}
          >
            {/* Plugin Name */}
            <Form.Item shouldUpdate name="name" label={t('robot-iframe.plugin-name')} help={null}>
              <Input placeholder={t('common.please-input') as string} variant="filled" maxLength={200}
                onBlur={()=>onBlur('name')} />
            </Form.Item>
            {/* Plugin Introduction */}
            <Form.Item shouldUpdate name="introduction" label={t('robot-iframe.plugin-introduction')}>
              <TextArea
                maxLength={2000} placeholder={t('common.please-input') as string}
                // onChange={(e: any) => onChangeInputValue(e)}
                // value={inputValue}
                onBlur={()=>onBlur('introduction')}
                variant="filled"
                autoSize={{ minRows: 2, maxRows: 7 }}
              />
            </Form.Item>
            <Form.Item shouldUpdate name="appearance" label={t('appearance')}>
              <Segmented<string>
                className="common-segmented"
                options={appearanceOptions.map(i => {
                  // i.label = t(i.label)
                  return i
                })}
                onChange={onChangeAppearance}
              />
            </Form.Item>
          </Form>
        </div>
      </div>
      <div className="robot-iframe-right">
        <Flex className="ifram-nav" justify="space-between" align="center">
          <div className="iframe-title">{t('setting.preview')}</div>
        </Flex>

        <Flex className={`preview-con ${previewInfo.appearance}`} vertical>
          <Flex gap={12} vertical justify="center" className="preview-text">
            <div className="preview-icon">
              <img src={robotLogo} alt="" />
            </div>
            <div className="preview-title">{previewInfo.name}</div>
            {
              $isNull(previewInfo.introduction) ? null :
                <div className="preview-desc">
                  {previewInfo.introduction}
                </div>
            }
          </Flex>
          <Flex className="preview-input">
            <div className="preview-input-placeholder">{t('robot-iframe.talk-to',{robot: previewInfo.name})}</div>
            <Flex className="preview-input-enter-img" align="center" justify="center">
              <SvgIcon draggable="false" svgName="icon_send" />
            </Flex>
          </Flex>
        </Flex>
      </div>
    </Flex>
  )
})

const mapStateToProps = (state: any) => ({
  userInfo: state.app.userInfo,
  themeInfo: state.app.themeInfo
});

const mapDispatchToProps = (dispatch: (e: any) => void) => ({
  // dispatch_api_create_api_key: (data = api_create_api_key_params) => dispatch(dispatch_api_create_api_key(data)),
});

export default connect(mapStateToProps, mapDispatchToProps, null, { forwardRef: true })(RobotIFrame);
