import * as types from "../constant";
import {
} from "../reducers";

export const change_app_globalLang = (globalLang: string) => ({
  type: types.CHANGE_APP_GLOBALANG,
  globalLang
});

export const change_app_userInfo = ({ isLogin, userInfo, token } : { isLogin: boolean, userInfo: any, token?: null|string }) => ({
  type: types.CHANGE_APP_USERINFO,
  isLogin,
  token,
  userInfo
});

// export const change_app_robot_list = (robotList: object[]) => ({
//   type: types.GET_APP_ROBOTLIST,
//   robotList
// });

// export const change_app_robot_type_list = (robotTypeList: any[]) => ({
//   type: types.GET_ROBOT_TYPE_LIST,
//   robotTypeList
// });

// export const change_app_robot_lim_model = (robotLlmModel: object[]) => ({
//   type: types.GET_ROBOT_LIM_MODEL,
//   robotLlmModel
// })

// export const change_app_embedding_model = (embeddingModelList: object[]) => ({
//   type: types.GET_EMBEDDING_MODEL_LIST,
//   embeddingModelList
// })

// export const change_app_phone_area_list = (phoneAreaList: object[]) => ({
//   type: types.GET_PHONE_AREA_LIST,
//   phoneAreaList
// })

// export const change_app_global_region = (globalRegion: string) => ({
//   type: types.CHANGE_APP_GLOBAL_REGION,
//   globalRegion
// });

// export const change_app_user_power = (userPower: string[]) => ({
//   type: types.GET_USER_POWER,
//   userPower
// });

// export const change_app_permission_map = (permissionMap: any) => ({
//   type: types.CHANGE_APP_PERMISSION_MAP,
//   permissionMap
// });

// export const change_app_themeInfo = (themeInfo: object ) => ({
//   type: types.CHANGE_APP_THEMEINFO,
//   themeInfo
// });

// export const change_app_loginInfo = (loginInfo: object ) => ({
//   type: types.CHANGE_APP_LOGIN_INFO,
//   loginInfo
// });

// // IP地区码
// export const change_app_ip_areacode = (code: any ) => ({
//   type: types.CHANGE_APP_IPAREA,
//   code
// });

// // 更新企业列表
// export const change_app_companies = (list: object[]) => ({
//   type: types.GET_APP_COMPANIES,
//   list
// });

// // 更新选中企业
// export const change_app_active_company = (id: any) => ({
//   type: types.CHANGE_APP_ACTIVE_COMPANY,
//   id
// });