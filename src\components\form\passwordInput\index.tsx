

import React, { useState, useEffect } from 'react';
import { Input, message, Form } from 'antd';
import { useTranslation } from 'react-i18next';
import Svgicon from '@/components/svgicon'
import classnames from "classnames";
import './index.scss'

// 密码输入框
const PasswordInput = (props: any) => {
    const { t, i18n } = useTranslation();
    const { id, value, onChange,placeholder } = props;
    // console.log('value',value,props)
    // const [value, setValue] = useState('')
    const [password, setPassword] = useState('')
    // const [rules, setRules] = useState<any[]>(props.rules || [])
    const [isShowPwd, setIsShowPwd] = useState(false)

    const triggerChange = (password: string) => {
        onChange?.(password);
    };

    const handleToggleEyePwd = () => {
        setIsShowPwd(!isShowPwd)
    }

    const onChangeInput = (e: any) => {
        const inputValue = e.target.value;
        // console.log('inputValue',inputValue)
        // 只允许英文大小写、数字、特殊字符
        const filteredValue = inputValue.replace(/[^a-zA-Z0-9!@#$%^&*~()_+\-=[\]{};':"\\|,.<>/?]/g, '');
        // console.log('filteredValue',filteredValue)
        setPassword(filteredValue);
        triggerChange(filteredValue);

        // if (props?.onChange) props?.onChange(filteredValue)
    }
    const onKeyUp = (e: any) => {
        if (props?.onKeyUp) props?.onKeyUp(e)
    }

    // useEffect(() => {
    //     // console.log('props.value',props.value)
    //     if (props.value) setValue(props.value)
    // }, [props.value])

    // useEffect(() => {
    //     // console.log('props.value',props.value)
    //     if (rules) setRules([...rules,...props_rule])
    // }, [props_rule])

    

    /* 输入框规则：
    *1.支持输入英文大小写、数字、特殊字符，其他输入无效，最长支持18个字符。
    *2.6-18个字符 至少包含 数字 和 英文 （Password must be 6-18 characters with letters and numbers.）
    */
    return (
        <div className='password-input' id={id}>
            <Input
                value={value?.password || password}
                autoComplete="new-password"
                onKeyUp={(e) => onKeyUp(e)}
                onChange={(e: any) => onChangeInput(e)}
                onBlur={props.onBlur} // 关键！确保 onBlur 被触发
                className="ipt" 
                type={isShowPwd ? "text" : "password"} 
                variant="filled" 
                maxLength={18} 
                placeholder={(placeholder || t('login-pls.password'))  as string} />
            <Svgicon svgName={isShowPwd ? "eye_open" : "eye_closed"} className={"input-suffixicon"} onClick={handleToggleEyePwd} />
         </div>
    )
}
export default PasswordInput