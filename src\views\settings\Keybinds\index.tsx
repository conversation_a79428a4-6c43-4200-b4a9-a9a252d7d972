import { connect } from 'react-redux';
import { Button, Flex, Input, message } from 'antd';
import { useState, useEffect } from "react";
import { useTranslation } from 'react-i18next';
import './index.scss';

const resource = [
  {
    name: 'setting.send-message',
    keys: ['Enter']
  },
  {
    name: 'setting.new-line-input',
    keys: ['Shift', 'Enter']
  },
  {
    name: 'setting.stop-generating',
    keys: ['ESC']
  }
]

// 快捷键
const Keybinds = (props: any) => {
  const { t } = useTranslation();


  useEffect(() => {
  }, [])

  return (
    <Flex vertical className="keybinds-wrapper wrapper">
      <div className="title">{t('setting.keybinds')}</div>
      <div className="desc">{t('setting.keybind-desc')}</div>
      <div className="sub-title">{t('setting.ai-bot')}</div>
      <Flex className="keys-list" vertical gap={20}>
        {
          resource.map((i: any, index: number) => (
            <Flex className='key-item' key={index} gap={20}>
              <div className='key-name'>{t(i.name)}</div>
              <Flex className='key-btns' gap={8}>
                {
                  i.keys.map((k: string, kIndex: number) => (
                    <>
                      <div className='key-btn' key={kIndex}>
                        <div>{k}</div>
                      </div>
                      <div>{
                        kIndex !== i.keys.length - 1 ? '+' : ''
                      }
                      </div>
                    </>
                  ))
                }
              </Flex>
            </Flex>
          ))
        }
      </Flex>
    </Flex>
  );
};

const mapStateToProps = (state: any) => ({
});
const mapDispatchToProps = (dispatch: any) => ({
});
export default connect(mapStateToProps, mapDispatchToProps)(Keybinds);
