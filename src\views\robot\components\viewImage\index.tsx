import { useEffect, useState } from "react";
import {Modal,Flex, Button} from 'antd'
import Svgicon from '@/components/svgicon'
import { downloadImage } from '@/utils/download'
import './index.scss'
import i18n from "@/i18n";
/* 全屏查看图片，支持保存到本地*/ 
const ViewImage = ({ url, onClose,isCenter}:{ url:any,onClose?:any,isCenter?:boolean}) => {
    
    const [visible,setVisible] = useState(true);
    const [isLoading,setIsLoading] = useState(false);

    // 关闭弹窗
    const onCloseView=(e:any)=>{
        e?.stopPropagation()
        // setVisible(false)
        onClose()
    }

    // 保存至本地
    const onSaveImage= async()=>{
        if(isLoading) return
        let fileName = 'Image.jpg'
        if (url && url.lastIndexOf('/')!=-1) fileName = url.slice(url.lastIndexOf('/') + 1)
        // console.log('fileName', fileName)
        setIsLoading(true)
        await downloadImage(url, fileName)?.catch(()=> setIsLoading(false))
        setIsLoading(false)
    }

    return (
        <Modal
            width="100%"
            title=""
            maskClosable={false}
            open={visible}
            footer={null}
            closeIcon={null}
            className="view-image-modal"
            wrapClassName="view-image-modalwrap"
            centered={isCenter}
            style={isCenter?{}:{ top: 0 }}
        >
            <Flex vertical align="center">
                <Flex className="vi-top" gap={24} justify="flex-end" align="center">
                    <Button type="primary" icon={<Svgicon svgName="icon_save"/>} onClick={(e:any)=> onSaveImage()}>{i18n.t('saveToLocal')}</Button>
                    <Svgicon className="icon_close_view" svgName="icon_close" onClick={(e:any)=> onCloseView(e)}/>
                </Flex>
                <img src={url} alt="" />
            </Flex>
        </Modal>
    );
};

export default ViewImage;