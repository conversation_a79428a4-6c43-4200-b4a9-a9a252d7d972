import React from "react";
import { ConfigProvider, theme, App, message } from "antd";
import { connect } from "react-redux";
import 'moment/locale/zh-cn';
import localeCN from 'antd/es/locale/zh_CN';
import localeHK from 'antd/es/locale/zh_HK';
import localeUS from 'antd/es/locale/en_US';
import {RouterProvider} from "react-router-dom";
import router from "./routes";
import { refreshTheme , initThemeStaticVariables ,changeAutoThemeBySystem,getCSSVariablesValue} from "@/theme/index"
import { change_app_global_region, change_app_themeInfo } from "./store/action";
import {dispatch_api_get_configurations} from "@/api/platform";
import Svgicon from "@/components/svgicon"

// 初始化静态主题变量
initThemeStaticVariables();

// 全局配置 antd message 
message.config({
  maxCount: 1, // 设置最大显示数量
})


class Main extends React.Component<any, any> {
  private mediaQuery: any;
  
  constructor(props:any) {
    super(props);
    this.mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
}

  get antdThemeTokens(){
    const tokens = {...this.props.themeInfo}
    // 删除无效属性
    delete tokens.theme
    delete tokens.brandColor
    delete tokens.colorPrimarySecondary
    delete tokens.colorPrimaryContrasting
    return tokens
  }

  UNSAFE_componentWillMount () {
    this.props.change_app_global_region(["http://ama.eddid.com.hk", "https://ama.eddid.com.hk"].includes(window.location.origin) ? "hk" : "cn");
  }

  // 处理操作系统主题变化
  handleSystemThemeChange = async(e: MediaQueryListEvent) => {
    let newTheme:string =''
    if (e.matches) {
      // 用户选择了暗色模式
      console.log('当前操作系统主题：暗色模式');
      newTheme='dark'
    } else {
      // 用户选择了亮色模式
      console.log('当前操作系统主题：亮色模式');
      newTheme='light'
    }
    const base = {
      colorPrimarySecondary: this.props.themeInfo.colorPrimarySecondary,
      colorPrimaryContrasting:this.props.themeInfo.colorPrimaryContrasting
    }
    const result=changeAutoThemeBySystem(base,newTheme)
    if(!result) return
    const dynamicColors = {...this.props.themeInfo,...result?.themeColors, theme: result?.activeTheme}
    this.props.change_app_themeInfo(dynamicColors)
  };
  
  // 获取主题配置信息
  getSystemTheme= ()=>{
    refreshTheme()
  }

  getConfig = async()=>{
    // setIsLoading(true)
    await this.props.dispatch_api_get_configurations().catch((err:any)=>{
      console.log('err')
      // setIsLoading(false)
    })
  }

  componentDidMount(){
    // 删除无效localStorage 变量
    localStorage.removeItem('isLogin1')
    localStorage.removeItem('userInfo1')
    localStorage.removeItem('AMA_LOGIN_INFO')
    // 监听操作系统主题变化
    // console.log('this.mediaQuery.matches',  this.mediaQuery.matches)
    this.mediaQuery.addEventListener('change', this.handleSystemThemeChange);

    // 非邀请、登录、注册相关页面,获取主题配置
    const pathname = window.location.pathname
    const ignorePages = ['/invite', '/inviteSuccess','/login', '/register']
    // console.log('pathname',pathname)
    if (!ignorePages.includes(pathname)){
      // console.log('非邀请相关')
      this.getSystemTheme()
    }
  }

  componentWillUnmount(){
    // 清理监听器
    this.mediaQuery.removeEventListener('change', this.handleSystemThemeChange);
  }

  render() {
    const { globalLang,themeInfo } = this.props;
    const { defaultAlgorithm, darkAlgorithm} = theme;
    const themeActive = themeInfo.theme
    let locale: any = "";
    switch (globalLang) {
      case "zh":
        locale = localeCN;
        break;
      case "hk":
        locale = localeHK;
        break;
      case "en":
        locale = localeUS;
        break;
      default:
        locale = localeCN;
        break;
    }

    return (
      <ConfigProvider 
        locale={locale}
        wave={{disabled: true}} // 去除组件波纹效果
        modal={{
          closeIcon: <Svgicon svgName="icon_close"/>, // 自定义关闭图标
        }}
        drawer={{
          closeIcon: <Svgicon svgName="icon_close"/>, // 自定义关闭图标
        }}
        theme={{
          algorithm: themeActive ==="dark" ? darkAlgorithm:defaultAlgorithm,
          token: {
            ...this.antdThemeTokens,
            fontFamily: getCSSVariablesValue('--fontFamily')
          },
          components: {
            Menu: {
              itemBorderRadius: 12, // 设置菜单项的圆角
              // itemHoverBg: getCSSVariablesValue('--colorFillQuaternary')
            },
          },
        }}
      >
        {/* <OpenIMProvider> */}
          {/* <App> */}
          <RouterProvider router={router} />
        {/* </OpenIMProvider> */}
        {/* </App> */}
      </ConfigProvider>
    )
  }
}

const mapStateToProps = (state: any) => ({
  isLogin: state.app.isLogin,
  globalLang: state.app.globalLang,
  themeInfo: state.app.themeInfo
})


const mapDispatchToProps = (dispatch: any) => ({
  change_app_global_region: (globalRegion: string) => dispatch(change_app_global_region(globalRegion)),
  change_app_themeInfo: (themeInfo: object) => dispatch(change_app_themeInfo(themeInfo)),
  dispatch_api_get_configurations: () => dispatch(dispatch_api_get_configurations()),
})

export default connect(mapStateToProps, mapDispatchToProps)(Main);
