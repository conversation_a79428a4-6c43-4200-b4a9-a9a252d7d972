import HTTP from "@/utils/userRequest";
import * as actions from "@/store/action";
import { AxiosRequestConfig } from 'axios';

// 获取我所在的部门列表
export const api_get_my_direct_departments = (tenantId?: any, isSupervisor = false): Promise<unknown> => new Promise((resolve, reject) => {
    HTTP({
        method: 'get',
        url: '/system/dept/my-departments',
        params: isSupervisor ? { isSupervisor: true } : {},
        tenantId: tenantId ? tenantId : null
    } as AxiosRequestConfig).then(result => {
        const res = result?.data || {}
        resolve(res)
    }).catch(err => reject(err))
})

//获取所选的部门的子部门和直属人员列表
export const api_get_direct_depts_users = (params: any): Promise<unknown> => new Promise((resolve, reject) => {
    HTTP({
        method: 'get',
        url: `/system/dept/depts-users`,
        params
    } as AxiosRequestConfig).then(result => {
        const res = result?.data || {}
        resolve(res)
    }).catch(err => reject(err))
})

//获取部门中某个用户的名片
export const api_get_user_detail = (userTenantId: any): Promise<unknown> => new Promise((resolve, reject) => {
    HTTP({
        method: 'get',
        url: `/system/dept/users/${userTenantId}`,
    } as AxiosRequestConfig).then(result => {
        const res = result?.data || {}
        resolve(res)
    }).catch(err => reject(err))
})


// 获取部门面包屑
export const api_get_dept_paths = (deptId: any): Promise<unknown> => new Promise((resolve, reject) => {
    HTTP({
        method: 'get',
        url: `system/dept/${deptId}/crumbs`,
    } as AxiosRequestConfig).then(result => {
        const res = result?.data || {}
        resolve(res)
    }).catch(err => reject(err))
})

//获取公司所有用户信息
export const api_get_all_depts_users = (params: any): Promise<unknown> => new Promise((resolve, reject) => {
    HTTP({
        method: 'get',
        url: `/system/dept/users`,
        params
    } as AxiosRequestConfig).then(result => {
        const res = result?.data || {}
        resolve(res)
    }).catch(err => reject(err))
})


// 获取多个用户信息
export const api_get_multi_user_profile = (data: any): Promise<unknown> => new Promise((resolve, reject) => {
    HTTP({
        method: 'post',
        url: `system/user/profile/multi-user-profile`,
        data,
    } as AxiosRequestConfig).then(result => {
        const res = result?.data || {}
        resolve(res)
    }).catch(err => reject(err))
})


// 退出企业 
export const api_exit_tenant = (): Promise<unknown> => new Promise((resolve, reject) => {
    HTTP({
        method: 'delete',
        url: `/system/tenants/myself`,
    } as AxiosRequestConfig).then(result => {
        const res = result?.data || {}
        resolve(res)
    }).catch(err => reject(err))
})