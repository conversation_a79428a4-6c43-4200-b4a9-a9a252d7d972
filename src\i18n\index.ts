import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import Backend from "i18next-http-backend";
import LanguageDetector from "i18next-browser-languagedetector";
import en from "./locales/en.json";
import zh from "./locales/zh.json";
import hk from "./locales/hk.json";

import en_errorCode from "./locales/errorCode/en.json";
import zh_errorCode from "./locales/errorCode/zh.json";
import hk_errorCode from "./locales/errorCode/hk.json";



i18n.use(Backend).use(LanguageDetector).use(initReactI18next).init({
  fallbackLng: localStorage.getItem("i18nextLng") || 'en',
  lng: localStorage.getItem("i18nextLng") || 'en',
  debug: false,
  resources: {
    en: { translation: {...en,...en_errorCode} },
    zh: { translation: {...zh,...zh_errorCode} },
    hk: { translation: {...hk,...hk_errorCode} },
  },
  interpolation: {
    escapeValue: false,
  }
});

export default i18n;