import React, { Component, createRef, forwardRef } from "react";
import { connect } from "react-redux";
import suneditor from "suneditor";
import { SunEditorOptions } from "suneditor/src/options";
import { en } from "suneditor/src/lang";
import zh_hk from "./lang/zh_hk";
import zh_cn from "./lang/zh_cn";
import plugins from "suneditor/src/plugins";
// import CodeMirror from "codemirror";
import katex from "katex";
import { api_upload_image } from '@/api/information'
import SvgIcon from '@components/svgicon';
import i18next from "i18next";

import "suneditor/dist/css/suneditor.min.css";
// import "codemirror/mode/htmlmixed/htmlmixed";
// import "codemirror/lib/codemirror.css";

import "katex/dist/katex.min.css";
import "./index.scss"
import { Button, message, Upload } from "antd";
import { getFileTypeByName } from "@/utils/file";
// console.log('-----zh_hk', zh_hk)

interface Props {
  contents?: string;
  onBlur?: Function;
  onSave: Function;
  activeCompanyId: any
}

interface State {
  // lang: any,
  options: SunEditorOptions,
  options2: SunEditorOptions,
  uploadImageVisible: boolean,
  totalCharCount: number
}

const langMap: any = {
  en: en,
  zh: zh_cn,
  hk: zh_hk
}

const imageAccept = '.jpg,.jpeg,.png,.gif'
const imageSizeLimit = 10
const maxCharCount = 5000
// const MAX_IMAGES = 5;
// const MAX_VIDEOS = 2;

class Editor extends Component<any, State> {
  txtArea: any;
  editor: any;

  constructor(props: any) {
    super(props);
    this.txtArea = createRef();

    this.state = {
      options: {
        toolbarContainer: '#toolbar_container',
        placeholder: this.props.readOnly ? '' : i18next.t('common.please-input') as string,
        plugins: plugins,
        lang: langMap[props.globalLang] || en,
        // callBackSave: (contents: string) => this.props.onSave(contents),
        // codeMirror: CodeMirror,
        // stickyToolbar: 0,
        katex: katex,
        width: '100%',
        height: 'auto',
        maxHeight: this.props.readOnly ? 'auto' : '500px',
        // minHeight: this.props.readOnly ? 'auto': '500px',
        // value: this.props.contents,
        /* 图片配置 */
        imageUploadUrl: '', // 禁用默认上传URL
        // imageUploadSizeLimit: 1 * 1024 * 1024, // 图片大小限制1MB
        imageAccept: imageAccept, // 允许的文件类型
        imageMultipleFile: false,
        // imageUploadUrl: '/gateway/admin-api/infra/file/upload',
        imageUploadHeader: {
          'Authorization': localStorage.getItem('AMA_USER_TOKEN') as string || '',
          'Tenant-id': this.props.activeCompanyId,
        },
        imageUrlInput: false,
        // imageResizing: false,
        // imageHeightShow: false,
        // imageRotation: false,
        // imageAlignShow: false,

        /* 视频配置 */
        // videoUploadSizeLimit: 20 * 1024 * 1024, // 图片大小限制20MB
        videoRatio: 0.4285,
        videoTagAttrs: { controls: false, loop: true, autoplay: true, }, // loop:true, autoplay:true,
        // mediaAutoSelect: true,
        videoRatioList: [
          { name: i18next.t('app.default'), value: 0.4285 },
        ],
        // videoRotation: false,
        // videoHeightShow: false,
        // videoRatioShow: false,

        /* toolbar配置 */
        font: ['Arial', 'Comic Sans MS', 'Courier New', 'Impact', 'Georgia', 'tahoma', 'Trebuchet MS'], // , 'Verdana'
        fontSize: [
          8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 36, 48, 72
        ],
        formats: ['p', 'div', 'blockquote', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'],
        buttonList: [
          // default
          ['undo', 'redo'],
          ['font', 'fontSize', 'formatBlock'],
          ['bold', 'underline', 'italic', 'strike', 'subscript', 'superscript'],
          ['fontColor', 'hiliteColor', 'removeFormat'],
          ['align', 'list'],//, 'horizontalRule'
          ['table', 'link',
            'image'
            // {// 替换默认图片按钮为自定义按钮
            //   name: 'customImageUpload',
            //   // icon: '<i class="se-icon-image"></i>',
            //   // icon: <SvgIcon svgName="icon_image_upload"/>,
            //   icon: <img src={avatarDefault} alt="" />,
            //   title: '图片',
            //   action: this.handleDirectImageUpload
            //   // action: () => this.setState({uploadImageVisible: true})
            // }
            , 'video'],
          // ['fullScreen', 'showBlocks', 'codeView'],
          // ['preview', 'print']
        ],
        resizingBar: true,
        charCounter: true,
        charCounterLabel: i18next.t('info-hub.char-count') as string,
        maxCharCount: maxCharCount,
        // charCounterType:"byte-html",
      },
      uploadImageVisible: false,
      totalCharCount: 0,
      options2: {
        // // plugins: plugins,
        // font: ['Arial', 'Comic Sans MS', 'Courier New', 'Impact','Georgia','tahoma', 'Trebuchet MS', 'Verdana'],
        // fontSize: [
        //   8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 36, 48, 72
        // ],
        // paragraphStyles: [
        //   'spaced',
        //   'neon',
        //   {
        //     name: 'Custom',
        //     class: '__se__customClass'
        //   }
        // ],

        textStyles: [
          'translucent',
          // {
          //   name: 'Emphasis',
          //   style: '-webkit-text-emphasis: filled;'
          // }
        ],

        // width: '100%',
        // maxWidth: '600px',
        // minWidth: '400px',
        // height: 'auto',
        // videoWidth: '80%',
        // youtubeQuery: 'autoplay=1&mute=1&enablejsapi=1',
        // popupDisplay: 'local',
        // resizingBar: false,
        // buttonList: [
        //   ['font', 'fontSize'],
        //   ['fontColor', 'hiliteColor', 'video'],
        //   ['fullScreen', 'codeView'],
        //   ['preview', 'save']
        // ]

      }
    };
  }

  // 检验图片 格式和大小
  validateImage = (file: any) => {
    // 验证文件类型和大小
    const fileType = getFileTypeByName(file.name)
    const typeAccept = imageAccept.split(',')
    if (!typeAccept.includes(`.${fileType}`)) {
      message.error(i18next.t('info-hub.unsurpport-image-format'));
      return false
    }
    // console.log('file.size', file.size)
    if (file.size > imageSizeLimit * 1024 * 1024) {
      message.error({ content: i18next.t('info-hub.image-size-limit', { num: imageSizeLimit }) });
      return false
    }
    return true
  }

  // 图片上传
  handleImageUpload = async (file: any, info: any, core: any) => {
    // console.log('file', file)

    const isValid = this.validateImage(file)
    // console.log('isValid',isValid)
    if (!isValid) return

    // 上传到服务器
    const formData = new FormData();
    formData.append('file', file);

    const response: any = await api_upload_image(formData).catch(() => {

    })
    // const response = {
    //   data: '/vama-avatar/629fb1a88e68c5b0c738f515fd1e7f264a050c106a3881255fdc9c6d63dea461.gif'
    // }
    // console.log('response', response)
    // console.log('info', info)
    // console.log('core', core)
    if (response) {
      // const imageUrl = URL.createObjectURL(file);
      const align = info.align
      const baseUrl = "/gateway/app-api/system/notification/image?url=" // 后端要求的
      // console.log('align', align)
      const imageObj = `<img src="${baseUrl + response.filePath}" alt="${info.alt}" data-align="${align}"  data-file-name="${file.name}">`
      // console.log('imageObj', imageObj)
      this.editor.insertHTML(imageObj, true, true);
      // this.editor.insertImage(response.data, {
      //   name: file.name,
      //   size: file.size,
      //   element: null,
      //   alt: info.alt || '示例图片',       // 替代文本
      //   dataCustom: 'value'    // 自定义数据属性
      // });
    } else {
      // 上传失败
    }
  }

  // 视频上传
  handleVideoUpload = (state: any, info: any) => {
    // const contents = this.editor && this.editor?.getContents();
    // console.log('contents',contents)
    // const videos = this.editor.getFilesInfo('video');
    // console.log('videos',videos)
  }



  // 内容改变
  onChangeContent = (contents: any, core: any) => {
    try {
      // console.log('core',core)
      if (this.props.disabled || !this.editor) return
      // console.log('this.editor', this.editor)
      // 获取字符数
      const charCount = this.editor && this.editor?.getCharCount();
      const images = this.editor.getFilesInfo('image') || [];
      const videos = this.editor.getFilesInfo('video') || [];
      const filesInfo = [...images, ...videos]
      // console.log('----onChangeContent filedInfo', filesInfo)
      // console.log('----onChangeContent charCount', charCount)

      // const byteCount= 0
      // console.log('this.editor?.getCharCount', this.editor?.getCharCount) 
      // console.log('----charCount2', charCount2)
      this.props.onUpdate && this.props.onUpdate(contents, charCount, filesInfo)
      const isFullChar = charCount && charCount == maxCharCount
      this.setState({
        totalCharCount: charCount
      })
      if (isFullChar) {
        // console.log('到达上限')
        this.editor.toolbar.disable()
      } else {
        this.editor.toolbar.enable()
      }
    } catch (error) {
      console.log('error', error)
    }
  }

  // 设置编辑器状态：可编辑 or 只读
  setEditorStatus = () => {
    if (this.props.disabled) {
      // 编辑器只读
      // console.log('编辑器只读', this.editor)
      this.editor.disable()
      this.editor.readOnly(true)
      this.editor.toolbar.hide()

    } else {
      this.editor.enable()
      this.editor.readOnly(false)
      this.editor.toolbar.show()
    }
    this.editor.setOptions({
      // ...this.state.options,
      resizingBar: !this.props.disabled
    })
    // console.log('this.editor.', this.editor)
  }

  componentDidMount() {
    // if (this.editor) this.editor.core.history.reset(true);

    // console.log('this.props.disabled', this.props.disabled)
    const editor: any = this.editor = suneditor.create(this.txtArea.current, this.state.options);
    // console.log('editor', editor)
    this.setEditorStatus()
    this.editor.noticeClose();

    /**** 编辑器监听事件 start****/
    // 失去焦点
    editor.onBlur = () => {
      if (typeof this.props.onBlur === 'function') this.props.onBlur()
    }

    editor.uploadHandler = () => {
      // console.log('uploadHandler')
    }

    // // 粘贴事件
    // editor.onPaste = (e: any) => {
    //   this.handlePasteEvent(e)
    // }
    // 图片上传前
    editor.onImageUploadBefore = (files: any, info: any, core: any, uploadHandler: any) => {
      // console.log('onImageUploadBefore files', files)
      if (files[0]) this.handleImageUpload(files[0], info, core)
      return false
    }

    // 图片上传
    editor.onImageUpload = (targetElement: any, index: any, state: any, info: any, remainingFilesCount: any, core: any) => {
      // console.log('onImageUpload targetElement', targetElement)
      // console.log(`targetElement:${targetElement}, index:${index}, state('create', 'update', 'delete'):${state}`)
      // console.log(`info:${info}, remainingFilesCount:${remainingFilesCount}`)
    }

    editor.onVideoUploadBefore = (files: any, info: any, core: any, uploadHandler: any) => {
      // console.log('onVideoUploadBefore files', files)
      // return false
    }
    // 视频上传
    editor.onVideoUpload = (targetElement: any, index: any, state: any, info: any, remainingFilesCount: any, core: any) => {
      // console.log(`onVideoUpload targetElement:${targetElement}, index:${index}, state('create', 'update', 'delete'):${state}`)
      // console.log(`onVideoUpload info:${info}, remainingFilesCount:${remainingFilesCount}`)
      // this.handleVideoUpload(state, info)
    }

    // 编辑器内容更新
    editor.onChange = (contents: any, core: any) => {
      this.onChangeContent(contents, core)
    }
    // editor.onImageUpload = this.imageUpload.bind(this);
    // editor.onVideoUpload = videoUpload;

    /**** 编辑器监听事件 end****/
  }

  componentDidUpdate(prevProps: any) {
    // 内容改变
    if (this.props.contents !== prevProps.contents) {
      // console.log('this.props.contents',this.props.contents)
      this.editor.setContents(this.props.contents);
      this.editor.core.history.reset(true);

    }
    // 多语言切换
    if (this.props.globalLang !== prevProps.globalLang) {
      this.editor.setOptions({
        // ...this.state.options,
        videoRatioList: [
          { name: i18next.t('app.default'), value: 0.4285 },
        ],
        charCounterLabel: i18next.t('info-hub.char-count') as string,
        placeholder: this.props.readOnly ? '' : i18next.t('common.please-input') as string,
        lang: langMap[this.props.globalLang] || en

      });
    }
    // 编辑器状态
    if (this.props.disabled !== prevProps.disabled) {
      // console.log('this.props.disabled', this.props.disabled)
      this.setEditorStatus()
    }
  }

  componentWillUnmount() {
    if (this.editor) this.editor.destroy();
  }



  render() {
    return <div className={`ama-richtext-editor ${this.props.themeInfo?.theme} ${this.props.disabled ? 'disabled' : ''} ${this.props.readOnly ? 'read-only' : ''}`}>
      <textarea disabled={this.props.disabled} ref={this.txtArea} />
      {/* 隐藏的Antd Upload组件 */}
    </div>;
  }
}
const mapStateToProps = (state: any) => ({
  globalLang: state.app.globalLang,
  themeInfo: state.app.themeInfo,
})
const mapDispatchToProps = (dispatch: any) => ({
})
// export default connect(mapStateToProps, mapDispatchToProps)(Editor);


const ForwardedChildComponent = forwardRef((props: any, ref: any) => {
  return <Editor ref={ref}  {...props} />;
});

export default connect(mapStateToProps, mapDispatchToProps, null, { forwardRef: true })(ForwardedChildComponent);

// export default Editor;