
@import '@/styles/mixin.scss';
.ant-popover.ann-tag-operate-popover .ant-popover-inner{
    padding: 0;
    // margin-top: -40px;
}
.ann-tag-operate{
    width: 400px;
    .to-top{
        padding: 8px 16px;
        border: 1px solid var(--colorBorderSecondary);
        background: var(--colorFillSecondary);
        border-radius: 8px 8px 0 0;
    }
    .to-top-input{
        display: flex;
        flex: 1 1 60px;
        max-width: 100%;
        width: auto;
        min-width: 60px;
    }
    .to-top-tag-name{
        flex: 1;
        max-width: 80px;
        @include ellipsis()
    }
    .to-top-tag-delete{
        flex-shrink: 0;
        color: var(--colorTextTertiary);
        cursor: pointer;
        font-size: 14px;
    }
    .to-tip{
        padding: 8px;
        @include font(12px,18px,400,var(--colorTextTertiary));
    }
    .to-list{
        padding: 4px;
    }
    .to-list-item{
        padding: 4px 12px;
        padding-right: 2px;
        cursor: pointer;
        &:hover{
            background: var(--colorFillQuaternary);
            border-radius: 8px;
            .to-list-operate{
                display: flex;
            }
        }
        &.active{
            // background: var(--colorFillTertiary);
            // border-radius: 8px;
            .to-list-operate{
                display: flex;
            }
        }
        &.create{
            background: var(--colorFillTertiary);
            border-radius: 8px;
            .create-title{
                @include font(14px,22px,400,var(--colorTextSecondary));
            }
            .create-tag{

            }
        }
    }
    .to-list-operate{
        display: none;
        font-size: 18px;
        padding: 2px 8px;
    }
}

.ant-popover.ann-tag-operate-color-popover{
    .ann-tag-delete{
        padding: 10px 4px;
        border-bottom: 1px solid var(--colorBorderSecondary);
        cursor: pointer;
        @include font(14px,22px,400,var(--colorTextSecondary));
        .svg-icon{
            font-size:16px;
        }
    }
    .color-picker{
        .cp-title{
            @include font(12px,18px,400,var(--colorTextSecondary));
            padding: 8px 4px;
        }
        .cp-tag-item{
            cursor: pointer;
            padding: 4px;
            @include font(14px,22px,400);
            &:hover{
                background: var(--colorFillQuaternary);
                border-radius: 8px;
            }
            .icon_selected{
                margin-left: auto;
                font-size: 18px;
                color: var(--colorPrimary);
            }
        }
        .ant-tag{
            width: 18px;
            height: 18px;
            // box-shadow: rgba(15, 15, 15, 0.1) 0px 0px 0px 1px inset;
            border-radius: 4px;
        }
    }
}

.ann-tag-edit{
    height: auto;
}

.popver-mask{
    position: fixed;
    top: 0px;
    inset-inline-start: 0px;
    width: 100vw;
    height: 100vh;
    z-index: 20;
}