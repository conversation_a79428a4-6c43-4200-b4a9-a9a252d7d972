import { connect } from "react-redux";
import React, { useState, useRef, useEffect } from "react";
import { Badge, Button, Checkbox, Empty, Flex, Input, message, Pagination, Popover, Segmented, Select, Spin, Tabs, TabsProps, Tag, Tooltip } from 'antd'
import { useTranslation } from 'react-i18next';
import SvgIcon from '@components/svgicon';
import { getPriorityFlag, highlightText } from '../common'
import TagManage from '../components/tagManage'
import AnnDetail from './detail'
import { api_get_notify_tags, api_get_user_notification, api_update_notification_pin, api_update_notification_archive } from '@/api/information'
import { change_app_is_refresh_ann_unread } from '@/store/action'

import './index.scss'
import classNames from "classnames";
import { $isNull, $trim, parseTime } from "@/utils/common";
import { calculatePageCount } from '@/utils/pagination'

// 公告
const Announcements = (props: any) => {
    const { t } = useTranslation();
    const sideMenuRef = useRef<any>(null)
    const { activeCompanyId } = props

    const [panel, setPanel] = useState<any>('List'); // List 列表 Detail 详情
    const [annType, setAnnType] = useState<any>('Active'); // 当前选中的公告类型
    const [searchValue, setSearchValue] = useState<any>(''); // 关键字输入框
    const [selectedTag, setSelectedTag] = useState<any>(null); // 标签筛选
    const [tagOptions, setTagOptions] = useState<any>([]
        // [{ id: 1, name: 'vama', color: 'pink' },
        // { id: 2, name: 'vintech', color: 'blue' },
        // { id: 3, name: 'vintech', color: 'blue' },
        // { id: 4, name: 'vintech', color: 'blue' },
        // { id: 5, name: 'vintech', color: 'blue' },
        // { id: 6, name: 'vintech', color: 'blue' },
        // { id: 7, name: 'vintech', color: 'blue' },
        // { id: 8, name: 'vintech', color: 'blue' },
        // { id: 9, name: 'vintech', color: 'blue' },
        // { id: 10, name: 'vintech', color: 'blue' }
        // ]
    ); // tag 选项
    const [isLoading, setIsLoading] = useState<any>(false);
    const [isResting, setIsResting] = useState<any>(false);
    const [hasFilter, setHasFilter] = useState<any>(false); // 是否有筛选项
    // const [isSearchResult, setIsSearchResult] = useState<any>(false); // 是否是筛选结果
    const [isUnread, setIsUnread] = useState<any>(false);
    const [currentPage, setCurrentPage] = useState<any>(1); // 分页-当前页
    const [totalCount, setTotalCount] = useState<any>(0); // 分页-总页数
    const [pageSize, setPageSize] = useState<any>(10); // 分页-一页数目
    const [dataList, setDataList] = useState<any>([]); // 列表数据

    const [detailResource, setDetailResource] = useState<any>({ visible: false, data: {} }); // 公告详情

    const annTypes: TabsProps['items'] = [
        {
            key: 'Active',
            label: t('info-hub.active'),
            children: null,
        },
        {
            key: 'Archived',
            label: t('info-hub.archived'),
            children: null,
        }
    ];

    // 初始化
    useEffect(() => {
        if (activeCompanyId != '0') init()
    }, [activeCompanyId])

    const init = () => {
        queryList()
        getTags()
        // setIsLoading(true)
        // let _list: any = []
        // for (let i = 1; i < 50; i++) {
        //     _list.push({
        //         id: i,
        //         title: "公告标题" + i,
        //         operator: "谢佳佳",
        //         content: '公告内容公告内容公告内容公告内容公告内容公告内容公告内容公告内容公告内容公告内容公告内容公告内容公告内容公告内容公告内容公告内容公告内容' + i,
        //         // tags: [...tagOptions],
        //         tags: [],
        //         createTime: '2025-05-05',
        //         isPin: i < 3,
        //         isUnread: i != 1,
        //         priority: i == 1 ? 'Critical' : i == 2 ? 'Important' : 'Normal'
        //     })
        // }
        // setTotalCount(100)
        // setTimeout(() => {
        //     setIsLoading(false)
        //     setDataList(_list)
        // }, 200)
    }

    // 获取公告列表
    const queryList = async (pageNo = currentPage, _annType?: any, _isUnread?: boolean, isReset?: boolean) => {
        // console.log('_annType', _annType)
        // console.log('_isUnread', _isUnread)
        // console.log('isUnread', isUnread)
        const type = _annType || annType
        const p: any = {
            pageNo: pageNo,
            pageSize: pageSize,
            archived: type === 'Archived'
        }
        if (type === 'Active' && (_isUnread)) {
            p.unreadOnly = true
        }
        setDataList([])
        if (!isReset) {
            if (!$isNull(searchValue)) { // 索关键词
                p.keyword = $trim(searchValue)
            }
            if (selectedTag) {
                p.tagIds = selectedTag
            }
        }
        // console.log('p', p)
        setIsLoading(true)
        const res: any = await api_get_user_notification(p).catch(() => {
            setIsLoading(false)
        })
        setIsLoading(false)
        if (!res || res.code !== 0) return
        const total = res?.data?.total
        setTotalCount(total)
        let list = res?.data?.list || []
        const pageCount = calculatePageCount(total, pageSize)
        console.log('pageCount', pageCount)
        //  如果当前页大于总页数, 重新请求
        if (total > 0 && pageNo > pageCount) {
            const page = currentPage - 1
            setCurrentPage(page)
            queryList(page)
            return
        }
        list = list.map((item: any) => {
            item.publishedTime = item.publishedTime ? parseTime(item.publishedTime, "{y}-{m}-{d} {h}:{i}") : ''
            item.content = item.contentText
            item.operator = item.behalfOfName
            item.isUnread = !item.readStatus
            item.isPin = item.pinnedStatus
            item.isPinRequest = false // 请求标识
            item.isArchiveRequest = false // 请求标识
            // 判断公告是否已过期
            item.isExpired = item?.validityAt === 'TIMED' && Date.now() >= item?.expireTime
            return item
        })
        setDataList(list)
        // setIsSearchResult(!$isNull(searchValue))
        // console.log('list', list)
    }

    // 获取所有标签
    const getTags = async () => {
        const res: any = await api_get_notify_tags().catch(() => {
        })
        if (!res) return
        let datas = res.data || []
        // console.log('datas', datas)
        datas = datas.map((i: any) => {
            return {
                id: i.id,
                name: i.tagName,
                color: i.tagColor
            }
        })
        setTagOptions([...datas])
    }

    // 类型切换
    const onChangeTab = (key: any) => {
        setAnnType(key)
        setCurrentPage(1)
        setTotalCount(0)
        queryList(1, key)
    }

    // 关键字输入框输入
    const onSearchValueChange = (e: any) => {
        const value = $trim(e.target.value)
        setSearchValue(value)
        setHasFilter(!$isNull(value) || selectedTag)
    }

    // 标签筛选
    const onChangeSetSelectedTag = (value: any) => {
        // console.log('value', value)
        setSelectedTag(value)
        setHasFilter(true)
    }

    // 未读勾选切换
    const onChangeIsUnread = () => {
        setPanel('List')
        const _isUnread = !isUnread
        setIsUnread(_isUnread)
        setCurrentPage(1)
        queryList(1, undefined, _isUnread)
    }

    // 搜索
    const onSearch = () => {
        // setIsSearching()
        setCurrentPage(1)
        queryList(1)
    }

    // 重置
    const onReset = () => {
        setCurrentPage(1)
        setHasFilter(false)
        setSelectedTag(null)
        setSearchValue(null)
        setIsUnread(false)
        // setSelectedTag()
        queryList(1, undefined, undefined, true)
    }

    const setDataListItemProp = (propName: string, propValue: any, index: number) => {
        setDataList(dataList.map((item: any, i: number) => {
            if (i === index) {
                item[propName] = propValue
            }
            return item
        }))
    }

    // 归档-取消归档
    const onToggleArchived = async (e: any, isArchived: boolean, index: number) => {
        e.stopPropagation()
        //  isArchiveRequest
        setDataListItemProp('isArchiveRequest', true, index)
        const data = {
            id: dataList[index].id,
            archived: isArchived
        }
        const res: any = await api_update_notification_archive(data).catch(() => {
            setDataListItemProp('isArchiveRequest', false, index)
        })
        setDataListItemProp('isArchiveRequest', false, index)
        if (!res) return
        if (res.success) {
            message.success(t('app.operate-success'))
            queryList()
            // 归档公告需要更新未读数 
            if (isArchived) {
                props.dispatch_change_app_is_refresh_ann_unread(true)
            }
        }
    }

    // 置顶-取消置顶
    const onTogglePin = async (e: any, isPin: boolean, index: number) => {
        e.stopPropagation()
        // isPinRequest
        setDataListItemProp('isPinRequest', true, index)
        const data = {
            id: dataList[index].id,
            pinned: isPin
        }
        const res: any = await api_update_notification_pin(data).catch(() => {
            setDataListItemProp('isPinRequest', false, index)
        })
        setDataListItemProp('isPinRequest', false, index)
        if (!res) return
        message.success(t('app.operate-success'))
        queryList()
    }

    // 分页
    const onChangePagination = (page: any) => {
        // console.log('page', page)
        setCurrentPage(page)
        queryList(page)
    }

    // 查看公告详情
    const onViewDetail = (e: any, item: any) => {
        e.stopPropagation()
        e.preventDefault()
        setPanel('Detail')
        setDetailResource({ ...item, visible: true })
    }

    // 详情页面 操作返回
    const onBack = () => {
        setPanel('List')
        setDetailResource({ visible: false })
    }

    // 详情页面 通知刷新列表字段
    const onRefreshTarget = (targetAnnId: any, obj: object) => {
        // console.log(targetAnnId, obj)
        if ('isUnread' in obj) {
            console.log('-------更新未读')
            // 通知侧边栏 更新未读数
            props.dispatch_change_app_is_refresh_ann_unread(true)
            if (isUnread) {
                // 当前筛选了未读，刷新列表
                queryList(undefined, undefined, true)
                return
            }
        }
        setDataList(
            dataList.map((item: any) => {
                if (item.id == targetAnnId) {
                    item = { ...item, ...obj }
                    // console.log('找到了', item)
                }
                return item
            })
        )
    }

    // 详情页面 通知刷新列表
    const onRefreshList = () => {
        queryList()
    }

    // tag管理通知 刷新某条通知-标签
    const onRefreshListItemTag = (targetId: any, tags: any) => {
        let list = [...dataList]
        list.forEach((item: any) => {
            if (item.id == targetId) {
                item.tags = [...tags]
                return
            }
        })
        setDataList(list)
        // 刷新标签筛选项
    }

    // tag管理通知 刷新通知列表 相关标签
    const onRefreshListTargetTag = (targetTag: any) => {
        let list = [...dataList]
        list.forEach((item: any) => {
            if (item.tags && item.tags.length > 0) {
                item.tags = item.tags.map((i: any) => {
                    if (i.id === targetTag.id) {
                        // console.log('找到了')
                        i.tagName = targetTag.name
                        i.tagColor = targetTag.color
                    }
                    return i
                })
            }
        })
        setDataList(list)
    }

    // tag管理通知 列表 移除相关标签
    const onDeleteListTargetTag = (targetTag: any) => {
        let list = [...dataList]
        list.forEach((item: any) => {
            if (item.tags && item.tags.length > 0) {
                item.tags = item.tags.filter((t: any) => t.id != targetTag.id)
            }
        })
        setDataList(list)
        setTagOptions(tagOptions.filter((t: any) => t.id != targetTag.id))
    }

    // tag管理通知 刷新标签选项数据
    const onRefreshTags = (tags: any = []) => {
        // console.log('onRefreshTags tags', tags)
        setTagOptions([...tags])
    }


    return (
        <Flex className="ann-page common-ann-page" vertical>
            <Flex className={`list-panel-con common-wrap ${panel === 'Detail' ? "hidden" : ""}`} vertical>
                <Flex className="page-nav" gap={20} align="center" justify="space-between" wrap={true}>
                    <span className="nav-title">{t('info-hub.ann')}</span>
                    {/* 搜索 */}
                    <Flex className="search-area" gap={20} align="center">
                        <Input
                            className='search-input'
                            prefix={<SvgIcon svgName="search" />}
                            placeholder={t('info-hub.search-input') as string}
                            value={searchValue}
                            onChange={onSearchValueChange}
                            onKeyDown={(e) => e.stopPropagation()}
                        // variant="filled"
                        />
                        <Select
                            // variant="filled"
                            value={selectedTag}
                            onChange={onChangeSetSelectedTag}
                            popupClassName="ann-tag-selector"
                            placeholder={t('info-hub.filter-tags') as string}
                            suffixIcon={<SvgIcon svgName="icon_select_suffix" />}
                            options={
                                tagOptions.map((tag: any) => {
                                    tag.value = tag.id
                                    tag.label = tag.name
                                    return tag
                                })
                            }
                        />
                        <Button type="primary" onClick={() => onSearch()} disabled={isLoading}>{t('search')}</Button>
                        <Button color="default" variant="filled" onClick={() => onReset()} disabled={isLoading}>{t('common.reset')}</Button>
                    </Flex>
                    {
                        annType === 'Active' ?
                            <Checkbox checked={isUnread} onChange={onChangeIsUnread}>{t('info-hub.show-unread')}</Checkbox>
                            // <Button className="toggle-unread" icon={<SvgIcon svgName="icon_ann_unread" />} color="default" variant="filled">Show Unread</Button>
                            : <div></div>
                    }
                </Flex>
                <div className="segmented-types">
                    <Segmented
                        className="common-segmented"
                        value={annType}
                        options={annTypes.map((i: any) => {
                            i.value = i.key
                            return i
                        })}
                        onChange={onChangeTab}
                    />
                </div>
                <Flex className={`list-panel page-main`} vertical>
                    <>
                        {
                            isLoading ?
                                <Spin className="full-spin" size="large" /> :
                                <>
                                    {
                                        dataList.length > 0 ?
                                            <Flex className="ann-list-con" vertical gap={16}>
                                                {dataList?.map((item: any, index: number) => (
                                                    <Flex
                                                        onClick={(e) => onViewDetail(e, item)}
                                                        className={`list-item ${annType === 'Active' && item.isPin ? 'is-pin' : ''}`}
                                                        // style={item.isPin ? { top: `calc(201px * ${index} +  20px * ${index+1}) ` } : {}}
                                                        align="center"
                                                        gap={20}
                                                        key={item.id}
                                                    >
                                                        {annType === 'Active' && item.isPin ? <div className="li-pin"><SvgIcon svgName="icon_pin" /></div> : null}
                                                        <div className={`li-card ${annType}`}>
                                                            <Flex className="li-card-top" gap={8} align="center">
                                                                <div className="li-card-priority">{getPriorityFlag(item.priority)}</div>
                                                                <div className="li-card-title"
                                                                    dangerouslySetInnerHTML={{ __html: item.title }}
                                                                // dangerouslySetInnerHTML={
                                                                //     !isSearchResult ? {__html:item.title} : highlightText(item.title, searchValue)
                                                                // }
                                                                ></div>
                                                                <div className="li-card-time">{item.publishedTime}</div>
                                                                {annType === 'Active' && item.isUnread ? <Badge dot className="li-card-unread" /> : null}
                                                            </Flex>
                                                            {/* 发布者 */}
                                                            <div className="li-card-operator" dangerouslySetInnerHTML={{ __html: item.operator }}></div>
                                                            {/* <Flex align="center" gap={8} className="li-card-operator">
                                                                <SvgIcon svgName="icon_user" />
                                                                <span>{item.operator}</span>
                                                            </Flex> */}
                                                            <div className="li-card-content"
                                                                dangerouslySetInnerHTML={{ __html: item.content }}
                                                            // dangerouslySetInnerHTML={
                                                            //     !isSearchResult ? {__html:item.content} : highlightText(item.content, searchValue)
                                                            // }
                                                            >
                                                            </div>
                                                            <Flex className="li-card-bottom" gap={40} justify="center">
                                                                {/* 标签 */}
                                                                {
                                                                    <Flex className="li-card-tags" gap={8} align="center" wrap={true}>
                                                                        <TagManage
                                                                            notificationId={item.id}
                                                                            options={item.tags}
                                                                            onRefreshListItemTag={onRefreshListItemTag}
                                                                            onRefreshListTargetTag={onRefreshListTargetTag}
                                                                            onDeleteListTargetTag={onDeleteListTargetTag}
                                                                            onRefreshTags={onRefreshTags}
                                                                        >
                                                                            {

                                                                                item.tags && item.tags.length > 0 ?
                                                                                    item.tags.map((tag: any) => (
                                                                                        <Tag className={`"li-card-tag" ${tag.tagColor}`} key={tag.id} bordered={false}
                                                                                        // color={tag.color || 'purple'}
                                                                                        >{tag.tagName}</Tag>
                                                                                    ))
                                                                                    : null
                                                                            }
                                                                        </TagManage>


                                                                    </Flex>
                                                                }
                                                                {
                                                                    annType === 'Active' || (annType === 'Archived' && !item.isExpired) ?
                                                                        <Flex className="more" justify="flex-end" align="center">
                                                                            <SvgIcon svgName="more_dot" />
                                                                        </Flex> : <div className="more"></div>
                                                                }

                                                                <Flex className="li-card-operate" justify="flex-end" align="center" gap={10}>
                                                                    {
                                                                        annType === 'Active' ?
                                                                            <>
                                                                                <Button type="primary"
                                                                                    disabled={item.isArchiveRequest}
                                                                                    onClick={(e) => onToggleArchived(e, true, index)}
                                                                                // icon={<SvgIcon svgName="icon_archive" />}
                                                                                >{t('info-hub.archive')}</Button>
                                                                                <Button color="default" variant="filled"
                                                                                    disabled={item.isPinRequest}
                                                                                    onClick={(e) => onTogglePin(e, !item.isPin, index)}
                                                                                // icon={<SvgIcon svgName={item.isPin? "icon_unpin":"icon_pin"} />}
                                                                                >{item.isPin ? t('info-hub.unpin') : t('info-hub.pin')}</Button>
                                                                            </> : null
                                                                    }
                                                                    {
                                                                        annType === 'Archived' && !item.isExpired ?
                                                                            <>
                                                                                <Button disabled={item.isArchiveRequest}
                                                                                    onClick={(e) => onToggleArchived(e, false, index)} type="primary">{t('info-hub.activation')}</Button>
                                                                            </> : null
                                                                    }
                                                                </Flex>
                                                            </Flex>
                                                        </div>
                                                        {/* <Flex className="li-operate" gap={10} vertical>
                                                        {
                                                            annType === 'Active' ?
                                                                <>
                                                                    <Button className="quaternary primary"
                                                                        color="default" variant="filled">Archived</Button>
                                                                    <Button className="quaternary" color="default" variant="filled">{item.isPin ? 'Unpin' : 'Pin'}</Button>
                                                                </> : null
                                                        }
                                                        {
                                                            annType === 'Archived' ?
                                                                <>
                                                                    <Button className="quaternary primary" color="default" variant="filled">Active</Button>
                                                                </> : null
                                                        }

                                                    </Flex> */}
                                                    </Flex>
                                                ))}
                                            </Flex>
                                            :
                                            // 空数据
                                            <Empty className="page-empty" image={null} description={
                                                <><div>{t('common.no-result')}</div>
                                                    <div>{t('robot-manage.search-result')}</div>
                                                </>} />
                                    }
                                </>
                        }
                    </>
                    {/* 分页 */}
                    <Flex className="pagition" justify="flex-end">
                        {
                            totalCount > 0 ?

                                <Pagination
                                    current={currentPage}
                                    onChange={onChangePagination}
                                    total={totalCount}
                                    // showQuickJumper
                                    showSizeChanger={false}
                                    showTotal={(total) => `${t('common.total')} ${total}`}
                                    size="small"
                                />
                                : null}
                    </Flex>
                </Flex>
            </Flex>
            {/*  公告详情*/}
            <div className={`detail-panel ${panel === 'List' ? "hidden" : ""}`}>
                {
                    detailResource.visible ?
                        <AnnDetail
                            resource={detailResource}
                            onBack={onBack}
                            onRefreshTarget={onRefreshTarget}
                            onRefreshList={onRefreshList}
                            onRefreshListItemTag={onRefreshListItemTag}
                            onRefreshListTargetTag={onRefreshListTargetTag}
                            onDeleteListTargetTag={onDeleteListTargetTag}
                            onRefreshTags={onRefreshTags}
                        />
                        :
                        null
                }
            </div>
        </Flex>
    )
}


const mapStateToProps = (state: any) => ({
    companies: state.app.companies,
    activeCompanyId: state.app.activeCompanyId
})
const mapDispatchToProps = (dispatch: any) => ({
    dispatch_change_app_is_refresh_ann_unread: (data: any) => dispatch(change_app_is_refresh_ann_unread(data))
    // dispatch_api_: (data:any) => dispatch(dispatch_api_(data))
})
export default connect(mapStateToProps, mapDispatchToProps)(Announcements);