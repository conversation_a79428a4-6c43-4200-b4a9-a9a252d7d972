import React from 'react';
import { Provider } from "react-redux";
import store from "./store";
import WidgetMain from "./main";
import { withTranslation } from "react-i18next";

class App extends React.Component<any, any> {
  componentDidMount(){
    try {
      // 命名当前窗口，为了前后台切换功能
      window.name = window.location.origin
    } catch (error) {
      
    }
  }
  render() {
    return (
      <Provider store={store}>
        <WidgetMain/>
      </Provider>
    )
  }
}

export default withTranslation()(App);
