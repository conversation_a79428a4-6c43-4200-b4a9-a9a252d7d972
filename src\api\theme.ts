import HTTP, { APP_HTTP } from '@/utils/userRequest'
import { AxiosRequestConfig } from 'axios';

// 获取系统 默认配置  /system/tenants/platform-config
export const api_get_app_default_theme = (data?:any): Promise<unknown> => new Promise((resolve, reject) => {
    HTTP({
        method: 'get',
        url: "/system/tenants/platform-config",
        params: {},
        noTenant: true
    } as AxiosRequestConfig).then(result => {
        const res = result?.data || {}
        resolve(res)
    }).catch(err => reject(err))
})

//获取 公司配置详情
export const api_get_theme_of_company = (tenantId?: any): Promise<unknown> => new Promise((resolve, reject) => {
    // }
    HTTP({
        method: 'get',
        url: "/system/tenants/detail",
        params: {},
        hideTip: true,
        tenantId: tenantId || null
    } as AxiosRequestConfig).then(result => {
        const res = result?.data || {}
        resolve(res)
    }).catch(err => reject(err))
})

//根据域名获取主题配置
export const api_domain_theme = (tenantId?: any): Promise<unknown> => new Promise((resolve, reject) => {
    const config: any = {
        method: 'get',
        url: "/system/tenants/detail",
        params: {},
        hideTip: true
    }
    if (tenantId!==undefined){
        config.tenantId = tenantId
        config.noToken = true
    } else{
        config.noTenant = true
    }
    APP_HTTP(config as AxiosRequestConfig).then(result => {
        const res = result?.data || {}
        resolve(res)
    }).catch(err => reject(err))
})