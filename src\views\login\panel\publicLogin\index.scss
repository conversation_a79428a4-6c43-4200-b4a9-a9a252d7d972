@import "../../../../styles/mixin.scss";
.public-login.content {
    max-width: 100%;
    .ant-tabs-nav {
        margin-bottom: 28px;
    }
    .icon-back {
        cursor: pointer;
    }
    .hidden2 {
        visibility: hidden;
    }
    .concat-me {
        margin-top: auto;
    }
    .operate-item {
        margin-bottom: 24px;
    }
    // .invite-info {
    //     max-width: 370px;
    //     overflow: hidden;
    //     .join-content,
    //     .join-info {
    //         max-width: 100%;
    //         @include ellipsis();
    //     }
    //     .join-content {
    //         @include font(16px, 24px, 500);
    //     }
    //     .join-info {
    //         @include font(14px, 22px, 400, var(--colorTextSecondary));
    //         @include ellipsis();
    //     }
    // }
}
