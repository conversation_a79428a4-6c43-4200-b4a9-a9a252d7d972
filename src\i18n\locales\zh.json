{"app": {"cancel": "取消", "ok": "确认", "create-dialog": "机器人", "email": "邮箱", "delete": "删除", "confirm-del": "确定要删除吗？", "max-msg": "每天最多可以发送200条消息。请明天再试。", "copy": "复制", "references": "引用知识", "enhance": "增强", "enhance-notice": "请确认是否将资料发给外部AI 模型:", "edit": "编辑", "edited": "已编辑", "rollback": "还原", "document": "原文", "check-agree-enhance": "我已审查以下从知识库中检索到的数据，确认无敏感信息，同意将数据传输给外部AI模型以获得回复。", "information-source": "答案依据", "change-llm-tip": "切换模型将会清空当前上传的文件和已选的历史对话", "change-llm-confirm": "确认要切换吗？", "change-llm": "切换", "no-change-llm": "不切换", "filebox-overflow": "文件过大，超出限制", "filebox-overflow-token": "当前超出 {{num}} token{{unit}}，请先删除部分文件再进行增强操作", "i-know": "我知道了", "max-input-count": "最多输入{{n}}字符", "no-more-data": "没有更多了", "select-conversation": "选择对话", "cancel-select": "取消选择", "copyed": "复制", "operate-success": "操作成功", "default": "默认"}, "nav": {"logout": "退出登录", "docs": "文档中心", "drive": "云盘", "email": "邮件", "approvals": "审批", "ann": "公告", "wiki": "Wiki", "project": "项目管理", "coming-soon": "即将开放，敬请期待", "docs-desc": "在线创建和协作编辑文档", "drive-desc": "安全存储、管理和共享文件", "email-desc": "内置安全防护的专业邮箱", "approvals-desc": "人力资源、财务和行政流程的快捷审批", "ann-desc": "定向推送、阅读跟踪、标签分类", "wiki-desc": "集中管理和共享团队知识库", "project-desc": "高效规划、跟踪和管理项目"}, "modal": {"save-ok": "保存"}, "error": {"no-send-blank": "不能发送空白消息", "ai-response": "回复生成中…...请稍等。", "logo-timeout": "登录会话超时，请重新登录", "llm-disabled-tip": "模型尚未安装，请联系支持团队以获取帮助。", "stop-generating-answers": "停止生成(ESC)", "request-failed": "请求失败，请稍后重试", "request-failed-code": "请求失败，错误码：{{code}}"}, "success": {"submit": "提交成功"}, "placeholder": {"opinion": "请输入你的建议或问题", "msg-placeholder": "与 {{platformName}} 交谈"}, "introduction": {"default": "欢迎使用"}, "homebot": {"describe": "Home Bot 是您日常的首选机器人，能够处理所有一般性问答和网络搜索需求。它旨在取代您目前从网络订阅的AI工具，甚至革新您使用谷歌进行网络搜索的方式。Home Bot 不依赖于任何特定公司的知识库，使其成为处理各种任务的完美工具。该助理依赖于外部人工智能模型，因此在共享任何文件或私人数据时请谨慎。"}, "upload": {"upload-file": "上传文件", "upload-trigger-tip1": "最多可上传5份pdf、txt或docx文件，每份不超过4.5MB；", "upload-trigger-tip2": "支持拖入和粘贴", "pdf": "pdf文件", "docx": "docx文件", "txt": "txt文件", "file-excced": "已达最大文档上传数量", "file-draging-tip": "将文件拖拽至此处", "upload-failed": "上传失败", "file-count-error": "最多支持上传5个文件", "file-format-error": "文件格式不受支持", "file-size-error": "文件大小超过4.5M", "file-format-size-error": "文件格式不受支持，且文件大小超过4.5M", "file-token-excced-error1": "已达模型可处理的字数上限", "file-token-excced-error2": "若要继续上传请先删除部分文件", "file-empty-error": "文档为空", "file-name-long-error": "文件名过长", "attachFile": "附加文件", "clear": "清除", "continue-uploading": "继续上传", "continue-to-select": "继续选择", "history-chat-tip": "将历史对话引用至提问中"}, "robot": {"details": "助理详情", "create-by": "由 {{user}} 创建", "llm-model": "AI 模型 (默认)", "llm-model-enhance": "AI 模型 (增强)", "robot-id": "助理ID", "create-time": "创建时间", "ai-model-creativity": "创造力", "relevance": "知识相关性", "knowlege-count": "引文数量", "my-robot": "我的机器人", "share-robot": "与我共享的机器人", "functions": "功能"}, "home": {"historical-chat": "历史对话", "google-search": "网络搜索", "size": "大小", "relevance": "相关性", "upload-time": "上传时间", "context-cleared": "上下文已清除", "clearing-the-context": "清除上下文", "delete-conversation": "删除所选", "confirm-delete-chat-tip": "此操作无法恢复，且提问和回答将同时被删除"}, "copied": "已复制", "you": "你", "google-seatch": "打开后，可透过Google搜索引擎获得最新资讯，以增强答案质量。", "search": "搜索", "saveToLocal": "保存至本地", "settings": "设置", "control-center": "控制中心", "submit": "提交", "automatic": "自动", "light": "亮色", "dark": "暗色", "system-setting": "设置", "feedback": "意见反馈", "feedback-tip": "我们由衷地感谢您的反馈 —— 它有助于我们把产品做得更好！", "feedback-placeholder": "请输入你的建议或问题", "appearance": "外观", "system-language": "系统语言", "system-language-tip": "为{{platformName}}界面设置首选语言", "text-generation": "文本生成", "image-generation": "图像生成", "reasoning": "正在推理", "reasoning-time": "已推理，用时{{time}}秒", "logical-reasoning": "逻辑推理", "login": {"sign": "登录", "unable-login": "无法登录?", "sign-about": "联系技术人员", "sign-success": "登录成功", "logout": "退出登录", "control-center": "用户中心", "user-center": "控制中心", "login-tip2": "访问多种先进的定制机器人", "email": "邮箱", "email-address": "邮箱地址", "password": "密码", "mobile": "手机号", "verify-code": "验证码", "get-code": "获取验证码", "username": "用户名", "privacy-policy": "隐私政策", "terms-conditions": "条款协议", "agree-privacy-terms": "我同意 {{privacy}} 和 {{terms}}", "agree-privacy-terms-tip": "请阅读并同意以下条款", "privacy-terms": "{{privacy}} 和 {{terms}}", "agree": "同意", "verifiy-code-login": "验证码登录", "password-login": "密码登录", "forgot-password": "忘记密码？", "register": "注册 vama", "register-btn": "注册", "has-account": "已有vama账号？", "verify-mobile": "验证手机号", "continue": "继续", "skip": "跳过", "back": "返回", "login-tip": "让我们帮您登录", "joinContent": "{{userName}} 邀請您加入 vama 團隊", "welcom-to": "欢迎来到 {{platName}}", "no-account": "没有 vama 账户？"}, "login-pls": {"email": "请输入邮箱", "password": "请输入密码", "new-password": "请输入新密码", "mobile": "请输入手机号", "verify-code": "请输入验证码", "username": "4-20 个字符，例如 <PERSON>"}, "login-validate": {"password-format-error": "密码必须由6-18个字符组成，包含字母和数字", "email-format-error": "邮箱地址无效", "mobile-format-error": "手机号无效", "invalid-verifiy-code-error": "验证码无效", "username-format-error": "用户名无效"}, "login-msg": {"send-success": "发送成功"}, "common": {"please-input": "请输入", "please-select": "请选择", "please-search": "请搜索", "ok2": "好的", "search-keyword": "搜索", "input-required": "必填", "select-required": "必选", "no-result": "没有结果", "query": "查询", "reset": "重置", "edit": "编辑", "delete": "删除", "edit-success": "编辑成功", "update-success": "修改成功", "delete-success": "删除成功", "status-all": "全部状态", "status-enable": "启用", "status-disable": "禁用", "no-data": "暂无数据", "warning": "警告", "total": "总计"}, "company": {"title": "我的公司/团队", "personal-space": "个人空间", "create": "创建", "join": "加入", "apply": "申请", "create-title": "创建公司/团队", "create-tip": "您将以公司管理员身份启动 vama", "company-name": "公司名称", "location": "位置", "industry": "行业", "employee-size": "员工规模", "service-agreement": "V AMA服务条款", "organization-agreement": "V AMA平台使用协议", "agreement": "我已阅读并同意：{{service}}，{{organization}}", "service-organization": "{{service}}，{{organization}}", "join-title": "加入公司/团队", "join-tip": "搜索并申请加入公司或团队", "no-data": "无可用数据", "search-no-result": "请输入正确的公司名称", "apply-submitted": "申请已提交", "apply-submitted-tip": "请等待组织管理员审批", "create-success": "创建成功", "apply-failed": "申请失败", "apply-failed-reason": "您已加入该公司/组织。"}, "no-my-bot": "没有可用的机器人", "no-share-bot": "没有机器人与我分享", "robot-manage": {"search-result": "搜索结果将显示在这里", "arrangement": "布局", "arrangement-1": "网格视图", "arrangement-2": "列表视图", "robot-type": "类别", "robot-type-1": "所有机器人", "robot-type-2": "我创建的机器人", "robot-type-3": "与我共享的机器人", "basic-info": "基本信息", "ai-model": "AI模型", "create-robot": {"robot-type": "机器人类型", "personal-bot": "个人机器人", "department-bot": "部门机器人", "department-bot-desc": "创建部门机器人", "company-bot": "公司机器人", "company-bot-desc": "创建公司机器人", "robot-id": "机器人ID", "robot-logo": "机器人头像", "robot-name": "机器人名称", "robot-name-hint": "请输入机器人名称", "robot-introduction": "机器人简介", "robot-introduction-hint": "请输入机器人简介", "robot-introduction-hint2": "简明地表达机器人的功能", "llm-model": "AI 模型 (默认)", "llm-model-hint": "请选择 AI 模型", "escalate-llm-model": "AI 模型 (增强)", "escalate-llm-model-hint": "请选择增强 AI 模型", "intelligence": "创造力", "intelligence-hint": "较低数值使回答更精确稳定，较高数值使回答更随机", "relevance": "知识相关性", "relevance-hint": "数值越高，您的提问与知识库的内容（引文）匹配越精准，但匹配结果可能变少。", "enable-conversation-memory": "记忆", "show-knowledge": "访问引文", "allow": "允许", "not-allowed": "不允许", "show-knowledge-notice": "是否允许在对话界面，为用户展示机器人回答时所引用的知识库内容。", "empty-knowlwdge": "当无法检索到知识库内容时, 回复为：", "llm-response": "所选模型的回复", "canned-response": "默认回复", "canned-response-hit": "请输入默认回复。", "long-term-memory": "长期记忆", "long-term-memory-tip": "在聊天界面，清除上下文后将重置长期记忆", "long-term-memory-desc": "总结聊天对话的内容，并用于更好的响应用户的消息。", "upload-image": "上传图片", "bot-logo-edit": "图片裁剪", "is-irrelevant-questions": "开场白", "robot-welcome-msg": "开场白文案", "robot-welcome-msg-hint": "请输入开场白文案", "robot-identity-prompt": "身份描述", "robot-identity-prompt-hint": "身份描述决定机器人角色和功能，请尽量清晰填写", "robot-identity-prompt-notice": "这非常重要，因为它将塑造助理的身份、能力、边界、语气等。值得您花更多时间进行构建。", "knowlege-document-count": "知识库"}, "save-confirm-msg": "离开此页面？", "save-confirm-desc": "是否需要保存修改的内容？", "dont-save": "不保存", "save-changes": "保存", "create-robot-success": "添加成功", "delete-robot-error": "删除失败", "upload-document-max-size-error": "上传的图片大小不能超过1M", "file-format-error": "文件格式不受支持", "update-robot-permission-success": "设置成功", "llm-disabled-tip": "模型尚未安装，如有需要，请联系我们。", "embedding-search": "知识库内搜索", "confirm-del": "确定要删除吗？", "confirm-delete-tip": "此操作无法恢复", "setting-robot": "设置", "publish-robot": "发布", "share-robot": "分享", "publish-robot-info": {"front-permision": "可使用该机器人的用户", "back-permision": "可编辑该机器人的用户", "permision-0": "本人", "permision-1": "本部门所有人", "permision-2": "本部门及下级部门所有人", "permision-3": "全部", "permision-4": "自定义", "select-account": "选择组织架构", "custom-empty-tip": "自定义的用户数据不能为空"}, "create-robot-button": "新建机器人", "label": "机器人管理", "setting-debug": "设置和调试", "debug": "调试", "robot-knowlege": "知识库", "api-manage-label": "API管理", "embedding-search-empty": "知识库内搜索结果将显示在这里", "no-knowlege": "没有可用的文档", "no-knowlege-desc": "暂未导入任何文件", "role-setting-label": "权限设置", "user-manage-label": "用户管理", "role-manage-label": "角色管理", "department-manage-label": "部门管理", "embedding-search-detail": "来源", "document": {"document-name": "文档名称", "document-name-hint": "文档名称", "document-id": "文档ID", "status": "状态", "status-hint": "请选择文档状态", "status-0": "处理中", "status-1": "已可用", "status-2": "删除中", "status-3": "已删除", "status-4": "服务器错误", "status-5": "文件格式不可识别", "status-6": "文件为空", "status-7": "文件字符数量太大", "select-status-0": "处理中", "select-status-1": "已可用", "select-status-2": "失败", "characters": "字符数量", "hit-count": "命中次数", "account": "创建者", "update-time": "更新时间", "action": "操作", "document-type-error": "不支持的文件格式", "document-count-exceed": "最多上传10个文件", "add-document": "添加", "edit-document": "编辑", "upload-document": "文件上传", "upload-document-hint": "将文件拖拽至此处", "upload-document-type-hint": "支持的文件格式：.pdf、.txt、.docx", "upload-document-success": "上传成功", "document-content": "内容", "document-content-hint": "请输入文档内容", "multi-delete-document": "批量删除"}, "api-manage": {"api-key": "KEY", "name": "名称", "name-hint": "名称", "status": "状态", "status-delete": "删除", "update-time": "更新时间", "action": "操作", "create-api-key-success": "新建成功", "create-api-key": "新建", "update-api-key": "编辑API", "multi-delete-api-key": "批量删除", "is-multi-delete-api-key": "确认批量删除 KEY 吗？", "multi-delete-api-key-notice": "这次操作会批量删除 KEY，请谨慎确认。", "no-api": "没有可用的API", "no-api-desc": "暂未查找到任何API"}, "chat-debug": {"content-count": "字符数量", "hit-count": "命中次数", "token": "token", "search-hint": "请输入关键字", "search-notice": "关键字不能为空"}}, "contacts": {"title": "通讯录", "organize": {"menu-label": "组织联系人", "supervisor": "主管", "department": "部门", "company": "公司", "work-email": "企业邮箱", "work-phone": "工作电话", "user-id": "用户ID"}, "admin-panel": "管理面板", "leave-organize": {"menu-label": "退出企业", "unable-leave": "无法退出企业", "unable-leave-reason": "您目前是 {{companyName}} 的主要管理员。为确保帐户安全，请在退出前将您的管理员角色转移给其他用户。", "leave-confirm": "您确定要继续吗？", "leave-confirm-tip": "确认后，您将失去所有 {{companyName}} 服务的访问权限。此操作不可逆。"}, "my-groups": "我的群组", "i-created": "我创建的", "i-joined": "我加入的", "group-members": "{{count}}名成员", "new-contacts": "新的好友", "wait-for-verify": "等待验证", "added": "已添加", "rejected": "已拒绝", "reject": "拒绝", "agree": "同意", "want-to-add-you": "请求添加你为朋友", "my-contacts": "我的好友"}, "chat": {"title": "聊天", "type-a-message": "输入消息", "create-group": "创建群聊", "picture": "图片", "video": "视频", "file": "文件", "FILE": "FILE", "emoji": "表情", "you": "你", "create-group-chat": "创建了群聊", "leave-group-chat": "退出群聊", "kicked-out-group-chat": "{{who}}被{{opUser}}踢出群聊", "join-group-chat": "{{opUser}}邀请了{{users}}加入群聊", "change-group-name": "{{who}}修改群名为{{name}}", "quit-group-tip": "无法在已退出的群聊中发送消息！", "unknown-type-message": "未知类型消息", "invite": "邀请", "group-name": "群名称", "search-user": "搜索用户", "select-group-members": "选择群成员", "Expand": "下级", "organize-contacts": "组织联系人", "selected": "已选择", "select-at-least": "请至少选择一项", "group-name-required": "请输入群名称", "group-member-limit": "已超过群成员数量上限1000", "company-organize": "公司/组织", "messages": "消息", "send-messages": "发送消息", "settings": "设置", "group-members": "群成员", "group-member-list": "群成员列表", "view-more": "查看全部成员", "group-owner": "群主", "kick-out-member": "踢出成员", "kick-out-member-confirm": "您是否想把{{user}}踢出群聊？", "yes": "确认", "today": "今天", "yesterday": "昨天", "copy-success": "复制成功", "online": "在线", "offline": "离线", "me": "我", "add-contact": "添加好友", "search-user-id": "搜索 User ID", "search-no-user": "未搜索到相关结果", "add": "添加", "friend-verify": "好友验证", "verify-msg": "验证信息", "send": "发送", "add-success": "发送好友请求成功", "start-chat": "你们已经成为好友，可以开始聊天了！"}, "setting": {"title": "设置", "user-setting": "用户设置", "my-account": "我的账户", "profiles": "个人资料", "system-setting": "系统设置", "general": "常规", "keybinds": "按键绑定", "other": "其他", "feedback": "反馈", "accountDetails": "账户详情", "change-email": "更改邮箱", "change-email-ok": "邮箱更新成功", "password-set": "已设置密码", "no-set": "未设置", "change-mobile": "修改手机号码", "bind-mobile": "绑定手机号码", "change-mobile-ok": "手机号码更新成功", "change-password": "更改密码", "change-password-ok": "密码更新成功", "authentication": "身份验证", "delete-confirm-tip": "删除帐户 - 您确定吗？", "delete-confirm": "确认删除帐户", "confirm-delete": "确认删除", "verify-password": "请输入您的登录密码以验证您的身份", "delete-confirm-text1": "删除您的帐户是永久性的。您的所有个人数据、个人文档、聊天记录和设置都将被永久删除，且无法撤消。", "delete-confirm-text2": "您创建的共享文件或项目将保留在团队中，直到公司/团队解散。", "delete-confirm-text3": "删除您的帐户将使您无法访问所有 vama 服务，包括 AI 助手、聊天、知识库等。", "delete-confirm-text4": "vama 不提供帐户删除宽限期。一旦确认，您的帐户将立即被删除。请谨慎考虑。", "delete-confirm-text5": "如果您确定要继续删除账户，请点击“继续”并完成身份验证。", "cannot-delete-account": "该帐户目前无法删除。您仍有未离开的公司/团队。", "deleteAccount": "注销账号", "deleteDes": "账户注销后，所有账户内容和数据将永久丢失", "profilesubTitle": "您可以在此处切换查看不同公司/组织的名片信息", "avatar": "头像", "changeAvatar": "更改头像", "restore": "恢复默认设置", "position": "职位", "email-verifiy": "邮箱验证", "mobile-verifiy": "手机号码验证", "new-email": "新邮箱", "new-password": "新密码", "new-mobile": "新手机号码", "delete-account-success": "账号注销成功", "profile-in": "{{company}}中的个人资料", "profile-in-text": "个人资料", "update-image": "编辑图片", "preview": "预览", "change-no-save": "更改未保存", "save-change": "保存更改", "keybind-desc": "所有按键绑定仅适用于用户中心", "ai-bot": "人工智能机器人", "send-message": "发送消息", "new-line-input": "输入框中换行", "stop-generating": "停止生成", "feedback-desc": "我们真诚地感谢您的反馈——这有助于我们改进产品！"}, "invite": {"sign-as": "登录身份", "apply-join": "申请加入", "no-account": "没有 vama 账号？", "enter-vama": "进入 vama", "use-another-account": "使用其他 vama 账号：", "resister": "注册并加入团队", "switch": "切换", "to-vama": "前往 vama", "invitation-expired": "此邀请链接已过期。", "invitation-used": "此邀请链接已被使用。", "contact-obtain": "请联系贵公司/组织的管理员获取新的链接。", "register-title": "注册 vama 加入团队", "register-submit": "注册并申请加入", "have-account": "已经有 vama 账户了吗？", "use-account-join": "使用其他帐户", "reset-apply": "重置并申请加入"}, "bots-marketplace": {"title": "机器人市场", "explore-bots": "探索机器人", "explore-bots-desc": "发现根据您的独特需求，精心设计、包含指令、技能和额外知识的机器人", "search-input-placeholder": "搜索机器人名称或创建者", "owner": "所有者", "usage": "使用数量", "default-model": "默认 AI 模型", "enhanced-model": "增强型 AI 模型", "ai-creativity": "AI 模型创造力", "relevance": "知识相关性", "files": "文件", "published": "已发布", "unpublished": "未发布"}, "orphaned-manage": {"title": "机器人回收管理"}, "info-hub": {"title": "消息中心", "ann": "公告", "ann-manage": "公告管理", "active": "活动", "archived": "已存档", "published": "已发布", "drafts": "草稿", "rule": {"title": "标题无效"}, "pin": "置顶", "unpin": "取消置顶", "publish": "发布", "unpublish": "撤回", "activation": "激活", "archive": "存档", "filter-tags": "筛选标签", "search-input": "搜索公告或发布者", "show-unread": "显示未读", "add-ann": "新建公告", "edit-ann": "编辑公告", "delete-ann-confrim": "确认删除该公告吗？", "ann-title": "标题", "behalf-of": "发布代表", "priority": "优先级", "content": "内容", "publish-type": "发布类型", "publish-time": "发布时间", "validity-period": "公告有效期", "validity-until": "有效期至", "attachments": "附件", "personal": "个人", "personal-tip": "以个人名义发布", "depart-tip": "以部门的名义发布", "company-tip": "以公司名义发布", "critical": "严重", "important": "重要", "normal": "正常", "publish-at": "定时发布", "publish-now": "立即发布", "forever": "永久有效", "no-attachment": "暂无附件", "publish-success": "发布成功", "ann-details": "公告详情", "publish-ann": "发布公告", "file-exceeds": "文件大小超出限制（20M）", "time-error": "设置的时间早于当前时间，请设置未来的时间。", "upload-error": "上传附件失败", "operation-log": "操作日志", "operator-name": "发布者", "operator-time": "操作时间", "update-validity": "修改有效期", "unpublish-ann": "撤销发布", "publish-range": "可接收该公告的用户", "add-tag": "创建标签", "add-select-tag": "选择或创建一个选项", "tag-limit": "最多支持创建{{count}}个标签", "tag-color": "颜色", "ai-summary": "AI 摘要", "ai-summary-tip": "您可点击生成文章摘要", "no-summary": "暂无摘要", "char-count": "字符数", "unsurpport-image-format": "不支持的图片格式", "image-size-limit": "图片大小不能超过{{num}}MB", "default": "默认", "orange": "橙色", "yellow": "黄色", "lime": "草绿", "green": "绿色", "cyan": "青色", "purple": "紫色", "pink": "粉色", "red": "红色", "gray": "灰色", "search-tag": "搜索选项"}, "robot-iframe": {"title": "内嵌框架", "inter-code": "集成代码", "light-mode": "浅色模式", "dark-mode": "深色模式", "plugin-name": "插件名称", "plugin-introduction": "插件简介", "talk-to": "与{{robot}}交谈..."}}