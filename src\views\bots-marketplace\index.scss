@import "../../styles/mixin.scss";

.bots-marketplace {
    $cardWidth: 392px;
    $cardGap: 20px;

    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    background: var(--colorBgBase);
    .page-nav-area {
        flex-shrink: 0;
        width: 100%;
        &.hidden {
            display: none;
        }
    }
    .page-nav {
        flex-shrink: 0;
        width: 100%;
        height: var(--page-nav-height);
        padding: 0 20px;

        .nav-title {
            flex-shrink: 0;
            font-weight: 600;
            font-size: 18px;
            color: var(--colorText);
            line-height: 24px;
        }
    }
    .page-main {
        flex: 1;
        overflow: auto;
        padding: 20px 40px;
        padding-right: 30px;
        > .infinite-scroll-component__outerdiv {
            width: 100%;
            display: flex;
            justify-content: center;
        }
        .infinite-scroll-component.infinite-scroll {
            display: flex;
            flex-wrap: wrap;
            gap: $cardGap;
            // justify-content: center;
            width: calc($cardWidth * 3 + $cardGap * 2);
        }
    }
    .page-empty {
        height: 600px;
    }
    $space: 12px;
    // 卡片/列表样式

    .card-list.grid {
        display: flex !important;
    }

    // 卡片/网格样式
    .card-list-item {
        min-height: 163px;
        flex-shrink: 0;
        padding: 0;
        border: 1px solid transparent;
        background: var(--colorFillQuaternary);
        width: $cardWidth;
        &:hover {
            // border-color: var(--colorBorder);
            // .cl-operate {
            //     display: none;
            // }
            // .cl-operate.cl-operate-btns {
            //     display: flex;
            //     padding-top: 7px;
            //     padding-bottom: 14px;
            // }
        }
    }
    .cl-content {
        flex: 1;
        padding: $space 16px 0;
    }
    .cl-content-img {
        width: 48px;
        height: 48px;
        border-radius: 10px;
        border: 1px solid var(--colorBorderSecondary);
        flex-shrink: 0;
    }
    .cl-content-text {
        flex: 1;
    }
    .cl-content-title {
        margin-bottom: 4px;
        @include ellipsis-multiline(1);
    }
    .cl-content-llm {
        font-weight: 400;
        font-size: 12px;
        color: var(--colorTextTertiary);
        line-height: 20px;
        .svg-icon {
            font-size: 16px;
        }
        @include ellipsis-multiline(1);
    }
    .cl-content-bottom {
        margin: 10px 0;
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
        color: var(--colorTextSecondary);
        @include ellipsis-multiline(2);
    }
    .cl-operate {
        padding: 8px 0 $space;
        width: calc(100% - $space * 2);
        margin: 0 auto;
        border-top: 1px solid var(--colorBorderSecondary);
        font-weight: 400;
        font-size: 12px;
        color: var(--colorTextTertiary);
        line-height: 20px;
        .svg-icon {
            font-size: 16px;
        }
        .more {
            width: 32px;
            height: 32px;
            padding: 8px;
            color: var(--colorIcon);
            flex-shrink: 0;
        }
        .svg-icon.icon_org {
            font-size: 12px;
        }
    }
    .cl-operate-left {
        flex: 1;
        overflow: hidden;
        .cl-operate-left-con {
            flex: 1;
            overflow: hidden;
        }
        .cl-name {
            flex: 1;
            @include ellipsis;
        }
    }
    .cl-operate-btns {
        display: none;
    }
    .margin-left-auto {
        margin-left: auto;
    }
    .top-area {
        flex-shrink: 0;
        background: linear-gradient(180deg, rgba(180, 63, 255, 0.05) 0%, rgba(180, 63, 255, 0) 100%);
        // background:linear-gradient(90deg, var(--colorText, rgba(0, 0, 0, 0.77)) 0%, var(--colorText, rgba(0, 0, 0, 0.77)) 25.96%, rgba(202, 76, 152, 0.88) 74.52%, rgba(88, 65, 210, 0.88) 100%);
        padding-top: 32px;
        padding-bottom: 12px;
        .txt,
        .keyword-input {
            width: 420px;
            height: 38px;
            .ant-input-suffix {
                color: var(--colorIconTertiary);
                .svg-icon {
                    font-size: 16px;
                }
            }
        }
        .title {
            @include font(38px, 46px, 600);
            background: linear-gradient(
                90deg,
                var(--colorText, rgba(0, 0, 0, 0.77)) 0%,
                var(--colorText, rgba(0, 0, 0, 0.77)) 25.96%,
                rgba(202, 76, 152, 0.88) 74.52%,
                rgba(88, 65, 210, 0.88) 100%
            );
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .txt {
            @include font(16px, 24px, 400, var(--colorTextTertiary));
            text-align: center;
        }
    }
    .icon-collect {
        font-size: 16px;
        width: 20px;
        height: 20px;
        &.filled {
            color: var(--colorWarning);
        }
    }
    .card-loading-area {
        flex-basis: 100%;
        margin-left: 0;
        padding-bottom: 10px;
        .loading-more {
            padding-top: 0;
        }
    }
    .ant-divider-plain.ant-divider-with-text.card-no-more-data {
        margin-top: 0;
    }
}
