@import '../../../styles/mixin.scss';
.orga-area{
    .ant-breadcrumb{
        flex-shrink: 0;
        width: 100%;
        padding: 14px 16px;
        border-bottom: 1px solid var(--colorBorderSecondary);
        .ant-breadcrumb-separator{
            font-size: 16px;
            margin: 0 4px;
        }
        li:not(:last-child){
            &:hover{
                color: var(--colorText);
            }
        }
        .ant-breadcrumb-link{
            cursor: pointer;
            word-break: break-word;
        }
    }
    .orga-content{
        flex:1;
        overflow: auto;
        overflow-x: hidden;
        width: 100%;
    }
    .orga-list-items{
        padding: 16px;
        .orga-list-item{
            padding: 6px 8px;
            border-radius: 12px;
            cursor: pointer;
            &:hover{
                background: var(--colorFillTertiary);
            }
        }
        
        .orga-list-item-icon{
            flex-shrink: 0;
        }
        .orga-list-item-icon.dept{
            color: var(--colorPrimary);
            background: var(--colorPrimaryBg);
            font-size: 16px;
            padding: 8px;
            border-radius: 12px;
        }
        .orga-list-item-name{
            @include font(14px,22px,500);
            &.dept{
                // flex:1;
                @include ellipsis();
            }
            &.user {
                // flex:1;
                // max-width: calc(100% - 40px);
                // flex: ;
                overflow: hidden;
            }
        }
        .orga-list-item-count,.user-position{
            @include font(12px,18px,400,var(--colorTextQuaternary));
            flex-shrink: 0;
        }
        .name-item{
            max-width: 100%;
            >div:first-child{
                // max-width:90%;
                @include ellipsis();
            }
            .is-super{
                background: #E8F3FF;
                @include font(10px,14px,400,#165DFF);
                border-radius: 6px;
                padding: 2px 6px;
                flex-shrink: 0;
            }
        }
        .user-position{
            max-width:100%;
            @include ellipsis();
        }
    }
    .orga-list-item-arrow{
        margin-left: auto;
        font-size: 16px;
        color: var(--colorIconQuaternary);
    }
}

.contacts-user-info-popover{
    $borderRadius:12px;
    width: 330px;
    border-radius: $borderRadius;
    .ant-popover-inner{
        padding: 0;
    }
    .ant-popover-content{
        border-radius: $borderRadius;
        width: 330px;
        height: 482px;
    }
    .ant-popover-inner-content{
        background: url('../../../icons/png/contacts/contact-user-bg.png')no-repeat left top;
        border-radius: $borderRadius;
    }
    .name-avatar-position{
        padding:54px 20px 0;
    }
    .UserAvatar--inner{
        border:2px solid var(--colorBgElevated);
    }
    .name-position{
        flex: 1;
        overflow: hidden;
        >div:first-child{
            @include font($line-height:22px,$color:var(--colorTextLightSolid));
            @include ellipsis();
        }
        >div:last-child{
            @include font(12px,18px,400,var(--colorTextSecondary));
            @include ellipsis();
        }
    }
    .infos{
        padding: 16px 20px 20px;
        height: 380px;
        overflow-y: auto;
    }
    .field-name{
        @include font(12px,18px,400,var(--colorTextTertiary));
        flex-shrink: 0;
        width: 68px;
    }
    .field-value{
        @include font(12px,18px,400);
        word-break:break-word;
        word-wrap: break-word;
    }
}