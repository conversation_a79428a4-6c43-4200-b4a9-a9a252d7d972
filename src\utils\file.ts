// 文件大小格式化
function formatFileSize(bytes: number) {
    if (bytes < 1024) return "1KB"; // 小于1KB的文件显示为1KB

    const units = ["KB", "MB", "GB", "TB"];
    let size = bytes / 1024; // 先转换为KB
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024;
        unitIndex++;
    }

    const unit = units[unitIndex];
    // 四舍五入到1位小数
    const result =
        unit === "KB"
            ? `${Math.ceil(Number(size))}${unit}`
            : `${Number(size.toFixed(1))}${unit}`;
    return result;
}

// 下载文件
function downloadBlobFile(response: any, fileName: any, fileType?: string) {
    // fileType为none表示fileName包含文件后缀名
    const blobData = response.data
    const blob = new Blob([blobData])
    if (!fileType ||  fileType != 'none') {
        // 从响应头部获取后缀名
        let filename = response.headers['filename']
        if (!filename) {
            try {
                const disposition = response.headers['content-disposition']
                filename = disposition.split(';')[1].split('filename=')[1]
                filename = ['"', "'"].indexOf(filename.charAt(filename.length - 1)) != -1 ? filename.slice(0, filename.length - 1) : filename
            } catch (error) {
                console.log(error)
            }
        }
        fileType = filename ? filename.slice(filename.lastIndexOf('.') + 1) : '' // 获取文件后缀名
    }
    const file = fileType != 'none' ? `${fileName}.${fileType}` : fileName
    if ('download' in document.createElement('a')) { // 非IE下载
        const elink = document.createElement('a')
        elink.download = file
        elink.style.display = 'none'
        elink.href = URL.createObjectURL(blob)
        document.body.appendChild(elink)
        elink.click()
        URL.revokeObjectURL(elink.href) // 释放URL 对象
        document.body.removeChild(elink)
    } else { // IE10+下载
        // navigator?.msSaveOrOpenBlob(blob, file)
    }
}

// 根据文件名称获取文件类型
function getFileTypeByName (fileName:any){
    if (!fileName) return ''
    let fileType = fileName.substring(fileName.lastIndexOf(".") + 1);
    fileType = fileType ? fileType.toLowerCase() :fileType
    return fileType
}

export { formatFileSize, downloadBlobFile, getFileTypeByName }