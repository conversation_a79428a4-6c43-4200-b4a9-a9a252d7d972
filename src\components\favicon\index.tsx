import React, { useEffect } from 'react';

interface FaviconProps{
  href: string
}

const Favicon:React.FC<FaviconProps> = ({ href }) => {
  useEffect(() => {
    const link:any = document.querySelector('link[rel~="icon"]')|| document.createElement('link');
    if (!link) {
      console.error('Favicon link not found.');
      return;
    }
    link.href = href;
  }, [href]);
 
  return null;
};
 
export default Favicon;