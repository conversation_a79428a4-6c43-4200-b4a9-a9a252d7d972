@import "@/styles/mixin.scss";
.ann-operate-page {
    height: 100%;
    width: 100%;
    .wrap-nav {
        border-bottom: 1px solid var(--colorBorderSecondary);
        background: var(--colorBgBase);
    }
    .icon-back {
        cursor: pointer;
        font-size: 20px;
        padding: 8px;

    }
    .wrap-content {
        background: var(--colorBgLayout);
        padding-bottom: 20px;
    }
    .ant-form {
        background: var(--colorBgBase);
        padding: 20px;
        border-radius: 10px;
        min-height: 100%;
        width: 1000px;
        margin: 0 auto;
    }
    .ant-picker{
        width: 100%;
        &.ant-picker-filled.ant-picker-disabled{
            border: 0;
        }
    }
    .ant-list-item{
        position: relative;
    }
    .ant-upload-list {
        padding-top: 12px;
        max-width: 60%;
        .ant-upload-list-item {
            max-width: 100%;
            width: auto;
            padding: 8px;
            // padding-left: 20px;
            font-weight: 400;
            font-size: 14px;
            color: var(--colorText);
            line-height: 20px;
            cursor: pointer;
            height: 36px;
            margin-top: 0;
            &:hover {
                background: var(--colorFillQuaternary);
                border-radius: 12px;
                .attachment-item-remove-icon{
                    display: inline-block;
                }
            }
            img {
                width: 16px;
            }
            // .ant-upload-list-item-actions {
            //     width: 16px;
            //     .ant-btn-variant-text:not(:disabled):not(.ant-btn-disabled):hover {
            //         background: transparent;
            //     }
            // }
        }
        .ant-upload-icon {
            display: none;
        }
        .attachment-item-file-icon{
            font-size: 16px;
            color: var(--colorTextSecondary);
        }
        .attachment-item-remove-icon{
            margin-left: auto;
            display: none
        }
        // .icon_attachment {
        //     position: absolute;
        //     top: 10px;
        //     left: 8px;
        //     color: var(--colorIconTertiary);
        //     font-size: 16px;
        //     z-index: 10;
        // }
    }
    .no-data{
        @include font(12px,18px,400,var(--colorTextTertiary));
    }
    .ant-form-item.disabled{
        .ant-upload-wrapper{
            height: 0;
            display: none;
        }
        .ant-upload-list{
            padding-top: 0;
        }
        .ant-upload-list-item:hover{
            .attachment-item-remove-icon{
                display: none;
            }
        }
    }
}
.behalf-of-selector-text{
    .svg-icon{
        font-size: 18px;
    }
    .desc{
        @include font(12px,18px,400,var(--colorTextTertiary));
        padding-left: 26px;
    }
}
