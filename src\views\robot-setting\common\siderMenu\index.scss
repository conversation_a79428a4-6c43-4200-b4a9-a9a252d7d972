.page-detail-sider{
    height: 100%;
    .layout-menu{
        padding-top: 4px;
    }
    .layout-menu {
        flex: 1;
        display: flex;
        flex-flow: column;
        height: 100%;
        padding: 0 16px;
        padding-bottom: 80px;
        // width: 220px;
        .logo {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 60px;
            img {
                width: 48px;
                height: 48px;
            }
            h1 {
                display: inline-block;
                margin: 0 0 0 0;
                color: white;
                font-weight: 600;
                font-size: 18px;
                vertical-align: middle;
                animation: pro-layout-title-hide 0.3s;
                white-space:nowrap;
                text-overflow:ellipsis
            }
        }
        .toggle-collapse {
            display: flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            left: 100%;
            top:50%;
            width: 24px;
            height: 24px;
            z-index: 200;
            font-size: 20px;
            color: var(--colorFill);
            cursor: pointer;
            margin-top: -12px;
            &.collapsed{
                transform:rotate(180deg);
            }
        }
        .ant-menu {
            flex: 1;
            overflow-y: auto;
        }
        .ant-menu-inline .ant-menu-item,.ant-menu-submenu-title{
            width: 100%;
            font-weight: 500;
            font-size: 14px;
            color: var(--colorText);
            line-height: 20px;
            height: 32px!important;
            line-height: 32px!important;
            margin: 6px 0;
            padding: 0 8px;
            &.ant-menu-item-selected{
                background: var(--colorPrimary);
                color: var(--colorPrimaryContrasting);
                border-radius: 12px;
            }
            
            .ant-menu-item-icon{
                font-size: 16px;
                min-width: 16px;
            }
            &:not(.ant-menu-item-selected):hover{
                background: var(--colorFillQuaternary);
                border-radius: 12px;
            }
        }
        .ant-menu-submenu {
            margin-top: 6px;
            margin-bottom: 6px;
        }
        .ant-menu-inline .ant-menu-sub.ant-menu-inline{
            background: transparent;
            .ant-menu-item{
                padding-left: 34px!important;
            }
        }
        .ant-menu .ant-menu-submenu-arrow{
            inset-inline-end: 8px;
        }
    }
}