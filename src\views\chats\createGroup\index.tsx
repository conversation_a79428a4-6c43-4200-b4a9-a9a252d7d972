
import { connect } from "react-redux";
import { useEffect, useState, useRef, useCallback,Fragment  } from "react";
import { Modal, message, Flex, Button, Input, Breadcrumb, Empty, Checkbox, Spin } from "antd"
import { LoadingOutlined } from "@ant-design/icons";
import { useTranslation } from 'react-i18next';
import { $trim, $isNull, $deduplicateByProp } from '@/utils/common'
import UserAvatar from "@/components/userAvatar";

import { api_get_tenant_depts_users, api_get_tenant_depts_crumbs, api_search_user } from '@/api/chat'
import debounce from 'lodash/debounce';

import SvgIcon from '@components/svgicon';
import './index.scss'
import classNames from "classnames";

const countLimit = 1000
const firstBreadcrumItem = [{ id: -1, title: 'contacts.title', "parentId": null, type: 'root' }] // 默认面包屑第一层数据
const CreateGroup = (props: any) => {
    const { t } = useTranslation()
    const { userInfo, onCreateGroup, resource: parentResource, onCreateOneConversation, activeCompanyId } = props
    const currentUserId = userInfo.imUserId
    const groupedUsers = parentResource.groupedUsers || null // 邀请用户-群聊已存在的用户ID
    const singleUser = parentResource.singleUser || null // 用户单聊-添加群聊，需默认选中该用户
    const groupId = parentResource.groupId || null // 邀请用户群聊ID
    const isInvite = groupedUsers !== null
    // console.log('groupedUsers',groupedUsers)
    // console.log('singleUser',singleUser)
    const breadcrumItemsRef: any = useRef()
    const selectedIdsRef: any = useRef()

    const [isSubmiting, setIsSubmiting] = useState(false) // 是否提交中
    const [groupName, setGroupName] = useState('') // 群名
    const [searchValue, setSearchValue] = useState('') // 搜索用户名
    const [organizeBreadcrumItems, setOrganizeBreadcrumItems] = useState<any[]>([]) // 组织架构 面包屑数据
    const [currentDepartmentData, setCurrentDepartmentData] = useState<any>([]) // 当前部门数据
    const [isLoading, setLoading] = useState(false)
    const [isRootContacts, setIsRootContacts] = useState(true) // 是否处于根目录
    const [rootContacts, setRootContacts] = useState([]) // 根目录通讯录数据
    const [selectedDatas, setSelectedDatas] = useState<any[]>([])
    const [searchUserResult, setSearchUserResult] = useState<any[]>([]) // 搜索用户结果
    const [searchResultNull, setSearchResultNull] = useState(false) // 搜索用户结果为空
    const [isSearching, setIsSearching] = useState(false) // 是否搜索中

    // 初始化
    useEffect(() => {
        getRootContactssData()
    }, [])

    // 监听父组件传参
    useEffect(() => {
        // console.log('resource变了', parentResource)
        // setCurrentUserName(resource.currentUserName)
        if (!isInvite) { // 群聊-邀请用户
            let _selectedDatas = [{
                isCheck: false, imUserId: currentUserId, userName: parentResource.currentUserName, postName: '', isCurrent: true,
                avatar: parentResource?.currentUserAvatar
            }, ...selectedDatas]
            // console.log('_selectedDatas11',_selectedDatas)
            const singleUser = parentResource.singleUser
            if (singleUser && singleUser.userID !== currentUserId) {// 用户单聊-添加群聊
                _selectedDatas = [..._selectedDatas, { isCheck: true, imUserId: singleUser.userID, userName: singleUser.userName, postName: '', isCurrent: false, avatar: singleUser?.avartar }]
            }
            // console.log('_selectedDatas22',_selectedDatas)
            setSelectedDatas(_selectedDatas)
        }
    }, [parentResource.currentUserName, singleUser, groupedUsers])

    useEffect(() => {
        breadcrumItemsRef.current = [...organizeBreadcrumItems]
    }, [organizeBreadcrumItems])

    // 监听选中项
    useEffect(() => {
        const ids: any[] = selectedDatas.map((i: any) => i.imUserId)
        // console.log('selectedDatas 变了ids',ids)
        selectedIdsRef.current = ids
    }, [selectedDatas])

    const getRootContactssData = async () => {
        let res: any = await getContactsData()
        // console.log('res',res)
        setRootContacts(res)
    }

    // 获取根目录通讯录数据
    const getContactsData = async (p: any = {}) => {
        setLoading(true)
        p.currentTenantId = activeCompanyId
        let res: any = await api_get_tenant_depts_users(p).catch(() => {
            setLoading(false)
        })
        setLoading(false)
        // console.log('res',res)
        if (!res) return []
        return res
    }

    // 获取当前公司/部门数据
    const getActiveDeptUserDatas = async (target: any, isBreadcrum = false) => {
        // console.log('target', target)
        // console.log('selectedIds',selectedIdsRef.current)
        setIsRootContacts(false)
        // 获取当前公司/部门数据
        const p: any = {}
        if (target.type === "dept") {
            p.deptId = isBreadcrum ? target.id : target.deptId
            p.tenantId = target.tenantId
        } else {
            p.tenantId = isBreadcrum ? target.id : target.tenantId
        }
        // console.log('p', p)
        let datas = await getContactsData(p)
        datas = datas.map((i: any) => {
            i.isCheck = selectedIdsRef.current.includes(i.imUserId) || (!isInvite && i.imUserId === currentUserId)
            // i.tenantId = i.tenantId
            return i
        })
        console.log('获取当前公司/部门数据 datas', datas)
        setCurrentDepartmentData([...datas])
    }

    // 获取组织架构 面包屑数据
    const getOrganizeBreadcrumItems = async (target: any, isClickBreadcrum?: boolean) => {
        // console.log('getOrganizeBreadcrumItems target', target)
        const base = [...firstBreadcrumItem]
        //  id=-1为根
        if (target.type === 'root') {
            setOrganizeBreadcrumItems(base)
            return
        }
        // tenantId deptId
        const p: any = { tenantId: target.tenantId, currentTenantId: activeCompanyId }
        if (target.type === 'dept') {
            p.deptId = isClickBreadcrum ? target.id : target.deptId
        }
        // 非公司层级
        const res: any = await api_get_tenant_depts_crumbs(p).catch(() => {
        })
        if (!res) return
        // console.log('res',res)
        let path_datas = res || []
        path_datas = path_datas.map((i: any) => {
            const isTenant = i.type === 'tenant'
            return { id: isTenant ? i.tenantId : i.deptId, tenantId: i.tenantId, title: isTenant ? i.tenantName : i.deptName, type: i.type } // , parentId: isTenant ? -1: i.parentId
        })
        let crumItems = [...base, ...path_datas]
        // console.log('crumItems', crumItems)
        crumItems = renderBreadcrum(crumItems)
        // console.log('获取组织架构 面包屑数据', crumItems)
        setOrganizeBreadcrumItems(crumItems)
    }

    const renderBreadcrum = (resource: any[]) => {
        return resource.map((i, index: number) => {
            i.title = i.id === -1 ? t(i.title as string) : i.title
            // return { title: i.title, key: i.id, id: i.id, onClick: () => onClickBreadcrum(i), parentId: i.parentId, type: i.type }
            i.onClick = index !== (resource.length - 1) ? () => onClickBreadcrum(i, index) : null
            i.index = index
            return i
        })
    }

    // 面包屑 部门点击
    const onClickBreadcrum = (target: any, index: number) => {
        // console.log('onClickBreadcrum target', target)
        setIsRootContacts(target.id === -1) // 根
        setCurrentDepartmentData([])
        if (target.id !== -1) {
            setIsRootContacts(false)
            getActiveDeptUserDatas(target, true)
        }
        getOrganizeBreadcrumItems(target, true)

    }

    const onSelectContact = (e: any, target: any, parent?: any) => {
        // console.log('target',target)
        e.stopPropagation()
        if (parent) target.parentId = parent.tenantId
        getActiveDeptUserDatas(target)
        getOrganizeBreadcrumItems(target)
    }

    // 确认提交
    const onOK = async () => {
        // console.log('selectedDatas', selectedDatas)
        // console.log('isInvite',isInvite)
        const minLength = isInvite ? 0 : 1
        // 校验选中用户
        if (selectedDatas.length === minLength) {
            message.error(t('chat.select-at-least'))
            return
        }
        // 若选中一个人，点击确定直接进入跟已选择用户的对话窗口，不校验群名是否为空
        if (!isInvite && selectedDatas.length === (minLength + 1)) {
            props.onClose()
            selectedDatas && onCreateOneConversation(selectedDatas[minLength], isInvite)
            return
        }
        // 若选中大于1人，校验群名
        if (!isInvite && selectedDatas.length > (minLength + 1) && $isNull($trim(groupName))) {
            message.error(t('chat.group-name-required'))
            return
        }
        // 创建群聊
        const memberUserIDs = selectedDatas.filter((i: any) => !i.isCurrent).map((i: any) => i.imUserId)
        // const userId = userInfo.imUserId
        // const memberUserIDs = [userId,...selectIds]
        console.log('memberUserIDs', memberUserIDs)
        if (onCreateGroup) {
            setIsSubmiting(true)
            const isOk = await onCreateGroup(groupName, memberUserIDs, isInvite, groupId).catch(() => {
                setIsSubmiting(false)
            })
            setIsSubmiting(false)
            // console.log('isOk',isOk)
            if (!isOk) return
            props.onClose()
        }
    }

    // 取消
    const onCancel = () => {
        props.onClose()
    }

    // 输入群名
    const onChangeGroupName = (e: any) => {
        const value = e.target.value
        // console.log('value',value)
        setGroupName(value)
    }
    const onChangeSearchValue = (e: any) => {
        const value = e.target.value
        // console.log('value', value)
        setSearchValue(value)
        debouncedOnChange(value)
        setIsSearching(!$isNull($trim(value)))
    }

    // 列表项点击
    const onClickListItem = (target: any) => {
        console.log('onClickListItem target', target)
        if (target.type === 'user') return
        getActiveDeptUserDatas(target)
        getOrganizeBreadcrumItems(target)
    }

    const getOrganizeIcon = (target: any) => {
        if (isSearching) {
            return <UserAvatar name={target.userName} src={target?.avatar} userId={target?.imUserId}/>
        }
        let icon = null
        if (target.type === 'dept') {
            icon = <SvgIcon svgName="menu_org" />
        }
        if (target.type === 'user') {
            icon = <UserAvatar name={target.userName} src={target?.avatar} userId={target?.imUserId}/>
        }
        return <Flex align="center" justify="center" className={`orga-list-item-icon ${target.type}`}>{icon}</Flex>
    }

    // 取消 or 选择
    const onToggleSelectItem = (e: any, item: any, index: number) => {
        // console.log('item',e, item)
        let result: any = [...selectedDatas]
        if (item.isCheck) {// 取消选择
            result = result.filter((i: any) => i.imUserId !== item.imUserId)
            if (isSearching) {
            }
        } else { // 选中
            const limit = isInvite ? countLimit - groupedUsers.length : countLimit
            if (selectedDatas.length === limit) {
                message.error(t('chat.group-member-limit'))
                return
            }
            result.push(item)
            // 去重result
            result = $deduplicateByProp(result, 'imUserId')
            // console.log('result', result)
        }
        setCurrentDepartmentData((prevItems: any) => prevItems.map((pi: any, i: number) =>
            item.imUserId === pi.imUserId ? { ...pi, isCheck: !item.isCheck } : pi
        ))
        console.log('选择最终结果result', result)
        setSelectedDatas(result)
        if (isSearching) {
            let _searchUserResult: any = [...searchUserResult]
            _searchUserResult[index].isCheck = !_searchUserResult[index].isCheck
            setSearchUserResult(_searchUserResult)
        }
    }

    // 删除选中
    const onRemoveSelected = (item: any, index: number) => {
        // console.log('item', item)
        const datas: any[] = [...selectedDatas]
        datas.splice(index, 1)
        setSelectedDatas(datas)
        setCurrentDepartmentData((prevItems: any) => prevItems.map((pi: any, i: number) =>
            item.imUserId === pi.imUserId ? { ...pi, isCheck: false } : pi
        ))
        if (isSearching) {
            setSearchUserResult((prevItems: any) => prevItems.map((pi: any, i: number) =>
                item.imUserId === pi.imUserId ? { ...pi, isCheck: false } : pi
            ))
        }
    }
    const initSearchResult = () => {
        setSearchUserResult([])
        setSearchResultNull(false)
    }

    // 防抖输入框
    const debouncedOnChange = useCallback(
        debounce(async (newValue: string) => {
            // console.log('ssss',newValue)
            if (newValue === '' || newValue === null || newValue === undefined) {
                initSearchResult()
                return
            }
            // 搜索
            let res: any = await api_search_user({ name: newValue, currentTenantId: activeCompanyId }).catch(err => {
                console.log(err)
            })
            if (!res) return
            setSearchResultNull(res && res?.length === 0) // 未有匹配公司
            // console.log('selectedIdsRef.current', selectedIdsRef.current)
            res = res.map((i: any) => {
                i.isCheck = selectedIdsRef.current.includes(i.imUserId)
                return i
            })
            // console.log("api_search_user res", res)
            setSearchUserResult(res)
        }, 200), // 设置延迟
        []);

    const renderUserItem = (item: any) => {
        return (
            <Flex align="center" style={{ maxWidth: '100%' }} gap={8}>
                {getOrganizeIcon(item)}
                <Flex className="orga-list-item-name user" vertical>
                    <Flex className="name-item" gap={8} align="center">
                        <div>{item.userName}</div>
                    </Flex>
                    <div className="user-position">{item.position}</div>
                </Flex>
            </Flex>
        )
    }

    return (
        <Modal
            className={"chat-create-group-modal border no-padding"}
            title={isInvite ? t('chat.invite') : t('chat.create-group')}
            open={true}
            onOk={onOK}
            onCancel={onCancel}
            maskClosable={false}
            centered={true}
            width={900}
            okText={t('app.ok')}
            cancelText={t("app.cancel")}
            cancelButtonProps={{ variant: "filled", color: "default" }}
            okButtonProps={{ disabled: isSubmiting }}
            footer={undefined}
        >
            {/* 群名 */}
            {
                !isInvite ?
                    <Flex className="field-item" vertical gap={6}>
                        <div className="field-name">{t('chat.group-name')}</div>
                        <Input value={groupName} onChange={onChangeGroupName} placeholder={t('common.please-input') as string} variant="filled"
                            allowClear={{ clearIcon: <SvgIcon svgName="icon_input_clear" /> }} maxLength={16} />
                    </Flex> : null
            }
            {/* 组织架构 */}
            <Flex className="group-member-con" vertical gap={12}>
                <div className="field-name">{t('chat.select-group-members')}</div>


                <Flex className="group-member-pick">
                    {isLoading ?
                        <Flex className="picke-options"><Spin className="modal-spin" size="large" indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />} /> </Flex> :
                        <Flex className="picke-options" vertical gap={12}>
                            <Input className="search" value={searchValue} onChange={onChangeSearchValue} placeholder={t('chat.search-user') as string} variant="filled"
                                allowClear={{ clearIcon: <SvgIcon svgName="icon_input_clear" /> }}
                                suffix={searchValue === '' ? <SvgIcon svgName="icon_search" /> : null}
                                maxLength={500} />
                            {/* 面包屑 */}
                            { //搜索结果
                                isSearching ?
                                    <>
                                        {
                                            searchResultNull ?
                                                <Empty className="page-empty full" image={null} description={
                                                    <>
                                                        <div>{t('common.no-data')}</div>
                                                        <div></div>
                                                    </>}
                                                /> :

                                                <Flex className="orga-list-items" vertical gap={12}>
                                                    {
                                                        searchUserResult.map((item: any, index: number) => {
                                                            return (
                                                                <Flex key={item.imUserId + '' + index} gap={8}
                                                                    className={classNames(["orga-list-item", item.type])} align="center">
                                                                    <Checkbox
                                                                        disabled={item.imUserId === currentUserId || (
                                                                            groupedUsers && (groupedUsers.includes(item.imUserId)
                                                                            ))} //群聊邀请用户，已在群里的用户不能被选择
                                                                        checked={item.isCheck}
                                                                        onChange={(e) => onToggleSelectItem(e, item, index)} >
                                                                        {renderUserItem(item)}
                                                                    </Checkbox>
                                                                </Flex>
                                                            )
                                                        })
                                                    }
                                                </Flex>
                                        }
                                    </> :
                                    <>
                                        {
                                            !isRootContacts ?
                                                <>
                                                    <Breadcrumb
                                                        separator={<SvgIcon svgName="arrow_right_line" />}
                                                        items={organizeBreadcrumItems}
                                                    />
                                                    {
                                                        currentDepartmentData && currentDepartmentData.length > 0 ?
                                                            <Flex className="orga-list-items" vertical gap={12}>
                                                                {
                                                                    currentDepartmentData.map((item: any, index: number) => {
                                                                        return (
                                                                            <Flex key={`${item.type}${index}`} gap={8}
                                                                                className={classNames(["orga-list-item", item.type])} align="center">
                                                                                {/* {getOrganizeIcon(item)} */}
                                                                                {item.type === 'user' ?
                                                                                    <Checkbox
                                                                                        disabled={item.imUserId === currentUserId || (
                                                                                            groupedUsers && (groupedUsers.includes(item.imUserId)
                                                                                            ))}//群聊邀请用户，已在群里的用户不能被选择
                                                                                        checked={item.isCheck}
                                                                                        onChange={(e) => onToggleSelectItem(e, item, index)} >
                                                                                        {renderUserItem(item)}
                                                                                    </Checkbox>
                                                                                    : null}
                                                                                {
                                                                                    item.type === 'dept' ?
                                                                                        <>
                                                                                            {getOrganizeIcon(item)}
                                                                                            <div className="orga-list-item-name dept">{item.deptName}</div>
                                                                                            <div className="orga-list-item-count">({item.headCount || 0})</div>
                                                                                            <Button size="small" color="default" variant="filled"
                                                                                                onClick={() => onClickListItem(item)} className="orga-list-item-arrow">
                                                                                                {t('chat.Expand')}
                                                                                            </Button>
                                                                                        </>
                                                                                        : null
                                                                                }
                                                                            </Flex>
                                                                        )
                                                                    })
                                                                }
                                                            </Flex>
                                                            :
                                                            <Empty className="page-empty full" image={null} description={
                                                                <>
                                                                    <div>{t('common.no-data')}</div>
                                                                    <div></div>
                                                                </>}
                                                            />
                                                    }
                                                </> : null
                                        }

                                        {/* 组织架构根目录：企业（多个）+ 当前用户所属最底层部门 */}
                                        {
                                            isRootContacts ?
                                                <Flex className="contacts" vertical gap={4}>
                                                    <Flex className="contacts-type" gap={8} align="center">
                                                        <SvgIcon svgName="menu_org" />
                                                        <span>{t('chat.organize-contacts')}</span>
                                                    </Flex>
                                                    <Flex className="contacts-type-content" vertical gap={4}>
                                                        {
                                                            rootContacts && rootContacts.map((item: any, index) =>
                                                                <Fragment key={`${index}${item.type}${item.tenantId}`}>
                                                                    <Flex onClick={(e) => onSelectContact(e, item)} className="contact-item" key={`${index}${item.type}${item.tenantId}`} gap={8} align="center">
                                                                        <div className="contact-icon">
                                                                            {item.type === 'tenant' ? <img src={item.touchIcon} alt="" /> : null}
                                                                        </div>
                                                                        <div className="contact-name">{item.tenantName}</div>
                                                                        <SvgIcon className="more" svgName="arrow_right_line" />
                                                                    </Flex>
                                                                    {
                                                                        item.deptList && item.deptList.length > 0 ?
                                                                            item.deptList.map((child: any, cIndex: number) =>
                                                                                <Flex onClick={(e) => onSelectContact(e, child, item)} className="contact-item child"
                                                                                    key={cIndex + '' + child.deptId + '' + index + '' + item.tenantId} gap={8} align="center">
                                                                                    <div className="contact-dept-icon">
                                                                                        {child.type === 'dept' ? <SvgIcon className="sublevel" svgName="menu_sublevel" /> : null}
                                                                                    </div>
                                                                                    <div className="contact-name">{child.deptName}</div>
                                                                                    <SvgIcon className="more" svgName="arrow_right_line" />
                                                                                </Flex>
                                                                            )

                                                                            : null
                                                                    }
                                                                </Fragment >
                                                            )
                                                        }

                                                    </Flex>
                                                </Flex> : null
                                        }
                                    </>
                            }

                        </Flex>
                    }
                    <Flex className="picker-result" vertical gap={10}>
                        <div className="pr-title">{t('chat.selected')} ({selectedDatas.length}/{countLimit})</div>
                        <Flex className="pr-items" vertical gap={4}>
                            {
                                selectedDatas && selectedDatas.map((item: any, index: number) => {
                                    return (
                                        <Flex className="pr-item" key={item.imUserId + '' + index} gap={8} align="center">
                                            <UserAvatar name={item.userName} src={item?.avatar} userId={item.imUserId}/>
                                            <Flex className="pr-item-content" gap={4}>
                                                <div className="name">{item.userName}</div>
                                                {/* <div className="">
                                                    {item.type === 'user' ? item.postName : null}
                                                    {item.type==='dept' ? item.headCount:null }
                                                </div> */}
                                            </Flex>
                                            {
                                                !item.isCurrent ? <Flex onClick={() => onRemoveSelected(item, index)} className="svg-operate" align="center" justify="center">
                                                    <SvgIcon svgName="icon_close" /></Flex> : null
                                            }
                                        </Flex>
                                    )
                                })
                            }
                        </Flex>
                    </Flex>
                </Flex>
            </Flex>

        </Modal>
    )
}

const mapStateToProps = (state: any) => ({
    companies: state.app.companies,
    activeCompanyId: state.app.activeCompanyId,
    activeCompanyName: state.app.activeCompanyName,
    themeInfo: state.app.themeInfo,
    userInfo: state.app.userInfo,
})
const mapDispatchToProps = (dispatch: any) => ({
    // dispatch_api_: (data:any) => dispatch(dispatch_api_(data))
})
const ConnectedCounter = connect(mapStateToProps, mapDispatchToProps, null, { forwardRef: true })(CreateGroup);
export default ConnectedCounter;