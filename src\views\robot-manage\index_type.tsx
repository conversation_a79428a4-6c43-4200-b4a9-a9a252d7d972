
import type { DataNode } from 'antd/es/tree';
export interface _RobotInterface {
    [key: string]: any;
    trigger: string,
    robotName?: string,
    robotId?: string,
    robotLogo?: any,
    robotIntroduction?: string,
    robotType?: string | null,
    robotIdentityPrompt?: string,
    robotWelcomeMsg?: string,
    robotLlmModel?: any[],
    escalateLlmModel?: any[],
    embeddingModel?: string,
    intelligence?: number,
    relevance?: number,
    konwledgeCount?: number,
    isIrrelevantQuestions?: number | string
    isIrrelevantQuestionsBoolean: boolean,
    irrelevantRespond?: string,
    creater?: string,
    maxResponseLength?: number,
    showKnowledge?: boolean,
    enableConversationMemory?: boolean,
}

export interface ContextState {
    robotList:any[],
    keyWord: string,
    robotType: string|number,
    dispalyType: string|number,
    robotTypeOptions: any,
    dispalyTypeOptions: any,
    deleteRobotModal: {
        visible: boolean,
        robot_id: any
    },
    cardLoading: boolean,
    isCreateRobotModalOpen: boolean,
    isPublishModalOpen: boolean,
    currentSelectRobot: _RobotInterface,
    interfaceRobotType: string | null,
    treeData: DataNode[],
    frontChecked: string[],
    backChecked: string[]
}
