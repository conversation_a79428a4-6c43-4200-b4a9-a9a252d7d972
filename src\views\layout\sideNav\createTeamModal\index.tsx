import { connect } from "react-redux";
import { useState, useEffect} from "react";
import { Space, Modal, Checkbox, Form, Input, Select, message } from "antd";
import { useTranslation } from 'react-i18next';
import industrys from '../datasource/industrys.json'
import areaDatas from '@/components/form/mobileInput/areaCode.json'
import SvgIcon from '@components/svgicon'; // 根据实际路径引入
import { sortObjectArrayByFistWord,sortObjectArrayByPingyin } from '@/utils/common'
import FilterSelect from '@components/form/select'
import { api_create_company } from '@/api/company'
import FormUtils from '@/views/common/formUtils'
import './index.scss'

// 人员规模数据源
const employeeSizeData = [
    { label: '0-10', value: 0 },
    { label: '11-50', value: 1 },
    { label: '51-100', value: 2 },
    { label: '101-200', value: 3 },
    { label: '201-500', value: 4 },
    { label: '>500', value: 5 },
]

// 地区数据源
let countryOptions:any = JSON.parse(JSON.stringify(areaDatas))
const zh_countryOptions = sortObjectArrayByPingyin(countryOptions, 'name_zh').map((i:any) =>({value:i.value,label: i.name_zh}))
const hk_countryOptions = sortObjectArrayByPingyin(countryOptions, 'name_hk').map((i:any) =>({value:i.value,label: i.name_hk}))
const en_countryOptions = sortObjectArrayByFistWord(countryOptions, 'name_en').map((i:any) =>({value:i.value,label: i.name_en}))
const countryOptionsResource:any = {
    zh: zh_countryOptions,
    hk: hk_countryOptions,
    en: en_countryOptions
}
// console.log('countryOptionsResource',countryOptionsResource)
// 行业数据源
let industryOptions:any = JSON.parse(JSON.stringify(industrys))
const zh_industryOptions = industryOptions.map((i:any) =>({value:i.value,label: i.name_zh}))
const hk_industryOptions = industryOptions.map((i:any) =>({value:i.value,label: i.name_hk}))
const en_industryOptions = industryOptions.map((i:any) =>({value:i.value,label: i.name_en}))
const industryOptionsResource:any= {
    zh: zh_industryOptions,
    hk: hk_industryOptions,
    en: en_industryOptions
}
// console.log('industryOptionsResource',industryOptionsResource)


// 新增 弹窗
const CreateTeamModal = (props: any) => {
    const { visible, lang,userInfo, onActiveCompany } = props
    const { t } = useTranslation()

    // const [createModalVisible, setCreateModalVisible] = useState(false)
    const [form] = Form.useForm(); // 新增表单
    const [isSubmiting, setIsSubmiting] = useState(false) // 是否提交中
    const [isAgree, setIsAgree] = useState(false)
    const [submittable, setSubmittable] = useState<boolean>(false);
    const [showAgreementAgree, setShowAgreementAgree] = useState(false)

    // Watch all values
    const values = Form.useWatch([], form);
    useEffect(() => {
        form
            .validateFields({ validateOnly: true })
            .then(() => setSubmittable(true))
            .catch((err) => {
                setSubmittable(false)
                // console.log('err', err)
            });
    }, [form, values]);

    // 新增表单项 输入改变事件
    const onCreateFormValuesChange = (changedFields: any) => {
        try {
            // console.log('changedFields', changedFields)
            Object.keys(changedFields).forEach((fieldName:any)=>{
                const fieldError = form?.getFieldError(fieldName)
                if (fieldError.length>0) FormUtils.clearValidate(form,fieldName)
            })
        } catch (error) {

        }
    }

    const onCreateTeamOk = ()=>{
        if(!isAgree){
            setShowAgreementAgree(true)
            return
        }
        onCreateRequest()
    }

    // 新增请求
    const onCreateRequest = async()=>{
        const values:any = form.getFieldsValue()
        const {companyName, location,industry,employeeSize}= values
        // console.log('values',values)
        const data ={
            userId: userInfo?.userId,
            companyName,
            regionCode: location,
            industry,
            employeeSize
        }
        // console.log('data',data)
        setIsSubmiting(true)
        const res:any = await api_create_company(data).catch(err=>{
            // console.log(err)
            setIsSubmiting(false)
        })
        // console.log("res", res)
        setIsSubmiting(false)
        if (!res) return
        // 成功
        message.success(t('company.create-success'))
        onCreateTeamCancel()
        onActiveCompany && onActiveCompany(res?.data?.tenantId)
    }

    const onCreateTeamCancel = () => {
        props.onClose()
    }
    
    // const onEmployeeSizeChange = (value: string) => {
    //     form.setFieldValue("employeeSize", value)
    // }

    const onChangeIndustry= (value: string) => {
        form.setFieldValue("industry", value)
    }

    const onChangeCountry=(value: string) => {
        form.setFieldValue("location", value)
    }

    const onChangeAgree = (e:any) => {
        setIsAgree(e.target.checked)
    }

    // 同意协议
    const onAgreeOk = ()=>{
        setIsAgree(true)
        setShowAgreementAgree(false)
        // 创建
        onCreateRequest()
    }

    const onAgreeCancel = ()=>{
        setShowAgreementAgree(false)
    }

    const serviceRender=(`<a href="/serviceAgreement" target={"_blank"}>${t('company.service-agreement')}</a>`)
    const organizeRender=(`<a href="/organizeAgreement" target={"_blank"}>${t('company.organization-agreement')}</a>`)

    return (
        <Modal
            className={"create-team-modal"}
            title={t('company.create-title')}
            open={visible}
            onCancel={onCreateTeamCancel}
            onOk={onCreateTeamOk}
            maskClosable={false}
            centered={true}
            width={400}
            footer={undefined}
            okText={t('company.create')}
            cancelText={t("app.cancel")}
            cancelButtonProps={{ variant: "filled", color: "default" }}
            okButtonProps={{ disabled: isSubmiting || !submittable }}
        >
            <div className="modal-tip">{t('company.create-tip')}</div>
            <Form
                name="basic"
                layout="vertical"
                form={form}
                onValuesChange={onCreateFormValuesChange}
                validateTrigger="onBlur"
                autoComplete="off"
                requiredMark={false}
            >
                <Form.Item label={t('company.company-name')} name="companyName" rules={[{ required: true, whitespace: true, message: '' }]}>
                    <Input placeholder={t('common.please-input') as string} variant="filled" allowClear />
                </Form.Item>
                <Form.Item label={t('company.location')} name="location"  rules={[{ required: true, message: '' }]}>
                    <FilterSelect
                        placeholder={t('common.please-select') as string}
                        filterable={true}
                        onChange={onChangeCountry}
                        options={countryOptionsResource[lang]}
                        validateStatus="success"
                    />
                </Form.Item>
                <Form.Item label={t('company.industry')} name="industry" rules={[{ required: true, message: '' }]}>
                    <FilterSelect
                        placeholder={t('common.please-select') as string}
                        options={industryOptionsResource[lang]}
                        onChange={onChangeIndustry}
                    />
                </Form.Item>
                <Form.Item label={t('company.employee-size')} name="employeeSize" rules={[{ required: true, message: '' }]}>
                    <Select options={employeeSizeData} placeholder={t('common.please-select') as string} variant="filled" suffixIcon={<SvgIcon svgName="icon_select_suffix" />}>
                    </Select>
                </Form.Item>
                <Space className='privacy-term' align="start">
                    <Checkbox checked={isAgree} onChange={onChangeAgree}></Checkbox>
                    <div dangerouslySetInnerHTML={{ __html: t('company.agreement', 
                    { service: serviceRender,organization: organizeRender}) as string, }} />
                </Space>
            </Form>
            {/* 协议 同意弹窗 */}
            <Modal width={400} title={t('login.agree-privacy-terms-tip')} maskClosable={false} centered closable={false} 
            okText={t('login.agree')} cancelText={t("app.cancel")}
                open={showAgreementAgree} wrapClassName="common-confirm-modal" onOk={onAgreeOk} onCancel={onAgreeCancel}>
                <div className='modal-privacy-content'>
                    <div dangerouslySetInnerHTML={{ __html: t('company.service-organization', 
                        { service: serviceRender,organization: organizeRender}) as string, }} />
                    </div>
            </Modal>
        </Modal>
    )
}

const mapStateToProps = (state: any) => ({
    userInfo: state.app.userInfo,
    // globalLang: state.app.globalLang,
    // themeInfo: state.app.themeInfo
})
const mapDispatchToProps = (dispatch: any) => ({
})
export default connect(mapStateToProps, mapDispatchToProps)(CreateTeamModal);
