import { useState, useEffect, useRef } from "react";
import { connect } from 'react-redux';
import avatarDefault from '@/icons/png/avatar_default.png'
import Avatar from '@/components/avatar'
import './index.scss'
import { Flex } from "antd";

// 用户头像
const UserAvatar = (props: any) => {
  const {
    userProfile: loginUserProfile, userProfilePersonal: loginUserProfilePersonal,  // state 数据：登录账号 在 当前企业中的 个人资料
    src, // 用户头像url
    name, // 用户名
    userId, // 用户ID
    isLoginUser = false, // 是否是当前登录用户
    userInfo: loginUserInfo, //登录账号 用户信息
    size = 32, borderRadius = '50%', isPersonal = false // 其他数据
  } = props


  const currentUserId = String(loginUserInfo.imUserId)
  const [avatar, setAvatar] = useState<any>(null) // 其他账号 用户头像
  const [userName, setUserName] = useState<any>('')// 其他账号 用户名

  // 初始化用户信息，区分 当前登录用户 和 其他普通用户
  useEffect(() => {
    const _isLoginUser = String(currentUserId) === String(userId)
    if (!isLoginUser && !_isLoginUser) { // 其他普通用户
      // console.log('其他普通用户 currentUserId', userId)
      initUser({ userName: name, avatar: src })
    } else { //  当前登录用户
      // console.log('当前登录用户 currentUserId')
      // console.log('企业 用户头像变了+++++userProfile', loginUserProfile)
      // console.log('企业 用户头像变了+++++userProfile, isPersonal', isPersonal, loginUserProfilePersonal)
      if (isPersonal && loginUserProfilePersonal) {
        initUser(loginUserProfilePersonal)
        return
      }
      initUser()
    }

  }, [isLoginUser, loginUserProfile, loginUserProfilePersonal, isPersonal, userId, currentUserId, src, name])


  // 获取个人资料
  const initUser = async (profile = loginUserProfile) => {
    try {
      setAvatar(profile.avatar)
      setUserName(profile.userName)
    } catch (error) {

    }
  }

  return (
    <Flex align="center" justify="center"
      className={`ama-user-avatar size${size}`} style={{ width: size + 'px', height: size + 'px', borderRadius: borderRadius }}>
      {
        avatar ? <img draggable="false" className={"user-avatar"} src={avatar || (userName === '' && avatarDefault)} alt=""
          style={{ borderRadius: borderRadius }} /> : null
      }
      {
        !avatar ? <Avatar name={userName} size={size} borderRadius={borderRadius} /> : null
      }
    </Flex>
  )
}
const mapStateToProps = (state: any) => ({
  userInfo: state.app.userInfo,
  userProfile: state.app.userProfile,
  userProfilePersonal: state.app.userProfilePersonal,
  activeCompanyId: state.app.activeCompanyId,
});
const mapDispatchToProps = (dispatch: any) => ({});
export default connect(mapStateToProps, mapDispatchToProps)(UserAvatar);