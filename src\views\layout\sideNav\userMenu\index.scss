@import '../../../../styles/mixin.scss';
.user-menu{
    width: 100%;
    // padding: 10px 16px 20px;
    .user-setting{
        span{

        }
    }
    .ant-dropdown-trigger {
        cursor: pointer;
        color: var(--colorText);
        font-size: 14px;
        line-height: 20px;
        padding: 0 8px;
        height: 32px;
    }
    img{
        width: 32px;
        cursor: pointer;
    }
}


.ant-dropdown.user-setting-dropdown{
    width: 228px;
    .svg-icon{
        font-size: 16px;
        color: var(--colorText);
        margin-right: 10px;
    }
    .ant-dropdown-menu{
        border-radius: 12px;
        padding: 8px;
        box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.1);
    }
    .ant-dropdown-menu-item{
        font-weight: 500;
        &:hover{
            border-radius: 12px;
        }
    }
}
.sm-menu-l-dropdown{
    .ant-dropdown-menu-item,.ant-dropdown-menu-title-content{
        text-align: center;
    }
}