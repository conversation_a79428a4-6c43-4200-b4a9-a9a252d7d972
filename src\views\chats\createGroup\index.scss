@import "../../../styles/mixin.scss";
.chat-create-group-modal{
    .field-item{
        padding-top: 12px;
    }
    .field-name{
        @include font(12px,18px,500);

    }
    .group-member-con{
        margin-top: 16px;
        height: 514px;
        .group-member-pick{
            flex:1;
            border: 1px solid var(--colorBorderSecondary);
            border-radius: 8px;
            overflow: hidden;
        }
        .picke-options{
            width: 50%;
            padding: 12px 16px 0;
            border-right: 1px solid var(--colorBorderSecondary);
            height: 100%;
            overflow: hidden;
            .ant-input-affix-wrapper,.ant-breadcrumb {
                flex-shrink: 0;
            }
            .orga-list-items{
                flex: 1;
                overflow-y: auto;
            }
        }
        .picker-result{
            width: 50%;
            padding: 12px 16px 0;
            .pr-title{
                @include font(16px,24px,600);
                padding: 6px 16px;
                flex-shrink: 0;
            }
        }
    }
    .contacts{
        overflow: auto;
        .contacts-type{
            @include font(14px,25px,500);
            padding: 9px 8px;

        }
        .contact-item{
            width: 100%;
            padding: 6px 8px 6px 32px;
            cursor: pointer;
            .contact-icon{
                flex-shrink: 0;
                img{
                    width: 24px;
                    height: auto;
                    border-radius: 8px;
                }
            }
            .contact-dept-icon{
                font-size: 16px;
            }
            .contact-name{
                flex: 1;
                @include ellipsis();
            }
            .more{
                flex-shrink: 0;
                margin-left: auto;
                font-size: 16px;
            }
            &.child{

            }
        }
    }
    .orga-list-item-icon.dept{
        padding: 8px;
        border-radius: 10px;
        font-size: 16px;
        background: var(--colorPrimaryBg);
        color: var(--colorPrimary);
    }
    .orga-list-item-arrow{
        margin-left: auto;
        flex-shrink: 0;
        cursor: pointer;
        height: auto;
    }
    .sublevel{
        color: var(--colorIconQuaternary);
    }
    .ant-breadcrumb{
        .ant-breadcrumb-separator{
            font-size: 16px;
            margin: 0 4px;
        }
        li:not(:last-child){
            &:hover{
                color: var(--colorText);
            }
        }
        .ant-breadcrumb-link{
            cursor: pointer;
            word-break: break-word;
        }
    }
    .pr-items{
        flex:1;
        overflow-y: auto;
    }
    .pr-item{
        width: 100%;
        padding: 6px 8px;
        padding-right: 0;
        overflow: hidden;
        .UserAvatar{
            flex-shrink: 0;

        }
        .svg-operate{
            margin-left: auto;
            flex-shrink: 0;
        }
        .pr-item-content{
            flex: 1;
            overflow: hidden;
        }
        .name{
            max-width: 100%;
            @include ellipsis();
        }
    }
}