
import { Carousel as ACarousel, ConfigProvider } from 'antd';
import carouselLogin01Icon from "@/icons/png/login/login01.png"
import carouselLogin02Icon from "@/icons/png/login/login02.png"
import carouselLogin03Icon from "@/icons/png/login/login03.png"
export default function Carousel(){
    return(
        <ConfigProvider
            theme={{
                components: {
                Carousel: {
                    dotActiveWidth:60,
                    dotWidth:62,
                    dotHeight:6,
                    dotOffset:20,
                    dotGap:3
                },
                },
            }}
        >
            <ACarousel className={"carousel"} autoplay effect={"fade"}>
            <div>
                <img src={carouselLogin01Icon} alt={"logo"} />
            </div>
            <div>
                <img src={carouselLogin02Icon} alt={"logo"} />
            </div>
            <div>
                <img src={carouselLogin03Icon} alt={"logo"} />
            </div>
            </ACarousel>
        </ConfigProvider>
    )
}