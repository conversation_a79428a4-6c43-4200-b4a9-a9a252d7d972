import React from "react";
import { connect } from "react-redux";
import { LoadingOutlined, UndoOutlined, CheckCircleFilled } from '@ant-design/icons';
import { Spin, Modal, message, Tooltip, Checkbox, Button, Divider, Popover, Flex } from 'antd';
import classnames from "classnames";
import i18next from "i18next";
import Markdown from "@/components/markdown";
import CopyToClipboard from 'copy-to-clipboard';
import InfiniteScroll from 'react-infinite-scroll-component';
import Lot<PERSON> from 'lottie-react';
import loadingAnimation from '@/assets/loading_animation.json';
import ImageLoadingAnimation from '@/assets/image_loading.json';
import HomeBotIocn from "@/assets/homebot.png";
import DefaultRobot from "@/assets/bot_default_avatar.png";

import { change_app_globalLang, change_app_userInfo } from "@/store/action";
import {
    api_get_robot_token_limit, dispatch_api_get_robot_llmmModel, api_check_max_token, api_query_paged_chat_history, dispatch_api_ai_message, dispatch_api_ai_enhance_message, dispatch_api_stop_streaming,
    dispatch_api_delete_messages, api_clear_current_chat, dispatch_api_privacy_check
} from '@/api/robot'
import { refreshTheme } from '@/theme/index'
import { LoginExpiredCode } from '@/utils/userRequest'

import QuestionInputComponent from '../components/questionInput' // 问题输入框子组件
import FileUploadComponent from '../components/fileUpload/index' // 上传文件
import TokenOverflowTip from '../modal/tokenOverflowTip' // token 超出限制 提示框
import InformationSourcesDrawer from '../modal/informationSources' // 信息来源弹框
import LLModelCascaderComponent from '../components/llmModel' // llm 模型选择框
import EnhanceModalComponent from '../modal/enhance' // 增强
import RobotDetailDrawer from '../modal/robotDetail' // 机器人详情
import Svgicon from "@/components/svgicon"
import ViewImage from '../components/viewImage';
import { wss } from '../socketService'
import { EmbeddingChunkFace, StackConfig, ContextState } from "./index_type"
import { openIMService } from '@/services/ImChatService'
import UserAvatar from '@/components/userAvatar'
import './index.scss'


class Robot extends React.Component<any, ContextState> {
    private responseRef: React.LegacyRef<HTMLDivElement> | any;
    private fileUploadRef: React.LegacyRef<HTMLDivElement> | any;
    private infomationSoruceRef: React.LegacyRef<HTMLDivElement> | any;
    public questionInputRef: React.LegacyRef<HTMLDivElement> | any;
    public sideMenuRef: React.LegacyRef<HTMLDivElement> | any;
    private isClearChatIng: boolean;// 是否清除会话中，防重复提交
    private activeRobotId: any;// 当前机器人ID（实时获取）
    private isFromRobotDefault: any; // 页面来源-机器人列表
    private isFromBotMarket: any;// 页面来源-机器人市场

    constructor(props: any) {
        super(props);
        this.responseRef = React.createRef();
        this.questionInputRef = React.createRef()
        this.fileUploadRef = React.createRef();
        this.infomationSoruceRef = React.createRef();
        this.sideMenuRef = React.createRef();
        this.state = {
            stack: [],
            isMaxMsgModal: false,
            currentDialog: {
                robotId: "",
                robot_type: '',
                robotName: "",
                describe: "",
                robotLlmModel: [],
                escalateLlmModel: [],
                can_show_citations: false,
                llmModelImage: null, // LLM模型图标（本地或增强）
                llmCategory: null,
                llmModel: null
            },
            currentInformationSourcesModal: {
                type: '',
                visible: false,
                resource: {
                    subType: {},
                    list: [],
                    user: null
                }
            },
            isGoogleSearch: false,
            currentEnhanceModal: {
                visible: false,
                resource: {}
            },
            currentStack: {
                user: "",
                assistant: "",
                index: 0,
                searchResults: [],
                referenceChunkList: [],
                u_attachments: null,
                a_attachments: null,
                loading: false,
                isToggleAi: true,
                isEnhance: false // 区分普通对话还是增强对话
            },
            llmModelList: [], // llm模型数据
            llmModelSelected: [], // llm模型选中值,
            isDragingFileToPanel: false,
            currentFilebox: { // filebox(包含附件和历史会话)
                isRequesting: false,
                attachments: [],
                attachmentTokens: 0, // 附件token总数
                // isTokenOverflow: false
            },
            changeLlmConfirmModal: {
                visible: false,
                llm: null
            },
            deleteChatConfirmModal: {
                visible: false,
                messageId: null
            },
            tokenExceedModal: {
                visible: false,
                overflow: null
            },
            currentRobotDetail: { // 机器人详情
                visible: false,
                resource: null
            },
            viewImage: {
                visible: false,
                url: ''
            },
            currentInputTokenLimit: null,
            currentRemainTokenLimit: 1000,
            currentPageSize: 6, // 分页size, 为了匹配 问-答 一对 最好设置偶数！
            currentLastMessageId: '0',
            isLoadingMore: false, // 下拉加载更多中
            loadAllFinished: false, // 加载所有完成
            openSelectConversation: false,
            historyConversationSelected: [],
            historyConversationMemory: [],
            copyFinished: false,
            copyFinishedTimer: null,
            containerHeight: 593,
            scrollTopMemory: null,
            keyDownEventListener: null,
            loadingLLm: false,
            pageOriginId: 'ROBOT'
        }
        this.isClearChatIng = false
        this.activeRobotId = ''
        this.isFromRobotDefault = props.pageOriginId === 'ROBOT'
        this.isFromBotMarket = props.pageOriginId === 'MARKET'
    }

    get isAllLoadingComplete() {
        const { stack } = this.state;
        return stack.every((d) => d.isToggleAi);
    }

    get multipleSelectionConfirmDisabled() {
        const { historyConversationSelected } = this.state;
        return !historyConversationSelected || historyConversationSelected.length === 0
    }
    // 机器人切换
    $_handleToggleDialog = async (_d: any) => {
        const d = { ..._d }
        d.robotId = _d.robot_id
        d.robotName = _d.name
        this.activeRobotId = d.robotId
        const { currentDialog } = this.state;
        this.questionInputRef?.current?.emit_clear_input_value() // 清空输入框值
        // if (currentFilebox.isRequesting) return // filebox上传文件中

        if (!this.isAllLoadingComplete) {
            const res = await this.handleStopAssistantStreaming().catch() // 停止当前问答
            if (!res) return
        }

        // 清空多选，初始化 加载更多
        this.setState({ openSelectConversation: false, loadAllFinished: false, isLoadingMore: false, historyConversationSelected: [], currentLastMessageId: '0' })
        this.handleSetInformationSourcesVisible(false) // 关闭信息来源弹窗
        this.handleSetRobotDetailVisible(false)// 关闭机器人来源弹窗
        if (d.robotId !== currentDialog.robotId) {
            this.setState({
                currentDialog: d,
                stack: [],
            }, () => {
                this.handleQueryCurrentRobotMessage();
                if (this.questionInputRef?.current) this.questionInputRef.current.emit_init() // 输入框组件-初始化
                if (this.fileUploadRef?.current) this.fileUploadRef.current?.emit_init(d.robotId) // 上传文件组件-初始化
            });
        }
    }

    // 向后端发送清空请求
    handleClearCurrentChat = async () => {
        const { openSelectConversation, stack } = this.state;
        if (openSelectConversation || !this.isAllLoadingComplete) return // 多选状态和信息发送状态，禁止清屏
        if ((stack?.length > 0 && stack[stack.length - 1].meta_contextClear) || stack.length === 0) return // 防止重复清屏
        const baseParams = this.getCommonParams()
        const lastMessageId = stack[stack.length - 1].messageId
        if (this.isClearChatIng || !stack || stack?.length === 0) return // 防重复提交请求：请求中 或 当前对话为空
        let data: any = { ...baseParams, lastMessageId: lastMessageId };
        this.isClearChatIng = true
        await api_clear_current_chat(data).catch((error: any) => {
            this.isClearChatIng = false
        })
        this.isClearChatIng = false
        this.setState({ currentLastMessageId: '0', stack: [] }, () => {
            this.handleQueryCurrentRobotMessage();
        })
    }

    // 公共请求参数 region account robotId
    getCommonParams = () => {
        let { currentDialog } = this.state;
        return { seq: 0, robotId: currentDialog.robotId }
    }

    // 新增Stack 元素 
    handleCreateStackItem(question: any, attachments: any, seq?: any, includedMessages?: any) {
        let { stack, currentDialog } = this.state;
        // stack 新增问答数据，开启问答loading
        let stackIndex = stack.length > 0 ? stack[stack.length - 1].index + 1 : 0;
        const sourceTypes: any = []
        if (attachments?.length > 0 && !sourceTypes.includes('u_attachments')) sourceTypes.push('u_attachments')
        if (includedMessages?.length > 0 && !sourceTypes.includes('u_historyChat')) sourceTypes.push('u_historyChat')
        stack.push({
            user: question, assistant: "", index: stackIndex, loading: true, isImage: false, isToggleAi: false, isThinking: false, thinkingText: '', thinkingTime: null, isThinkingEnd: null,
            robotId: currentDialog.robotId, searchResults: [], referenceChunkList: [], u_includedMessages: includedMessages, u_attachments: attachments, isChecked: false, seq: seq || 0, sourceTypes: sourceTypes
        })
        this.setState({ stack }, () => this.handleResponseBoxScrollTopBottom());
        return stackIndex
    }

    // 发送信息前
    handlePreSendMessage = async (sendString: string) => {
        let { currentDialog, stack, currentFilebox, currentEnhanceModal, historyConversationSelected } = this.state;
        // 检验1：filebox有请求
        if (currentFilebox.isRequesting) {
            return 1
        }
        // 校验2：存在问答中的消息，禁止发送新的问题
        if (!this.isAllLoadingComplete) {
            message.info(i18next.t(["error.ai-response"]))
            return 1
        }

        this.questionInputRef?.current?.emit_clear_input_value() // 清空输入框值
        let question = sendString

        // 校验3：不能发送空白信息
        if (!question.trim()) {
            message.error(i18next.t(["error.no-send-blank"]))
            return;
        }
        const baseParams = this.getCommonParams()
        const attachments = [...currentFilebox.attachments] // 附件
        const includedMessages: any = new Set(historyConversationSelected.map((tag: any) => tag.messageId)) // 历史会话
        const privacyData: any = { text: question, ...baseParams }

        // stack 新增问答数据，开启问答loading
        const stackIndex = this.handleCreateStackItem(question, attachments, null, [...includedMessages])
        // return
        // Home bot 直接请求
        if (this.isHomeBot()) {
            this.handleSendMessage(question)
            return
        }
        // 其他，发送问答请求前，判断LLM是否为外部LLM，若是需要弹出数据确认弹框给用户确认
        let privacyCheckRes: any = await this.props.dispatch_api_privacy_check(privacyData).catch(() => {
            if (stackIndex != undefined) {
                stack[stackIndex].loading = false
                this.setState({ stack })
            }
        })
        privacyCheckRes = privacyCheckRes?.data?.data || {}
        // 检验robotId 是否一致
        if (privacyCheckRes?.robotId != this.activeRobotId) return
        const needPrivacyPopup = privacyCheckRes?.needPrivacyPopup || false
        const embeddingChunkList = privacyCheckRes?.embeddingChunkList || []

        // 存储答案
        let searchResults: EmbeddingChunkFace[] = [];
        let referenceChunkList: EmbeddingChunkFace[] = [];
        embeddingChunkList.forEach((d: any, index: number) => {
            if (d.internetSearch === true) {
                searchResults.push(this.deepClone(d))
            } else {
                referenceChunkList.push(this.deepClone(d))
            }
        });
        const referenceChunkList_clone = this.deepClone(referenceChunkList)
        if (needPrivacyPopup && referenceChunkList.length > 0) {
            let currentStack: StackConfig = {
                user: question,
                assistant: "",
                index: 0,
                loading: false,
                isToggleAi: true,
                searchResults: this.deepClone(searchResults),
                referenceChunkList: referenceChunkList_clone,
                isEnhance: false // 区分普通对话还是增强对话
            }
            currentDialog.llmModelImage = privacyCheckRes?.image || null;// 设置LLM图标
            currentDialog.llmCategory = privacyCheckRes?.llmCategory || null;// 设置LLM分类
            currentDialog.llmModel = privacyCheckRes?.llmModel || null;// 设置LLM名称
            currentEnhanceModal.resource = {
                llmModelImage: currentDialog.llmModelImage,
                llmCategory: currentDialog.llmCategory,
                llmModel: currentDialog.llmModel,
                isEnhance: false,
                dataList: referenceChunkList_clone
            }
            currentEnhanceModal.visible = true
            this.setState({ currentEnhanceModal, currentStack, currentDialog });
            return
        }
        this.handleSendMessage(question, embeddingChunkList)
    }

    // 向后端发送真正请求
    handleSendMessage = async (sendString: string, embeddingChunkList?: EmbeddingChunkFace[]) => {
        const question = sendString;
        if (!question.trim()) {
            message.open({
                type: 'error',
                content: i18next.t(["error.no-send-blank"])
            })
            this.questionInputRef?.current?.emit_update_generate_answer_status(false) // 修改输入框-生成答案状态
            return;
        }
        const baseParams = this.getCommonParams()
        let attachmentIds = [] // 附件
        let { stack, isGoogleSearch, llmModelSelected, currentFilebox, historyConversationSelected } = this.state;
        let stackIndex = stack[stack.length - 1].index || 0
        const seq = Date.now();

        if (currentFilebox.attachments?.length > 0) {
            attachmentIds = currentFilebox.attachments
        }

        stack[stackIndex].loading = true; // 开启loading效果
        stack[stackIndex].isToggleAi = false;
        stack[stackIndex].seq = seq;
        this.setState({ stack })
        let data: any = { text: question, ...baseParams };
        const isHomeBot = this.isHomeBot()
        data.action = "chat";
        data.canSearchInternet = isGoogleSearch
        if (!isHomeBot) data.embeddingChunkList = embeddingChunkList && embeddingChunkList.length > 0 ? embeddingChunkList : []

        data.seq = seq;
        data.attachmentIds = attachmentIds// 上传文件
        data.includedMessageIds = [] // 包含对话
        if (historyConversationSelected?.length > 0) {
            const ids: any = new Set(historyConversationSelected.map((tag: any) => tag.messageId))
            data.includedMessageIds = [...ids] //去重
        }

        data.msgType = "chatMsg"
        // 设置Home Bot 请求参数
        if (isHomeBot) {
            data.msgType = 'homeChatMsg'
            data.canSearchInternet = false
            data.llmId = llmModelSelected ? llmModelSelected[llmModelSelected.length - 1] : null

            // 设置homebot llm信息
            stack[stackIndex].llmModel = document.getElementById('cascader-option-selected-name')?.innerHTML
            let imgElement: HTMLImageElement | null = document.getElementById('cascader-option-selected-image') as HTMLImageElement;
            if (imgElement) {
                stack[stackIndex].llmImage = imgElement.getAttribute('data-image')
            }

            this.setState({ stack })
        }
        // chatMsg、homeChatMsg请求，优先请求websocket，其次http
        if (wss?.isconnect) {
            data.tenantId = this.props.activeCompanyId
            // websocket
            this.questionInputRef?.current?.emit_update_generate_answer_status(true)// 修改输入框-生成答案状态-开启
            wss.registerCallBack(data.msgType, (res) => { // websocket 回调函数
                this.handleWebsocketChatResponse(res, stackIndex, isHomeBot)
            })
            wss.send(data)
        } else {
            // http
            const res = await this.props.dispatch_api_ai_message(data).catch((error: any) => {
                stack[stackIndex].loading = false; // 关闭loading效果
                stack[stackIndex].isToggleAi = true;
                this.setState({ stack })
            })
            if (!res) return
            this.handleHTTPChateResponse(res?.data, stackIndex, isHomeBot)
        }
    }

    // 向后端发送真正增强请求
    handleSendEnhanceMessage = async (d: StackConfig, embeddingChunkList?: EmbeddingChunkFace[]) => {
        const question = d.user;
        if (!question.trim()) {
            message.open({
                type: 'error',
                content: i18next.t(["error.no-send-blank"])
            })
            return;
        }
        const baseParams = this.getCommonParams()
        let { isGoogleSearch, currentStack } = this.state;
        const a_attachments = d?.a_attachments || [] // 答案附件
        const u_attachments = d?.u_attachments || [] // 问题附件
        const a_includedMessages = d?.a_includedMessages || [] // 答案历史会话
        const u_includedMessages = d?.u_includedMessages || [] // 问题历史会话
        const seq = Date.now();
        const attachments = [...u_attachments] // TODO深拷贝
        const includedMessages = [...u_includedMessages] // TODO深拷贝
        const latestStackIndex = this.handleCreateStackItem(question, attachments, seq, includedMessages)
        let data: any = { msgType: 'enhanceChatMsg', text: currentStack.user, ...baseParams };
        data.action = "chat";
        data.canSearchInternet = isGoogleSearch
        data.embeddingChunkList = embeddingChunkList && embeddingChunkList?.length > 0 ? embeddingChunkList : []
        data.seq = seq;
        // 附件
        data.attachmentIds = []
        let _attachments: any = [...a_attachments, ...u_attachments]
        if (_attachments?.length > 0) {
            _attachments = new Set(_attachments)
            data.attachmentIds = [..._attachments] //去重
        }
        // 历史会话
        data.includedMessageIds = []
        let _includedMessages: any = [...a_includedMessages, ...u_includedMessages]
        if (_includedMessages?.length > 0) {
            _includedMessages = new Set(_includedMessages)
            data.includedMessageIds = [..._includedMessages] //去重
        }
        // enhanceChatMsg请求, 优先请求websocket，其次http
        if (wss?.isconnect) {
            data.tenantId = this.props.activeCompanyId
            this.questionInputRef?.current?.emit_update_generate_answer_status(true)// 修改输入框-生成答案状态-开启
            wss.registerCallBack('enhanceChatMsg', (res) => { // 回调函数
                this.handleWebsocketChatResponse(res, latestStackIndex, false)
            })
            wss.send(data)
        } else {
            const res = await this.props.dispatch_api_ai_enhance_message(data).catch((error: any) => {
                console.log(error)
            })
            if (!res) return
            this.handleHTTPChateResponse(res?.data, latestStackIndex, false)
        }
    }

    // 处理Websocket会话响应
    handleWebsocketChatResponse = (wsRes: any, stackIndex: number, isHomeBot: boolean = false) => {
        const oldStack = JSON.stringify(this.state.stack[stackIndex])
        const { stack, currentInformationSourcesModal: sourcesModal } = this.state
        const initCustomStatus = () => {
            stack[stackIndex].loading = false;
            stack[stackIndex].isToggleAi = true;
            this.setState({ stack });
        }

        // seq 请求和回应不一致时，则丢弃该包
        if (!wsRes.seq || stack[stackIndex].seq !== wsRes.seq) {
            console.log('wsRes seq error, ', wsRes);
            this.questionInputRef?.current?.emit_update_generate_answer_status(false)// 修改输入框-生成答案状态-关闭
            return;
        }
        stack[stackIndex].isThinking = wsRes.isThinking // 是否正在思考
        stack[stackIndex].thinkingTime = wsRes.thinkingTime // 思考时间
        const newThinkingText = stack[stackIndex].thinkingText + wsRes.thinkingToken  // 歷史對話請讀取 'thinkingText', 即時的對話用 'thinkingToken'
        if (stack[stackIndex].thinkingText != null && stack[stackIndex].thinkingText != '' && !stack[stackIndex]?.sourceTypes?.includes('a_logicalReasoning')) {
            stack[stackIndex].sourceTypes.push('a_logicalReasoning')
        } // 思考过程（逻辑推理）
        if (wsRes.isThinking) { // 开启推理状态
            stack[stackIndex].isThinkingEnd = false //推理中
        } else {
            if (stack[stackIndex].isThinkingEnd === false) stack[stackIndex].isThinkingEnd = true //推理结束
        }
        if (newThinkingText !== stack[stackIndex].thinkingText) {
            stack[stackIndex].thinkingText = newThinkingText
            // 若打开逻辑推理 信息来源弹窗，实时更新 逻辑推理文字
            if (sourcesModal.visible && stack[stackIndex].seq == sourcesModal.seq) {
                this.infomationSoruceRef?.current?.emit_update_logical_reasoning(newThinkingText)
            }
        }
        // 一旦有响应，去除loading
        if (stack[stackIndex].loading) {
            stack[stackIndex].loading = (wsRes.isImage || wsRes.isThinking); // 若是生成图片 则加载 生成图片的动画
            stack[stackIndex].isImage = wsRes.isImage;
        }
        try {
            if (wsRes.isFinished) {// 回答结束
                this.questionInputRef?.current?.emit_update_generate_answer_status(false) // 修改输入框-生成答案状态-关闭
                const response = wsRes?.response
                console.log('回答结束', response)
                initCustomStatus()
                if (response?.error && response?.code != LoginExpiredCode) { // 有报错信息
                    message.open({ type: 'error', content: response.error })
                }
                switch (response.code) {
                    case 0:
                        // 成功
                        this.handleChatSucessResponse(response.data, stackIndex, isHomeBot)
                        break
                    case LoginExpiredCode:
                        // 登录失效，保证IM先退出
                        const amaLogout = () => {
                            this.props.change_app_userInfo({ isLogin: false, userInfo: {}, token: null })
                            message.open({ type: 'error', content: i18next.t(["error.logo-timeout"]) })
                            refreshTheme(true, true)
                            this.props.router.navigate("/login");
                        }
                        openIMService.then(async (im: any) => {
                            // console.log('🥶🥶🥶🥶🥶获取IM实例', im,im.isLogouting)
                            if (!im.isLogouting) await im.logout()
                            amaLogout()
                        }).catch((err) => {
                            console.log('err', err)
                            amaLogout()
                        })
                        break
                    default:
                        break
                }
            } else {
                // 若回答未结束，前端逐字显示返回字符串 streamingToken
                stack[stackIndex].assistant = stack[stackIndex].assistant + wsRes.streamingToken;
                stack[stackIndex].messageId = wsRes?.messageId
                if (oldStack === JSON.stringify([stackIndex])) {// 检查stack对象是否变化
                    this.handleResponseBoxScrollTopBottom();
                    return
                }
                this.setState(prevState => ({
                    stack: prevState.stack.map((item, index) => {
                        if (index == stackIndex) item = stack[stackIndex]
                        return item
                    })
                }), () => {
                    this.handleResponseBoxScrollTopBottom();
                });
            }
        } catch (error) {
            console.log(error)
            this.questionInputRef?.current?.emit_update_generate_answer_status(false) // 修改输入框-生成答案状态-关闭
        }
    }

    // 处理HTTP会话响应
    handleHTTPChateResponse = (res: any, stackIndex: number, isHomeBot: boolean = false) => {
        this.questionInputRef?.current?.emit_update_generate_answer_status(false) // 修改输入框-生成答案状态
        const { stack } = this.state
        const initCustomStatus = () => {
            stack[stackIndex].loading = false;
            stack[stackIndex].isToggleAi = true;
            this.setState({ stack });
        }
        // console.log('res',res)
        switch (res?.code) {
            case 0:
                // 成功
                stack[stackIndex].loading = false;
                this.setState({ stack })
                const response = res?.data
                this.handleChatSucessResponse(response, stackIndex, isHomeBot)
                break
            default:
                initCustomStatus()
                break
        }
    }

    // HTTP、Websocket会话请求成功处理
    handleChatSucessResponse = (response: any, stackIndex: number, isHomeBot: boolean = false) => {
        try {
            const { stack, currentDialog, currentInformationSourcesModal: sourcesModal } = this.state
            const hasThink = response.thinkingText != null && response.thinkingText != '' && stack[stackIndex].isThinkingEnd !== null && response.thinkingTime != null && response.thinkingTime != ''
            const attachments = Array.isArray(response.attachmentsId) ? response.attachmentsId : []
            stack[stackIndex].loading = false;
            stack[stackIndex].isToggleAi = true;
            stack[stackIndex].messageId = response.messageId;
            stack[stackIndex].canEnhance = response.canEnhance;
            stack[stackIndex].searchResults = Array.isArray(response.searchResults) ? response.searchResults : [];
            stack[stackIndex].referenceChunkList = Array.isArray(response.embeddingChunkList) ? response.embeddingChunkList : [];
            stack[stackIndex].a_attachments = attachments;
            stack[stackIndex].a_includedMessages = Array.isArray(response.includedMessagesId) ? response.includedMessagesId : [];
            stack[stackIndex].a_messageToken = response.messageToken || 0;
            stack[stackIndex].isChecked = false;
            stack[stackIndex].inPairs = true;
            stack[stackIndex].assistant = response.content
            stack[stackIndex].robotId = response.robotId
            stack[stackIndex].meta_contextClear = response.meta_contextClear
            stack[stackIndex].isImage = response.isImage;
            stack[stackIndex].isThinking = false
            stack[stackIndex].isThinkingEnd = hasThink // 有推理中
            stack[stackIndex].thinkingText = response.thinkingText
            stack[stackIndex].thinkingTime = response.thinkingTime // 思考时间
            const sourceTypes: any = stack[stackIndex].sourceTypes || []
            if (attachments.length > 0 && !sourceTypes.includes('a_attachments')) { sourceTypes.push('a_attachments') }
            if (response?.includedMessagesId?.length > 0 && !sourceTypes.includes('a_historyChat')) { sourceTypes.push('a_historyChat') }
            if (!isHomeBot && currentDialog.can_show_citations === true && response?.embeddingChunkList?.length > 0 && !sourceTypes.includes('a_reference')) { sourceTypes.push('a_reference') } // 引用知识
            if (hasThink && !sourceTypes.includes('a_logicalReasoning')) { sourceTypes.push('a_logicalReasoning') } // 思考过程（逻辑推理）
            if (response?.searchResults?.length > 0 && !sourceTypes.includes('a_googleSearch')) { sourceTypes.push('a_googleSearch') }
            stack[stackIndex].sourceTypes = sourceTypes
            this.setState({ stack }, () => {
                if (sourcesModal.visible && sourceTypes.length > 1 && stack[stackIndex].seq == sourcesModal.seq && sourceTypes.includes('a_logicalReasoning') && sourcesModal.type == "logicalReasoning") {
                    this.handleShowInformationSourcesModal('logicalReasoning', stack[stackIndex], 'a')
                }
                this.handleResponseBoxScrollTopBottom();
            });
        } catch (error) {
            console.log('error', error)
        }
    }

    // 问答时保持滚动条位置，默认置底
    handleResponseBoxScrollTopBottom = (locateMessageId?: any, lastClearMessageId?: any) => {
        try {
            if (this.responseRef?.current) {
                if (locateMessageId) { // 针对分页加载，滚动条定位到 上一次置顶messageId
                    const target = document.getElementById(locateMessageId)
                    if (target) {
                        const rect = target.getBoundingClientRect();
                        const parentNode: any = target.parentNode
                        const parentRect = parentNode ? parentNode?.getBoundingClientRect() : 0
                        const distance = rect.top - parentRect.top
                        this.responseRef.current.scrollTop = distance - 50
                    } else {
                        this.responseRef.current.scrollTop = 200
                    }
                } else {
                    let scrollTop = this.responseRef.current.scrollHeight
                    if (lastClearMessageId != null) {// 针对清除上下文
                        const targetElement: any = document.getElementById(lastClearMessageId);
                        const topPosition = targetElement.offsetTop;
                        scrollTop = topPosition
                    }
                    this.responseRef.current.scrollTop = scrollTop
                }
            }
        } catch (error) {
            console.log(error)
        }
    }

    // 超过每日最大发送条数限制
    handleIsMaxLengthSetFalse = () => {
        this.setState({ isMaxMsgModal: false })
    }

    // stack 重新排序
    reoderStack(stack: StackConfig[]) {
        const { currentDialog } = this.state
        const isHomeBot = this.isHomeBot()
        return stack.map((item, index: number) => {
            item.inPairs = item.user != ""
            item.index = index
            const sourceTypes = []
            if (item?.u_attachments && item.u_attachments?.length > 0) { sourceTypes.push('u_attachments') }
            if (item?.u_includedMessages && item.u_includedMessages?.length > 0) { sourceTypes.push('u_historyChat') }
            if (item?.a_attachments && item.a_attachments?.length > 0) { sourceTypes.push('a_attachments') }
            if (item?.a_includedMessages && item.a_includedMessages?.length > 0) { sourceTypes.push('a_historyChat') }
            if (!isHomeBot && currentDialog.can_show_citations === true && item?.referenceChunkList?.length > 0) { sourceTypes.push('a_reference') } // 引用知识
            if (item?.searchResults && item.searchResults?.length > 0) { sourceTypes.push('a_googleSearch') }
            if (item.isThinkingEnd) { sourceTypes.push('a_logicalReasoning') } // 思考过程（逻辑推理）
            item.sourceTypes = sourceTypes
            return item
        })
    }



    // 分页加载当前机器人数据
    handleQueryPagedChatHistory = (isScrollTop?: boolean, pageSize?: number) => {
        const { currentLastMessageId, stack, currentPageSize } = this.state
        const baseParams = this.getCommonParams()
        const data = { ...baseParams, lastMessageId: currentLastMessageId, pageSize: pageSize || currentPageSize }
        this.setState({ isLoadingMore: true })
        api_query_paged_chat_history(data).then((result: any) => {
            this.setState({ loadAllFinished: result.lastMessageId == '-1', isLoadingMore: false })
            // robotId 校验，避免多次点击robot，回应消息匹配当前点击的robot
            if (result.robotId && result.robotId !== this.state.currentDialog.robotId) return

            let newMessageAry: StackConfig[] = this.handleParseMessageFormat(result.chatHistory);
            let newStack: StackConfig[] = [...newMessageAry, ...stack]
            // 重新根据顺序 赋值 index
            newStack = this.reoderStack(newStack)
            // newStack[newStack.length-1].assistant = "![image](https://img1.baidu.com/it/u=2620888382,246971237&fm=253&fmt=auto&app=138&f=JPG?w=417&h=500)" + newStack[newStack.length-1].assistant
            this.setState({ stack: newStack, currentLastMessageId: result.lastMessageId }, () => {
                try {
                    const lastClearMessageId = newStack?.length > 0 && newStack[newStack.length - 1].meta_contextClear ? newStack[newStack.length - 1].messageId : null // 最后一条信息是清除上下文，需要修改滚动条位置
                    this.handleResponseBoxScrollTopBottom(isScrollTop ? currentLastMessageId : undefined, lastClearMessageId)
                } catch (error) {
                    console.log('error', error)
                }
            });
        }).catch((err: any) => {
            this.setState({ isLoadingMore: false })
            console.log(err)
        })

    }

    // 查询当前所选机器人信息
    handleQueryCurrentRobotMessage = () => {
        this.handleQueryPagedChatHistory()
        const { currentDialog, llmModelList } = this.state;
        // home bot 获取llm模型数据
        if (currentDialog.robot_type + '' === '0' && (!llmModelList || llmModelList.length === 0)) {
            this.handleQueryRobotLlmModel()
        } else {
            // 不是home bot， 直接获取当前机器人最大tokens数
            this.handleQueryRobotLimitToken()
        }

    }

    // 会话响应数据格式化
    handleParseMessageFormat = (ary: any) => {
        try {
            let result: StackConfig[] = [];

            ary.forEach((d: any, index: number) => {
                const attachments = Array.isArray(d.attachmentsId) && d.attachmentsId.length > 0 ? [...d.attachmentsId] : [] // 附件
                const includedMessages = Array.isArray(d.includedMessagesId) ? d.includedMessagesId : [] // 历史会话
                const messageId = d.messageId

                if (d.role === "user" || d.role === "system") { // 问题 或上下文标识
                    let stackIndex = result.length > 0 ? result.length : 0;
                    const stackItem = {
                        messageId: messageId, user: d.content, assistant: "", index: stackIndex, loading: false, isToggleAi: true, isChecked: false,
                        searchResults: [], u_attachments: attachments, referenceChunkList: [], u_messageToken: d.messageToken || 0, u_includedMessages: includedMessages,
                        meta_contextClear: d.meta_contextClear, robotId: d.robotId || this.state.currentStack.robotId
                    }
                    result.push(stackItem);
                }
                if (d.role === "assistant") { // 答案
                    if (result.length === 0) {
                        result.push({ user: "", index: 0, assistant: "", inPairs: false, messageId: messageId, loading: false, isChecked: false, isToggleAi: true, searchResults: [], referenceChunkList: [], a_messageToken: 0 });
                    }
                    let stackIndex = result.length > 0 ? result.length - 1 : 0;
                    const isUserNull = result[stackIndex].user === ""
                    result[stackIndex].inPairs = !isUserNull;
                    result[stackIndex].messageId = result[stackIndex].messageId || messageId;
                    result[stackIndex].assistant = d.content;
                    result[stackIndex].canEnhance = d.canEnhance;
                    result[stackIndex].enhanceLoading = false;
                    result[stackIndex].searchResults = Array.isArray(d.searchResults) ? d.searchResults : [];
                    result[stackIndex].referenceChunkList = Array.isArray(d.embeddingChunkList) ? d.embeddingChunkList : [];
                    result[stackIndex].a_messageToken = d.messageToken || 0;
                    result[stackIndex].a_includedMessages = includedMessages;
                    result[stackIndex].a_attachments = attachments;
                    result[stackIndex].llmModel = d.llmModel;
                    result[stackIndex].llmImage = d.llmImage;
                    result[stackIndex].robotId = d.robotId;
                    result[stackIndex].isImage = d.isImage;
                    result[stackIndex].isThinkingEnd = d.thinkingText != null && d.thinkingText != '' && d.thinkingText != undefined && d.thinkingTime != null && d.thinkingTime != ''
                    result[stackIndex].thinkingText = d.thinkingText
                    result[stackIndex].thinkingTime = d.thinkingTime
                }
            })
            return result;
        } catch (error) {
            console.log('error', error)
            return []
        }
    }

    // 获取当前机器人tokens数
    handleQueryRobotLimitToken = async () => {
        const baseParams = this.getCommonParams()
        const { llmModelSelected } = this.state;
        const data: any = { ...baseParams }
        const isHomeBot = this.isHomeBot()
        data.robot_id = data.robotId
        if (isHomeBot) {
            data.llmId = llmModelSelected ? llmModelSelected[llmModelSelected.length - 1] : null
            delete data.robot_id
        }
        delete data.robotId
        const res: any = await api_get_robot_token_limit(data).catch(() => {
            return false
        })
        const inputTokenLimit = res.inputTokenLimit // 输入框token限制
        const sharedTokenLimit = res.sharedTokenLimit // filebox 和 多选勾选 总和的token 限制
        this.setState({ currentInputTokenLimit: inputTokenLimit, currentRemainTokenLimit: sharedTokenLimit })
        return res
    }

    // Google Search切换
    handleIsGoogleSearchChange = (isGoogleSearch: boolean) => {
        this.setState({ isGoogleSearch: isGoogleSearch })
    }

    // 复制回答
    handleClickCopyAssistant = (assistant: string) => {
        CopyToClipboard(assistant);
        this.setState({ copyFinished: true }, () => {
            const copyFinishedTimer = window.setTimeout(() => {
                this.setState({ copyFinished: false })
            }, 2000)
            this.setState({ copyFinishedTimer })
        })
    }

    // 删除会话
    handleDeleteChat = (d: StackConfig) => {
        this.setState({ deleteChatConfirmModal: { visible: true, messageId: d.messageId } })
    }
    handleDeleteChatConfirmModalCancel = () => {
        this.setState({ deleteChatConfirmModal: { visible: false, messageId: null } })
    }

    handleDeleteChatConfirmModalOk = async () => {
        await this.handleDeleteMessage([this.state.deleteChatConfirmModal.messageId])
        this.handleDeleteChatConfirmModalCancel()
    }

    // 切换llm 确认取消
    handleChangeLlmModalCancel = () => {
        this.setState({ changeLlmConfirmModal: { visible: false, llm: null } })
    }

    // home bot确定切换llm模型
    handleChangeLlmModalOk = () => {
        const { changeLlmConfirmModal } = this.state
        const value = changeLlmConfirmModal.llm
        changeLlmConfirmModal.visible = false
        // 赋值
        localStorage.setItem('ama_hbllm', JSON.stringify(value))// 缓存
        this.fileUploadRef?.current?.emit_clearFileBox() // 告知子组件清空filebox
        this.updateStackCheckStatus()
        this.setState({ llmModelSelected: value, changeLlmConfirmModal, historyConversationSelected: [] }, () => {
            // 获取所选llm模型的输入框最大token
            this.handleQueryRobotLimitToken()
        })
    }

    // 查询机器人LLM信息
    handleQueryRobotLlmModel = () => {
        const baseParams = this.getCommonParams()
        const data: any = { ...baseParams }
        delete data.robotId
        this.setState({ loadingLLm: true })
        this.props.dispatch_api_get_robot_llmmModel(data).then((res: any) => {
            this.setState({ loadingLLm: false })
            const llmModelList = res.llmModelList || []
            const options: any[] = []
            // 格式化为 Cascader 组件支持的options 数据格式
            const transferChildren = (list: any) => {
                return list?.length > 0 ? list.map((item: any) => {
                    return {
                        label: item.name,
                        value: item.llmId,
                        image: item.image || null,
                        disabled: !item.enabled,
                        image_output: item.image_output,
                        text_output: item.text_output,
                    }
                }) : null
            }
            llmModelList.forEach((item: any) => {
                options.push({
                    label: item.Category,
                    value: item.llmId || item.Category,
                    imageSelected: item.image || null, // 针对Perplexity被选中后需要显示其图标
                    disabled: item.llmId && !item.enabled,
                    // Icon: item.Icon || null,
                    children: transferChildren(item.ModelList)
                })
            });
            this.setState({ llmModelList: options }, () => {
                // if (!options || options.length===0) return
                try {
                    const llmCache = localStorage.getItem('ama_hbllm')
                    let llmModelSelected = ['Poe', 'POE_GPT-4o'] // 默认值
                    if (llmCache) {
                        llmModelSelected = JSON.parse(llmCache)
                    }
                    this.setState({ llmModelSelected }, () => {
                        this.handleQueryRobotLimitToken()
                    })
                } catch (error) { }
            })
        }).catch(() => {
            this.setState({ loadingLLm: false })
        });
    }

    // 获取第一个机器人会话数据
    $_handleInitFirstRobot = (_activeRobot: any, _activeCompanyId: any) => {
        const { currentDialog } = this.state
        const update = () => {
            const activeRobot = { ..._activeRobot, companyId: _activeCompanyId }
            activeRobot.robotId = _activeRobot?.robot_id
            activeRobot.robotName = _activeRobot?.name
            // console.log('onInit',activeRobot)
            this.activeRobotId = activeRobot.robot_id
            // 获取第一个机器人会话数据
            this.setState({ currentDialog: this.deepClone(activeRobot) }, () => {
                this.fileUploadRef?.current?.emit_init(this.activeRobotId)
                this.handleQueryCurrentRobotMessage();
            });
        }

        if (currentDialog.companyId !== _activeCompanyId) {
            // console.log('切换企业，清空当前会话',_activeCompanyId)
            // 清空多选，初始化 加载更多
            this.setState({ stack: [], openSelectConversation: false, loadAllFinished: false, isLoadingMore: false, historyConversationSelected: [], currentLastMessageId: '0' }, () => {
                update()
            })
            this.handleSetInformationSourcesVisible(false) // 关闭信息来源弹窗
            this.handleSetRobotDetailVisible(false)// 关闭机器人来源弹窗
        } else {
            // console.log('还是当前会话',_activeCompanyId)
            update()
        }
    }

    // 初始化机器人数据失败
    $_handleInitError = () => {
        this.fileUploadRef?.current?.emit_init_error()
    }

    // 更新currentDialog
    $_updateCurrentDialog = (obj: any) => {
        this.setState({ currentDialog: { ...this.state.currentDialog, ...obj } })
    }

    // 判断是否是 home bot
    isHomeBot = () => {
        const { currentDialog } = this.state
        return this.isFromRobotDefault ? currentDialog.robot_type + '' === '0' : false
    }

    // 深拷贝
    deepClone(origin: any) {
        try {
            return JSON.parse(JSON.stringify(origin))
        } catch (error) {
            console.log('deepClone error', error)
            return origin
        }
    }

    // 信息来源-显示弹窗前
    handleShowInformationSourcesModal = (type: string, d: StackConfig, role: string) => {
        try {
            // 获取当前信息 信息来源 类型集合
            const sourceTypes = d.sourceTypes || []
            const invalidRole = role === 'a' ? 'u' : 'a'
            const validSourceTypes = sourceTypes.filter((type: string) => type.indexOf((invalidRole + "_")) == -1)
            const types = validSourceTypes.map((type: string) => type.indexOf('_') != -1 ? type.slice(type.indexOf('_') + 1) : type)
            let subTypeMap: any = {}
            if (validSourceTypes.includes('attachments') != -1) { subTypeMap['attachments'] = `${role}_attachments` }
            if (validSourceTypes.includes('historyChat') != -1) { subTypeMap['historyChat'] = `${role}_includedMessages` }
            const { currentInformationSourcesModal, currentRobotDetail } = this.state
            const target = this.deepClone(d)
            currentInformationSourcesModal.resource = { types, subTypeMap, ...target }
            currentInformationSourcesModal.type = type
            currentInformationSourcesModal.visible = true
            currentInformationSourcesModal.seq = d.seq
            if (currentRobotDetail.visible) this.handleSetRobotDetailVisible(false) // 关闭打开状态的 机器人详情弹窗
            this.setState(() => ({ currentInformationSourcesModal, currentStack: target }))
        } catch (error) {
            console.log(error)
        }
    }

    //信息来源-弹窗显隐
    handleSetInformationSourcesVisible = (visible: boolean) => {
        this.setState(prevState => ({ currentInformationSourcesModal: { ...prevState.currentInformationSourcesModal, visible, resource: null, seq: null } }));
    }

    // 打开增强确认弹框时
    handleShowEnhanceModal = async (d: StackConfig, dIndex?: number) => {
        let { currentDialog, stack, currentFilebox, currentEnhanceModal } = this.state;
        if (currentFilebox.isRequesting) return
        // 存在问答中的消息，禁止点击增强
        if (!this.isAllLoadingComplete) {
            message.open({
                type: 'info',
                content: i18next.t(["error.ai-response"])
            })
            return
        }
        // 增强按钮loading 效果开关
        const setLoading = (isOpen: boolean) => {
            if (d.index != undefined) {
                stack[d.index].enhanceLoading = isOpen
                this.setState({ stack })
            }
        }
        if (d.index && stack[d.index].enhanceLoading) return

        const baseParams = this.getCommonParams()
        const privacyData: any = { text: d.user, isEnhance: true, ...baseParams }
        setLoading(true)
        // 1、打开增强弹框前先判断是否超出token限制
        let tokenRes: any = await this.checkWithinMaxToken().catch(() => {
            setLoading(false)// 取消loading
        })
        if (!tokenRes || !tokenRes.result === undefined) return
        if (!tokenRes?.result && tokenRes?.overflow != 0) {
            this.toggleTokenOverflowTip(true, tokenRes.overflow)// token超出，弹出提示
            setLoading(false)// 取消loading
            return
        }
        // 2、再判断LLM是否为外部LLM
        let privacyCheckRes = await this.props.dispatch_api_privacy_check(privacyData).catch(() => {
            setLoading(false)// 取消loading
        })
        setLoading(false) // 取消loading
        if (!privacyCheckRes) return
        privacyCheckRes = privacyCheckRes?.data?.data
        const needPrivacyPopup = privacyCheckRes?.needPrivacyPopup || false

        const referenceChunkList: EmbeddingChunkFace[] = this.deepClone(d.referenceChunkList)
        let currentStack = this.deepClone(d);
        currentStack.isEnhance = true;

        // 增强确认弹框
        if (needPrivacyPopup && (referenceChunkList && referenceChunkList.length > 0)) {
            currentDialog.llmModelImage = privacyCheckRes?.image || null;// 设置LLM图标
            currentDialog.llmCategory = privacyCheckRes?.llmCategory || null;// 设置LLM分类
            currentDialog.llmModel = privacyCheckRes?.llmModel || null;// 设置LLM名称
            currentEnhanceModal.resource = {
                llmModelImage: currentDialog.llmModelImage,
                llmCategory: currentDialog.llmCategory,
                llmModel: currentDialog.llmModel,
                isEnhance: true,
                dataList: referenceChunkList
            }
            currentEnhanceModal.visible = true
            this.setState({ currentEnhanceModal, currentStack, currentDialog });
            return
        }
        // 直接发送请求，无需弹框
        this.setState({ currentStack }, () => {
            const embeddingChunkList = referenceChunkList.concat(this.deepClone(d.searchResults));
            this.handleSendEnhanceMessage(d, embeddingChunkList);
        })
    }

    // 增强-确认
    handleEnhanceOk = (isEnhance: boolean, embeddingChunkList: any) => {
        let { currentStack } = this.state;
        embeddingChunkList = embeddingChunkList.concat(currentStack.searchResults);
        const question = currentStack.user;
        // 如果普通对话的确认弹框，发起chatMsg普通对话请求，并带上embeddingChunkList参数
        if (!isEnhance) {
            this.handleSendMessage(question, embeddingChunkList)
            return
        } else {
            this.handleSendEnhanceMessage(currentStack, embeddingChunkList);
        }
    }

    // 增强-取消
    handleEnhanceCancel = (isEnhance: boolean) => {
        // 若是普通对话取消，需要删除最新的对话
        let { currentStack, stack, currentEnhanceModal } = this.state;
        if (!isEnhance) {
            stack = stack.slice(0, stack.length - 1)
            currentStack.searchResults = [];
            currentStack.referenceChunkList = [];
            currentStack.user = '';
        }
        currentEnhanceModal.resource = {}
        this.setState({ stack, currentStack, currentEnhanceModal });
    }

    //增强-弹窗显隐
    handleSetEnhanceVisible = (visible: boolean) => {
        this.setState(prevState => ({ currentEnhanceModal: { ...prevState.currentEnhanceModal, visible, resource: {} } }));
    }

    handleDeleteMessage(messageIds: any[]) {
        if (!messageIds || messageIds.length == 0) return
        return new Promise((resolve, reject) => {
            let { stack } = this.state
            const baseParmas = this.getCommonParams()
            const data = { ...baseParmas, messageIds }
            this.props.dispatch_api_delete_messages(data).then((res: any) => {
                const finalMessageIds = [...messageIds, ...res.messageIds] // 接口可能会返回 删除的上下文 messageId
                // console.log('finalMessageIds',finalMessageIds)
                let newStack: StackConfig[] = stack.filter(i => !finalMessageIds.includes(i.messageId))
                // console.log('newStack',newStack)
                newStack = this.reoderStack(newStack)
                this.setState({ stack: newStack })
                resolve(res)
            }).catch(() => {
                resolve(false)
            })
        })
    }

    reverseScrollTopMemory = () => {
        const { scrollTopMemory } = this.state
        const container = document.getElementById('scrollableDiv')
        if (scrollTopMemory !== null && container) {
            container.scrollTop = scrollTopMemory
        }
    }

    // 选择对话-取消
    handleCancelSelectedConversation() {
        const { historyConversationMemory, stack, scrollTopMemory } = this.state
        // 恢复上次记忆的数据
        stack.forEach(item => {
            item.isChecked = historyConversationMemory.filter(h => h.messageId === item.messageId).length > 0
        })
        const historyConversationSelected = this.deepClone(historyConversationMemory)
        this.setState({ openSelectConversation: false, stack, historyConversationSelected }, () => {
            this.reverseScrollTopMemory()
        })
    }

    // 选择对话-确认
    handleConfirmSelectedConversation() {
        this.setState({ openSelectConversation: false }, () => {
            this.reverseScrollTopMemory()
            if (this.fileUploadRef?.current) this.fileUploadRef.current?.emit_updateSelectChat(this.state.historyConversationSelected) // 上传文件组件-更新选择对话 列表
        })
    }

    // 删除所选对话
    async handleDeleteSelectedConversation() {
        let { historyConversationSelected } = this.state
        let messageIds: any = new Set(historyConversationSelected.map((tag: any) => tag.messageId)) //去重
        messageIds = [...messageIds]
        await this.handleDeleteMessage(messageIds)
    }

    // 删除上下文
    handleDeleteContextClear(d: StackConfig) {
        this.handleDeleteMessage([d.messageId])
    }

    // 处理多选会话内容
    handleCheckConversationContent(d: StackConfig) {
        let assistantContent = d.assistant
        try {
            assistantContent = assistantContent.replace(/!\[([^\]]*)\]\(([^)]+)\)/g, "[Image]")// 正则表达式匹配 Markdown 图片格式，替换为［图片］字符串
            const tmpDom = document.createElement("div")
            tmpDom.innerHTML = assistantContent
            tmpDom.style.display = "none"
            document.body.appendChild(tmpDom)
            const text = tmpDom.innerText // 提取存文本
            document.body.removeChild(tmpDom)
            return text
        } catch (error) {
            return assistantContent
        }
    }

    // 保持勾选的历史会话数据
    handleAddHistoryChat(d: StackConfig, tags: any[]) {
        const obj = { messageId: d.messageId, question: d.user, assistant: "" }
        if (d.assistant != "") {
            obj.assistant = this.handleCheckConversationContent(d)
        }
        tags.push(obj)
    }

    // 问答区域勾选对话
    onChangeCheckConversationArea = (e: any, d: StackConfig) => {
        this.onChangeCheckConversation(e, d, !d.isChecked)
    }

    // 勾选对话
    onChangeCheckConversation = (e: any, d: StackConfig, isChecked?: boolean) => {
        try {
            let { stack, historyConversationSelected } = this.state
            let tags = [...historyConversationSelected]
            const checked = isChecked !== undefined ? isChecked : e.target.checked
            const updateStack = (prop: string, value: any) => {
                stack.forEach((item: any) => {
                    if (item.index !== d.index) return
                    item[prop] = value
                })
            }
            if (checked) {
                const messageToken = (d.a_messageToken || 0) + (d.u_messageToken || 0)
                const isOverflow = this.calcTokensWhetheOverflow(messageToken)
                if (isOverflow) {// token溢出不能勾选
                    message.warning(i18next.t("upload.file-token-excced-error1"))
                    return
                }
            }
            updateStack("isChecked", checked)
            if (checked) { //勾选
                this.handleAddHistoryChat(d, tags)
            } else { // 未勾选
                tags = tags.filter((tag: any) => ![d.messageId].includes(tag.messageId))
            }
            this.setState({ stack, historyConversationSelected: tags })
        } catch (error) {
            console.log(error)
        }
    }

    // 获取当前机器人勾选对话的总token 数
    getCurrentCheckedMsgTotalToken = () => {
        const { stack } = this.state
        return stack.reduce((prev: any, cur: StackConfig) => {
            if (cur.isChecked) {
                return prev + (cur.a_messageToken || 0) + (cur.u_messageToken || 0)
            }
            return prev
        }, 0)
    }

    // 计算当前机器人所用token是否超出总token数
    calcTokensWhetheOverflow = (readyTokenAddition?: number) => {
        try {
            // filebox 附件token总数 + filebox历史 会话 token总数 + 输入框总数 <= 机器人token总数
            const { currentFilebox, currentRemainTokenLimit } = this.state
            const attachmentTokens = currentFilebox.attachmentTokens || 0
            const checkedMsgTokens = this.getCurrentCheckedMsgTotalToken()
            const isOverflow = (attachmentTokens + checkedMsgTokens + (readyTokenAddition || 0)) > currentRemainTokenLimit
            return isOverflow
        } catch (error) {
            console.log(error)
        }
    }
    // 获取当前机器人已使用token数
    getCurrentRobotTokenUsed = () => {
        // filebox token总数 + 勾选会话 token总数
        const { currentFilebox } = this.state
        const fileTokens = currentFilebox.attachmentTokens || 0
        const checkedMsgTokens = this.getCurrentCheckedMsgTotalToken()
        return fileTokens + checkedMsgTokens
    }

    // llm Cascader change
    handleLlmCascaderOptionChange = async (value: any, selectedOptions: any) => {
        const { currentFilebox, historyConversationSelected } = this.state
        if ((!currentFilebox.attachments || currentFilebox.attachments.length === 0) && historyConversationSelected.length === 0) {
            localStorage.setItem('ama_hbllm', JSON.stringify(value))// 缓存
            this.setState({ llmModelSelected: value }, () => {
                // 获取所选llm模型的机器人token
                this.handleQueryRobotLimitToken()
            })
            return
        }
        // 清空filebox确认弹框
        this.setState({ changeLlmConfirmModal: { visible: true, llm: value } })
    };

    handleDragEnterPanel = (e: any) => {
        // console.log('---DragEnter 开启-当拖动文件到面板中', Date.now())
        e.preventDefault(); // 阻止默认行为
        e.stopPropagation() // 针对firefox
        const dt = e.dataTransfer;
        // 若机器人在对话中, 禁止拖放操作
        if (dt.types && dt.types.indexOf('Files') === -1) {
            dt.dropEffect = 'none';
            return
        }
        // 若机器人在对话中, 禁止拖放操作
        if (!this.isAllLoadingComplete) {
            return;
        }
        this.setFileDragingStatus(true)
    };

    // 当拖动文件到面板中
    handleFileDragOverPanel = (e: any) => {
        // console.log('---DragOver 开启-当拖动文件到面板中', Date.now())
        e.preventDefault(); // 阻止默认行为
        e.stopPropagation() // 针对firefox
        const dt = e.dataTransfer;
        dt.dropEffect = 'copy'; // 指定拖放类型
    };

    // 文件拖放到面板结束
    handleFileDropOverPanel = (e: any) => {
        // console.log('结束-文件拖放到面板结束', e)
        e.preventDefault();
        e.stopPropagation();
        const items = e.dataTransfer.items
        if (items) {
            // 使用 DataTransferItemList 接口访问拖放的数据, 筛选出 file类型的文件
            try {
                let fileItems: any[] = []
                for (let i = 0; i < e.dataTransfer.items.length; i++) {
                    if (e.dataTransfer.items[i].kind === 'file') {
                        const file = e.dataTransfer.items[i].getAsFile();
                        fileItems.push(file)
                    }
                }
                this.updateFileDragToPanelInfo(fileItems)
            } catch (error) {
                console.log(error)
            }
        }
        this.setFileDragingStatus(false)
    }

    // 当拖拽的文件离开面板
    handleFileDragLeavePanel = (e: any) => {
        e.preventDefault();
        e.stopPropagation();
        // console.log('离开-当拖拽的文件离开面板', Date.now())
        this.setFileDragingStatus(false)
    }

    // 增强-查看机器人Token是否超出
    checkWithinMaxToken = () => { // isEnhance?:boolean
        return new Promise((resolve, reject) => {
            const baseParams = this.getCommonParams()
            let { llmModelSelected } = this.state;
            const isHomeBot = this.isHomeBot()
            const p: any = {
                ...baseParams,
                isEnhance: true
            }
            // homebot 需要发送llmId
            if (isHomeBot) {
                p.llmId = llmModelSelected ? llmModelSelected[llmModelSelected.length - 1] : null
            }
            p.robot_id = p.robotId
            delete p.robotId
            api_check_max_token(p).then((res: any) => {
                resolve(res)
            }).catch(() => {
                reject(false)
            })
        })
    }

    // token超出提示框 显隐控制
    toggleTokenOverflowTip = (visible: boolean, overflow?: any) => {
        const { tokenExceedModal } = this.state
        tokenExceedModal.visible = visible
        if (overflow) tokenExceedModal.overflow = overflow
        this.setState({ tokenExceedModal });
    }

    // 开启或关闭拖拽中样式
    setFileDragingStatus(open: boolean) {
        let { isDragingFileToPanel } = this.state
        // if (open !== isDragingFileToPanel){
        isDragingFileToPanel = open
        this.setState({ isDragingFileToPanel }) // 拖拽样式
        // }
    }

    // 更新拖拽文件
    updateFileDragToPanelInfo = (newFileList?: any) => {
        try {
            // 通知上传组件 更新文件并上传
            if (this.fileUploadRef?.current) {
                this.fileUploadRef.current?.emit_updateFiles([...newFileList])
            }
        } catch (error) {
            console.log('error', error)
        }
    }

    // 修改stack数据 勾选框状态
    updateStackCheckStatus = (isChecked?: boolean) => {
        const { stack } = this.state
        stack.forEach((item: any) => {
            item["isChecked"] = isChecked
        })
        this.setState({ stack })
    }

    // 多选状态下 监听键盘事件
    handleMultipleSelectionKeyDown = (event: any) => {
        if (!this.state.openSelectConversation) return
        console.log('robot 多选状态下 监听键盘事件')
        if (event.key === 'Escape') {// 检查是否按下 ESC 键
            this.handleCancelSelectedConversation()
        }
        if (event.key === 'Enter') {// 检查是否按下 Enter 键
            if (this.multipleSelectionConfirmDisabled) return
            this.handleConfirmSelectedConversation()

        }
    }
    // 来自FileUpload的update事件处理
    onUpdateEventFromFileUpload = (eventName: string, eventParams?: any) => {
        switch (eventName) {
            case "filebox_attachments":
                this.onUpdateFileboxInfo("attachments", eventParams)
                break
            case "filebox_request_status":
                this.onUpdateFileboxInfo("isRequesting", eventParams)
                break
            case "open_select_chat": // 开启选择历史会话
                const dom = document.getElementById('scrollableDiv')
                const scrollTopMemory = dom?.scrollTop || null
                const historyConversationMemory = this.deepClone(this.state.historyConversationSelected)
                this.setState({ historyConversationMemory, openSelectConversation: true, scrollTopMemory })
                break
            case "clear_all_historychat": // 清除历史会话
                this.updateStackCheckStatus(false)
                this.setState({ historyConversationSelected: [], historyConversationMemory: [] })
                break
            case "clear_one_historychat": // 清除某个历史会话
                const { target, index } = eventParams
                const { historyConversationSelected, stack } = this.state
                const _stack = stack.map(i => (i.messageId === target.messageId ? { ...i, isChecked: false } : i))
                historyConversationSelected.splice(index, 1)
                this.setState({ historyConversationSelected, stack: _stack })
                break
            default:
                break
        }
    }

    // 更新当前机器人已上传的file
    onUpdateFileboxInfo = (propName: string, propValue: any) => {
        const { currentFilebox } = this.state
        if (propName === "isRequesting") {
            currentFilebox.isRequesting = propValue
        } else if (propName === "attachments") {
            let fileTokens: number = 0
            if (propValue?.length > 0) {
                currentFilebox.attachments = this.deepClone(propValue).map((i: any) => {
                    fileTokens += Number(i.fileLength)
                    // 去除多余属性
                    delete i.isDeleteing
                    delete i.isTruncate
                    delete i.ref
                    return i
                }).map((i: any) => i.attachmentId)
            } else {
                currentFilebox.attachments = propValue
            }
            currentFilebox.attachmentTokens = fileTokens
        }
        this.setState({ currentFilebox })
    }

    handleViewRobotDetails = () => {
        const robotLogo = this.renderDiaLogImg(this.state.currentDialog)
        if (this.state.currentInformationSourcesModal.visible) this.handleSetInformationSourcesVisible(false)// 关闭打开状态的 信息来源弹窗
        this.setState(prevState => ({ currentRobotDetail: { ...prevState.currentRobotDetail, resource: { robotId: this.state.currentDialog.robotId, robotLogo: robotLogo }, visible: true } }))
    }

    handleSetRobotDetailVisible = (visible: boolean) => {
        this.setState(prevState => ({ currentRobotDetail: { ...prevState.currentRobotDetail, resource: { robotId: null }, visible } }))
    }

    // 回答区域滚动条事件
    handleResponseScroll = (e: any) => {
        try {
            // 滚动到顶部
            if (e.target.scrollTop === 0) {
                const { isLoadingMore, loadAllFinished } = this.state
                if (isLoadingMore || loadAllFinished) return
                this.handleQueryPagedChatHistory(true) // 请求分页加载更多
            }
        } catch (error) {
            console.log(error);
        }
    }

    // 停止生成回答
    handleStopAssistantStreaming = async () => {
        console.log('robot的停止生成')
        const baseParmas = this.getCommonParams()
        const data = { ...baseParmas }
        const res = await this.props.dispatch_api_stop_streaming(data).catch((err: any) => { return err })
        this.questionInputRef?.current?.emit_update_generate_answer_status(false) // 修改输入框-生成答案状态'
        if (!res?.result) { // 停止生成 请求失败
            message.error(i18next.t('error.request-failed'))
        }
        const { stack } = this.state
        const lastIndex = stack.length - 1
        this.setState(prevState => ({
            stack: prevState.stack.map((item, index) =>
                index === lastIndex
                    ? { ...item, loading: false, isToggleAi: true }  // 创建新对象并修改属性
                    : item                      // 保留其他对象不变
            )
        }));
        return res
    }

    $_handleClickHomeWrapper = () => {
        const { isDragingFileToPanel } = this.state
        if (!isDragingFileToPanel) return
        this.setFileDragingStatus(false)
    }

    handleToggleImageView = (url?: any) => {
        const { viewImage } = this.state
        viewImage.visible = url != undefined
        viewImage.url = url || ''
        this.setState({ viewImage })
    };

    // robotmanage通知更新机器人类别
    onUpdateRobotList = () => {
        // console.log('onUpdateRobotList',this.sideMenuRef)
        this.sideMenuRef?.current?.emit_refresh_robotlist && this.sideMenuRef?.current.emit_refresh_robotlist()
    }

    // 回到机器人市场
    onBackToMarket = () => {
        const fun = this.props.onBack
        fun && fun()
    }

    renderRobotInfo = (d?: StackConfig) => {
        const { currentDialog } = this.state;
        // console.log('currentDialog',currentDialog)
        const isHomeBot = this.isHomeBot()
        let infoIcon = this.renderDiaLogImg(currentDialog)
        let infoName = currentDialog.robotName
        let isLLMImageParent = false
        if (d && isHomeBot) {
            if (d.llmModel && ['perplexity', 'cloud', 'poe', 'local'].includes(d.llmModel?.toLocaleLowerCase())) {
                isLLMImageParent = true // perplexity cloud poe local特殊处理，显示svg图标
            }
            infoIcon = d.llmImage ? d.llmImage : infoIcon
            infoName = d.llmModel ? d.llmModel : infoName
        }
        return (// home bot 回答 需要显示llm信息，其他机器人显示机器人信息
            <div className="ai-info">
                {isLLMImageParent ? <Svgicon svgName={d?.llmModel?.toLocaleLowerCase()} /> : <img draggable="false" src={infoIcon} alt="" />}
                <span>{infoName}</span>
            </div>
        )
    }

    renderRobotResponseMe = (d: StackConfig, index: number) => {
        return (
            <div className={classnames("me")}>
                {d.user ?
                    <>
                        <div className="me-info">
                            <UserAvatar size={20} isLoginUser={true} />
                            <span>{i18next.t('you')}</span>
                        </div>
                        <div className="me-res">
                            <p>{d.user}</p>
                            <div className={classnames(["additional-operation", { hidden: this.state.openSelectConversation }])}>
                                <div className="additional-operation-left">
                                    {
                                        d?.sourceTypes?.includes('u_attachments') ?
                                            <span onClick={(e) => this.handleShowInformationSourcesModal('attachments', d, "u")}>
                                                <Svgicon svgName="icon_attachment" /><span>{i18next.t('upload.attachFile')}·{d?.u_attachments?.length}</span>
                                            </span> : null
                                    }
                                    {
                                        d?.sourceTypes?.includes('u_historyChat') ?
                                            <span onClick={(e) => this.handleShowInformationSourcesModal('historyChat', d, "u")}>
                                                <Svgicon svgName="icon_historychat" /><span>{i18next.t('home.historical-chat')}·{d?.u_includedMessages?.length}</span>
                                            </span> : null
                                    }
                                </div>
                            </div>
                        </div>
                    </> : null
                }
            </div>
        )
    }

    // 机器人回答item
    renderRobotResponseAssist(d: StackConfig, index: number) {
        const { currentDialog, copyFinished, openSelectConversation } = this.state;
        const isHomeBot = this.isHomeBot()
        return (
            <>
                {this.renderRobotInfo(d)}
                {!d.loading && d.isThinkingEnd === true ? <Flex onClick={() => this.handleShowInformationSourcesModal('logicalReasoning', d, 'a')} className="loading-area is-think" align="center">
                    <div className="loading-text"><span>{i18next.t('reasoning-time', { time: d.thinkingTime, unit: d?.thinkingTime > 1 ? 's' : '' })}</span><Svgicon svgName="arrow_right_line" /></div> </Flex> : null}
                {
                    d.loading ? <Flex onClick={() => !d.isThinking ? null : this.handleShowInformationSourcesModal('logicalReasoning', d, 'a')} className={classnames(["loading-area", { 'is-think': d.isThinking }])} align="center">
                        <Lottie className={classnames(["loading-animation", { image: d.isImage }])} animationData={d.isImage ? ImageLoadingAnimation : loadingAnimation} loop={true} autoplay={true} />
                        {d.isThinking ? <div className="loading-text"><span>{i18next.t('reasoning')}</span><Svgicon svgName="arrow_right_line" /></div> : null}
                    </Flex>
                        : <div className={classnames(["result", `copy-assistant-${index}`])}>
                            <Markdown key={index} content={d.assistant} onViewImage={this.handleToggleImageView} imageViewDisabled={openSelectConversation} />
                            <div className={classnames(["additional-operation", "light", { hidden: this.state.openSelectConversation }])}>
                                <div className="additional-operation-left">
                                    {
                                        d?.sourceTypes?.includes('a_attachments') ?
                                            <span onClick={(e) => this.handleShowInformationSourcesModal('attachments', d, "a")}>
                                                <Svgicon svgName="icon_attachment" /><span>{i18next.t('upload.attachFile')}·{d?.a_attachments?.length}</span>
                                            </span> : null
                                    }
                                    {
                                        d?.sourceTypes?.includes('a_historyChat') ?
                                            <span onClick={(e) => this.handleShowInformationSourcesModal('historyChat', d, "a")}>
                                                <Svgicon svgName="icon_historychat" /><span>{i18next.t('home.historical-chat')}·{d?.a_includedMessages?.length}</span>
                                            </span> : null
                                    }
                                    { //引用知识
                                        d?.sourceTypes?.includes('a_reference') ?
                                            <span onClick={(e) => this.handleShowInformationSourcesModal('reference', d, "a")}>
                                                <Svgicon svgName="icon_reference" /><span>{i18next.t('app.references')}·{d.referenceChunkList.length}</span>
                                            </span> : null
                                    }
                                    {// 谷歌搜索
                                        d?.sourceTypes?.includes('a_googleSearch') ?
                                            <span onClick={(e) => this.handleShowInformationSourcesModal('googleSearch', d, "a")}>
                                                <Svgicon svgName="icon_googlesearch" /><span>{i18next.t('home.google-search')}·{d.searchResults.length}</span>
                                            </span> : null
                                    }
                                </div>
                                {
                                    d.isToggleAi ?
                                        <div className="additional-operation-right">
                                            {
                                                d.assistant != null && d.assistant != '' ? <>
                                                    { // 图片 类型不显示复制按钮
                                                        d.isImage ? null : <Tooltip placement="top" rootClassName="copy-tooltip" title={copyFinished ? (<div className="copyed"><CheckCircleFilled />{i18next.t("copied")}</div>) : i18next.t('app.copy')} arrow={false}>
                                                            <span onClick={() => this.handleClickCopyAssistant(d.assistant)}><Svgicon svgName="copy" /></span>
                                                        </Tooltip>
                                                    }
                                                    {
                                                        !isHomeBot && d.canEnhance ? d.enhanceLoading ? <Spin indicator={<LoadingOutlined style={{ fontSize: 16 }} spin />} />
                                                            : <Tooltip rootClassName='enhance' placement="top" title={i18next.t('app.enhance')} arrow={false}><span onClick={() => this.handleShowEnhanceModal(d, index)}><Svgicon svgName="enhance" /></span> </Tooltip> : null
                                                    }</> : null
                                            }
                                            {
                                                d.messageId ? <Tooltip placement="top" title={i18next.t('app.delete')} arrow={false}><span onClick={() => this.handleDeleteChat(d)}>
                                                    <Svgicon svgName="icon_delete" />
                                                </span>
                                                </Tooltip> : null
                                            }
                                        </div>
                                        : null
                                }
                            </div>
                        </div>
                }
            </>)
    }

    // 机器人logo配置
    renderDiaLogImg = (d: any): string => {
        interface MapImg {
            [key: string]: string
        }
        if (!["robot_0001"].includes(d.robotId)) {
            if (d.logo) {
                return d.logo;
            }
        } else {
            const { themeInfo } = this.props
            const map: MapImg = { "robot_0001": themeInfo.homebotAvatar || HomeBotIocn }
            if (map[d.robotId]) {
                return map[d.robotId]
            }
        }
        return DefaultRobot
    }

    componentDidMount() {
        // if (this.isFromRobotDefault) wss.connect() // 市场是嵌套在机器人列表
        wss.connect()
        try {
            // 添加事件监听器
            window.addEventListener('keydown', this.handleMultipleSelectionKeyDown);
            const responseRef = this.responseRef
            this.setState({ containerHeight: (responseRef?.current.offsetHeight) })
        } catch (error) {
        }
    }

    componentDidUpdate(prevProps: Readonly<any>, prevState: Readonly<ContextState>, snapshot?: any): void {
        // 监听 公司ID 变化
        if ((this.props.activeCompanyId + '') !== (prevProps.activeCompanyId + '') && prevProps.activeCompanyId != null) {
            // console.log('activeCompanyId 状态变化:', this.props.activeCompanyId,prevProps.activeCompanyId);
            // this.handleStopAssistantStreaming()// 停止当前问答
            // 断开旧WS连接，重新为当前公司新建连接
            wss.disconent()
            wss.connect(this.props.activeCompanyId)
        }
        // if (this.props.activeRobot !== prevProps.activeRobot) {
        //     console.log('activeRobot 变化:', this.props.activeRobot);
        //     this.setState({ currentDialog: this.props.activeRobot })
        // }
    }

    componentWillUnmount() {
        wss.unRegisterCallBack('chatMsg')
        wss.unRegisterCallBack('homeChatMsg')
        wss.unRegisterCallBack('enhanceChatMsg')
        wss.onbeforeunload()
        window.removeEventListener('keydown', this.handleMultipleSelectionKeyDown);  // 移除键盘事件监听器
        if (this.state.copyFinishedTimer) window.clearTimeout(this.state.copyFinishedTimer)
    }

    render() {
        const {
            stack, currentDialog, currentInformationSourcesModal, isMaxMsgModal, currentEnhanceModal, changeLlmConfirmModal,
            llmModelList, llmModelSelected, isDragingFileToPanel, currentInputTokenLimit, tokenExceedModal, currentFilebox, isLoadingMore, loadAllFinished,
            currentRobotDetail, openSelectConversation, containerHeight, currentPageSize, deleteChatConfirmModal, viewImage, loadingLLm
        } = this.state;

        let dialogItem: any = undefined;
        const stack_reverse = [...stack] // 为了配合上拉加载更多组件 InfiniteScroll，倒序渲染
        stack_reverse.reverse()

        if (currentDialog.robotId) {
            dialogItem = currentDialog;
        }

        const isHomeBot = this.isHomeBot()

        const nameRender = (dialogItem: any) => {
            return <div className={"name"} onClick={() => isHomeBot ? null : this.handleViewRobotDetails()}>
                <div className={"pic"}>
                    {dialogItem ? <img draggable="false" src={this.renderDiaLogImg(dialogItem)} alt="" /> : null}
                </div>
                <div className={"text"}>
                    {dialogItem.chatName ? dialogItem.chatName : dialogItem.robotName ? dialogItem.robotName : ""}
                </div>
                <Svgicon className="robot-detail" svgName="icon_right_arrow" />
            </div>
        }

        return (
            <div className="robot-chat-container">
                <div className={classnames(['right-content', { draging: isDragingFileToPanel }])}>
                    <div className={"header"}>
                        {this.isFromBotMarket ? <Svgicon className="reback" svgName="reback" onClick={this.onBackToMarket} /> : null}
                        {
                            openSelectConversation ?
                                <div className="select-chat-status">
                                    <span>{i18next.t("app.select-conversation")}</span>
                                    <Flex className="select-chat-btns" gap={12} align="center">
                                        <Button onClick={() => this.handleCancelSelectedConversation()} className="tertiary" color="default" variant="filled">{i18next.t(`app.cancel`)} [ESC]</Button>
                                        <Button onClick={() => this.handleConfirmSelectedConversation()} disabled={this.multipleSelectionConfirmDisabled} type="primary">{i18next.t(`app.ok`)} [Enter]</Button>
                                    </Flex>
                                </div>
                                :
                                <>
                                    {dialogItem ?
                                        (
                                            <div className={"name-con"}>
                                                {
                                                    isHomeBot ? <Popover arrow={false} placement="bottom" content={i18next.t("homebot.describe")} overlayClassName="homebot-describe-popover">
                                                        {nameRender(dialogItem)}
                                                    </Popover> : nameRender(dialogItem)
                                                }
                                            </div>
                                        ) : null
                                    }
                                    <div className="operate">
                                        { // Home Bot 才显示LLM模型选择框
                                            isHomeBot ? <LLModelCascaderComponent options={llmModelList} value={llmModelSelected} onUpdate={this.handleLlmCascaderOptionChange} loading={loadingLLm} disabled={currentFilebox.isRequesting} /> : null
                                        }
                                    </div>
                                </>
                        }

                    </div>
                    <Flex style={{ width: '100%', flex: 1, overflow: "hidden", position: 'relative' }}>
                        <Flex className={classnames(['right-main', { 'auto-size': currentInformationSourcesModal.visible }])} vertical align="center"
                            onDragEnter={this.handleDragEnterPanel} onDragOver={this.handleFileDragOverPanel}>
                            <div id="scrollableDiv" className={classnames(["response"])} ref={this.responseRef} key="response" style={{ height: containerHeight + 'px' }}>
                                <InfiniteScroll
                                    dataLength={stack_reverse.length}
                                    next={() => null}
                                    hasMore={!loadAllFinished}
                                    loader={<span></span>}
                                    endMessage={stack_reverse.length > currentPageSize / 2 ? <Divider className="no-more-data" plain>{i18next.t("app.no-more-data")}</Divider> : null}
                                    scrollableTarget="scrollableDiv"
                                    inverse={true}// 顶部加载
                                    onScroll={this.handleResponseScroll}
                                    style={{ display: 'flex', flexDirection: 'column-reverse' }} // 为了顶部加载的样式
                                >
                                    {stack_reverse.map((d, index) =>
                                        <div id={d.messageId + ''} className={classnames(["item", {
                                            "open-check": openSelectConversation && d.inPairs,
                                            "context-cleared": d.meta_contextClear, "last-index": index === 0, 'is-checked': d.isChecked
                                        }])}
                                            key={index + '' + d.seq}>
                                            {
                                                d.meta_contextClear ? (<Divider plain={true}>{i18next.t("home.context-cleared")}
                                                    {!openSelectConversation ? <UndoOutlined onClick={() => this.handleDeleteContextClear(d)} /> : null}</Divider>) :
                                                    <div className={classnames(["me-ai", { 'is-checked': d.isChecked }])} onClick={openSelectConversation ? (e) => this.onChangeCheckConversationArea(e, d) : () => null}>
                                                        { // 问题和答案成对出现才能显示勾选框
                                                            openSelectConversation && d.inPairs ?
                                                                <Checkbox disabled={!this.isAllLoadingComplete || currentFilebox.isRequesting} checked={d.isChecked} onChange={e => this.onChangeCheckConversation(e, d)} />
                                                                : null}
                                                        {this.renderRobotResponseMe(d, index)}
                                                        <div className={"ai"}>
                                                            <div className="ai-res">
                                                                {this.renderRobotResponseAssist(d, index)}
                                                            </div>
                                                        </div>
                                                    </div>
                                            }
                                        </div>
                                    )}
                                    {
                                        !isLoadingMore && loadAllFinished ?
                                            <>
                                                {
                                                    // 欢迎语
                                                    currentDialog.welcome_message != "" && currentDialog.welcome_message != undefined ?
                                                        <Flex className="item welcome">
                                                            {this.renderRobotInfo()}
                                                            <div className="result">{currentDialog.welcome_message}</div>
                                                        </Flex>
                                                        : null
                                                }
                                                <div className={"prompt"}>
                                                    {dialogItem?.robotIntroduction || i18next.t(["introduction.default"])}
                                                </div>
                                            </>
                                            : null
                                    }
                                    {/* 滚动到顶部，下拉加载更多loading */}
                                    <div className="loading-area">
                                        {isLoadingMore ? <div className="loading-more"><Spin indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />} /></div> : null}
                                        {/* { loadAllFinished ? <div className="no-more-data">{i18next.t("app.no-more-data")}</div> : null } */}
                                    </div>
                                </InfiniteScroll>
                            </div>
                            <div className={classnames(["submit", { hidden: openSelectConversation }])} key="submit">
                                <Tooltip placement="top" className="clearing-context-tooltip" title={i18next.t('home.clearing-the-context')} arrow={false}>
                                    <Flex onClick={this.handleClearCurrentChat} className={classnames(["clear-current-chat", { disabled: !this.isAllLoadingComplete }])} align="center" justify="center">
                                        <Svgicon svgName="icon_clear" draggable="false" />
                                    </Flex>
                                </Tooltip>
                                <div className="submit-right">
                                    <div className="input-area">
                                        {
                                            <FileUploadComponent
                                                ref={this.fileUploadRef}
                                                isDraging={isDragingFileToPanel}
                                                isHomeBot={this.isHomeBot()}
                                                llm={llmModelSelected}
                                                robotId={currentDialog.robotId}
                                                getActiveRobotId={() => this.activeRobotId}
                                                getTokenUsed={this.getCurrentRobotTokenUsed}
                                                onUpdate={this.onUpdateEventFromFileUpload} />
                                        }
                                        <QuestionInputComponent ref={this.questionInputRef} isHomeBot={isHomeBot} maxLength={currentInputTokenLimit} onUpdateGoogleSearch={this.handleIsGoogleSearchChange} onUpdateFileList={this.updateFileDragToPanelInfo} onSend={this.handlePreSendMessage} onStopStreaming={this.handleStopAssistantStreaming} />
                                    </div>
                                </div>
                            </div>
                            { //  文件拖拽提示
                                isDragingFileToPanel ?
                                    <div className={classnames(["drag-drop-overlay"])} data-content={i18next.t("upload.file-draging-tip")} data-icon={<Svgicon svgName="icon_drag" />}
                                        onDragOver={this.handleFileDragOverPanel} onDragLeave={this.handleFileDragLeavePanel} onDrop={this.handleFileDropOverPanel}></div> : null
                            }
                        </Flex>
                        {// 信息来源
                            currentInformationSourcesModal.visible ?
                                <InformationSourcesDrawer ref={this.infomationSoruceRef} type={currentInformationSourcesModal.type} visible={currentInformationSourcesModal.visible} appendParent={true}
                                    resource={currentInformationSourcesModal.resource} updateVisible={this.handleSetInformationSourcesVisible} /> : null
                        }
                        {// 机器人详情
                            currentRobotDetail.visible ? <RobotDetailDrawer resource={currentRobotDetail.resource} appendParent={true}
                                visible={currentRobotDetail.visible} updateVisible={this.handleSetRobotDetailVisible} /> : null
                        }
                    </Flex>
                </div>
                {/* 超过每日最大发送条数限制 */}
                <Modal open={isMaxMsgModal} centered wrapClassName="common-confirm-modal" width={272} onCancel={() => this.handleIsMaxLengthSetFalse()} okText={i18next.t("app.ok")}
                    footer={(_, { OkBtn }) => (<div><OkBtn /></div>)} onOk={this.handleIsMaxLengthSetFalse} closable={false} maskClosable={false}
                ><div>{i18next.t(["app.max-msg"])}</div></Modal>
                <Modal width={272} title={i18next.t("app.change-llm-confirm")} maskClosable={false} centered closable={false} okText={i18next.t("app.change-llm")} cancelText={i18next.t("app.no-change-llm")}
                    open={changeLlmConfirmModal.visible} onCancel={() => this.handleChangeLlmModalCancel()} wrapClassName="common-confirm-modal" onOk={this.handleChangeLlmModalOk}>
                    <div>{i18next.t("app.change-llm-tip")}</div>
                </Modal>
                <Modal width={272} title={i18next.t("app.confirm-del")} maskClosable={false} centered closable={false} okText={i18next.t("app.ok")} cancelText={i18next.t("app.cancel")}
                    open={deleteChatConfirmModal.visible} onCancel={() => this.handleDeleteChatConfirmModalCancel()} wrapClassName="common-confirm-modal" onOk={this.handleDeleteChatConfirmModalOk}>
                    <div>{i18next.t("home.confirm-delete-chat-tip")}</div>
                </Modal>
                {// 增强弹框
                    currentEnhanceModal ? <EnhanceModalComponent visible={currentEnhanceModal.visible} resource={currentEnhanceModal.resource} onOk={this.handleEnhanceOk}
                        onCancel={this.handleEnhanceCancel} updateVisible={this.handleSetEnhanceVisible} /> : null
                }
                {// token超出
                    tokenExceedModal.visible ? <TokenOverflowTip visible={tokenExceedModal.visible} overflow={tokenExceedModal.overflow} updateVisible={this.toggleTokenOverflowTip} /> : null
                }
                {// 查看图片全屏
                    viewImage.visible ? <ViewImage url={viewImage.url} onClose={() => this.handleToggleImageView()} /> : null
                }
            </div>
        )
    }
}

const mapStateToProps = (state: any) => ({
    userInfo: state.app.userInfo,
    actionList: state.app.actionList,
    globalRegion: state.app.globalRegion,
    globalLang: state.app.globalLang,
    themeInfo: state.app.themeInfo,
    activeCompanyId: state.app.activeCompanyId,
    isPrivate: state.app.isPrivate,
});

const mapDispatchToProps = (dispatch: any) => ({
    change_app_globalLang: (globalLang: string) => dispatch(change_app_globalLang(globalLang)),
    change_app_userInfo: (data: any) => dispatch(change_app_userInfo(data)),
    dispatch_api_ai_message: (data: any) => dispatch(dispatch_api_ai_message(data)),
    dispatch_api_ai_enhance_message: (data: any) => dispatch(dispatch_api_ai_enhance_message(data)),
    dispatch_api_privacy_check: (data: any) => dispatch(dispatch_api_privacy_check(data)),
    dispatch_api_get_robot_llmmModel: (data: any) => dispatch(dispatch_api_get_robot_llmmModel(data)),
    dispatch_api_delete_messages: (data: any) => dispatch(dispatch_api_delete_messages(data)),
    dispatch_api_stop_streaming: (data: any) => dispatch(dispatch_api_stop_streaming(data)),
})

const ConnectedCounter = connect(mapStateToProps, mapDispatchToProps, null, { forwardRef: true })(Robot);
export default ConnectedCounter;
