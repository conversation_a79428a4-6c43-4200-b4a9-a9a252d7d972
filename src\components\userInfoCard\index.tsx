
import { useEffect, useRef, useState } from "react";
import { Button, Flex, message, Popover, Spin, Input, Modal } from "antd"
import SvgIcon from '@components/svgicon';
import './index.scss'
import { useTranslation } from "react-i18next";
import UserAvatar from "@/components/userAvatar";
import { connect } from "react-redux";
import CopyToClipboard from 'copy-to-clipboard';

import { api_get_user_profile } from '@/api/chat'
import { useOpenIM } from '@/contexts/OpenIMContext';
import classNames from "classnames";

const { TextArea } = Input;

// 用户信息名片
const UserInfoCard = (props: any) => {
    const { service: ImService, isConnected: isConnectedIm } = useOpenIM();
    let { userInfo, currentUserInfo, activeCompanyId, isSearchAdd = false,
        children, placement = "left", onSendMessageToUser, trigger = 'click' } = props // isSearch true表示 通过搜索添加好友方式 打开用户名片
    const currentUserId = currentUserInfo.imUserId
    // console.log('userInfo',userInfo)
    const { t } = useTranslation();

    const [isLoadingUseInfo, setIsLoadingUseInfo] = useState<any>(false)
    const [isOpen, setIsOpen] = useState<any>(false)
    // const [userInfo, setSelectUserInfo] = useState<any>({}) // 选中用户数据
    const [selectUserInfo, setSelectUserInfo] = useState<any>({}) // 选中用户数据
    const [userStatus, setUserStatus] = useState<any>(null) // 在线状态 0 表示离线，1 表示在线
    const [isOverflow, setIsOverflow] = useState<any>(false)

    const [isFriend, setIsFriend] = useState<any>(false) // 用户是否是好友
    const [userStep, setUserStep] = useState('user') // verify 加好友验证
    const [inputValue, setInputValue] = useState('') // 验证码输入框
    const [isSending, setIsSending] = useState(false) // 是否发送申请中

    const infosRef = useRef(null)

    useEffect(() => {
        // console.log('change userInfo',userInfo)
        const status = userInfo.status !== undefined && userInfo.status !== null ? userInfo.status : null
        // console.log('status', status)
        setUserStatus(status)
    }, [userInfo])

    // 初始化
    useEffect(() => {
        // console.log('+++sssss',userInfo.status)
        if ((isOpen || isSearchAdd) && ImService) {
            // 检测是否是好友
            if (isOpen) {
                setUserStep('user')
                checkFriend()
            }
            if ((userInfo.status === null || userInfo.status === undefined)) {
                // console.log('+++获取用户在线状态')
                // 获取用户在线状态
                ImService.subscribeUsersStatus([userInfo.userID]).then((userStatus: any) => {
                    if (userStatus) {
                        const status = userStatus[0].status
                        // console.log('setCurrentConversationUserStatus status', status)
                        setUserStatus(status);
                    }
                })
            }
        }
    }, [isOpen, ImService, userInfo, isSearchAdd])

    useEffect(() => {
        // console.log('isLoadingUseInfo',isLoadingUseInfo)
        // console.log('setSelectUserInfo',selectUserInfo)
        if (!isLoadingUseInfo && Object.keys(selectUserInfo).length > 0) {
            // console.log('+++++++++变化了')
            checkScroll()
        }
    }, [selectUserInfo, isLoadingUseInfo])

    // 搜索添加好友 场景
    useEffect(() => {
        if (isSearchAdd) {
            // 检测是否是好友
            checkFriend()
            // console.log('userInfo', userInfo)
            setSelectUserInfo({ userName: '', imUserId: userInfo.userID })
            getUserInfo(userInfo) // 获取好友信息，目的为获取正确用户名
        }
    }, [isSearchAdd])

    // 检测是否是好友
    const checkFriend = async () => {
        // 是好友 显示‘发送按钮’，不是好友 显示 ‘发送按钮’ 和 ‘添加好友’
        const res0 = await ImService.checkFriend([userInfo.userID]).catch(() => {
        })
        let _isFriend = currentUserId === userInfo.userID
        if (res0) {
            const result = res0[0]?.result
            _isFriend = String(result) === '1' || currentUserId === userInfo.userID
        }
        setIsFriend(_isFriend)
        console.log('是朋友吗:', _isFriend)
    }

    // 查看用户名片
    const onOpenUserDetail = async (isOpen: any, user: any) => {
        // console.log('isOpen', isOpen)
        setIsOpen(isOpen)
        // console.log('usere', user)
        if (isOpen) {
            getUserInfo(user)
        }
    }

    // 获取用户信息
    const getUserInfo = async (user: any) => {
        let _selectUserInfo = { userName: user.userName, imUserId: user.userID }
        setIsLoadingUseInfo(true)
        setSelectUserInfo({ ..._selectUserInfo })
        // 获取ama用户信息
        const res: any = await api_get_user_profile({
            currentTenantId: activeCompanyId,
            "imUserList": [
                user.userID
            ]
        }).catch(() => {
            setIsLoadingUseInfo(false)
        })
        if (!res) return
        const info: any = res[user.userID] || {}
        // info.departments = [...info?.departments, ...info?.departments, ...info?.departments]
        // info.positions = [
        //     { id: 0, name: '徐vvvv是地方上班' },
        //     { id: 2, name: '44徐vvvv是地方上班' },
        //     { id: 3, name: '55徐vvvv是地方上班' },
        // ]
        _selectUserInfo = { ..._selectUserInfo, ...info }
        // console.log('_selectUserInfo', _selectUserInfo)
        setSelectUserInfo(_selectUserInfo)
        setIsLoadingUseInfo(false)
        if (isSearchAdd) return
        // 获取 IM 用户信息
        await ImService.getUsersInfo([user.userID]) // 刷新 IM user info
    }

    // 发送信息
    const onSendMessage = () => {
        setIsOpen(false)
        const userId = userInfo.userID
        onSendMessageToUser && onSendMessageToUser(userId)
        if (isSearchAdd) {
            onCancelAddFriend()
        }
    }

    // 添加好友
    const onAdd = () => {
        setInputValue('')
        setUserStep('verify')
    }

    const onCopyUserId = () => {
        const userID = isSearchAdd ? userInfo.userID : selectUserInfo.imUserId
        CopyToClipboard(userID)
        message.success(t("chat.copy-success"))
    }

    // 检测内容是否溢出
    function checkScroll() {
        try {
            const content: any = infosRef.current;
            if (!content) return
            const isOverflowing = content.scrollHeight > content.clientHeight;
            setIsOverflow(isOverflowing)
        } catch (error) {
            console.log(error)
        }
    }

    // 取消
    const onCancelAddFriend = () => {
        props.onClose()
    }

    // 返回
    const backToUser = () => {
        setUserStep('user')
    }

    const onChangeInputValue = (e: any) => {
        const inputValue = e.target.value;
        setInputValue(inputValue)
    }

    // 添加好友验证
    const renderVerify = () => {
        return (<Flex className="user-verify" vertical>
            <Flex className="uv-title" gap={12} align="center">
                <SvgIcon className="icon_back" onClick={backToUser} svgName="icon_back2" />
                <span>{t('chat.friend-verify')}</span>
            </Flex>
            <Flex className="uv-user-infos" gap={8}>
                <div className="avatar">
                    <UserAvatar name={selectUserInfo.userName} size={48} src={selectUserInfo.avatar} userId={selectUserInfo.imUserId} />
                </div>

                <div className="usr-info">
                    <div>{selectUserInfo.userName}</div>
                    <div>{selectUserInfo.imUserId}</div>
                </div>
            </Flex>
            <Flex className="uv-input" vertical gap={6}>
                <div className="til">{t('chat.verify-msg')}</div>
                <TextArea
                    maxLength={50} placeholder={t('common.please-input') as string}
                    onChange={(e: any) => onChangeInputValue(e)}
                    value={inputValue}
                    variant="borderless"
                    showCount={true}
                    autoSize={{ minRows: 2, maxRows: 7 }}
                />
            </Flex>
            <Flex className="btn" align="center" justify="center" >
                <Button disabled={isSending} onClick={onSend} className="tertiary" color="default" variant="filled">{t('chat.send')}</Button>
            </Flex>
        </Flex>)
    }

    // 发送验证信息
    const onSend = async () => {
        setIsSending(true)
        const res = await ImService.addFriend(selectUserInfo.imUserId, inputValue).catch(() => {
            setIsSending(false)
        })
        setIsSending(false)
        if (res) {
            message.success(t('chat.add-success'))
            setUserStep('user')
        }
    }

    const renderUserDeail = (item: any) => {
        return (
            <div className="user-card">
                {/* 姓名、头像、职位 */}
                <Flex className="name-avatar-position" gap={8}>
                    <div className="avatar">
                         <UserAvatar name={item.userName} size={48} src={selectUserInfo.avatar} userId={selectUserInfo.imUserId} />
                    </div>
                    {
                        isLoadingUseInfo ? null :
                            <Flex className="name-position" vertical gap={8}>
                                <div className="name">{item.userName}</div>
                                {!isLoadingUseInfo && (!isSearchAdd || isFriend) ?
                                    <div className="post">{selectUserInfo?.positions && selectUserInfo?.positions.length > 0 ? selectUserInfo.positions.map((i: any) => i.name).join(',') : item.postName}</div>
                                    : null
                                }
                            </Flex>
                    }

                </Flex>
                {/* 在线状态 */}
                {
                    (!isSearchAdd || isFriend) && (userStatus !== null && userStatus !== undefined) ?
                        <Flex className="user-status" align="center" gap={8}>
                            <SvgIcon svgName={userStatus === 1 ? 'icon_inline' : "icon_outline"} />
                            {userStatus === 1 ? t('chat.online') : t('chat.offline')}
                        </Flex> : null
                }
                {/* 用户信息 */}
                <Flex ref={infosRef} className="infos" vertical gap={12}>
                    {
                        isLoadingUseInfo ?
                            <div style={{ minHeight: '156px' }}>
                                <Spin className="full-spin" size="large" />
                            </div>
                            :

                            <>
                                {
                                    (!isSearchAdd || isFriend) && selectUserInfo?.isInTenant ?
                                        <>
                                            <Flex className="depts" vertical gap={12}>
                                                { //部门集合
                                                    selectUserInfo.departments && selectUserInfo.departments.length > 0 && selectUserInfo.departments.map((dept: any, index: number) => {
                                                        return (
                                                            <Flex className="dept-field" key={dept.id + '' + index} gap={14}>
                                                                <div className="field-name">{t('contacts.organize.department')}</div>
                                                                <div className="field-value">{dept.name}</div>
                                                            </Flex>
                                                        )
                                                    })
                                                }
                                            </Flex>
                                            <Flex gap={14}>
                                                <div className="field-name">{t('chat.company-organize')}</div>
                                                <div className="field-value">{selectUserInfo.tenantFullName || '-'}</div>
                                            </Flex>
                                            <Flex gap={14}>
                                                <div className="field-name">{t('contacts.organize.work-email')}</div>
                                                <div className="field-value">{selectUserInfo.corpEmail || '-'}</div>
                                            </Flex>
                                            <Flex gap={14}>
                                                <div className="field-name">{t('contacts.organize.work-phone')}</div>
                                                <div className="field-value">{selectUserInfo.corpPhone || '-'}</div>
                                            </Flex>
                                        </>
                                        : null
                                }
                                <Flex gap={14}>
                                    <div className="field-name">{t('contacts.organize.user-id')}</div>
                                    {
                                        selectUserInfo.imUserId ?
                                            <div onClick={onCopyUserId} className="field-value pointer">{selectUserInfo.imUserId}</div> :
                                            <div className="field-value">-</div>
                                    }
                                </Flex>
                            </>
                    }
                </Flex>
                {/* 操作按钮 */}
                {
                    selectUserInfo.imUserId ?
                        <Flex className={classNames(["btn", { 'has-scroll': isOverflow }])} align="center" justify="center" gap={12}>
                            {
                                !isFriend ? <Button onClick={onAdd} className="tertiary" color="default" variant="filled"
                                    icon={<SvgIcon svgName={"icon_new_user"} />}>{t('chat.add')}</Button> : null
                            }
                            {!isSearchAdd || isFriend ? <Button onClick={onSendMessage} className="tertiary" color="default" variant="filled" icon={<SvgIcon svgName="icon_send_message" />}>
                                {t('chat.messages')}</Button> : null}
                        </Flex>
                        : null
                }
            </div>
        )
    }

    // 用户信息详情
    const renderPopoverDeail = (item: any) => {
        return (
            userStep === 'verify' ?
                renderVerify()
                :
                renderUserDeail(item)
        )
    }

    return isSearchAdd ?
        (<Modal
            className={"chat-add-friend-user-modal"}
            title={''}
            open={true}
            onCancel={onCancelAddFriend}
            mask={false}
            maskClosable={true}
            centered={true}
            width={330}
            closable={false}
            footer={null}
        >
            {
                userStep === 'user' ?
                    renderUserDeail(selectUserInfo)
                    : null
            }
            {
                userStep === 'verify' ? renderVerify() : null
            }
        </Modal>)
        :
        (
            <Popover onOpenChange={(e) => onOpenUserDetail(e, userInfo)} trigger={trigger} open={isOpen}
                placement={placement} arrow={false} overlayClassName="chat-user-info-popover"
                content={renderPopoverDeail(userInfo)}
                getPopupContainer={(triggerNode: any) => { return props.popupContainer || triggerNode.parentNode }}>
                {children}
            </Popover>
        )
}

const mapStateToProps = (state: any) => ({
    companies: state.app.companies,
    activeCompanyId: state.app.activeCompanyId,
    activeCompanyName: state.app.activeCompanyName,
    currentUserInfo: state.app.userInfo,
})
const mapDispatchToProps = (dispatch: any) => ({
    // dispatch_api_: (data:any) => dispatch(dispatch_api_(data))
})
const ConnectedCounter = connect(mapStateToProps, mapDispatchToProps, null, { forwardRef: true })(UserInfoCard);
export default ConnectedCounter;