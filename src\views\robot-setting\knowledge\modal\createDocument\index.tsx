import React, { useState, useEffect, forwardRef, useRef, useImperativeHandle } from "react";
import { connect } from "react-redux";
import { Modal, message, Upload, Flex, List } from "antd";
import classNames from "classnames";
import i18next from "i18next";
// import { $trim } from '@/utils/common'

import { api_add_robot_document } from "@/api/robotManage2";

import './index.scss'
import Svgicon from "@/components/svgicon";
import fileDeleteIcon from '@/icons/png/icon_file_delete.png'

// 添加数据集
const CreateDocument = forwardRef((props: any, ref) => {
  /** 自定义暴露给父组件的实例值 **/
  // useImperativeHandle(ref, () => ({
  //   init() {
  //   }
  // }))

  const fileMaxCountLimit = 10
  const fileAccept = ".txt,.pdf,.docx"
  /* props */
  const isVisible = props.visible
  const resource = props.resource
  const robotId = resource?.robotId
  /* state */
  const [isSubmiting, setIsSubmiting] = useState(false) // 是否提交中
  const [fileList, setFileList] = useState([]) // 上传的文件
  const [uploadedFilelist, setUploadedFilelist] = useState<any>([]) // 上传的文件
  // const { Dragger } = Upload;

  /* method */
  // 确认
  const handleIsModalOk = async () => {
    // console.log('uploadedFilelist', uploadedFilelist)
    // const fileTypes = ["txt", "pdf", "docx"];
    const fileTypes = fileAccept.split(',').map(i=>i.slice(1))
    // console.log('fileTypes',fileTypes)

    for (let i = 0; i < uploadedFilelist.length; i++) {
      const f: any = uploadedFilelist[i];
      const fileAryMat = f.name.split(".");
      const fileLastMat = fileAryMat[fileAryMat.length - 1];
      console.log('fileLastMat.toLowerCase()',fileLastMat.toLowerCase())
      if (!fileTypes.includes(fileLastMat.toLowerCase())) {
        message.open({ type: "error", content: i18next.t("robot-manage.document.document-type-error") });
        return;
      }
    }

    if (uploadedFilelist.length == 0 || !robotId) return
    try {
      uploadedFilelist.forEach((f: any) => {
        const data = new FormData();
        data.append("document", f);
        data.append("seq", "0");
        data.append("robotId", robotId);
        data.append("documentName", f.name);
        setIsSubmiting(true)
        api_add_robot_document(data).then(() => {
          setIsSubmiting(false)
          message.open({ type: "success", content: i18next.t("robot-manage.document.upload-document-success") });
          handleIsModalCancel()
          props.onSucess()
        }).catch((err: any) => {
          console.log(err)
          setIsSubmiting(false)
        });
      });
    } catch (errorInfo) {
      console.log('Failed:', errorInfo);
    }
  }

   // 文件大小、格式校验
   const validateFileFormat = (file: any): boolean => {
    // 格式
    const fileType = file.name.substring(file.name.lastIndexOf("."));
    const _fileAccept = fileAccept.split(',')
    // console.log('_fileAccept',_fileAccept)

    const isFileFormatValid = _fileAccept.includes(fileType);
    let errorMessage = null;
    if (!isFileFormatValid) {
      errorMessage = `${i18next.t("robot-manage.document.document-type-error")}`;
    }
    if (errorMessage) {
      message.open({ type: "error", content: errorMessage});
    }
    return isFileFormatValid ;
};

  const handleCustomRequest = (e: any) => {
    const file = e.file;
    // const fileTypes = fileAccept.split(',').map(i => i.substring(1));
    // const fileAryMat = file.name.split(".");
    // const fileLastMat = fileAryMat[fileAryMat.length - 1];
    // if (!fileTypes.includes(fileLastMat.toLowerCase())) {
    //   message.open({ type: "error", content: i18next.t("robot-manage.document.document-type-error") });
    //   e.onError("error");
    //   return;
    // }
    // e.onSuccess({ "status": "success" }, e);
    const isFileFormatValid = validateFileFormat(file)
    console.log('handleCustomRequest isFileFormatValid', isFileFormatValid)
    if (!isFileFormatValid) {
      // message.open({ type: "error", content: i18next.t("robot-manage.document.document-type-error")});
      e.onError("error");
      return;
    }
    e.onSuccess({ "status": "success" }, e);
  }

  const beforeFileUpload = (file: any, uploadFileList: any) => {
    const totalFileList = [...fileList, ...uploadFileList]
    const isFileFormatValid = validateFileFormat(file)
    console.log('beforeFileUpload isFileFormatValid', isFileFormatValid)
    // 文件格式校验
    if (!isFileFormatValid){
      return false || Upload.LIST_IGNORE
    }
    // 文件个数校验
    if (totalFileList?.length > fileMaxCountLimit) {
      message.destroy('FileCountExceed')
      message.open({ type: "error", content: i18next.t(["robot-manage.document.document-count-exceed"]), key: 'FileCountExceed' }) // 提示
    }
    let result = true
    // 已上传的文件个数等于上限，限制当前上传
    if (fileList?.length === fileMaxCountLimit) {
      result = false
    }
    return result || Upload.LIST_IGNORE
  }

  const handleMultiFileOnChange = (e: any) => {
    const file = e.file;
    const fileList = e.fileList;

    setFileList(fileList)

    const currentFileList: object[] = [];
    fileList.forEach((f: any) => {
      currentFileList.push(f.originFileObj);
    });
    setUploadedFilelist(currentFileList)
  }

  const handleDropFile =(e: any)=>{
    // e.dataTransfer.files
    const files = e.dataTransfer.files
    console.log('Dropped files', e.dataTransfer.files);
  }

  // 取消
  const handleIsModalCancel = () => {
    props.onClose()
  }

  useEffect(() => {
  }, [])
  
  const itemRender = (originNode:any, file:any) => {
    return (
      // <Flex align={"center"} gap={4} className="ant-upload-list-item-con">
      //   {/* 自定义图标 */}
      //   <Svgicon svgName="attachment" />
      //   {originNode}
      // </Flex>
      <List.Item>
        {/* 自定义图标 */}
        {/* 自定义图标 */}
        <Svgicon className="icon_attachment" svgName="attachment" />
        {originNode}
      </List.Item>
    );
  };
  return (
    <>
      <Modal title={i18next.t("robot-manage.document.upload-document")}
        wrapClassName="create-robot-document-modal"
        width={500}
        open={isVisible}
        onOk={handleIsModalOk}
        onCancel={handleIsModalCancel}
        footer={undefined}
        okText={i18next.t("app.ok")}
        cancelText={i18next.t("app.cancel")}
        maskClosable={false}
        centered
        cancelButtonProps={{ variant: "filled", color: "default" }}
        okButtonProps={{disabled: isSubmiting || fileList.length==0}}
      >
        <div className={"mutil-upload-content"}>
          <Upload className={"mutil-upload-wrap"}
            multiple={true}
            accept={fileAccept}
            onChange={handleMultiFileOnChange}
            customRequest={handleCustomRequest}
            beforeUpload={beforeFileUpload}
            fileList={fileList}
            itemRender={itemRender} // 自定义渲染
            showUploadList={
              {
                // downloadIcon: <Svgicon svgName="search"/>,
                // previewIcon: <Svgicon svgName="search"/>,
                removeIcon: <img src={fileDeleteIcon} alt='' />
              }
            }
            maxCount={fileMaxCountLimit}
            // onDrop={handleDropFile}
            disabled={isSubmiting}
          >
            <div className="ant-upload-trigger">
              <p className="ant-upload-drag-icon">
                <Svgicon svgName="file_upload" />
              </p>
              <p className="ant-upload-text">{i18next.t("robot-manage.document.upload-document-hint")}</p>
              <p className="ant-upload-hint">
                {i18next.t("robot-manage.document.upload-document-type-hint")}
                <br />
                {i18next.t("robot-manage.document.document-count-exceed")}
              </p>
            </div>
          </Upload>
        </div>
      </Modal>
    </>
  );
});
const mapStateToProps = (state: any) => ({
  userInfo: state.app.userInfo,
  globalRegion: state.app.globalRegion,
  globalLang: state.app.globalLang,
});
const mapDispatchToProps = (dispatch: (e: any) => void) => ({
});
export default connect(mapStateToProps, mapDispatchToProps)(CreateDocument);