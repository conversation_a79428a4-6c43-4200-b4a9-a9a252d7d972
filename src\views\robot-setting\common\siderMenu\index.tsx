import React from "react";
import { connect } from "react-redux";
import i18next from "i18next";
import classNames from "classnames";
import Svgicon from "@/components/svgicon"
import { Layout, Flex } from "antd";
import { Menu } from "antd";
import type { MenuProps } from 'antd';
// import LayoutMenu from "@/components/layoutMenu";
import { getMenuItem } from "@/components/base";
import { getCSSVariablesValue } from '@/theme'

import './index.scss'
const { Sider, Content } = Layout;
interface ContextState {
    collapsed:boolean
}

// 读取菜单 collapsed缓存值 
const collapsedStorage = sessionStorage.getItem("AMAADMIN_SMCL2")
let collapsed_init = false
if (collapsedStorage){
    const collapsed = collapsedStorage== '1'
    collapsed_init = collapsed
}

// console.log('collapsed_init',collapsed_init)

class SiderMenu extends React.Component<any, ContextState> {

    constructor(props: any) {
        super(props);
        this.state = {
            collapsed: collapsed_init
        }
    };

    getMenuRouter(){
        const robotId = this.props.robotId
        return {
            basicinfo: "/robotSetting?pid=basicinfo&robotid=" + robotId, // 设置和调试
            knowledge: "/robotSetting?pid=knowledge&robotid=" + robotId, // 机器人知识
            embedding: "/robotSetting?pid=embedding&robotid=" + robotId, // 向量搜索
            // apiManage: "/robotSetting?pid=apiManage&robotid=" + robotId, // API管理
            iframe: "/robotSetting?pid=iframe&robotid=" + robotId, // iframe
        }
    }

    // 菜单收缩展开
    handleChangeCollapsed = () => {
        const activeCollapsed = !this.state.collapsed
        sessionStorage.setItem("AMAADMIN_SMCL2", activeCollapsed ? '1':'0')
        this.setState({
          collapsed: activeCollapsed
        })
    }

    // 菜单点击
    handleClickMenu = (item: any)=>{
        this.props.onChange(item.key)
        item.domEvent.preventDefault(); // 阻止默认行为
        item.domEvent.stopPropagation(); // 阻止事件冒泡
      return;
    }

    renderMenus(){
        const { userPower, permissionMap, themeInfo,activePath } = this.props;
        // console.log('activePath', activePath)
        const menuRouter:any = this.getMenuRouter()
        const items: MenuProps['items'] = [
            // 设置和调试
            getMenuItem(i18next.t("robot-manage.setting-debug"), menuRouter.basicinfo, <Svgicon svgName="setting_debug"/>),
            // 机器人知识
            // permissionMap.robotManageKnowledgeManageRobotBtn() ?
                getMenuItem(i18next.t("robot-manage.robot-knowlege"), menuRouter.knowledge, <Svgicon svgName="knowledge"/>),
                // :null,
            // 向量搜索
            // permissionMap.robotManageVolSearchRobotBtn ?
                getMenuItem(i18next.t("robot-manage.embedding-search"), menuRouter.embedding, <Svgicon svgName="embedding"/>),
                // :null,
            // API管理
            // getMenuItem(i18next.t("robot-manage.api-manage-label"), menuRouter.apiManage, <Svgicon svgName="api"/>),
            // iFrame
            getMenuItem(i18next.t("robot-iframe.title"), menuRouter.iframe, <Svgicon svgName="icon_bot_iframe"/>)
        ];
        const selectedKeys:any = [menuRouter[activePath]]
        // const selectedKeys:any = [activePath]
        // console.log('items',items)
        // console.log('selectedKeys',selectedKeys)
        return (
          <Menu
            selectedKeys={selectedKeys}
            defaultOpenKeys={[menuRouter.basicinfo]}
            mode="inline"
            theme={themeInfo.theme || "light"}
            onClick={this.handleClickMenu}
            style={{background:"transparent",borderRight: 0}}
            inlineIndent={8}
            items={items}
          />
        )
    }

    render() {
        const { collapsed } = this.state;
        const { themeInfo } = this.props;

        return (
            <div>
                <Sider className="page-detail-sider" width={getCSSVariablesValue('--sider-menu-width')} collapsedWidth="76" trigger={null} collapsible collapsed={collapsed} theme={themeInfo.theme}>
                    <Flex vertical justify="space-between">
                        <div className={"layout-menu"}>
                            {this.renderMenus()}
                            <div className={classNames(["toggle-collapse"],{collapsed: collapsed})}>
                                <Svgicon svgName="icon_collapse" onClick={this.handleChangeCollapsed}/>
                            </div>
                        </div>
                    </Flex>
                </Sider>
                {/* <UserMenu {...this.props} collapsed={collapsed} /> */}
            </div>
        )
    }
}

const mapStateToProps = (state: any) => ({
    userInfo: state.app.userInfo,
    permissionMap: state.app.permissionMap,
    themeInfo: state.app.themeInfo
});

const mapDispatchToProps = (dispatch: (e: any) => void) => ({
    //   dispatch_api_query_robot_list: (data = api_query_robot_list_params) => dispatch(dispatch_api_query_robot_list(data)),
});

export default connect(mapStateToProps, mapDispatchToProps)(SiderMenu);
