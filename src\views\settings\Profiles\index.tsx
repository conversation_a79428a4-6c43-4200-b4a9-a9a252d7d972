import { connect } from 'react-redux';
import { Button, Input, Flex, Spin, Upload, UploadProps, message, Popover, Form } from 'antd';
import { useState, useEffect } from "react";
import { useTranslation } from 'react-i18next';
import SvgIcon from '@components/svgicon'; // 根据实际路径引入
import Avatar from '@/components/avatar';
import { api_get_my_direct_departments } from '@/api/contacts'
import { api_get_my_profile, api_update_my_profile, api_update_avatar, api_reset_avatar } from '@/api/setting'
import { $trim, $isNull } from '@/utils/common'
import ImgCrop from 'antd-img-crop'; // 图片裁剪
import { change_app_user_profile, change_app_personal_user_profile } from "@/store/action";
import FormUtils from '@/views/common/formUtils'
import UserAvatar from '@/components/userAvatar'

import navLogoDefault from '@/icons/png/nav_logo_default.png'
import './index.scss';
import { UsernameRules } from '@/views/common/formRule';


// 默认表单字段
const DefaultFormItems = [
  { name: 'login.username', propName: "userName", value: '', status: '', visible: true, maxLength: 20, rules: UsernameRules }, // userNameInput
  { name: 'setting.position', propName: "positions", value: '', disabled: true, visible: false },
  { name: 'contacts.organize.department', propName: "departments", value: '', disabled: true, visible: false }, // 个人空间不显示
  { name: 'contacts.organize.work-email', propName: "corpEmail", value: '', disabled: true, visible: false }, // 个人空间不显示
  { name: 'contacts.organize.work-phone', propName: "corpPhone", value: '', disabled: true, visible: false }, // 个人空间不显示
  { name: 'contacts.organize.user-id', propName: "imUserId", value: '', disabled: true, visible: false } // userID,SASS化才显示
]

const imageTypeAccept = ["png", "jpeg", "jpg"]; // 头像文件类型限制
const imageSizeLimit = 1; // 头像大小限制 1M

// 个人资料，用户名 + 头像可编辑
const Profiles = (props: any) => {
  const { t } = useTranslation()
  const { userInfo, activeCompanyId, themeInfo, isPrivate, defaultThemeInfo, allCompanies = [] } = props
  const { theme, logoUserCenterLight, logoUserCenterDark } = themeInfo
  // const currentUserId = userInfo.imUserId
  const defaultTeam = [{ id: '0', companyName: t('company.personal-space'), logo: '', isPersonal: true }] // 默认有个人空间
  const [teamData, setTeamData] = useState([...defaultTeam]) // 所有公司数据（下拉框）
  const [openCompanyMenu, setOpenCompanyMenu] = useState(false)
  const [activeTeamSetting, setActiveTeamSetting] = useState(isPrivate ? '0' : activeCompanyId) //当前设置选中的公司

  const [formItems, setFormItems] = useState<any[]>([...DefaultFormItems])
  const [isLoadingUseInfo, setIsLoadingUseInfo] = useState<any>(false)
  const [isRestoringAvatar, setIsRestoringAvatar] = useState<any>(false) // 是否重置头像中

  const [isSubmit, setIsSubmit] = useState(false) // 提交中
  const [hasChange, setHasChange] = useState(false) // 有信息变更

  const [userNameInput, setUserNameInput] = useState('') // 用户名输入框
  const [userNameChange, setUserNameChange] = useState(false) // 用户名是否有更新

  const [userAvatarUploadFile, setUserAvatarUploadFile] = useState<any>(null) // 头像上传文件-暂存
  const [userAvatarUrl, setAvatarUrl] = useState<any>(null) // 头像Url
  const [userAvataChange, setUserAvataChange] = useState(false) // 头像是否有更新
  const [isUploadingAvatar, setIsUploadingAvatar] = useState(false) // 是否上传中

  const [currentUserInfo, setCurrentUserInfo] = useState<any>({}) // 当前企业下的 用户信息

  const [form] = Form.useForm(); // 用户信息表单，可编辑
  const [formValues, setFormValues] = useState<any>({}); // 用户信息表单值，可编辑

  // 监听 公司ID变化
  useEffect(() => {
    if (!isPrivate) {
      setActiveTeamSetting(activeCompanyId)
    }
  }, [isPrivate, activeCompanyId])

  useEffect(() => {
    // getMyDirectDepartments()
    // console.log('----activeTeamSetting change', activeTeamSetting)
    getProfileInfo()
  }, [activeTeamSetting])

  // useEffect(() => {
  //   // console.log('isPrivate', isPrivate)
  //   setFormItems(formItems.map((i: any) => {
  //     if (i.propName === 'imUserId') {
  //       i.visible = !isPrivate
  //     }
  //     return i
  //   }))
  // }, [isPrivate])

  useEffect(() => {
    // console.log('allCompanies change', allCompanies)
    if (allCompanies) setTeamData([...defaultTeam, ...allCompanies])
  }, [allCompanies])


  // 监听用户名 和 头像是否有更新
  useEffect(() => {
    // console.log('userNameInput change', userNameInput, currentUserInfo)
    const userNameChange = currentUserInfo && currentUserInfo.userName !== $trim(userNameInput) && !$isNull(userNameInput)
    setUserNameChange(userNameChange)
    const userAvataChange = !$isNull(userAvatarUploadFile) // && userAvatarUploadFile !== currentUserInfo.avatar
    setUserAvataChange(userAvataChange)
    // console.log('userAvataChange userNameChange', userNameChange, userAvataChange)
    setHasChange(userNameChange || userAvataChange)
  }, [userNameInput, userAvatarUploadFile, currentUserInfo])

  const toggleLoading = (isRestoreAvatar: boolean, isOpen = false) => {
    if (isRestoreAvatar) {
      setIsRestoringAvatar(isOpen)
    } else {
      setIsLoadingUseInfo(isOpen)
    }
  }

  // 获取个人资料
  const getProfileInfo = async (isRestoreAvatar = false, needRefresh = false) => {
    try {
      const isPersonal = activeTeamSetting == 0
      toggleLoading(isRestoreAvatar, true)
      const info: any = await api_get_my_profile(activeTeamSetting).catch(() => {
        toggleLoading(isRestoreAvatar, false)
      })
      toggleLoading(isRestoreAvatar, false)
      // console.log('info', info)
      // info.positions = [
      //   { id: 0, name: '徐vvvv是地方上班' },
      //   { id: 2, name: '44徐vvvv是地方上班' },
      //   { id: 3, name: '55徐vvvv是地方上班' },
      // ]
      if (!info) return
      const _formItems = formItems.map((i: any) => {
        let value = info[i.propName]
        if (typeof value === 'string') {
          value = $trim(value)
        }

        if (value && value.length > 0) {
          i.visible = !isPersonal || ['userName', 'imUserId'].includes(i.propName)
          // 部分职位,多个,逗号拼接
          if (["positions", "departments"].includes(i.propName)) {
            value = value.map((i: any) => i.name).join(',')
            // console.log('value', value)
          }
          i.value = i.visible ? value : ''
        } else {
          i.visible = false
          i.value = ''
        }
        if (i.propName === 'userName') {
          setUserNameInput(value)
        }
        if (i.propName === 'imUserId') {
          i.visible = !isPrivate //私有化部署不显示
        }
        form.setFieldValue(i.propName, i.value)
        return i
      })
      setFormItems(_formItems)
      console.log('_formItems', _formItems)
      // console.log('info', info)
      console.log('activeTeamSetting，activeCompanyId', activeTeamSetting, activeCompanyId)
      if (needRefresh) {
        // console.log('通知了')
        // 更新当前选中的企业 的用户头像
        if (activeTeamSetting == activeCompanyId) props.change_app_user_profile({ userName: info.userName, avatar: info.avatar })
        if (isPersonal) {
          // console.log('0000更新个人空间 的用户头像')
          props.change_app_personal_user_profile({ userName: info.userName, avatar: info.avatar })
        }
      }
      setAvatarUrl(info.avatar)
      setCurrentUserInfo(info)
    } catch (error) {
      console.log('error', error)
    }
  }

  // 提交保存
  const onSubmit = async () => {
    setIsSubmit(true)
    let needRefresh = false
    // 用户名有更新，保存用户名
    if (userNameChange) {
      const data = {
        userName: userNameInput
      }
      const res1: any = await api_update_my_profile(activeTeamSetting, data).catch(() => {
      })
      if (res1 && res1.success) {
        needRefresh = true
      }
    }
    // 用户头像有更新，保存用户头像
    if (userAvataChange) {
      const res2: any = await updateAvatarRquest().catch(() => {
        setIsSubmit(false)
      })
      if (res2) {
        needRefresh = true
        setUserAvatarUploadFile(null)
      }
    }
    setIsSubmit(false)
    if (needRefresh) {
      getProfileInfo(!userNameChange && userAvataChange, true)
    }

  };

  // 取消保存
  const onCancel = () => {
    // 信息回退到编辑前状态
    // console.log('currentUserInfo', currentUserInfo)
    const _formItems = formItems.map((i: any) => {
      let value = currentUserInfo[i.propName]
      if (i.propName === 'userName') { // 用户名才可编辑
        i.value = value
        form.setFieldValue(i.propName, value)
      }
      return i
    })
    setFormItems(_formItems)
    setUserNameInput(currentUserInfo.userName)
    setAvatarUrl(currentUserInfo.avatar) // 回退用户头像
    setUserAvatarUploadFile(null) // 清空暂存上传的用户头像file
    setHasChange(false)
  }

  // 头像恢复默认值
  const onResoreDefault = async () => {
    setIsUploadingAvatar(true)
    const data: any = {}
    const res: any = await api_reset_avatar(activeTeamSetting).catch(() => {
      setIsUploadingAvatar(false)
    })
    setIsUploadingAvatar(false)
    // console.log('res', res)
    if (res && res.success) {
      getProfileInfo(true, true)
      setUserAvatarUploadFile(null)
      // setAvatarUrl(null)
    }
  }

  // 修改表单项
  const onChangeFormItem = (e: any, item: any, index: number) => {
    const nv = e.target.value
    setUserNameInput(nv)
    // console.log(nv)
    // console.log(item)
    setFormItems(formItems.map(((i: any, fi: number) => {
      if (index === fi) {
        i.value = nv
      }
      i.status = $isNull(nv) ? 'error' : null
      return i
    })))
  };

  // 头像上传属性
  const UploadAvatarProps: UploadProps = {
    name: 'SettingAvatar',
    action: '#',
    accept: imageTypeAccept.map((i) => "." + i).join(","),
    showUploadList: false,
    disabled: isUploadingAvatar || isRestoringAvatar,
    // 上传前检验
    beforeUpload(file: any, uploadFileList: any) {
      // console.log('file', file)
      // TODO 校验上传文件格式
      const isValid = validateFileSizeFormat(file)
      // console.log('beforeFileUpload isValid', isValid)
      return isValid || Upload.LIST_IGNORE
    },
    customRequest: async (options: any) => {
      // console.log('customRequest uploadingFileTotal', uploadingFileTotal)
      const { onSuccess, onError, file, onProgress } = options;
      let fileType = file.name.substring(file.name.lastIndexOf(".") + 1);
      fileType = fileType.toLowerCase()
      // updateAvatarRquest(file)
      setUserAvatarUploadFile(file)
      previewAvatar(file)
    }
  }

  const ImgCropProps = {
    rotationSlider: false,
    modalTitle: t('setting.update-image'),
    modalClassName: "ant-upload-img-crop"
  }

  // 头像上传请求
  const updateAvatarRquest = async () => {
    const file = userAvatarUploadFile
    const formData = new FormData();
    formData.append('avatarFile', file)
    if (activeTeamSetting && activeTeamSetting != 0) {
      formData.append('tenantId', activeTeamSetting)
    }
    setIsUploadingAvatar(true)
    const res: any = await api_update_avatar(formData).catch(() => {
      setIsUploadingAvatar(false)
    })
    setIsUploadingAvatar(false)
    // console.log('res', res)
    if (!res) return false
    // 上传成功
    if (res) {
      setAvatarUrl(res) // 保存头像地址
    }
    return true
  }

  // 文件格式大小校验
  const validateFileSizeFormat = (file: any, fileList?: any): boolean => {
    // 格式
    const fileType = file.name.substring(file.name.lastIndexOf(".") + 1);
    const isfileFormatValid = imageTypeAccept.includes(fileType.toLocaleLowerCase());
    let errorMessage = null;// 大小
    const isfileSizeValid = file.size / 1024 / 1024 < imageSizeLimit;
    if (!isfileFormatValid) {
      errorMessage = `${t("upload.file-format-error")}`;
    } else if (!isfileSizeValid) {
      errorMessage = `${t("app.filebox-overflow")}`;
    }
    if (errorMessage) message.error(errorMessage)
    return isfileFormatValid && isfileSizeValid;
  };

  // 图像预览
  const previewAvatar = async (file: any) => {
    // console.log('file', file)
    let src = await getBase64(file)
    setAvatarUrl(src) // 保存头像地址
    return src
  }

  // 获取图片base64格式
  const getBase64 = (file: any): Promise<string> =>
    new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = (error) => reject(error);
    });

  // 公司切换
  const onSelectCompany = (team: any) => {
    setActiveTeamSetting(team.id)
    setOpenCompanyMenu(false)
  }

  // 公司下拉框显隐
  const onOpenChangeCompanyMenu = (open: boolean) => {
    setOpenCompanyMenu(open)
  }

  // 个人空间头像
  const getPersonalDefaultLogo = (size = 32) => {
    return <UserAvatar size={size} isPersonal={true} borderRadius="10px" isLoginUser={true} />
  }

  // 获取公司图标
  const getNavDefaultLogo = () => {
    try {
      const navLogo = theme === 'dark' ? defaultThemeInfo.logoUserCenterDark : defaultThemeInfo.logoUserCenterLight;
      return navLogo
    } catch (error) {
      return navLogoDefault
    }
  }

  // logo
  const getNavLogo = () => {
    try {
      const navLogo = theme === 'dark' ? logoUserCenterDark : logoUserCenterLight;
      return navLogo
    } catch (error) {
      return navLogoDefault
    }
  }

  // 企业logo
  const getCompanyLogo = (item: any) => {
    const prop = theme === 'dark' ? 'darkLogo' : 'lightLogo'
    return item[prop] || getNavLogo()
  }

  // 获取选中的公司名称
  const getActiveCompanyName = () => {
    let name = ''
    const target = teamData.filter((i: any) => String(i.id) === String(activeTeamSetting))
    return target.length > 0 ? target[0]?.companyName : name
  }

  // 公司下拉菜单
  const renderCompanyMenu = () => {
    return (
      <Flex vertical className="user-center-popover-content">
        <div className="menu-title">{t('setting.profile-in-text')}</div>
        <Flex className="menu-content" vertical gap={8}>
          {
            teamData.map((item: any, index: number) => {
              return (
                <Flex onClick={() => onSelectCompany(item)} className="menu-content-item" key={index} gap={8} align="center">
                  {
                    item.isPersonal ?
                      getPersonalDefaultLogo()
                      :
                      <img className="team-logo" src={getCompanyLogo(item)} alt="" ></img>
                  }
                  <div className="team-name">{item.companyName}</div>
                  {
                    activeTeamSetting == item.id ? <SvgIcon className="team-selected" svgName="icon_check" /> : null
                  }
                </Flex>
              )
            })
          }

        </Flex>
      </Flex>
    )
  }

  const previewFormItemVisble = (propName: string) => {
    const target = formItems.filter(i => i.propName === propName)
    return target.length > 0 && target[0].visible ? true : false
  }

  // 表单项 输入改变事件
  const onFiledValueChange = (changedFields: any, allValues: any) => {
    try {
      // console.log('changedFields', changedFields)
      // console.log('allValues',allValues)
      setFormValues({ ...formValues, ...allValues });
      Object.keys(changedFields).forEach((fieldName: any) => {
        const fieldError = form?.getFieldError(fieldName)
        if (fieldError.length > 0) FormUtils.clearValidate(form, fieldName)
      })
    } catch (error) {

    }
  }

  const gerUserName = () => {
    return !$isNull(form.getFieldValue('userName')) ? form.getFieldValue('userName') :
      currentUserInfo.userName
  }

  return (
    <>
      <Flex vertical className="profiles-wrapper wrapper">
        {!isPrivate ?
          <div className="profiles-title-content">
            <Popover open={openCompanyMenu} onOpenChange={onOpenChangeCompanyMenu} content={() => renderCompanyMenu()}
              destroyTooltipOnHide={true}
              trigger="click"
              placement="topLeft" overlayClassName="user-center-popover setting-profile" arrow={false}>
              <Flex className="pointer" align="center" onClick={(e) => e.preventDefault()}>
                <Flex align="center" gap={12} className="profiles-title">
                  <div className='title'>{t('setting.profile-in', { company: getActiveCompanyName() })}</div>
                  <SvgIcon svgName="icon_switch" />
                </Flex>
              </Flex>
            </Popover>

            <div className="profiles-subtitle">
              {t('setting.profilesubTitle')}
            </div>
          </div>
          :
          <div className="profiles-title-content">
            <div className='title'>{t('setting.profiles')}</div>
          </div>
        }
        <Flex gap={20} className="profiles-content">
          <div className="profiles-data-content">
            <div className="avatar-content">
              <div className="config-title">{t('setting.avatar')}</div>
              <Flex align="center" gap={12} className="avatar-btngroup">
                <ImgCrop {...ImgCropProps}>
                  <Upload className="chat-image-upload" {...UploadAvatarProps}>
                    <Button disabled={isRestoringAvatar} color="default" variant="filled">
                      {t('setting.changeAvatar')}
                    </Button>
                  </Upload>
                </ImgCrop>
                <Button disabled={isRestoringAvatar} onClick={() => onResoreDefault()} className="quaternary" color="default" variant="filled">
                  {t('setting.restore')}
                </Button>
              </Flex>
            </div>
            {
              isLoadingUseInfo ?
                <Flex align="center" justify="center" style={{ minHeight: '200px' }}>
                  <Spin className="full-spin" size="large" />
                </Flex> :
                <div className='userinfo-content'>
                  <Form
                    form={form}
                    className="setting-profile-form common-form mediumn"
                    name="basic"
                    autoComplete="off"
                    layout="vertical"
                    onValuesChange={onFiledValueChange}
                    validateTrigger="onBlur"
                    requiredMark={false}
                    initialValues={{
                      userName: ''
                    }}
                  >
                    {
                      formItems.map((item, index) => {
                        return (
                          item.visible ?
                            <div key={index + '' + item.name} className='userinfo-item'>
                              <Form.Item shouldUpdate name={item.propName} label={t(item.name)} help={null} rules={item.rules ? item.rules(t) : null}>
                                {/* <div className='config-title'>{t(item.name)}</div> */}
                                {/* <div> */}
                                <Input value={item.value} onChange={(e) => onChangeFormItem(e, item, index)} status={item.status}
                                  {...(item.maxLength ? { maxLength: item.maxLength } : {})}
                                  disabled={item.disabled} placeholder={t('common.please-input') as string} variant="filled" />
                                {/* </div> */}
                              </Form.Item>
                            </div>
                            : null
                        )
                      })
                    }
                  </Form>
                </div>
            }
          </div>
          <div className="profiles-data-preview">
            <div className='config-title'>{t('setting.preview')}</div>
            <div className="user-card">
              {/* 姓名、头像、职位 */}
              <Flex className="name-avatar-position" gap={8}>
                <ImgCrop {...ImgCropProps}>
                  <Upload className="chat-image-upload" {...UploadAvatarProps}>
                    <div className="avatar">
                      <div>
                        <Avatar name={gerUserName()} size={48} src={userAvatarUrl} />
                      </div>

                      <Flex className='avatar-update' align="center" justify="center">
                        <SvgIcon svgName="icon_update_avatar" />
                      </Flex>
                    </div>
                  </Upload>
                </ImgCrop>
                {
                  isLoadingUseInfo ? null :
                    <Flex className="name-position" vertical gap={8}>
                      <div className="name">{gerUserName()}</div>
                      {
                        previewFormItemVisble('positions') ?
                          <div className="post">{
                            currentUserInfo.positions.map((i: any) => i.name).join(',')}
                          </div>
                          : null
                      }
                    </Flex>
                }

              </Flex>
              {/* 用户信息 */}
              <Flex className="infos" vertical gap={12}>
                {
                  isLoadingUseInfo ?
                    <div style={{ minHeight: '156px' }}>
                      <Spin className="full-spin" size="large" />
                    </div>
                    :
                    <>
                      {
                        previewFormItemVisble('departments') ?
                          <Flex className="depts" vertical gap={12}>
                            { //部门集合
                              currentUserInfo.departments && currentUserInfo.departments.length > 0 && currentUserInfo.departments.map((dept: any, index: number) => {
                                return (
                                  <Flex className="dept-field" key={dept.id + '' + index} gap={14}>
                                    <div className="field-name">{t('contacts.organize.department')}</div>
                                    <div className="field-value">{dept.name}</div>
                                  </Flex>
                                )
                              })
                            }
                          </Flex> : null
                      }

                      {/* 公司名称 */}
                      {
                        activeTeamSetting != 0 ?

                          <Flex gap={14}>
                            <div className="field-name">{t('chat.company-organize')}</div>
                            <div className="field-value">{currentUserInfo.tenantFullName || '-'}</div>
                          </Flex>
                          : null}
                      {/* 企业邮箱 */}
                      {
                        previewFormItemVisble('corpEmail') ?
                          <Flex gap={14}>
                            <div className="field-name">{t('contacts.organize.work-email')}</div>
                            <div className="field-value">{currentUserInfo.corpEmail || '-'}</div>
                          </Flex> : null
                      }
                      {/* 工作电话 */}
                      {
                        previewFormItemVisble('corpPhone') ?
                          <Flex gap={14}>
                            <div className="field-name">{t('contacts.organize.work-phone')}</div>
                            <div className="field-value">{currentUserInfo.corpPhone || '-'}</div>
                          </Flex> : null
                      }
                      {
                        previewFormItemVisble('imUserId') ?
                          <Flex gap={14}>
                            <div className="field-name">{t('contacts.organize.user-id')}</div>
                            {
                              currentUserInfo.imUserId ?
                                <div className="field-value">{currentUserInfo.imUserId}</div> :
                                <div className="field-value">-</div>
                            }
                          </Flex>
                          : null}
                    </>
                }
              </Flex>
            </div>
          </div>
        </Flex >
        {
          hasChange ?
            <Flex justify="flex-end" className='bottom-operate' gap={12} align="center">
              <div className='no-save-tip'>{t('setting.change-no-save')}</div >
              <Button color="default" variant="filled" onClick={onCancel}>{t('app.cancel')}</Button>
              <Button loading={isSubmit} type="primary" onClick={onSubmit} disabled={isSubmit}> {t('setting.save-change')}</Button>
            </Flex >
            : null
        }
      </Flex >
    </>
  );
};

// // 上传插件
// const UploadImageComponent = (props:any)=>{
//   const {children}=props

//   return 
//     <ImgCrop rotationSlider>
//       <Upload className="chat-image-upload" {...UploadAvatarProps}>
//       {children}
//     </Upload>
//     </ImgCrop>
// }


const mapStateToProps = (state: any) => ({
  userInfo: state.app.userInfo,
  allCompanies: state.app.companies,
  activeCompanyId: state.app.activeCompanyId,
  isPrivate: state.app.isPrivate,
  themeInfo: state.app.themeInfo,
  defaultThemeInfo: state.app.defaultThemeInfo,
});
const mapDispatchToProps = (dispatch: any) => ({
  change_app_user_profile: (data: any) => dispatch(change_app_user_profile(data)),
  change_app_personal_user_profile: (data: any) => dispatch(change_app_personal_user_profile(data)),
});
export default connect(mapStateToProps, mapDispatchToProps)(Profiles);
