/* 密码校验规则：
*1.支持输入英文大小写、数字、特殊字符，其他输入无效，最长支持18个字符。
*2.6-18个字符 至少包含 数字 和 英文 （Password must be 6-18 characters with letters and numbers.）
*/
import areaCodeDatas from '@/components/form/mobileInput/areaCode.json'

export const PasswordRules =(t?:any)=>{
    // const { t } = useTranslation();
    return ([
        {
            whitespace: true,
            // validateTrigger: 'onBlur',
            validator: async (_: any, value: any) => {
                if (value ===null || value === undefined || value?.length ===0) return Promise.resolve() // 有值才校验
                // 1.校验 6-18个字符
                // 2.校验是否同时包含数字和英文
                const lengthInvalid= value.length < 6 || value.length > 18
                const hasNumber = /\d/.test(value);
                const hasLetter = /[a-zA-Z]/.test(value);
                if (lengthInvalid || !hasNumber || !hasLetter) {
                    // console.log('不过---')
                    return Promise.reject(new Error(t('login-validate.password-format-error') as string));
                }
            }
        }
    ])
} 

/* 验证码校验规则：
*1.仅支持输入数字，最长6个数字
*/
export const VerifyCodeRules = (t?:any)=>{
 return[
    { required: true, whitespace: true,message: '' },
    {
        whitespace: true,
        validator: async (_: any, value: any) => {
            if (value!=null && value!==undefined&&value.length ===0) return Promise.resolve() // 有值才校验
            // 校验格式:仅支持输入,最长6个数字
            const formaReg = /^\d{1,6}$/g
            // const lengthInvalid= value.length > 6
            if (!formaReg.test(value)) {
                return Promise.reject(new Error(t('login-validate.invalid-verifiy-code-error')));;
            }
        }
    }
]
}

/* 邮箱校验规则 */
export const EmailRules =(t?:any)=>{
    return ([
        {
            whitespace: true,
            // validateTrigger: 'onBlur',
            validator: async (_: any, value: any) => {
                if (value===null || value===undefined ||value?.length ===0) return Promise.resolve() // 有值才校验
                const regex = /^(?=.{1,254}$)(?=[^\s@]{1,64}@[^\s@]{1,255}$)([a-zA-Z0-9!#$%&'*+\-/=?^_`{|}~]+(\.[a-zA-Z0-9!#$%&'*+\-/=?^_`{|}~]+)*)@((([a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,})|(\[([0-9]{1,3}\.){3}[0-9]{1,3}\]))$/;
                if (! regex.test(value)) {
                    return Promise.reject(new Error(t('login-validate.email-format-error') as string));
                }
                return Promise.resolve();
            }
        }
    ])
} 

function generateRegex(bitLimits:any[]) {
    if (bitLimits.length === 1) {
        return new RegExp(`^\\d{${bitLimits[0]}}$`);
    } else {
        const patterns = bitLimits.map(bit => `^\\d{${bit}}$`).join('|');
        return new RegExp(patterns);
    }
}

/* 手机校验规则 */
export const MobileRules =(t?:any)=>{
    return ([
        { //不能为空
            whitespace: true,
            validateOnly: true,
            message: '',
            validator:async (_: any, value: {number:any, code:any}) => {
                // console.log('number',value.number)
                // console.log('code',value.code)
                const number = value.number
                const code = value.code
                if (code.value==null || code.value==undefined||number===null || number===undefined ||number?.length ===0) {
                    // console.log('--校验不通过')
                    return Promise.reject(null)
                }else{
                    // console.log('--校验通过')
                }
                
            }
        },
        { //位数限制
            // whitespace: true,
            validator: async (_: any, value: {number:any, code:any}) => {
                const number = value.number
                const code = value.code
                const code_value = code.value
                // console.log('code',code)
                // console.log('number',number)
                if (code_value==null ||code_value==undefined ||number===null || number===undefined ||number?.length ===0){
                    // console.log('有值才校验 ')
                    return // 有值才校验 
                }
                try {
                    const rule:any = areaCodeDatas.filter(i =>i.value == code_value)
                    // console.log('rule',rule)
                    if (rule.length>0){
                        const wordCountLimit = rule[0]['limit']
                        const bit = wordCountLimit
                        // console.log('wordCountLimit',wordCountLimit)
                        const reg =generateRegex(bit)
                        // console.log('reg',reg)
                        if (!reg.test(number)){
                            // console.log('校验不通过')
                            return Promise.reject(new Error(t('login-validate.mobile-format-error') as string))
                        } else{
                            // console.log('校验通过')
                            return Promise.resolve()
                        }
                    }
                } catch (error) {
                    console.log('error',error)
                }
                
            }
        }
    ])
} 

// 计算字符串长度，1个中文=2个字符 
export const CalculateLength = (str:any) => {
    let length = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      length += char >= 0 && char <= 128 ? 1 : 2;
    }
    return length;
};
/* 用户名校验规则
*1.允许输入中文、英文、特殊字符、空格，长度限制在4~20个字符，1个中文=2个字符。
*2.选中后失焦时需要校验格式正确性，如低于4个字符，提示用户：Invalid user name
*/
export const UsernameRules =(t?:any)=>{
    return ([
        { required: true,whitespace: true, message: '' },
        {
            whitespace: true,
            validator: async (_:any, value:any) => {
                // console.log('value',value)
                if (value!=null && value!==undefined&&value.length ===0) return Promise.resolve() // 有值才校验
                const length = CalculateLength(value);
                // console.log('length',length)
                // const reg =/^[\u4e00-\u9fa5a-zA-Z0-9 　!@#$%^&*()_+=\[\]{};':"\\|,.<>/?~！@#￥%…&*（）—；：‘’“”、。《》？，．／－]+$/
                if (length !=0 &&(length < 4||length > 20)) {
                  return Promise.reject(t('login-validate.username-format-error'));
                }
                return Promise.resolve();
              }
        }
    ])
} 