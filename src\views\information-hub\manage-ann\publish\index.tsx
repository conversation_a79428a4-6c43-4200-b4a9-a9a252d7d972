import { connect } from "react-redux";
import React, { useState, useEffect, forwardRef, useImperativeHandle, useCallback, useRef } from "react";
import {
    Badge, Button, DatePicker, Empty, Flex, Form, Input,
    Select, Spin, Tabs, TabsProps, Tag, Tooltip, Row, Col, Upload, List, message, Modal, Radio, Space, Checkbox, Breadcrumb
} from 'antd'
import { LoadingOutlined } from "@ant-design/icons";
import { useTranslation } from 'react-i18next';
import SvgIcon from '@components/svgicon';

import './index.scss'
import classNames from "classnames";
import Svgicon from "@components/svgicon";

import { $trim, $isNull, $deduplicateByProp } from '@/utils/common'
import { api_get_direct_depts_users, api_get_dept_paths } from '@/api/contacts'


// 公告发布
const AnnPublish = (props: any) => {
    const { t } = useTranslation();
    const editorRef = useRef<any>(null)

    /* props */
    const isVisible = props.visible
    const resource = props.resource
    const { activeCompanyId, activeCompanyName, onSumit } = props

    /* state */
    const [permissionInfo, setPermissionInfo] = useState<any>([]) // 权限详情
    const [permissionRadio, setFrontPermissionRadio] = useState('public') // 使用该机器人的用户（部门）

    const [checkedKeys, setCheckedKeys] = useState<any>([]) // 使用该机器人的用户（部门）-勾选数据

    const [loading, setLoading] = useState(false)
    const [isSubmit, setIsSubmit] = useState(false)

    const Types = [
        // {
        //     value: 'private', // 本人
        //     name: t(["robot-manage.publish-robot-info.permision-0"])
        // },
        {
            value: 'public', // 全部
            name: t(["robot-manage.publish-robot-info.permision-3"])
        },
        {
            value: 'custom', // 自定义
            name: t(["robot-manage.publish-robot-info.permision-4"])
        }
    ]
    // 初始化
    useEffect(() => {
        init()
    }, [])

    const init = () => {
    }
    // 确认发布
    const handleIsPublishOk = async () => {
        try {
            // console.log('frontChecked', checkedKeys)
            let arr:any = []
            if (permissionRadio==='public'){
                // 全部
                arr.push({
                    audienceType: 2, // 0-用户，1-部门 2公司
                    audienceId: Number(activeCompanyId)
                })
            } else if (permissionRadio === 'custom') { //自定义
                if (checkedKeys.length === 0) {
                    message.error(t('robot-manage.publish-robot-info.custom-empty-tip'))
                    return
                }
                arr = checkedKeys.map((item: any) => {
                    // 0-用户，1-部门 2公司
                    return {
                        audienceType: item.type == 'dept' ? 1 : 0,
                        audienceId: item.id
                    }
                })
            }
            console.log('arr', arr)
            onSumit && onSumit(arr)
        } catch (error) {
            console.log('error', error)
        }
    }

    // 取消发布
    const handleIsPublishCancel = () => {
        props.onClose()
    }

    // 在前台可使用该机器人的用户 选择事件
    const handleFrontPerChange = (e: any) => {
        setFrontPermissionRadio(e.target.value)
    }

    const onUpdateCheckedKeys = (datas: any) => {
        console.log('0datas', datas)
        setCheckedKeys(datas)
    }

    return (
        <Modal title={t('info-hub.publish')}
            open={isVisible}
            onOk={handleIsPublishOk}
            onCancel={handleIsPublishCancel}
            okText={t("app.ok")}
            cancelText={t("app.cancel")}
            maskClosable={false}
            centered
            width={900}
            cancelButtonProps={{ variant: "filled", color: "default" }}
            okButtonProps={{ disabled: loading || isSubmit }}
        >
            <div className={"publish-ann-wrap"}>
                {
                    loading ? <Spin className="modal-spin" size="large" indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />} />
                        :
                        <>
                            <div className="item">
                                <div className="label">{t('info-hub.publish-range')}</div>
                                <div className="value radio">
                                    <Radio.Group onChange={handleFrontPerChange} value={permissionRadio}>
                                        <Space direction="vertical" size={12}>
                                            {
                                                Types.map((item: any) => (
                                                    <Radio key={item.value} value={item.value}>{item.name}</Radio>
                                                ))
                                            }
                                        </Space>
                                    </Radio.Group>
                                    {
                                        permissionRadio === 'custom' ? (
                                            <div className="tree">
                                                <div className="tit">
                                                    {t(["robot-manage.publish-robot-info.select-account"]) || ""}
                                                </div>
                                                {/* 使用权限 */}
                                                <OrganizePickerComponent checkedKeys={checkedKeys} onUpdate={onUpdateCheckedKeys} />
                                            </div>
                                        ) : null
                                    }
                                </div>
                            </div>
                        </>
                }
            </div>
        </Modal>
    )
}
// 组织架构
const OrganizePicker = ((props: any) => {

    const { t } = useTranslation();
    const { userInfo, checkedKeys, activeCompanyId, activeCompanyName, onUpdate } = props

    const breadcrumItemsRef: any = useRef()
    const selectedIdsRef: any = useRef()

    const firstBreadcrumItem = [{ id: 0, title: activeCompanyName, "parentId": null, type: 'root' }] // 默认公司为第一层数据
    const [searchValue, setSearchValue] = useState('') // 搜索用户、部门
    const [loading, setLoading] = useState(false)
    const [searchResultNull, setSearchResultNull] = useState(false) // 搜索用户结果为空
    const [isSearching, setIsSearching] = useState(false) // 是否搜索中
    const [organizeBreadcrumItems, setOrganizeBreadcrumItems] = useState<any[]>([...firstBreadcrumItem]) // 组织架构 面包屑数据
    const [currentDepartmentData, setCurrentDepartmentData] = useState<any>([]) // 当前部门数据
    const [selectedDatas, setSelectedDatas] = useState<any[]>([])
    const [searchUserResult, setSearchUserResult] = useState<any[]>([]) // 搜索用户结果

    // 初始化
    useEffect(() => {
        getCurrentOrganizeTreeData(0)
    }, [])

    useEffect(() => {
        console.log('checkedKeys 变化', checkedKeys)
        setSelectedDatas([...checkedKeys])
    }, [checkedKeys])

    useEffect(() => {
        breadcrumItemsRef.current = [...organizeBreadcrumItems]
    }, [organizeBreadcrumItems])

    // 监听选中项
    useEffect(() => {
        const ids: any[] = selectedDatas.map((i: any) => i.key)
        // console.log('selectedDatas 变了ids',ids)
        selectedIdsRef.current = ids
    }, [selectedDatas])


    // 获取部门和用户数据
    const getCurrentOrganizeTreeData = async (deptId: any) => {
        const p: any = {}
        if (deptId != 0) p.deptId = deptId
        setLoading(true)
        const res: any = await api_get_direct_depts_users(p).catch(() => {
            setLoading(false)
        })
        setLoading(false)
        if (!res) return []
        const datas: any = res.data || []
        datas.forEach((item: any) => {
            item.parentDeptId = deptId
            item.key = `${item.type}_${item.id}` // 类型+id 标识唯一性
            item.isCheck = selectedIdsRef.current.includes(item.key)
        });
        console.log('获取当前公司/部门数据datas', datas)
        setCurrentDepartmentData([...datas])
        return datas
    }

    // 获取组织架构 面包屑数据
    const getOrganizeBreadcrumItems = async (target: any, isClickBreadcrum?: boolean) => {
        const deptId = target.id
        let resource = [...firstBreadcrumItem]
        let items = renderBreadcrum(resource)
        // id=0为公司层级
        if (String(deptId) === '0') {
            // console.log('000items',items)
            setOrganizeBreadcrumItems([...items])
            return
        }
        // 非公司层级
        const res: any = await api_get_dept_paths(deptId).catch(() => {
        })
        if (!res) return
        let path_datas = res.data || []
        path_datas = path_datas.map((i: any) => {
            return { id: i.id, title: i.name, parentId: i.parentId }
        })
        resource = [...path_datas]
        items = renderBreadcrum(resource)
        console.log('获取组织架构 面包屑数据', items)
        setOrganizeBreadcrumItems(items)
    }

    const renderBreadcrum = (resource: any[]) => {
        return resource.map((i, index: number) => {
            i.title = i.id === -1 ? t(i.title as string) : i.title
            // return { title: i.title, key: i.id, id: i.id, onClick: () => onClickBreadcrum(i), parentId: i.parentId, type: i.type }
            i.onClick = index !== (resource.length - 1) ? () => onClickBreadcrum(i, index) : null
            i.index = index
            return i
        })
    }

    // 面包屑 部门点击
    const onClickBreadcrum = (target: any, index: number) => {
        console.log('onClickBreadcrum target', target)
        setCurrentDepartmentData([])
        if (target.id !== -1) {
            getCurrentOrganizeTreeData(target.id)
        }
        getOrganizeBreadcrumItems(target, true)
    }

    // 列表项点击
    const onClickListItem = (target: any) => {
        console.log('onClickListItem target', target)
        if (target.type === 'user') return
        getCurrentOrganizeTreeData(target.id)
        getOrganizeBreadcrumItems(target)
    }

    const getOrganizeIcon = (target: any) => {
        if (isSearching) {
            return <Svgicon svgName="icon_user" />
        }
        let icon = null
        if (target.type === 'dept') {
            icon = <Svgicon svgName="menu_org" />
        }
        if (target.type === 'user') {
            icon = <Svgicon svgName="icon_user" />
        }
        return <Flex align="center" justify="center" className={`orga-list-item-icon ${target.type}`}>{icon}</Flex>
    }

    // 取消 or 选择
    const onToggleSelectItem = (e: any, item: any, index: number) => {
        // console.log('item',e, item)
        let result: any = [...selectedDatas]
        if (item.isCheck) {// 取消选择
            result = result.filter((i: any) => i.key !== item.key)
            if (isSearching) {
            }
        } else { // 选中
            result.push({
                id: item.id,
                key: item.key,
                name: item.name,
                type: item.type
            })
            // 去重result
            result = $deduplicateByProp(result, 'key')
            // console.log('result', result)
        }
        setCurrentDepartmentData((prevItems: any) => prevItems.map((pi: any, i: number) =>
            item.key === pi.key ? { ...pi, isCheck: !item.isCheck } : pi
        ))
        // console.log('选择最终结果result', result)
        setSelectedDatas(result)
        onUpdate && onUpdate(result)
        if (isSearching) {
            let _searchUserResult: any = [...searchUserResult]
            _searchUserResult[index].isCheck = !_searchUserResult[index].isCheck
            setSearchUserResult(_searchUserResult)
        }
    }

    // 删除选中
    const onRemoveSelected = (item: any, index: number) => {
        // console.log('item', item)
        const datas: any[] = [...selectedDatas]
        datas.splice(index, 1)
        setSelectedDatas(datas)
        onUpdate && onUpdate(datas)
        setCurrentDepartmentData((prevItems: any) => prevItems.map((pi: any, i: number) =>
            item.type === pi.type && item.id === pi.id ? { ...pi, isCheck: false } : pi
        ))
        if (isSearching) {
            setSearchUserResult((prevItems: any) => prevItems.map((pi: any, i: number) =>
                item.type === pi.type && item.id === pi.id ? { ...pi, isCheck: false } : pi
            ))
        }
    }
    const initSearchResult = () => {
        setSearchUserResult([])
        setSearchResultNull(false)
    }


    const renderUserItem = (item: any) => {
        return (
            <Flex align="center" style={{ maxWidth: '100%' }} gap={8}>
                {getOrganizeIcon(item)}
                <Flex className="orga-list-item-name user" vertical>
                    <Flex className="name-item" gap={8} align="center">
                        <div>{item.name}</div>
                    </Flex>
                    {/* <div className="user-position">{item.position}</div> */}
                </Flex>
            </Flex>
        )
    }

    return (<Flex className="group-member-con" vertical gap={12}>
        <Flex className="group-member-pick">
            {loading ?
                <Flex className="picke-options"><Spin className="modal-spin" size="large" indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />} /> </Flex> :
                <Flex className="picke-options" vertical gap={12}>
                    {/* <Input className="search" value={searchValue} onChange={onChangeSearchValue} placeholder={t('chat.search-user') as string} variant="filled"
              allowClear={{ clearIcon: <Svgicon svgName="icon_input_clear" /> }}
              suffix={searchValue === '' ? <Svgicon svgName="icon_search" /> : null}
              maxLength={500} /> */}
                    {/* 面包屑 */}
                    { //搜索结果
                        isSearching ?
                            <>
                                {
                                    searchResultNull ?
                                        <Empty className="page-empty full" image={null} description={
                                            <>
                                                <div>{t('common.no-data')}</div>
                                                <div></div>
                                            </>}
                                        /> :

                                        <Flex className="orga-list-items" vertical gap={12}>
                                            {
                                                searchUserResult.map((item: any, index: number) => {
                                                    return (
                                                        <Flex key={item.key + '' + index} gap={8}
                                                            className={classNames(["orga-list-item", item.type])} align="center">
                                                            <Checkbox
                                                                checked={item.isCheck}
                                                                onChange={(e) => onToggleSelectItem(e, item, index)} >
                                                                {renderUserItem(item)}
                                                            </Checkbox>
                                                        </Flex>
                                                    )
                                                })
                                            }
                                        </Flex>
                                }
                            </> :
                            <>
                                <Breadcrumb
                                    separator={<Svgicon svgName="arrow_right_line" />}
                                    items={organizeBreadcrumItems}
                                />
                                {
                                    currentDepartmentData && currentDepartmentData.length > 0 ?
                                        <Flex className="orga-list-items" vertical gap={12}>
                                            {
                                                currentDepartmentData.map((item: any, index: number) => {
                                                    return (
                                                        <Flex key={`${item.type}${index}`} gap={8}
                                                            className={classNames(["orga-list-item", item.type])} align="center">
                                                            {/* {getOrganizeIcon(item)} */}
                                                            <Checkbox
                                                                checked={item.isCheck}
                                                                onChange={(e) => onToggleSelectItem(e, item, index)} >
                                                                {item.type === 'user' ? renderUserItem(item) : null}
                                                                {
                                                                    item.type === 'dept' ?
                                                                        <Flex className="orga-list-item-dept" gap={8} align="center">
                                                                            {getOrganizeIcon(item)}
                                                                            <div className="orga-list-item-name dept">{item.name}</div>
                                                                            <div className="orga-list-item-count">({item.headCount || 0})</div>
                                                                            <Button size="small" color="default" variant="filled"
                                                                                onClick={() => onClickListItem(item)} className="orga-list-item-arrow">
                                                                                {t('chat.Expand')}
                                                                            </Button>
                                                                        </Flex>
                                                                        : null
                                                                }
                                                            </Checkbox>
                                                        </Flex>
                                                    )
                                                })
                                            }
                                        </Flex>
                                        :
                                        <Empty className="page-empty full" image={null} description={
                                            <>
                                                <div>{t('common.no-data')}</div>
                                                <div></div>
                                            </>}
                                        />
                                }
                            </>
                    }

                </Flex>
            }
            <Flex className="picker-result" vertical gap={10}>
                <div className="pr-title">{t('chat.selected')} ({selectedDatas.length})</div>
                <Flex className="pr-items" vertical gap={4}>
                    {
                        selectedDatas && selectedDatas.map((item: any, index: number) => {
                            return (
                                <Flex className="pr-item" key={item.key + '' + index} gap={8} align="center">
                                    <Flex className="pr-item-content" gap={4}>
                                        <div className="name">{item.name}</div>
                                    </Flex>
                                    {
                                        !item.isCurrent ? <Flex onClick={() => onRemoveSelected(item, index)} className="svg-operate" align="center" justify="center">
                                            <Svgicon svgName="icon_close" /></Flex> : null
                                    }
                                </Flex>
                            )
                        })
                    }
                </Flex>
            </Flex>
        </Flex>
    </Flex>)
})

const mapStateToProps = (state: any) => ({
    globalLang: state.app.globalLang,
    companies: state.app.companies,
    activeCompanyId: state.app.activeCompanyId
});
const mapDispatchToProps = (dispatch: (e: any) => void) => ({
});

const OrganizePickerComponent = connect(mapStateToProps, mapDispatchToProps, null, { forwardRef: true })(OrganizePicker);

export default connect(mapStateToProps, mapDispatchToProps)(AnnPublish);