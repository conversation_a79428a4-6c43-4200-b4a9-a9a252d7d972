@import "../../styles/mixin.scss";
.chat-user-info-popover {
    $borderRadius: 12px;
    width: 330px;
    border-radius: $borderRadius;
    .ant-popover-content {
        border-radius: $borderRadius;
        width: 330px;
        height: 480px;
        background: var(--colorBgElevated);
    }
    .ant-popover-inner {
        padding: 0;
        height: 100%;
    }
    .ant-popover-inner-content {
        height: 100%;
    }
}

.chat-add-friend-user-modal.ant-modal {
    $borderRadius: 12px;
    .ant-modal-content {
        padding: 0 !important;
    }
    .ant-modal-body {
        width: 330px;
        height: 480px;
    }
}
.chat-user-info-popover,
.chat-add-friend-user-modal.ant-modal {
    $borderRadius: 12px;
    .user-card {
        width: 100%;
        height: 100%;
        border-radius: $borderRadius;
        display: flex;
        flex-direction: column;
        background: url("../../icons/png/contacts/contact-user-bg.png") no-repeat left top;
        border-radius: $borderRadius;
        .avatar{
            position: relative;
            width: 48px;
            height: 48px;
            border-radius: 50%;
            overflow: hidden;
            background: #fff;
            img{
              width: 100%;
              height: 100%;
              border-radius: 50%;
              object-fit: contain;
            }
          }
        .name-avatar-position {
            padding: 54px 20px 8px;
            flex-shrink: 0;
        }

        .UserAvatar--inner {
            border: 2px solid var(--colorBgElevated);
        }

        .name-position {
            flex: 1;
            overflow: hidden;
            > div.name {
                @include font($line-height: 22px, $color: var(--colorTextLightSolid));
                @include ellipsis();
            }
            > div.post {
                @include font(12px, 18px, 400, var(--colorTextSecondary));
                word-wrap: break-word;
                // @include ellipsis();
            }
        }
        .infos {
            flex: 1;
            padding: 8px 20px 20px;
            overflow-y: auto;
        }
        .field-name {
            @include font(12px, 18px, 400, var(--colorTextTertiary));
            flex-shrink: 0;
            width: 72px;
            word-wrap: break-word;
            word-break: break-word;
        }
        .field-value {
            @include font(12px, 18px, 400);
            word-break: break-word;
            word-wrap: break-word;
        }
    }
    
    .user-status {
        padding: 6px 12px;
        margin: 0 20px;
        width: calc(100% - 20px * 2);
        border: 1px solid var(--colorBorderSecondary);
        border-radius: 8px;
        @include font(14px, 22px, 400);
        margin-top: 8px;
        flex-shrink: 0;
        margin-bottom: 8px;
    }
    .btn {
        flex-shrink: 0;
        width: 100%;
        padding: 8px 20px 20px;
        border-radius: 0 0 $borderRadius $borderRadius;
        button {
            width: 100%;
        }
        
        &.has-scroll {
            box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
        }
    }
    .user-verify {
        padding: 20px;
        height: 100%;
        width: 100%;
        box-sizing: border-box;
        .uv-title {
            @include font(16px, 24px, 500);
            margin-bottom: 32px;
        }
        .uv-user-infos {
            margin-bottom: 32px;
            .UserAvatar {
                flex-shrink: 0;
            }
            .usr-info {
                @include font(14px, 22px, 500);
                flex: 1;
                overflow: hidden;
                > div:first-child {
                    max-width: 100%;
                    @include ellipsis();
                }
                > div:last-child {
                    @include font(12px, 18px, 400, var(--colorTextSecondary));
                }
            }
        }
        .icon_back {
            font-size: 20px;
            cursor: pointer;
        }
        .uv-input {
            .til {
                @include font(12px, 18px, 500);
            }
            .ant-input {
                max-height: 140px !important;
                height: 140px !important;
                background: var(--colorFillTertiary);
                border-radius: 8px;
            }
        }
        .btn {
            margin-top: auto;
            padding: 0;
        }
    }
    
    .tertiary {
        margin-top: auto;
    }
}
