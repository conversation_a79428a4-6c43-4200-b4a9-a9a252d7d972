import { connect } from "react-redux";
import { useNavigate } from "react-router-dom";
import React, { useState, useRef, useEffect, forwardRef, useImperativeHandle } from "react";
import { Flex, Breadcrumb, Popover, Empty, Spin } from 'antd'
import { useTranslation } from 'react-i18next';
import SvgIcon from '@components/svgicon';
import UserAvatar from "@/components/userAvatar";
import UserInfoCard from '@/components/userInfoCard'

import { api_get_my_direct_departments, api_get_direct_depts_users, api_get_dept_paths, api_get_user_detail } from '@/api/contacts'

import './index.scss'
import classNames from "classnames";
import { string } from "mathjs";

const Organization = forwardRef((props: any, ref) => {
    // 自定义暴露给父组件的实例值
    useImperativeHandle(ref, () => ({
        // 初始化
        emit_init() {
        },
        // 更新组织架构数据
        emit_update_organization(resource: any) {
            // console.log('Organization resource',resource)
            getAllDatas(resource.id)
            // updateOrganizeChildren(resource)
        }
    }))

    const { t } = useTranslation();
    const navigate = useNavigate();
    const { companies, activeCompanyId, activeCompanyName, onUpdateDepartments } = props
    // console.log('companies', companies)
    // console.log('activeCompanyId', activeCompanyId)

    /**组织架构相关 */
    const [organizeBreadcrumItems, setOrganizeBreadcrumItems] = useState<any[]>([]) // 组织架构 面包屑数据
    const [currentDepartmentData, setCurrentDepartmentData] = useState<any>([]) // 当前部门数据
    const [selectUserInfo, setSelectUserInfo] = useState<any>({}) // 选中用户数据
    const [isLoadingUseInfo, setIsLoadingUseInfo] = useState<any>(false)
    const [firstBreadcrumItem, setFirstBreadcrumItem] = useState<any[]>([{ id: '0', title: '', "parentId": null }]) // 默认面包屑第一层数据
    const [isLoading, setLoading] = useState(false)

    // 初始化
    useEffect(() => {
    }, [])

    // 公司ID变化·
    useEffect(() => {
        if (string(activeCompanyId) === '0') return
        // 面包屑第一层数据读取公司名称
        const _firstBreadcrumItem = firstBreadcrumItem
        _firstBreadcrumItem[0].title = activeCompanyName
        setFirstBreadcrumItem(_firstBreadcrumItem)
        getAllDatas('0')
        getMyDirectDepartments()
        // console.log('activeCompanyName',activeCompanyName)
    }, [activeCompanyId])

    const getAllDatas = (deptId: any) => {
        getOrganizeBreadcrumItems(deptId)
        getDepartmentUserData(deptId)
    }

    // 获取组织架构 面包屑数据
    const getOrganizeBreadcrumItems = async (deptId: any) => {
        let resource = [...firstBreadcrumItem]
        let items = renderBreadcrum(resource)
        // id=0为公司层级
        if (String(deptId) === '0') {
            // console.log('000items',items)
            setOrganizeBreadcrumItems([...items])
            return
        }
        // 非公司层级
        const res: any = await api_get_dept_paths(deptId).catch(() => {
        })
        if (!res) return
        let path_datas = res.data || []
        path_datas = path_datas.map((i: any) => {
            return { id: i.id, title: i.name, parentId: i.parentId }
        })
        resource = [...path_datas]
        items = renderBreadcrum(resource)
        // console.log('111items',items)
        setOrganizeBreadcrumItems([...items])
    }

    const renderBreadcrum = (resource: any[]) => {
        return resource.map((i, index: number) => {
            return { title: i.title, key: i.id, id: i.id, onClick: () => onClickBreadcrum(i) }
        })
    }
    // 获取直属部门和用户数据
    const getDepartmentUserData = async (deptId: any) => {
        const p: any = {}
        if (deptId != 0) p.deptId = deptId
        setLoading(true)
        const res: any = await api_get_direct_depts_users(p).catch(() => {
            setLoading(false)
        })
        setLoading(false)
        if (!res) return
        const datas = res.data || []
        // console.log('datas', datas)
        setCurrentDepartmentData([...datas])
    }

    // 获取当前登录用户 的最小部门集合
    const getMyDirectDepartments = async () => {
        const res: any = await api_get_my_direct_departments().catch(() => {
        })
        if (!res) return
        const datas = res.data || []
        if (onUpdateDepartments) onUpdateDepartments(datas)
    }

    const getOrganizeIcon = (target: any) => {
        let icon = null
        if (target.type === 'dept') {
            icon = <SvgIcon svgName="menu_org" />
        }
        if (target.type === 'user') {
            icon = <UserAvatar name={target.name} src={target?.avatar} userId={target?.imUserId}/>
        }
        return <Flex align="center" justify="center" className={`orga-list-item-icon ${target.type}`}>{icon}</Flex>
    }

    // 面包屑 部门点击
    const onClickBreadcrum = (target: any) => {
        // console.log('onClickBreadcrum target', target)
        getAllDatas(target.id)
    }

    // 列表项点击
    const onClickListItem = (target: any) => {
        // console.log('onClickListItem target', target)
        if (target.type === 'user') return
        getAllDatas(target.id)
    }

    // 查看用户名片
    const onOpenUserDetail = async (isOpen: any, user: any) => {
        // console.log('isOpen', isOpen)
        // console.log('usere', user)
        if (isOpen) {
            let _selectUserInfo = { name: user.name, postName: user.postName }
            setSelectUserInfo({ ..._selectUserInfo })
            const userTenantId = user.userTenantId
            setIsLoadingUseInfo(true)
            const res: any = await api_get_user_detail(userTenantId).catch(() => {
                setIsLoadingUseInfo(false)
            })
            if (!res) return
            const info = res.data || {}
            // console.log('info', info)
            _selectUserInfo = { ..._selectUserInfo, ...info }
            setSelectUserInfo(_selectUserInfo)
            setIsLoadingUseInfo(false)
        }
    }


    const renderUserItem = (item: any) => {
        return (
            <Flex align="center" style={{ maxWidth: '100%' }} gap={8}>
                {getOrganizeIcon(item)}
                <Flex className="orga-list-item-name user" vertical>
                    <Flex className="name-item" gap={8} align="center">
                        <div>{item.name}</div>
                        {item.isSupervisor ? <Flex className="is-super" align="center">{t('contacts.organize.supervisor')}</Flex> : null}
                    </Flex>
                    <div className="user-position">{item.position}</div>
                </Flex>
            </Flex>
        )
    }
    
    // 发送信息给用户
    const onSendMessageToUser = (userID: any)=>{
         // 跳转至聊天，携带用户ID 参数
        //  console.log('userID',userID)
        if (userID) navigate("/chats",{state: { userID }});
    }

    return (
        <Flex className="orga-area contacts-area" vertical>
            <Breadcrumb
                separator={<SvgIcon svgName="arrow_right_line" />}
                items={organizeBreadcrumItems}
            />
            {
                isLoading ? <Spin className="full-spin" size="large" /> :
                    <div className="orga-content">
                        {
                            currentDepartmentData && currentDepartmentData.length > 0 ?
                                <Flex className="orga-list-items" vertical gap={12}>
                                    {
                                        currentDepartmentData.map((item: any, index: number) => {
                                            return (
                                                <Flex onClick={() => onClickListItem(item)} key={item.id + '' + index} gap={8}
                                                    className={classNames(["orga-list-item", item.type])} align="center">
                                                    {/* {getOrganizeIcon(item)} */}
                                                    {
                                                        item.type === 'dept' ?
                                                            <>
                                                                {getOrganizeIcon(item)}
                                                                <div className="orga-list-item-name dept">{item.name}</div>
                                                                <div className="orga-list-item-count">({item.headCount})</div>
                                                            </>
                                                            : null
                                                    }
                                                    {
                                                        item.type === 'user' ?
                                                            <>
                                                                {
                                                                    <UserInfoCard placement="right" 
                                                                        userInfo={{ userID: item.imUserId, userName: item.name}}
                                                                        onSendMessageToUser={onSendMessageToUser} >
                                                                        {renderUserItem(item)}
                                                                    </UserInfoCard>
                                                                }
                                                            </>

                                                            : null
                                                    }
                                                    {item.type === 'dept' ?
                                                        <span className="orga-list-item-arrow">
                                                            <SvgIcon svgName="arrow_right_line" />
                                                        </span> : null}
                                                </Flex>
                                            )
                                        })
                                    }
                                </Flex>
                                :
                                <Empty className="page-empty full" image={null} description={
                                    <>
                                        <div>{t('common.no-data')}</div>
                                        <div></div>
                                    </>}
                                />
                        }
                    </div>
            }
        </Flex>
    )
})


const mapStateToProps = (state: any) => ({
    companies: state.app.companies,
    activeCompanyId: state.app.activeCompanyId,
    activeCompanyName: state.app.activeCompanyName
})
const mapDispatchToProps = (dispatch: any) => ({
    // dispatch_api_: (data:any) => dispatch(dispatch_api_(data))
})
const ConnectedCounter = connect(mapStateToProps, mapDispatchToProps, null, { forwardRef: true })(Organization);
export default ConnectedCounter;