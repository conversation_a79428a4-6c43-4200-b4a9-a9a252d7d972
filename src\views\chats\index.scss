@import "../../styles/mixin.scss";
@mixin flex($flex-direction: row, $align-items: center, $justify-content: normal) {
    display: flex;
    flex-direction: $flex-direction;
    align-items: $align-items;
    justify-content: $justify-content;
}

$align-space: 16px;

.chats {
    display: flex;
    height: 100%;
    width: 100%;
    min-width: 1400px;

    .left-area {
        width: 410px;
        background: var(--colorFillQuaternary);
        .title {
            width: 100%;
            flex-shrink: 0;
            @include flex($justify-content: space-between);
            @include font(18px, 24px, 600);
            padding: 19px 24px;
            span {
                margin-right: 4px;
            }
            .anticon {
                font-size: 16px;
                cursor: pointer;
            }
        }
        .chats-new-icon {
            font-size: 20px;
            width: 36px;
            height: 36px;
            cursor: pointer;
            color: var(--colorIcon);
        }

        .chats-conversation-list {
            flex: 1;
            overflow-y: auto;
            padding: 0 16px;

            .conversation-item {
                @include flex;
                align-items: flex-start;
                gap: 8px;
                padding: 6px 8px;
                font-weight: 500;
                font-size: 14px;
                color: var(--colorText);
                line-height: 20px;
                margin-bottom: 6px;
                cursor: pointer;
                width: 100%;
                height: 60px;

                &.bot_default {
                    width: calc(100% - $align-space * 2);
                    flex-shrink: 0;
                    font-weight: 600;
                    font-size: 14px;
                    line-height: 22px;
                    padding: 4px 8px;
                    margin: 0 auto 12px;
                }

                &:hover {
                    background: var(--colorPrimaryBg);
                    border-radius: 12px;
                }

                &.active {
                    background: var(--colorFillTertiary);
                    border-radius: 12px;
                }

                .svg-icon {
                    font-size: 16px;
                    margin-right: 10px;
                    flex-shrink: 0;
                }

                .avatar {
                    height: 30px;
                    width: 30px;
                }

                .content {
                    display: flex;
                    flex-direction: column;
                    flex: 1;
                    overflow: hidden;
                    gap: 4px;
                    .content-tittle {
                        @include font(14px, 22px, 600, var(--colorText));
                        @include ellipsis();
                    }

                    .content-msg {
                        @include font(12px, 18px, 400, var(--colorTextSecondary));
                        max-width: 100%;
                        @include ellipsis();
                    }
                    .msg-draft {
                        > span:first-child {
                            @include font(10px, 12px, 400, blue);
                        }
                    }
                }

                .content-right {
                    flex-shrink: 0;
                    @include font(12px, 18px, 500, var(--colorTextSecondary));
                }
                .c-unread {
                    background: var(--colorError);
                    @include font(10px, 10px, 500, #fff);
                    z-index: 20;
                    border-radius: 16px;
                    padding: 2px 4px;
                    text-align: center;
                }
            }
        }
    }

    .right-area {
        flex: 1;
        overflow: hidden;
        height: 100%;
        .msg-item {
            min-height: 40px;
            min-width: 200px;
        }
        .msg-content {
            word-break: break-word;
            word-wrap: break-word;
        }
        .msg-content.no-avatar {
            width: 100%;
        }
        .conversation-tittle {
            flex-shrink: 0;
            width: 100%;
            padding: 10px 24px;
            border-bottom: 1px solid var(--colorBorderSecondary);
            .user-avatar {
                cursor: pointer;
            }
        }
        .conversation-tittle-user {
            overflow: hidden;
        }
        .conversation-tittle-operate {
            margin-left: auto;
            font-size: 20px;
            > div {
                cursor: pointer;
                padding: 8px;
            }
        }
        .conversation-content-area {
            width: 100%;
            flex: 1;
            overflow: auto;
            padding-top: 16px;
            position: relative;
            .content-area-spin {
                width: 100%;
                height: 100%;
                display: none;
                align-items: center;
                justify-content: center;
                position: absolute;
                top: 0;
                bottom: 0;
                right: 0;
                left: 0;
                z-index: 100;
                background: var(--colorBgElevated);
                &.show-loading {
                    display: flex;
                }
            }
        }
        .conversation-content {
            // flex:1;
            width: 832px; // 740+ 12 +32 + 24 +24= 832
            // overflow: auto;
            // height: ;
            position: relative;
            padding-bottom: 20px;
            margin: 0 auto;
            cursor: pointer;
            // flex-direction: column-reverse;
        }

        .user-name {
            @include font($font-weight: 600);
            @include ellipsis();
        }

        .conversation-msg {
            width: 100%;
            cursor: pointer;
            // padding: 4px 24px;
            box-sizing: border-box;
            .msg-detail {
                width: 100%;
            }
            .msg-detail-right {
                max-width: 740px;
            }

            .msg-user {
                max-width: 100%;
                .send-time {
                    flex-shrink: 0;
                    @include font(12px, 18px, 400, var(--colorTextTertiary));
                    display: none;
                }
            }
            &:hover {
                // background: var(--colorFillQuaternary);
                .send-time {
                    display: block;
                }
            }
        }

        .user-info {
            height: 100%;
            flex: 1;
            overflow: hidden;
            .user-name-status {
                max-width: 100%;
                overflow: hidden;
                align-self: flex-start;
            }
            .user-name {
                flex: 1;
            }
            .user-status {
                flex-shrink: 0;
                font-size: 14px;
            }
        }
        .user-post {
            @include font(12px, 18px, 400, var(--colorTextTertiary));
        }
        .msg-text {
            @include font($line-height: 24px, $font-weight: 400);
            white-space: pre-wrap;
        }

        .conversation-disabled {
            background: var(--colorFillTertiary);
            width: 100%;
            padding: 12px;
            @include font(12px, 18px, 400, var(--colorTextTertiary));
        }

        .conversation-send {
            flex-shrink: 0;
            padding: 8px 12px;
            margin: 12px auto;
            border: 1px solid var(--colorBorderSecondary);
            border-radius: 12px;
            width: 784px;
            max-height: 100px;
            .ant-input {
                border: 0;
                outline: none;
            }
            .send-operate {
                .operate-item {
                    cursor: pointer;
                    font-size: 16px;
                    color: var(--colorIconTertiary);
                    border-radius: 50%;
                    width: 28px;
                    height: 28px;
                    border: 1px solid var(--colorBorderSecondary);
                }
            }
            .send-text {
                border-color: transparent;
                margin-left: auto;
                background: var(--colorPrimarySecondary);
                .svg-icon {
                    color: var(--colorPrimaryContrasting);
                }
            }
            .send-text-disabled {
                opacity: 0.2;
            }
        }
    }

    .group-avatar {
        border-radius: 12px;
        border: 1px solid rgba(0, 0, 0, 0.02);
        width: 48px;
        height: 48px;
        box-sizing: border-box;
    }

    .msg-video {
        display: inline-block;
        position: relative;
        cursor: pointer;
        border-radius: 8px;
        min-width: 100px;
        img {
            max-width: 200px;
            height: auto;
            min-height: 20px;
            border-radius: 8px;
            border: 1px solid var(--colorBorderImage);
        }
        .duration {
            position: absolute;
            right: 6px;
            bottom: 6px;
            padding: 0 2px;
            background: var(--colorBgMask);
            border-radius: 4px;
            @include font(12px, 18px, 400, var(--colorTextLightSolid));
        }
        &:hover {
            .icon_play {
                display: block;
            }
        }
        .icon_play {
            font-size: 38px;
            width: 38px;
            position: absolute;
            top: 50%;
            left: 50%;
            margin-top: -19px;
            margin-left: -19px;
            z-index: 200;
            display: none;
            border: 0;
        }
    }
    .msg-submiting {
        display: inline-block;
        position: relative;
        cursor: pointer;
        border-radius: 8px;
        min-width: 100px;
        padding-top: 20px;
        img {
            max-width: 200px;
            height: auto;
            min-height: 20px;
            border-radius: 8px;
        }
    }
    .msg-image {
        display: inline-block;
        position: relative;
        cursor: pointer;
        border-radius: 8px;
        min-width: 100px;
        img {
            max-width: 200px;
            min-height: 20px;
            height: auto;
            min-height: 10px;
            border-radius: 8px;
            border: 1px solid var(--colorBorderImage);
        }

        &:hover {
            .msg-image-view {
                display: block;
            }
        }
        .msg-image-view {
            font-size: 32px;
            width: 30px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 200;
            display: none;
            color: var(--colorBgElevated);
        }
    }
    .msg-file {
        min-width: 120px;
        width: 350px;
        max-height: 82px;
        padding: 8px;
        border: 1px solid var(--colorBorderSecondary);
        border-radius: 12px;
        cursor: pointer;

        &:hover {
            .dl-icon {
                display: block;
            }
        }

        .dl-icon {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 100;
            cursor: pointer;
            display: none;
            font-size: 14px;
        }
        .msg-file-icon {
            position: relative;
            flex-shrink: 0;
            width: 30px;
            height: 42px;
            background: url("../../icons/png/chats/icon_chat_file.png") no-repeat center;
            font-size: 10px;
            color: #737373;
            padding-bottom: 2px;
        }
        .msg-file-text {
            > div:first-child {
                @include font($font-weight: 500);
                @include ellipsis-multiline(2);
            }
            > div:last-child {
                @include font(12px, 18px, normal, var(--colorTextTertiary));
            }
        }
    }

    .msg-group-operate {
        width: 100%;
        margin-bottom: 2px;
        @include font(12px, 18px, 400, var(--colorTextTertiary));
        flex-wrap: wrap;
        .name {
            color: var(--colorLink);
        }
    }
    .msg-notify {
        @include font(12px, 18px, 400, var(--colorTextTertiary));
    }
}

.chats-new-dropdown.ant-dropdown {
    .ant-dropdown-menu .ant-dropdown-menu-item {
        @include font($font-weight: 500);
        .ant-dropdown-menu-item-icon {
            font-size: 16px;
        }
    }
}

.svg-operate {
    font-size: 16px;
    padding: 8px;
    cursor: pointer;
}