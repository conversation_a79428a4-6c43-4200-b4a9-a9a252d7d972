

import React, { useState, useEffect, forwardRef, useImperative<PERSON>andle } from 'react';
import { useNavigate } from "react-router-dom";
import { connect } from "react-redux";
import { Input, message, Form, Flex, Button, Tabs, Modal } from 'antd';
import type { FormInstance, TabsProps } from 'antd';
import { useTranslation } from 'react-i18next';
import classNames from 'classnames';
import Svgicon from '@/components/svgicon'
import VerifyCode from '@/components/form/verifyCode'
import PasswordInput from '@/components/form/passwordInput'
import MobileInput from '@/components/form/mobileInput'
import { PasswordRules, EmailRules, MobileRules, VerifyCodeRules } from '@/views/common/formRule'
import { api_get_email_code, api_get_mobile_code, api_reset_password } from '@/api/user'
import { Encrypt } from '@/utils/forge'
import ErrorCodeMap from '@/views/common/formErrorCodeMap'
import FormUtils from '@/views/common/formUtils'
import { RequestFailedMessage } from '@/utils/userRequest'
import { loginedDefaultPath } from '@/routes'
import SvgIcon from '@components/svgicon'; // 根据实际路径引入

import './index.scss';

const items: TabsProps['items'] = [
    {
        key: 'Email',
        label: 'email',
        children: null,
    },
    {
        key: 'Mobile',
        label: 'mobile',
        children: null,
    }
];
const ForgotPassword = forwardRef((props: any, ref: React.LegacyRef<HTMLDivElement> | any) => {

    const { t, i18n } = useTranslation();
    const navigate = useNavigate();
    const [modal, contextHolder] = Modal.useModal()

    const isPrivate = props.isPrivate
    const { isInvite = false, inviteCode = null, condition } = props
    const [form] = Form.useForm();
    const [formValues, setFormValues] = useState<any>({});
    const [accountType, setAccountType] = useState('Email') // 邮箱 或手机号
    const [isSubmiting, setIsSubmiting] = useState(true)


    useEffect(() => {
        // console.log('condition',condition)
        const { type, data } = condition
        if (type === 'Email') {
            setAccountType('Email')
            form.setFieldsValue({ email: data })
        }
        if (type === 'Mobile') {
            // console.log('mobile',data)
            setAccountType('Mobile')
            form.setFieldsValue({ mobile: { ...data } })
        }
        form.getFieldValue("email")
    }, [condition, form])


    /**自定义暴露给父组件的实例值，接收父组件通知调用 */
    useImperativeHandle(ref, () => ({
        // 初始化
        emit_init: () => {
            form.resetFields()
        }
    }))

    //提交
    const handleSubmit = () => {
        FormUtils.resetValidate(form)
        const values: any = form.getFieldsValue()
        let { email, mobile, code, password } = values
        // console.log('values',values)
        password = Encrypt(values.password) // 加密
        const data: any = {
            resetType: accountType == 'Email' ? 'EMAIL_CODE' : 'MOBILE_CODE',
            code: code,
            newPassword: password,
        }
        if (accountType == 'Email') {
            data.email = email
        } else {
            data.areaCode = '+' + mobile.code.label
            data.mobile = mobile.number
        }
        if (isInvite && inviteCode) {
            data.invitationCode = inviteCode
        }
        // console.log('data', data)
        // return 
        setIsSubmiting(true)
        props.dispath_api_reset_password(data).then(async (res: any) => {
            setIsSubmiting(false)
            if (res?.code == 0) { // 重置成功，直接登录
                if (isInvite) {
                   navigate('/inviteSuccess?code='+inviteCode)
                } else {
                    navigate(loginedDefaultPath)
                }

            } else {
                const _code = res?.code
                const codeErros = ErrorCodeMap.code // 验证码相关
                if (!codeErros.includes(_code)) {
                    const msg = RequestFailedMessage(res?.msg, _code)
                    message.error(msg);
                    return
                }
                const errMsg = t(`errorCode.${_code}`)
                form.setFields([{ name: 'code', errors: [errMsg], warnings: [`errorCode.${_code}`] }])
            }

        }).catch((err: any) => {
            setIsSubmiting(false)
            console.log('err', err)
        })
    }


    const onVerifyCodeOk = (fieldName: any, fieldValue: any) => {

    }

    // 处理获取验证码失败 对应表单输入框错误提示
    const onVerifyCodeError = (err: any) => {
        // console.log('onVerifyCodeError err',err)
        const _code = err?.code
        // const codeErrors =  ErrorCodeMap.code
        const emailErrors = ErrorCodeMap.email
        const mobileErrors = ErrorCodeMap.mobile
        const errMsg = t(`errorCode.${_code}`)
        let targetField: any = null
        // if (codeErrors.includes(_code)) targetField = 'code'
        if (emailErrors.includes(_code)) targetField = 'email'
        if (mobileErrors.includes(_code)) targetField = 'mobile'
        // console.log('targetField',targetField)
        if (!targetField) return
        form.setFields([{ name: targetField, errors: [errMsg], warnings: [`errorCode.${_code}`] }])
    }


    const onChangeTab = (key: string) => {
        console.log(key);
        setAccountType(key)
    };

    // 区号切换时需要校验手机号
    const onValidteMobile = () => {
        form.validateFields(["mobile"])
    }

    const onBack = () => {
        // FormUtils.resetValidate(form)
        form.resetFields()
        props?.onBack(condition)
    }

    const renderBack = (onClick: any) => {
        return (
            <Flex onClick={onClick} className="back" gap={8} align="center">
                <Svgicon svgName="icon_back" />
                <span>{t('login.back')}</span>
            </Flex>
        )
    }

    // 表单项 输入改变事件
    const onFiledValueChange = (changedFields: any, allValues: any) => {
        try {
            // console.log('changedFields', changedFields)
            setFormValues(allValues)
            Object.keys(changedFields).forEach((fieldName: any) => {
                const fieldError = form?.getFieldError(fieldName)
                if (fieldError.length > 0) FormUtils.clearValidate(form, fieldName)
            })
        } catch (error) {

        }
    }

    // 语言切换时，重新渲染表单校验信息
    useEffect(() => {
        const fun: any = () => FormUtils.refreshValidate(form, t)
        // 监听语言变化
        i18n.on('languageChanged', fun);

        // 清理监听器
        return () => {
            i18n.off('languageChanged', fun);
        };
    }, [i18n]);


    return (

        <div className="forgot-password content">
            {renderBack(onBack)}
            <div className="content-tittle">{t('login.forgot-password')}</div>
            <Form form={form} className="forgot-from-1 common-form" name="validateOnly" layout="vertical"
                autoComplete="off"
                requiredMark={false}
                validateTrigger="onBlur"
                onValuesChange={onFiledValueChange}
                initialValues={{
                    email: '',
                    mobile: {
                        number: '', // 手机号
                        // code: {label: 1, value: 'US'} // 区号
                        code: { label: null, value: null }// 区号
                    },
                    password: '',
                    code: ''
                }}
            >
                {/* Sass登录 可切换邮箱和手机号 */}
                {!isPrivate ?
                    <Tabs className='common-tab without-border' activeKey={accountType} defaultActiveKey="Email" items={items?.map((item: any) => ({
                        key: item.key,
                        label: t(`login.${item.label}`), // 确保这里使用翻译函数
                        children: item.children,
                    }))} onChange={onChangeTab} />
                    : null}
                {/* 邮箱 */}
                <Form.Item name="email" label={""} className={accountType == 'Mobile' ? 'hidden' : ''}
                    rules={[{ required: true, whitespace: true, message: '' }, ...EmailRules(t)]}>
                    <Input autoComplete="new-email"
                        className="ipt" variant="filled" maxLength={300} placeholder={t('login-pls.email') as string} />
                </Form.Item>
                {/* 手机号 */}
                {
                    !isPrivate ?
                        <Form.Item name="mobile" label={""} rules={MobileRules(t)} className={classNames(['mobile-form-item', { hidden: accountType == 'Email' }])}>
                            <MobileInput onValidte={onValidteMobile} />
                        </Form.Item> : null
                }
                {/* 验证码 */}
                <Form.Item name="code" label={""}
                    rules={VerifyCodeRules(t)}>
                    <VerifyCode
                        api={accountType == 'Email' ? api_get_email_code : api_get_mobile_code}
                        scene="MEMBER_RESET_PASSWORD"
                        target={accountType.toLocaleLowerCase()}
                        {
                        ...
                        (accountType == 'Email' ? { email: form.getFieldValue("email") } : { mobile: form.getFieldValue("mobile") })
                        }
                        targetValidate={form.validateFields} key="forgot-code"
                        onOk={onVerifyCodeOk}
                        onError={onVerifyCodeError}
                    />
                </Form.Item>
                {/* 密码 */}
                <Form.Item name="password" label={""} rules={[{ required: true, whitespace: true, message: '' }, ...PasswordRules(t)]}>
                    <PasswordInput
                        key="forgot-password"
                        placeholder={t('login-pls.new-password')}
                    />
                </Form.Item>
                <div className={classNames(["form-submit"])}>
                    <SubmitButton form={form} disabled={isSubmiting} fields={isPrivate || accountType == 'Email' ? ['email', 'code', 'password'] : ["mobile", "code", 'password']}
                        onClick={handleSubmit}>{isInvite ? t('invite.reset-apply') : t('app.ok')}</SubmitButton>
                </div>
            </Form>
            {contextHolder}
        </div>
    )
})
interface SubmitButtonProps {
    form: FormInstance;
    onClick: any,
    disabled?: boolean,
    // step: number,
    // accountType: string,
    fields: any
}

const SubmitButton: React.FC<React.PropsWithChildren<SubmitButtonProps>> = ({ form, children, onClick, fields, disabled }) => {
    const [isSubmitBtnAble, setIsSubmitBtnAble] = useState(false)

    // Watch all values
    const values = Form.useWatch([], form);
    useEffect(() => {
        // console.log('values', values)
        // const fields = step == 1 ? ['email', 'code'] : ['email', 'code', 'password']
        form.validateFields(fields, { validateOnly: true })//
            .then(() => setIsSubmitBtnAble(true))
            .catch(() => setIsSubmitBtnAble(false));
    }, [form, values, fields]);

    useEffect(() => {
        setIsSubmitBtnAble(!disabled)
    }, [disabled])

    return (
        <Button size="large" type="primary" onClick={onClick} disabled={!isSubmitBtnAble} className={"btn"}>
            {children}
        </Button>
    );
};
const mapStateToProps = (state: any) => ({
    isPrivate: state.app.isPrivate,
})

const mapDispatchToProps = (dispatch: any) => ({
    dispath_api_reset_password: (data: any) => dispatch(api_reset_password(data)),
})

// export default connect(mapStateToProps, mapDispatchToProps)(ForgotPassword);
const ConnectedCounter = connect(mapStateToProps, mapDispatchToProps, null, { forwardRef: true })(ForgotPassword);
export default ConnectedCounter;
