import React from "react";
import { connect } from "react-redux";
import i18next from "i18next";
import classNames from "classnames";
import Svgicon from "@/components/svgicon"
import { Flex, Popover, Button, Radio, Modal, message, Empty, Spin, Segmented } from "antd";
import { ContextState } from "./index_type"

import { api_get_robot_list, api_delete_robot } from "@/api/robotManage2";

import defaultLogo from "@/assets/bot_default_avatar.png";
// import { forAryReplace } from "../permission/common";

import PublishRobotModal from './modal/publishRobot/index';
import CreateRobotModal from './modal/createRobot/index';
import MyInput from './component/Input';

import './style/index.scss'

class RobotManage extends React.Component<any, ContextState> {

  constructor(props: any) {
    super(props);
    this.state = {
      robotList: [],
      keyWord: '', // 关键字
      robotType: 0, // 机器人类别
      dispalyType: 'grid', // 排列方式
      robotTypeOptions: [
        { name: 'robot-manage.robot-type-1', value: 0 },//全部機器人
        { name: 'robot-manage.robot-type-2', value: 1 }, // 我創建的機器人
        { name: 'robot-manage.robot-type-3', value: 2 } // 與我共享的機器人
      ], // 机器人类别选项
      dispalyTypeOptions: [
        { name: 'robot-manage.arrangement-1', value: 'grid' },//網格視圖
        { name: 'robot-manage.arrangement-2', value: 'list' }, // 列表視圖
      ], //排列方式
      deleteRobotModal: {
        visible: false,
        robot_id: null
      },

      cardLoading: false,
      isCreateRobotModalOpen: false, // 新增机器人弹窗
      isPublishModalOpen: false,
      currentSelectRobot: {
        name: "",
        trigger: "add",
        logo: "",
        introduction_message: "",
        robotIdentityPrompt: "",
        robotType: null,
        robotWelcomeMsg: "",
        robotLlmModel: [],
        escalateLlmModel: [],
        embeddingModel: "",
        intelligence: 0,
        relevance: 0,
        konwledgeCount: 1,
        isIrrelevantQuestions: 0,
        isIrrelevantQuestionsBoolean: false,
        irrelevantRespond: "",
        maxResponseLength: 2048,
        showKnowledge: true,
        enableConversationMemory: false,
      },
      interfaceRobotType: "",
      treeData: [],
      frontChecked: [],
      backChecked: []
    }
  };

  // 打开或关闭 新建机器人弹窗
  handleToggleIsRobotModal = (isOpen: boolean) => {
    this.setState({ isCreateRobotModalOpen: isOpen ? true : false })
  }

  // 获取所有的机器人
  handleQueryRobotList = (_keyword?: any) => {
    const { keyWord, robotType } = this.state;
    const data = {
      robotName: _keyword !== undefined ? _keyword : keyWord,
      robotType,
      seq: 0
    }

    this.setState({ cardLoading: true })
    api_get_robot_list(data).then((res: any) => {
      // console.log('res',res?.data?.robotList)
      this.setState({ cardLoading: false, robotList: res?.data?.robotList || [] })
    }).catch((err: any) => {
      // console.log(err)
      this.setState({ cardLoading: false })
    }
    );
  }

  // 删除确认弹窗显示
  handleShowDeleteModal = (robot: any) => {
    this.setState({ deleteRobotModal: { robot_id: robot.robot_id, visible: true } })
  }

  // 取消删除
  handleDeleteModalCancel = () => {
    this.setState({ deleteRobotModal: { ...this.state.deleteRobotModal, visible: false } })
  }

  // 确认删除删除机器人
  handleDeleteModalOk = () => {
    const { deleteRobotModal } = this.state
    const data = {
      robot_id: deleteRobotModal.robot_id,
      seq: "0"
    };
    api_delete_robot(data).then((result: any) => {
      message.success(i18next.t("common.delete-success"));
      this.handleQueryRobotList();
      this.emitUpdateRobotList()
    }).catch((err: any) => {
      message.error(i18next.t("robot-manage.delete-robot-error"));
    })
    this.handleDeleteModalCancel()
  }

  // 通知父组件robot更新机器人列表
  emitUpdateRobotList = () => {
    this.props.onUpdateRobotList && this.props.onUpdateRobotList()
  }

  onSumitCreate = () => {
    this.handleQueryRobotList()
    this.emitUpdateRobotList()
  }

  // 设置
  handleSettingRobot = (d: any) => {
    // this.props.router.navigate(`/robotSetting/basicinfo?robotid=${d.robot_id}`)
    this.props.router.navigate(`/robotSetting?pid=basicinfo&robotid=${d.robot_id}`)
  }

  // 打开发布弹窗前
  handleShowIsPublishModal = (d: any) => {
    const { front_permission, back_permission } = d;

    let frontChecked: string[] = [];
    let backChecked: string[] = [];

    // if (front_permission.type === "4") {
    //   frontChecked = front_permission.key;
    // }

    // if (back_permission.type === "4") {
    //   backChecked = back_permission.key;
    // }
    this.setState({
      isPublishModalOpen: true,
      currentSelectRobot: d,
      frontChecked,
      backChecked,
    })
  }

  // 关闭发布弹窗
  handleIsPublishCancel = () => {
    this.setState({ isPublishModalOpen: false })
  }

  // // 获取公司组织成员
  // handleQueryEmployeeTree = () => {
  //   const { userInfo, globalRegion: region } = this.props;
  //   const data = {
  //     ...api_query_employee_tree_params,
  //     region,
  //     account: userInfo.account
  //   }

  //   this.props.dispatch_api_query_employee_tree(data).then((result: any) => {
  //     result.forEach((d: any) => {
  //       forAryReplace(d);
  //     })

  //     if (result.length) {
  //       this.setState({ treeData: result[0].children })
  //     }
  //   }).catch((err: any) => console.log(err))
  // }

  // Cascader 置灰选项，点击提示信息
  handleCascaderConainerClick(disabled?: boolean | undefined) {
    if (disabled) {
      message.open({ type: "error", content: i18next.t(["robot-manage.llm-disabled-tip"]) })
    }
  }

  // 输入框keyword 请求
  handleChangeKeyWord = (value: any) => {
    this.setState({ keyWord: value })
    this.handleQueryRobotList(value)// 发起请求
  };

  // 切换机器人类别事件
  onChangeRobotType = (e: any) => {
    this.setState({ robotType: e.target.value }, () => {
      this.handleQueryRobotList()
    });
  }

  // Segmented 切换机器人类别事件
  onChangeSegmentedRobotType = (value: any) => {
    this.setState({ robotType: value }, () => {
      this.handleQueryRobotList()
    });
  }

  // 切换排列方式事件
  onChangeDispalyType = (e: any) => {
    const value = e.target.value
    localStorage.setItem("AMA_ADMIN_ROBOT_DSP", value)
    this.setState({ dispalyType: value });
  }

  // 初始化
  init() {
    // 读取 排列方式缓存
    const dispalyTypeStore = localStorage.getItem("AMA_ADMIN_ROBOT_DSP")
    if (dispalyTypeStore) {
      this.setState({ dispalyType: dispalyTypeStore })
    }
  }

  componentDidMount() {
    this.init()
    // this.handleQueryEmployeeTree();
    this.handleQueryRobotList();
  }

  componentWillUnmount(): void {
  }

  // llm
  renderLLmInfo = (robot: any) => {
    return <Flex className="cl-content-llm" gap={4} align="center">
      <Svgicon svgName="llm" />
      <span>{robot?.default_LLM_display_name?.length > 0 ? robot?.default_LLM_display_name.join('/') : '-'}</span>
    </Flex>
  }

  // 简介
  renderRobotIntroduction = (robot: any) => {
    return <div className="cl-content-bottom">{robot.introduction_message}</div>
  }

  // 操作按钮
  renderOperateBtns = (robot: any) => {
    const { permissionMap } = this.props;
    const { dispalyType } = this.state
    return <>
      {/* {permissionMap.robotManageDeleteRobotBtn ? */}
      <Button className={dispalyType == 'list' ? "margin-left-auto" : ""} color="danger" variant="filled" onClick={() => this.handleShowDeleteModal(robot)}>{i18next.t('common.delete')}</Button>
      {/* : null} */}
      <Flex className={classNames(['cl-operate-right-btns', { "margin-left-auto": dispalyType === 'grid' }])} gap={10}>
        {/* {permissionMap.robotManagePublishRobotBtn ? 
            : null} */}
        <Button type="primary" onClick={() => this.handleShowIsPublishModal(robot)}>{i18next.t('robot-manage.share-robot')}</Button>
        {/* {permissionMap.robotManageVectorRobotBtn ?  */}
        <Button color="default" onClick={() => this.handleSettingRobot(robot)} variant="filled">{i18next.t('robot-manage.setting-robot')}</Button>
        {/* : null} */}
      </Flex>
    </>
  }

  // 获取名称
  renderName = (robot: any) => {
    let name = robot.username
    if (robot.robot_type === 'DEPARTMENT') {
      name = robot.department_name
    }
    if (robot.robot_type === 'COMPANY') {
      name = this.props.activeCompanyName
    }
    return name
  }

  render() {
    const { robotType, robotTypeOptions, dispalyType, dispalyTypeOptions, isPublishModalOpen, isCreateRobotModalOpen,
      currentSelectRobot, frontChecked, backChecked, treeData, deleteRobotModal, cardLoading, robotList
    } = this.state;
    // const { permissionMap  } = this.props;


    return (
      <div className={"robot-manage-wrap gray-bg"}>
        <Flex className="page-nav" align="center" justify="space-between">
          <span className="nav-title">{i18next.t('robot-manage.label')}</span>
          <Flex gap={10} align="center">
            <MyInput onChange={this.handleChangeKeyWord} />
            <Popover
              arrow={false}
              overlayClassName={"robot-filters-popover"}
              placement="bottomLeft"
              content={
                <div>
                  {/* <div className="rf-title">{i18next.t('robot-manage.robot-type')}</div>
                  <Radio.Group onChange={this.onChangeRobotType} value={robotType}>
                    {
                      robotTypeOptions.map((type: any) => {
                        return (<Radio key={type.value} value={type.value}>{i18next.t(type.name)}</Radio>)
                      })
                    }
                  </Radio.Group> */}
                  <div className="rf-title">{i18next.t('robot-manage.arrangement')}</div>
                  <Radio.Group onChange={this.onChangeDispalyType} value={dispalyType}>
                    {
                      dispalyTypeOptions.map((type: any) => {
                        return (<Radio key={type.value} value={type.value}>{i18next.t(type.name)}</Radio>)
                      })
                    }
                  </Radio.Group>
                </div>
              }
            >
              <Button className="button-icon" icon={<Svgicon svgName="filters" />} />
            </Popover>
          </Flex>
          <Button onClick={() => this.handleToggleIsRobotModal(true)} icon={<Svgicon svgName="new_bot" />} type="primary">{i18next.t('robot-manage.create-robot-button')}</Button>
        </Flex>
        <div className="segmented-types">
          <Segmented
            className="common-segmented"
            value={robotType}
            options={robotTypeOptions.map((i: any) => {
              i.label = i18next.t(i.name)
              return i
            })}
            onChange={this.onChangeSegmentedRobotType}
          />
        </div>
        <div className="page-main">
          {
            cardLoading ?
              <Spin className="full-spin" size="large" /> :
              <>
                {
                  robotList?.length > 0 ?
                    <Flex className={classNames(["card-list", dispalyType])} wrap={dispalyType === 'grid'} vertical={dispalyType === 'list'} gap={20}>
                      {
                        robotList?.map((robot: any, robotIndex: number) => {
                          return (
                            <Flex key={robot.robot_id + '' + robotIndex} className="card-list-item common-card" vertical justify={"space-between"}>
                              <div className="cl-content" onClick={() => this.handleSettingRobot(robot)}>
                                <Flex className="cl-content-top" gap={10} align="center">
                                  <img className="cl-content-img" src={robot.logo || defaultLogo} alt="" />
                                  <div className="cl-content-text">
                                    <div className="cl-content-title card-title">{robot.name}</div>
                                    {dispalyType === 'grid' ? this.renderLLmInfo(robot) : this.renderRobotIntroduction(robot)}
                                  </div>
                                </Flex>
                                {
                                  dispalyType === 'grid' ? this.renderRobotIntroduction(robot) : null
                                }
                              </div>
                              <>
                                <Flex className="cl-operate" justify={"space-between"} gap={20} align="center">
                                  {dispalyType === 'list' ? this.renderLLmInfo(robot) : null}
                                  <Flex gap={20} className={dispalyType === 'list' ? 'margin-left-auto cl-operate-left' : 'cl-operate-left'}>
                                    <Flex align="center" gap={4}>
                                      <Svgicon svgName="files" />
                                      <span>{robot.document_count}</span>
                                    </Flex>
                                    <Flex className="cl-operate-left-con" align="center" gap={4}>
                                      {
                                        robot.robot_type === 'PERSONAL' ? <Svgicon svgName="founder" /> : <Svgicon className="icon_org" svgName="icon_company" />
                                      }
                                      <div className="cl-name">{this.renderName(robot)}</div>
                                    </Flex>
                                  </Flex>
                                  {dispalyType === 'grid' ? <span className="margin-left-auto more"><Svgicon svgName="more_dot" /></span> : null}
                                </Flex>
                                <Flex className="cl-operate cl-operate-btns" justify={"space-between"} gap={10} align="center" wrap={false}>
                                  {
                                    dispalyType === "grid" ? this.renderOperateBtns(robot) :
                                      <>
                                        {this.renderLLmInfo(robot)}
                                        {this.renderOperateBtns(robot)}
                                      </>
                                  }
                                </Flex>
                              </>
                            </Flex>
                          )
                        })
                      }
                    </Flex>
                    :
                    // 空数据
                    <Empty className="page-empty" image={null} description={
                      <><div>{i18next.t('common.no-result')}</div>
                        <div>{i18next.t('robot-manage.search-result')}</div>
                      </>} />
                }
              </>
          }
        </div>
        { // 新建机器人弹窗 
          isCreateRobotModalOpen ?
            <CreateRobotModal visible={isCreateRobotModalOpen} onClose={() => this.handleToggleIsRobotModal(false)} onSumit={this.onSumitCreate} />
            : null
        }
        { // 发布弹窗
          isPublishModalOpen ?
            <PublishRobotModal visible={isPublishModalOpen} onClose={this.handleIsPublishCancel} onSumit={this.handleQueryRobotList}
              currentRobot={currentSelectRobot} frontChecked={frontChecked} backChecked={backChecked} treeData={treeData} /> : null
        }
        { // 删除确认弹窗
          <Modal width={272} title={i18next.t("robot-manage.confirm-del")} maskClosable={false} centered closable={false}
            okText={i18next.t("app.ok")} cancelText={i18next.t("app.cancel")} wrapClassName="common-confirm-modal"
            open={deleteRobotModal.visible} onCancel={() => this.handleDeleteModalCancel()} onOk={() => this.handleDeleteModalOk()}>
            <div>{i18next.t("robot-manage.confirm-delete-tip")}</div>
          </Modal>
        }
      </div>
    )
  }
}

const mapStateToProps = (state: any) => ({
  userInfo: state.app.userInfo,
  robotLlmModel: state.app.robotLlmModel,
  embeddingModelList: state.app.embeddingModelList,
  globalRegion: state.app.globalRegion,
  userPower: state.app.userPower,
  permissionMap: state.app.permissionMap,
  activeCompanyName: state.app.activeCompanyName,
});

const mapDispatchToProps = (dispatch: (e: any) => void) => ({
  // dispatch_api_delete_robot: (data = api_delete_robot_params) => dispatch(dispatch_api_delete_robot(data)),
});

export default connect(mapStateToProps, mapDispatchToProps)(RobotManage);
