@import "../../../../styles/mixin.scss";
.ant-select.user-model-cascader {
  min-width: 134px;
  height: 36px;
  width: auto;
  &.ant-select-single.ant-select-show-arrow .ant-select-selection-item{
    padding-inline-end: 24px;
  }
  .cascader-option-selected {
    display: flex;
    align-items: center;
    img {
      width: 20px;
      height: 20px;
      margin-right: 8px;
      border-radius: 50%;
      user-select: none;
    }
    svg{
      width: 20px;
      height: 20px;
      margin-right: 8px;
    }
    
    >span:first-child{
      display: flex;
      align-items: center;
    }
  }
  &.searching .ant-select-selection-item{
    visibility: visible!important;
  }
  .ant-select-selector {
    background: transparent;
    border-radius: 20px;
    border: 0;
    padding-left: 15px;
    box-shadow: none !important;
    .ant-select-selection-search-input {
      height: 100%;
      text-indent: 34px;
    }
  }
  .ant-select-arrow {
    color: var(--colorText);
    font-size: 16px;
  }
}
.custom-cascader-option {
  &.parent{
    align-items: center;
  }
  .cascader-option-image {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    flex-shrink: 0;
  }
  .svg svg{
    width: 20px;
    height: 20px;
    fill: currentColor;
    stroke: currentColor;
  }
  .cascader-option-parent-image {
    width: 16px;
    height: 16px;
  }
  .cascader-option-label {
    font-size: 14px;
    line-height: 20px;
    width: 100%;
    @include ellipsis;
  }
  .cascader-option-tag {
    font-weight: 400;
    font-size: 10px;
    color: var(--colorTextTertiary);
    line-height: 14px;
  }
  .cascader-option-texts{
    flex: 1;
    overflow: hidden;
  }
}

.user-model-cascader-dropdown {
  width: 453px;
  .ant-cascader-menu {
    height: 250px;
    &:first-child{
      padding-left: 16px;
      width: 166px;
      max-width:166px;
      border-inline-end: 1px solid var(--colorBorderSecondary);
    }
    &:last-child{
      .ant-cascader-menu-item{
        max-width: 270px;
      }
    }
  }
  &.searching {
    .ant-cascader-menu:first-child{
      max-width: 100%;
      border-inline-end: 0;
      .ant-cascader-menu-item{
        max-width: 100%;
      }
    }
  }
  .cascader-search-input {
    $space: 8px;
    margin: 8px auto;
    width: calc(100% - 32px);
    display: block;
    height: 32px;
    background: var(--colorFillSecondary);
    border-radius: 12px;
    font-weight: 500;
    font-size: 14px;
    padding-left: $space;
    padding-right: $space;
    color: var(--colorText);
    line-height: 20px;
    background-image: url("../../../../icons/png/robot/icon_search_light.png");
    background-repeat: no-repeat;
    background-position: 185px center;
    background-size: 16px; /* 图标大小 */
    text-indent: 24px;
    &::placeholder {
      padding-left: 174px;
      font-weight: 500;
      font-size: 14px;
      color: var(--colorTextTertiary);
      line-height: 20px !important;
      height: 20px;
    }
    &:focus {
      background-position: $space center;
      border-color: transparent;
      &::placeholder {
        padding-left: 0;
      }
    }
    &:hover{
      border-color: transparent;
    }
  }
  .ant-cascader-menu-item{
    font-size: 14px;
    line-height: 20px!important;
    padding: 6px 8px;
    border-radius: 12px;    
    // max-width: 270px;
    overflow: hidden;
    &:not(:last-child){
      margin-bottom: 6px;
    }
    .ant-cascader-menu-item-content{
      max-width: 100%;
    }
  }
}

.dark {
  .user-model-cascader-dropdown {
    .cascader-search-input {
      background-image: url("../../../../icons/png/robot/icon_search_dark.png");
    }
  }
}
