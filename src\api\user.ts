/* 登录注册模块*/
import HTTP from "@/utils/userRequest";
import * as actions from "@/store/action";
import { AxiosRequestConfig } from 'axios';
import {refreshTheme} from '@/theme'

// 验证邮箱地址是否存在
export const api_check_email_params = { email: "" };
export const api_check_email = (params=api_check_email_params): Promise<unknown>  => new Promise((resolve, reject) => {
  HTTP({
    method: 'get',
    url: "/system/auth/check-email",
    params,
    noTenant: true,
  }as AxiosRequestConfig).then(result => {
    resolve(result?.data || {});
  }).catch(err => reject(err))
})

// 验证手机号码是否存在
export const api_check_mobile_params = { areaCode: "",mobile:"" };
export const api_check_mobile = (params=api_check_mobile_params): Promise<unknown>  => new Promise((resolve, reject) => {
  HTTP({
    method: 'get',
    url: "/system/auth/check-mobile",
    params,
    noTenant: true,
  }as AxiosRequestConfig).then(result => {
    resolve(result?.data || {});
  }).catch(err => reject(err))
})


// 通过邮箱获取验证码	
export const api_get_email_code_params = { email: "",scene: '' };
export const api_get_email_code = (params=api_get_email_code_params): Promise<unknown> => new Promise((resolve, reject) => {
  HTTP({
    method: 'get',
    url: "/system/auth/email-code-requests",
    params:{...api_get_email_code_params,...params},
    handleCode: true,
    noTenant: true,
  }as AxiosRequestConfig).then(result => {
    resolve(result?.data || {});
  }).catch(err => reject(err))
})

//登录验证 邮箱+密码、邮箱+验证码、手机号码+密码、手机号码+验证码
export const api_verify_email_password_params = { 
  // email: "",
  // mobile: ""
  password: "",
  loginType: "", // EMAIL_PASSWORD, EMAIL_CODE, MOBILE_PASSWORD, MOBILE_CODE, SOCIAL
 };
export const api_auth_sessions = (data=api_verify_email_password_params, isInvite?: boolean) => (dispatch: any) => new Promise((resolve, reject) => {
  HTTP({
    url: '/system/auth/sessions',
    method: 'post',
    data,
    handleCode: true,
    noTenant: true,
  } as AxiosRequestConfig).then(result => {
    if (result?.data?.code === 0){
      // if (!isInvite){ // 非邀请场景，修改用户状态为已登录
        const {accessToken,...otherInfo  } = result.data?.data || {}
        dispatch(actions.change_app_userInfo({ isLogin: true, userInfo: otherInfo, token: accessToken }));
      // }
      
    } 
    resolve(result?.data || {});
  }).catch(err => reject(err))
})

// 通过手机号获取验证码
export const api_get_mobile_code_params = { areaCode: "", mobile:'',scene: '' };
export const api_get_mobile_code = (params=api_get_mobile_code_params): Promise<unknown> => new Promise((resolve, reject) => {
  HTTP({
    method: 'get',
    url: "/system/auth/mobile-code-requests",
    params,
    handleCode: true,
    noTenant: true,
  } as AxiosRequestConfig).then(result => {
    resolve(result?.data || {});
  }).catch(err => reject(err))
})


// 验证手机验证码
export const api_verify_phone_code_params = {
  "areaCode": "", /*国家地区编码*/
  "mobile": "", /*手机号码*/
  "code": "" /*验证码*/
};
export const api_verify_phone_code = (data=api_verify_phone_code_params): Promise<unknown> => new Promise((resolve, reject) => {
  HTTP({
    method: 'post',
    url: "/system/auth/sms-verification",
    data,
    noTenant: true,
  }as AxiosRequestConfig).then(result => {
    resolve(result?.data || {});
  }).catch(err => reject(err))
})

// 获取所有国家地区的区号列表	
export const api_get_phone_rules = (): Promise<unknown> => new Promise((resolve, reject) => {
  HTTP({
    method: 'get',
    url: "/system/global-phone-rules",
    noTenant: true,
  }as AxiosRequestConfig).then(result => {
    resolve(result?.data || {});
  }).catch(err => reject(err))
})

// 创建用户帐号 
export const api_registrations_params = {
  "registrationType": "",  //注册类型 email mobile
  "email": "", //Email地址
  "emailCode": "",  // Email 验证码
  "password": "",  // 密码
  "nickname": "", // 用户名
};
export const api_registrations = (data = api_registrations_params): Promise<unknown> => new Promise((resolve, reject) => {
  HTTP({
    url: '/system/auth/registrations',
    method: 'post',
    data,
    handleCode: true,
    noTenant: true,
    noToken: true,
  } as AxiosRequestConfig).then(result => {
    resolve(result?.data || {});
  }).catch(err => reject(err))
})

// 绑定用户手机号码
export const api_bind_user_phone_params = {
  "areaCode": "", //国家地区编码
  "mobile": "", //手机号码
  "code": "" // 验证码
};
export const api_bind_user_phone = (data = api_bind_user_phone_params): Promise<unknown> => new Promise((resolve, reject) => {
  HTTP({
    method: 'post',
    url: "/system/auth/user",
    data,
    handleCode: true,
    noTenant: true,
  }as AxiosRequestConfig).then(result => {
    resolve(result?.data || {});
  }).catch(err => reject(err))
})

// 退出登录  /system/auth/sessions DELETE 
export const api_logout_params = {  };
export const api_logout = (data = api_logout_params) => HTTP({
  url: '/system/auth/sessions',
  method: 'delete',
  noTenant: true,
} as AxiosRequestConfig);
export const dispatch_api_logout = (params: any,ImService?:any, isInvite?:boolean) => (dispatch: any) => new Promise(async(resolve, reject) => {
  const data = { ...api_logout_params, ...params }
  // 保证IM 退出登录
  if (ImService) await ImService.logout()
  api_logout(data).then(result => {
    resolve(result);
    dispatch(actions.change_app_userInfo({ isLogin: false, userInfo: {}, token: null }));
    if (!isInvite){
      dispatch(actions.change_app_companies([]));//清空企业列表
      refreshTheme(true, true) // 恢复主题为默认配置
    }
  }).catch(err => reject(err))
})

// 忘记密码-重置密码
export const api_reset_password_params = 
  {
    "resetType": "",/* mobile: 手机，email: 邮件*/
    "areaCode": "", /*国家地区编码*/
    "mobile": "", /*手机*/
    /*如果resetType为email，则存在"email"参数，没有areaCode和mobile参数*/
    "code": "",  /*验证码*/
    "newPassword": ""  /*新密码*/
  }

export const api_reset_password = (data = api_reset_password_params) => (dispatch: any) => new Promise((resolve, reject) => {
  HTTP({
    method: 'PATCH',
    url: "/system/auth/password-reset-requests",
    data,
    handleCode: true,
    noTenant: true,
  }as AxiosRequestConfig).then(result => {
    if (result?.data?.code === 0){
      const {accessToken,...otherInfo  } = result.data?.data || {}
      dispatch(actions.change_app_userInfo({ isLogin: true, userInfo: otherInfo, token: accessToken }));
    } 
    resolve(result?.data || {});
  }).catch(err => reject(err))
})

// 获取邀请详情
export const api_get_dept_invitation = (code: string): Promise<unknown> => new Promise((resolve, reject) => {
  HTTP({
      method: 'get',
      url: `/system/dept/invitation/${code}/detail`,
      noTenant: true,
      noToken: true,
      handleCode: true,
    }as AxiosRequestConfig).then(result => {
      resolve(result?.data || {});
    }).catch(err => reject(err))
})

// 接受邀请（已登录状态）
export const api_accept_invitation = (code: string): Promise<unknown> => new Promise((resolve, reject) => {
  HTTP({
      method: 'post',
      url: `/system/auth/invitations/${code}/apply`,
      noTenant: true,
      data:{}
    }as AxiosRequestConfig).then(result => {
      resolve(result?.data?.data || {});
    }).catch(err => reject(err))
})