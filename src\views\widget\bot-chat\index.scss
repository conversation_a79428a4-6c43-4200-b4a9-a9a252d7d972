@import "@/styles/mixin.scss";
#root {
    min-width: auto !important;
}
.widget-bot-con {
    height: 100%;
    width: 100%;
    // padding-bottom: 40px;
    // padding-right: 24px;
    // padding-left: 24px;
    // 亮色色值
    .is-open {
        // min-width: 450px;
        // min-height: 700px;
        height: 100%;
        width: 100%;
        overflow: hidden;
        border-radius: 10px;
        border-left: 1px solid var(--colorBorderSecondary);
        box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.1);
        background: var(--colorBgBase);

        .nav {
            padding: 12px;
            border-bottom: 1px solid var(--colorBorderSecondary);
            .bot-icon {
                width: 24px;
                height: 24px;
                border-radius: 50%;
                flex-shrink: 0;
            }
            .bot-name {
                @include font(14px, 22px, 500);
                flex: 1;
                @include ellipsis();
            }
            .zoom-out-icon {
                margin-left: auto;
                font-size: 16px;
                padding: 3px 8px;
                flex-shrink: 0;
                color: var(--colorText);
                cursor: pointer;
            }
        }
        $space: 20px;
        .chat-list {
            flex: 1;
            overflow: auto;
            padding: 16px $space;
            // 暗色色值
            .chat-list-item {
            }
            .chat-question {
                // margin: 16px 0;
                background: var(--colorBgLayout);
                border-radius: 12px;
                padding: 8px 12px;
                max-width: 100%;
                word-wrap: break-word;
                margin-left: auto;
            }
            .chat-answer {
                // margin-top: 16px;
                max-width: 100%;
                word-wrap: break-word;
                &:hover {
                    .chat-operation {
                        visibility: visible;
                    }
                }
            }
            .chat-operation {
                margin-left: auto;
                color: var(--colorTextTertiary);
                visibility: hidden;
                > span {
                    display: inline-block;
                    width: 30px;
                    height: 30px;
                    padding: 8px;
                    cursor: pointer;
                    &:hover {
                        background: var(--colorPrimaryBg);
                        border-radius: 8px;
                        color: var(--colorText);
                    }
                }
                .svg-icon {
                    font-size: 16px;
                }
            }
        }
        .chat-input-area {
            flex-shrink: 0;
            padding: 12px;
            margin-top: auto;
            // height: 100px;
            $minHeight: 36px;
            .chat-input {
                display: flex;
                flex: 1;
                min-height: $minHeight;
                height: 100px;
                border-radius: 12px;
                border: 1px solid var(--colorBorderSecondary);
                padding: 12px;
                .text-area {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    max-height: 100px;
                    position: relative;
                    margin-right: 12px;
                }
                .ant-input {
                    position: relative;
                    z-index: 100;
                    padding: 0;
                    // border-radius: 25px;
                    font-size: 14px;
                    line-height: 24px;
                    color: var(--colorText);
                    &:focus {
                        box-shadow: none;
                    }
                    &.ant-input-out-of-range {
                        color: var(--colorText);
                    }
                    &::placeholder {
                        background: transparent;
                        color: var(--colorTextTertiary);
                    }
                }
                .placeholder {
                    position: absolute;
                    z-index: 10;
                    top: 0;
                    left: 0;
                    width: 100%;
                    @include font(14px, 24px, 400, var(--colorTextTertiary));
                    @include ellipsis;
                    pointer-events: none; /* 禁止事件 */
                    /* 当输入框有值时，隐藏占位符 */
                    &.hidden {
                        opacity: 0;
                    }
                }
                img {
                    cursor: pointer;
                    width: $minHeight;
                    height: $minHeight;
                    user-select: none;
                }
                .max-count-error {
                    text-align: right;
                    color: var(--colorError);
                    font-size: 12px;
                    line-height: 17px;
                    margin-bottom: 16px;
                }
                .enter-img {
                    width: 32px;
                    height: 32px;
                    background: var(--colorPrimarySecondary);
                    border-radius: 50%;
                    margin-top: auto;
                    margin-left: auto;
                    cursor: pointer;
                    .svg-icon {
                        color: var(--colorPrimaryContrasting);
                    }
                }
                .enter-img-disabled {
                    opacity: 0.2;
                }
            }
        }
        .preview-text {
            padding: 8px 0 24px;
            .preview-icon {
                text-align: center;
                img {
                    width: 48px;
                }
            }
            .preview-title {
                @include font(18px, 24px, 600);
                text-align: center;
            }
            .preview-desc {
                @include font(14px, 22px, 400);
                text-align: center;
            }
        }
    }
    .toggle-area {
        padding-top: 24px;
        > div {
            width: 48px;
            height: 48px;
            cursor: pointer;
            background: #fff;
            border-radius: 50%;
            font-size: 20px;
            box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
        }
    }
}
.copy-tooltip {
    .copyed .anticon {
        color: var(--colorSuccess);
        margin-right: 4px;
    }
    .ant-tooltip-inner {
        padding: 8px 10px;
    }
}
