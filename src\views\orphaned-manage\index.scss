@import "@/styles/mixin.scss";

.orphaned-manage {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    // background: var(--colorBgBase);
    .page-nav {
        flex-shrink: 0;
        width: 100%;
        height: var(--page-nav-height);
        padding: 0 20px;
        .nav-title {
            flex-shrink: 0;
            font-weight: 600;
            font-size: 18px;
            color: var(--colorText);
            line-height: 24px;
        }
    }
    .page-main {
        flex: 1;
        overflow: auto;
        padding: 20px 40px;
        padding-right: 30px;
    }
    .page-empty{
        height: 600px;
    }
    $space: 12px;
    // 卡片/列表样式
    
    .card-list.grid {
        display: flex !important;
    }

    .card-list .infinite-scroll-component {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
    }

    // 卡片/网格样式
    .card-list-item {
        min-height: 163px;
        flex-shrink: 0;
        padding: 0;
        border: 1px solid transparent;
        // background: var(--colorFillQuaternary);
        width: 392px;
        &:hover {
            border-color: var(--colorBorder);
            // .cl-operate {
            //     display: none;
            // }
            // .cl-operate.cl-operate-btns {
            //     display: flex;
            //     padding-top: 7px;
            //     padding-bottom: 14px;
            // }
        }
    }
    .cl-content {
        flex: 1;
        padding: $space 16px 0;
    }
    .cl-content-img {
        width: 48px;
        height: 48px;
        border-radius: 10px;
        border: 1px solid var(--colorBorderSecondary);
        flex-shrink: 0;
    }
    .cl-content-text {
        flex: 1;
    }
    .cl-content-title {
        margin-bottom: 4px;
        @include ellipsis-multiline(1);
    }
    .cl-content-llm {
        font-weight: 400;
        font-size: 12px;
        color: var(--colorTextTertiary);
        line-height: 20px;
        .svg-icon {
            font-size: 16px;
        }
        @include ellipsis-multiline(1);
    }
    .cl-content-bottom {
        margin: 10px 0;
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
        color: var(--colorTextSecondary);
        @include ellipsis-multiline(2);
    }
    .cl-operate {
        padding: 16px 0;
        width: calc(100% - $space * 2);
        margin: 0 auto;
        border-top: 1px solid var(--colorBorderSecondary);
        font-weight: 400;
        font-size: 12px;
        color: var(--colorTextTertiary);
        line-height: 20px;
        .svg-icon {
            font-size: 16px;
        }
        .more {
            width: 32px;
            height: 32px;
            padding: 8px;
            color: var(--colorIcon);
        }
        .icon_org{
            font-size: 12px;
        }
    }
    .cl-operate-left{
        flex: 1;
        overflow: hidden;
        .cl-operate-left-con{
            flex: 1;
            overflow: hidden;
        }
        .cl-name{
            flex: 1;
            @include ellipsis;
        }
    }
    .cl-operate-btns {
        display: none;
    }
    .margin-left-auto {
        margin-left: auto;
    }
}
