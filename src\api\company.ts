import HTTP, { APP_HTTP } from "@/utils/userRequest";
import * as actions from "@/store/action";
import { AxiosRequestConfig } from 'axios';
import store from "@/store";

// 创建公司
export const api_create_company = (data: any): Promise<unknown> => new Promise((resolve, reject) => {
    HTTP({
        method: 'post',
        url: "/system/tenants/create-company",
        data,
        noTenant: true
    } as AxiosRequestConfig).then(result => {
        const res = result?.data || {}
        resolve(res)
    }).catch(err => reject(err))
})

// 获取已加入的公司列表	
export const api_get_mycompanies = (activeCompany?: any) => (dispatch: any) => new Promise((resolve, reject) => {
    APP_HTTP({
        method: 'get',
        url: "/system/tenants/my-companies",
        params: {},
        noTenant: true
    } as AxiosRequestConfig).then(result => {
        const res = result?.data || {}
        const datas = res?.data || []

        // 更新store企业列表数据
        const companies: any = datas.map((i: any) => ({ id: i.id, companyName: i.companyName, darkLogo: i.darkLogo, lightLogo: i.lightLogo, domainName: i.domainName }))
        dispatch(actions.change_app_companies(companies))
        // 设置 选中的企业 数据，先取缓存，没有再默认第一项: 个人空间
        const storeCompanyId = activeCompany?.id || store.getState().app.activeCompanyId
        const storeCompanyName = activeCompany?.companyName || store.getState().app.activeCompanyName
        const isExist = datas.filter((i: any) => String(i.id) === String(storeCompanyId)).length > 0
        let activeId = storeCompanyId
        let activeName = storeCompanyName
        // 如果都没有，则使用个人空间
        if ((!isExist || !storeCompanyId)) {
            activeId = '0'
            activeName = '个人空间'
        }
        // console.log('activeId', activeId)
        dispatch(actions.change_app_active_company({ id: activeId, name: activeName }))
        resolve(res)
    }).catch(err => reject(err))
})

// 公司搜索
export const api_search_company = (params: any): Promise<unknown> => new Promise((resolve, reject) => {
    HTTP({
        method: 'get',
        url: "/system/tenants/search",
        params,
        noTenant: true
    } as AxiosRequestConfig).then(result => {
        const res = result?.data || {}
        resolve(res)
    }).catch(err => reject(err))
})

//申请加入公司
export const api_join_company = (data: any): Promise<unknown> => new Promise((resolve, reject) => {
    HTTP({
        method: 'post',
        url: "/system/tenants/apply",
        data,
        handleCode: true,
        noTenant: true
    } as AxiosRequestConfig).then(result => {
        const res = result?.data || {}
        resolve(res)
    }).catch(err => reject(err))
})