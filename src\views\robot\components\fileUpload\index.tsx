
import React, { useEffect, useState, useRef, forwardRef, useImperativeHandle } from "react";
import {connect} from "react-redux";

import type { UploadProps } from "antd";
import {App, message, Upload, Tooltip, Popover, Progress, Spin,notification, Flex, Tabs,TabsProps, Button} from "antd"; 
import { FileOutlined, LoadingOutlined } from "@ant-design/icons";
import classnames from "classnames";
import i18next from "i18next";
import "./style/index.scss";
import { getFileTypeInfo } from './util'
import deleIcon from "../../../../assets/delete-icon.png";

import {
    dispatch_api_upload_document,
    dispatch_api_get_user_uploaded_files,
    dispatch_api_delete_attachment,
    dispatch_api_clear_current_filebox
  } from "@/api/filebox";

import Svgicon from "@/components/svgicon"

const FileUploadComponent = forwardRef((props: any, ref:React.LegacyRef<HTMLDivElement> | any) => {
    // console.log('ref', ref)
    /**自定义暴露给父组件的实例值，接收父组件通知调用 */
    useImperativeHandle(ref,()=>({
        // 初始化
        emit_init:(robotId?:any) => {
          setLoading(true)
          setChatList([])
          setOpenUploadedFiles(false)
          setOpenActionMenuPopover(false)
            // console.log('父组件通知 上传组件 初始化',robotId)
          if (robotId!==undefined && robotId!==null) common_params.robotId=robotId // 更新robotId
          getUploadedFiles(false) // 获取当前机器人上传的文件
        },
        emit_init_error:()=>{
            setLoading(false)
        },
        // 更新文件
        emit_updateFiles:(fileResource: any[]) =>{
            // console.log('父组件通知 上传组件更新文件',fileResource)
            handleFileFromParent(fileResource)
        },
        // 清空filebox
        emit_clearFileBox(){
            // 清空选择的历史对话
            setChatList([])
            // 请求清空fileList
            if (fileList.length>0){
                updateFileList([])
                handleClearFileList(false, true)
            }
        },
        emit_destroy_notification(){
            // console.log('父组件通知 emit_destroy_notification')
            // notificationApi.destroy()
        },
        // 更新选择对话 列表
        emit_updateSelectChat:(chats: any[]) =>{
            setLoading(false)
            setChatList(chats)
            setActiveTab("historyChat")
            setOpenUploadedFiles(true)
        },
        emit_cancelSelectChat:() =>{
        },
    }))

    // 全局变量
    let uploadingFileTotal = 0 // 每次上传操作时，文件的总份数
    let batchUploadFiles:any[] = [] // 批量上传的文件

    const uploadUrl = '#'
    /* props */
    const { robotId} = props;
    const initTabItems = [
        {
            key: 'attachments',
            label: i18next.t("upload.attachFile"),
            // forceRender: true,
            children: ""
        },
        {
            key: 'historyChat',
            label: i18next.t("home.historical-chat"),
            // forceRender: true,
            children: ""
        }
    ]

    /* state */
    const [fileList, setFileList] = useState<any[]>([]); // 上传的文件
    const [fileCountLimit] = useState(5); // 文件个数限制
    const [fileSizeLimit] = useState(22.5); // 文件大小限制 20M
    const [fileTypeAccept] = useState(["txt", "pdf", "docx"]); // 文件类型限制
    const [loading, setLoading] = useState(true); // 上传进度条
    const [openUploadedFiles, setOpenUploadedFiles] = useState(false); // 是否显示已上传文件弹窗
    const [openActionMenuPopover, setOpenActionMenuPopover] = useState(false); // 是否显示上传操作菜单弹窗
    const [isClearAll, setIsClearAll] = useState(false); // 是否 清空
    const [tabItems, setTabItems] =useState<TabsProps['items']>(initTabItems)
    const [activeTab, setActiveTab] = useState("attachments"); // tab active
    const [chatList, setChatList] = useState<any[]>([]); // 选择的对话
    const [api, contextHolder] = notification.useNotification( {stack: { threshold:10}});

    // 额外公共参数
    const common_params:any = {
        seq:0,
        robotId
    }
    // console.log('common_params', common_params)

    // 上传属性
    const uploadProps: UploadProps = {
        name: "file",
        multiple: true,
        accept: fileTypeAccept.map((i) => "." + i).join(","),
        action: uploadUrl,
        // action: "https://660d2bd96ddfa2943b33731c.mockapi.io/api/upload",
        headers: {
            authorization: "authorization-text",
        },
        // data: common_params,
        // fileList: fileList,
        showUploadList: false,
        customRequest:async (options:any) => {
            const { onSuccess, onError, file, onProgress } = options;
            // 自定义批量上传
            batchUploadFiles.push(file)
            if (batchUploadFiles.length === uploadingFileTotal){
                handleBatchUpload(batchUploadFiles).then(()=>{
                    onSuccess(true)
                }).catch(err=> console.log(err))
            }
          },
        maxCount: fileSizeLimit,
        // 上传前检验
        beforeUpload(file: any, uploadFileList: any) {
            if (uploadingFileTotal === 0){
                uploadingFileTotal = uploadFileList.length
            }
            const isValidFileCount = validateFileCount(uploadFileList, true); // 校验文件个数，批量操作，要么成功，要么失败
            // console.log('beforeUpload fileName', uploadFileList.length, isValidFileCount)
            if (!isValidFileCount){
                uploadingFileTotal = 0
                return false || Upload.LIST_IGNORE
            }
            const isvalidateFileSizeFormat = validateFileSizeFormat(file) // 校验格式和大小
            if(!isvalidateFileSizeFormat){
                uploadingFileTotal--
                uploadingFileTotal = uploadingFileTotal<0 ? 0 : uploadingFileTotal 
                return false || Upload.LIST_IGNORE
            }
            return true
        }
    };

    /* methods */

    // 批量上传请求
    const handleBatchUpload = async(fileList:any) =>{
        return new Promise((resolve, reject) => {
            const formData = new FormData();
            fileList.forEach((file:any) => {
                formData.append('files', file);
            });
            common_params.robotId = robotId
            const {llm,isHomeBot } = props
            const currentUsedTokens = props.getTokenUsed() // 当前已使用token = 多选总token + filebox已上传文件总token
            formData.append("currentUsedTokens", currentUsedTokens)
            if (isHomeBot){
                const llmId = llm?.length>0 ? llm[llm.length-1] : null
                formData.append("llmId", llmId)
            }
            Object.keys(common_params).forEach((key:string) => {
                formData.append(key, common_params[key]);
              });
            setLoading(true);
            props.onUpdate('filebox_request_status',true)
            // 请求
            props.dispatch_api_upload_document(formData).then((res:any) =>{
                batchUploadFiles=[] // 重置
                uploadingFileTotal = 0
                setLoading(false)
                setActiveTab("attachments")
                getUploadedFiles(true) // 获取最新已上传的文件
                // setOpenUploadedFiles(true)
                // setOpenActionMenuPopover(false)
                props.onUpdate('filebox_request_status',false)
                const responses:any = res?.response
                if (responses?.length>0){
                    responses.forEach((per_res:any) => {
                        if(per_res?.status != 1){
                            handleResponseErrorStatus(per_res?.status, per_res.filename)// 上传失败
                        }
                    })
                }
                resolve(responses)
            }).catch((error:any)=>{
                batchUploadFiles=[] // 重置
                uploadingFileTotal = 0
                getUploadedFiles(true) // 获取最新已上传的文件
                setLoading(false)
                props.onUpdate('filebox_request_status',false)
                reject(error)
            })
        })
    }

    // 处理上传status，提示对应错误信息
    const handleResponseErrorStatus = (status:number, filename?:string)=>{
        let reason:any = null
        switch (status){
            case 4: // 上传失败
                reason = i18next.t("upload.upload-failed")
            break;
            case 5: // 文件类型不支持
                reason = i18next.t("upload.file-format-error")
            break;
            case 6: // 文档为空
                reason = i18next.t("upload.file-empty-error")
            break;
            case 7: // token超出
                reason = i18next.t("upload.file-token-excced-error1") // + `${fileList?.length>0 ? (", " + i18next.t("upload.file-token-excced-error2")): ''}`
            break;
            case 8: // 文件名过长
                reason = i18next.t("upload.file-name-long-error")
                break
            default:
                reason = i18next.t("upload.upload-failed")
                break
        }
        api.error(
            {
                message: filename,
                // key: "FileUploadError",
                className: "common-notification-error upload-notification-error",
                description: reason,
            }
        )
    }

    const getUploadedFiles = (openPopover?:boolean, hideLoading?:boolean)=>{
        const activeRobotId = props?.getActiveRobotId() // 获取实时最新的robotId，针对上传大文件时，切换其他机器人的场景
        return new Promise<void>((resolve, reject) => {
            common_params.robotId = robotId
            // 检验robotId 是否一致
            if (activeRobotId && activeRobotId!=robotId){
                return resolve()
            }
            // console.log('common_params.robotId', common_params.robotId)
            !hideLoading && setLoading(true)
            props.dispatch_api_get_user_uploaded_files(common_params).then((res:any) =>{
                if (res?.robotId !== common_params.robotId) return
                let attachments = res?.attachmentList || []
                if ((attachments.length>0 || chatList.length>0) && openPopover){
                    if (!openUploadedFiles) setOpenUploadedFiles(true)
                } else{
                    if (openUploadedFiles) setOpenUploadedFiles(false)
                }
                attachments = attachments.map((file:any)=>{
                    file.ref = React.createRef()
                    return file
                })
                updateFileList(attachments)
                resolve(res)
                !hideLoading && setLoading(false)
            }).catch((error:any)=>{
                !hideLoading && setLoading(false)
                resolve(error)
            })
        })
    }

    // 文件个数校验
    const validateFileCount = (uploadFilelist: any, showTip?:boolean): boolean => {
        const totalFileList = [...fileList, ...uploadFilelist];
        if (totalFileList.length > fileCountLimit) {
            if (!showTip) return false
            notification.destroy("FileCountExceed");
            notificationErrorApi({
                key: "FileCountExceed",
                message: `${i18next.t("upload.upload-failed")}: ${i18next.t("upload.file-count-error")}`,
            })
            return false;
        }
        return true;
    };

    // 文件大小、格式校验
    const validateFileSizeFormat = (file: any, fileList?: any): boolean => {
        // 格式
        const fileType = file.name.substring(file.name.lastIndexOf(".") + 1);
        const isfileFormatValid = fileTypeAccept.includes(fileType);
        // 大小
        const isfileSizeValid = file.size / 1024 / 1024 < fileSizeLimit;
        let errorMessage = null;
        const fileName = file.name;
        if (!isfileFormatValid && !isfileSizeValid) {
            errorMessage = `${i18next.t("upload.file-format-size-error")}`;
        } else if (!isfileFormatValid) {
            errorMessage = `${i18next.t("upload.file-format-error")}`;
        } else if (!isfileSizeValid) {
            errorMessage = `${i18next.t("upload.file-size-error")}`;
        }
        if (errorMessage) {
            notificationErrorApi({
                message: fileName,
                description: errorMessage,
            })
        }
        return isfileFormatValid && isfileSizeValid;
    };

    const updateFileListProp = (prop:string, value:any, index:number)=>{
        setFileList(prevItems => prevItems.map((item, i) => 
            i === index ? { ...item, [prop]: value } : item
        ))
    }

    const updateChatListProp = (prop:string, value:any, index:number)=>{
        setChatList(prevItems => prevItems.map((item, i) => 
            i === index ? { ...item, [prop]: value } : item
        ))
    }

    // 单个删除已上传的文件
    const handleDeleteFile = async(file: any, index:number) => {
        updateFileListProp("isDeleteing", true, index)
        const data ={
            ...common_params,
            robotId: robotId,
            attachmentId:file.attachmentId,
        }
        const res = await props.dispatch_api_delete_attachment(data).catch(()=>{
            updateFileListProp("isDeleteing", false, index)
        })
        judgeOpenPopoverStatus()
        getUploadedFiles(true, true).then(()=>{
            updateFileListProp("isDeleteing", false, index)
        }).catch(()=>{
          updateFileListProp("isDeleteing", false, index)
        })
        if (!res) return
    };

    // 单个删除历史对话
    const handleDeleteChat = async (chat:any, index:number) =>{
        updateChatListProp("isDeleteing", true, index)
        props.onUpdate("clear_one_historychat", {target:chat, index})
        const newChatList = chatList.filter((item,i)=> chat.messageId !=item.messageId)
        // console.log('newChatList', newChatList.length)
        setChatList(newChatList)
        judgeOpenPopoverStatus(newChatList.length)
    }

    // 自定义 Popover 弹出层的挂载容器
    const getPopupContainer = (triggerNode: any) => {
        return triggerNode.parentNode;
    };

    // 文件名后2个字+文件名 需要显示，其他文字超出显示省略号
    const truncateFileName = (element:any, index:number) =>{
        updateFileListProp("isTruncate", true, index)
        // console.log('截断处理', index)
        const maxWidth = 300
        const fullText = element.textContent;
        const lastDotIndex = fullText.lastIndexOf('.');
        const extension = lastDotIndex !== -1 ? fullText.slice(lastDotIndex) : '';
        const name = lastDotIndex !== -1 ? fullText.slice(0, lastDotIndex) : fullText;
      
        const canvas = document.createElement('canvas');
        const context = canvas.getContext('2d');
        if (!context) return
        context.font = window.getComputedStyle(element).font;
      
        // 保留的部分：最后两个字符 + 后缀名
        const reservedPart = name.slice(-2) + extension;
        const reservedWidth = context.measureText(reservedPart).width;
      
        // 如果全文已经适合，或者保留部分已经超过最大宽度，则不做处理
        if (context.measureText(fullText).width <= maxWidth || reservedWidth >= maxWidth) {
          return;
        }
      
        // 计算可用于前部的宽度
        const availableWidth = maxWidth - reservedWidth - context.measureText('...').width;
      
        let left = 0;
        let right = name.length - 2; // 减去2是因为我们要保留最后两个字符
      
        while (left <= right) {
          const mid = Math.floor((left + right) / 2);
          const testText = name.slice(0, mid);
          
          if (context.measureText(testText).width <= availableWidth) {
            left = mid + 1;
          } else {
            right = mid - 1;
          }
        }
      
        const truncatedText = name.slice(0, right) + '...' + reservedPart;
        element.textContent = truncatedText;
    }

    const handleWithDataPopoverOpen =(open: boolean)=>{
        setOpenUploadedFiles(open)
        handleActiveTab()
    }
    const handleEmptyPopoverOpen =(open: boolean)=>{
        setOpenActionMenuPopover(open)
    }

    // 更新fileList
    const updateFileList = (attachments:any) =>{
        try {
            let result:any = attachments
            if (result.length>0){
                result = result.map((i:any) => {
                    i.isDeleteing = false // 删除loading
                    i.isTruncate = false // 是否做过截断判断和处理
                    return i
                })
                // console.log('更新fileList result2', result)
                setFileList(result)
            }else{
                setFileList(result)
            }
            
            props.onUpdate('filebox_attachments',result)
        } catch (error) {
            console.log(error)
        }
    }

    const handleFileNameOverflow = ()=>{
        fileList.forEach((file:any, index:number)=>{
            if (file?.ref?.current && !file.isTruncate) truncateFileName(file.ref?.current, index)
        })
    }

    // 上传文件错误提示方法
    const notificationErrorApi = (obj:any) =>{
        notification.error({
            ...obj,
            className: "common-notification-error upload-notification-error",
            stack:{
                threshold:10
            }
        });
    }

    // 处理来自父组件的fileList（粘贴或复制）
    const handleFileFromParent = (fileResource: any) =>{
        if (fileResource && fileResource.length>0){
            // console.log('处理来自父组件的 fileList：' + origin, fileResource)
            const uploadFileList = [...fileResource]
            const isValidFileCount = validateFileCount(uploadFileList, true); // 校验文件个数，批量操作，要么成功，要么失败
            if (!isValidFileCount) { // 文件个数不通过
                uploadingFileTotal = 0
                updateFileList(fileList)
                return
            }
            // 逐一校验文件格式和大小
            // const promises:any[] = []
            const validFiles:any[] = [] 
            uploadFileList.forEach((file:any) =>{
                const isvalidateFileSizeFormat = validateFileSizeFormat(file) // 校验格式和大小
                if(isvalidateFileSizeFormat){
                    // 校验通过, 新增上传请求
                    // promises.push(handleUpload(file))
                    validFiles.push(file)
                }
            })
            // 发起批量上传请求
            if (validFiles.length>0) handleBatchUpload(validFiles).then(()=>{}).catch(err=> console.log(err))
        }
    }
    
    // 清除操作
    const handleClear = ()=>{
        if (activeTab === "attachments"){
            handleClearFileList(true)
        }else if (activeTab === "historyChat"){
            setChatList([])
            props.onUpdate("clear_all_historychat")
            judgeOpenPopoverStatus(0)
        }
    }

    // 清空附件
    const handleClearFileList = (needRefresh?: boolean,  hiddenPopover?:boolean)=>{
        const p ={
            ...common_params,
            robotId:robotId
        }
        setIsClearAll(true)
        props.dispatch_api_clear_current_filebox(p).then((res:any) =>{
            updateFileList([])
            if (!hiddenPopover) judgeOpenPopoverStatus(undefined,0)
            setIsClearAll(false)
            if (needRefresh) getUploadedFiles(true) // 获取最新已上传的文件
        }).catch(()=>{
            setIsClearAll(false)
        })
    }

    // 弹窗状态判断
    const judgeOpenPopoverStatus = (chatListLength?:number, fileListLength?:number)=>{
        const len1 = chatListLength!==undefined? chatListLength: chatList.length
        const len2 = fileListLength!==undefined? fileListLength: fileList.length
        const isEmpty = len1 === 0 && len2===0
        setOpenUploadedFiles(!isEmpty)
        setOpenActionMenuPopover(isEmpty)
    }

    // 切换tab
    const onChangeTab = (key: string) => {
        setActiveTab(key)
    }

    // 选择会话
    const handleSelectChat=() => {
        props.onUpdate("open_select_chat")
    }

    const handleActiveTab =()=>{
        let _activeTab = "attachments"
        if (fileList.length === 0 && chatList.length>0){
            _activeTab = "historyChat"
        }
        // console.log("_activeTab", _activeTab)
        setActiveTab(_activeTab)
    }

    /**useEffect */
    
    // 监听当前组件打开fileList弹框时
    useEffect(()=>{
        if (openUploadedFiles){
            const newfileList = fileList.map((file:any)=>{
                if (!file.ref || !file.ref?.current) file.ref = React.createRef()
                return file
            })
            setFileList(newfileList)
            handleFileNameOverflow()
        }
    },[openUploadedFiles, fileList.length])
    
    useEffect(()=>{
        setTabItems([...initTabItems])
    }, [props.globalLang])

    return (
        <div className={"ama-upload"}>
            {contextHolder}
            {/* loading*/}
            {/* {loading ?  (
                <div className="upload-trigger loading">
                    <Spin
                        indicator={<LoadingOutlined style={{ fontSize: 20 }} spin />}
                    />
                </div>
            ) : null} */}
            {/* filebox为空 */}
            {
                !loading && ((!fileList || fileList.length === 0 )&& (!chatList || chatList.length === 0)) ? (
                    <Popover
                        trigger="click"
                        arrow={false}
                        // defaultOpen={true}
                        open={openActionMenuPopover}
                        onOpenChange={handleEmptyPopoverOpen}
                        overlayClassName={classnames(["upload-operate-popover", {'popover-loading': loading}])}
                        placement="topLeft"
                        getPopupContainer={getPopupContainer}
                        zIndex={props.isDragingFileToPanel ? 999 : 1100}
                        content={
                            <div>
                                <Upload {...uploadProps}>
                                    <div className="ope-item">
                                        <div className="ope-icon">
                                            <Svgicon svgName="icon_upload_files"/>
                                        </div>
                                        <div className="ope-text">
                                            <span>{i18next.t("upload.upload-file")}</span>
                                            <span>{i18next.t("upload.upload-trigger-tip1")} {i18next.t("upload.upload-trigger-tip2")}</span>
                                        </div>
                                    </div>
                                </Upload>
                                <div className="ope-item" onClick={handleSelectChat}>
                                        <div className="ope-icon">
                                            <Svgicon svgName="icon_chat_historical"/>
                                        </div>
                                        <div className="ope-text">
                                            <span>{i18next.t("app.select-conversation")}</span>
                                            <span>{i18next.t("upload.history-chat-tip")}</span>
                                        </div>
                                </div>
                            </div>
                        }
                    >
                        <div className="upload-trigger">
                            <Svgicon svgName="icon_plus"/>
                        </div>
                    </Popover>
                ) : null
            }
            {/* filebox不为空 */}
            <Popover
                trigger="click"
                arrow={false}
                defaultOpen={true}
                open={openUploadedFiles}
                onOpenChange={handleWithDataPopoverOpen}
                overlayClassName={classnames(["uploaded-list-popover", {'popover-loading': loading}])}
                placement="topLeft"
                getPopupContainer={getPopupContainer}
                zIndex={props.isDragingFileToPanel ? 999 : 1100}
                content={
                    <div className="uploaded-list">
                        <Tabs className="common-tab without-border" activeKey={activeTab} defaultActiveKey={"attachments"} items={tabItems} onChange={onChangeTab} indicator={{ size: (origin) => origin / 3 }}></Tabs>
                        {/* list */}
                        <div className="bottom">
                            { // 附加文件list
                                activeTab === 'attachments' ? 
                                    fileList.length>0? fileList.map((file: any, index: number) => {
                                        return (
                                            <div className="file-item" key={file.attachmentId + '-' +index}>
                                                <div ref={file.ref} className="file-name">{file.fileName}.{file.fileType}</div>
                                                <div className="file-type-size">
                                                    <Flex className="type-image" align="center">
                                                        {getFileTypeInfo(file.fileType).image ? (
                                                            <img
                                                                draggable="false"
                                                                src={getFileTypeInfo(file.fileType).image}
                                                                alt=""
                                                            />
                                                        ) : (
                                                            <FileOutlined />
                                                        )}
                                                    </Flex>
                                                    <span className="type-text">
                                                        {getFileTypeInfo(file.fileName).text}
                                                    </span>
                                                    <span className="size">
                                                        {/* ·{formatFileSize(file.fileLength)} */}
                                                        ·&nbsp;{ file?.fileLength>0 ? `${file.fileLength} ${file.fileLength<=1 ?'token':'tokens'}` : file.fileLength }
                                                    </span>
                                                </div>
                                                { file.isDeleteing?
                                                    <Spin
                                                        className="operate" indicator={<LoadingOutlined style={{ fontSize: 16 }} spin />}
                                                    />
                                                    :
                                                    <img draggable="false" className="operate delete" onClick={() => handleDeleteFile(file, index)} src={deleIcon} alt=""/>
                                                }
                                            </div>
                                        );
                                    })
                                :
                                <div className="empty-tip">
                                    {i18next.t("upload.upload-trigger-tip1")}<br />
                                    {i18next.t("upload.upload-trigger-tip2")}
                                </div>
                                :null
                            }
                            {// 历史对话list 
                                activeTab === 'historyChat' ? 
                                    chatList.length>0?chatList.map((chat: any, index: number) => {
                                        return (
                                            <div className="file-item chat-item" key={chat.messageId + '-' +index}>
                                                <div className="chat-q">{ chat.question}</div>
                                                <div className="chat-a">{ chat.assistant}</div>
                                                { chat.isDeleteing?
                                                    <Spin
                                                        className="operate" indicator={<LoadingOutlined style={{ fontSize: 16 }} spin />}
                                                    />
                                                    :
                                                    <img draggable="false" className="operate delete" onClick={() => handleDeleteChat(chat,index)} src={deleIcon} alt=""/>
                                                }
                                            </div>
                                        );
                                    }):
                                    <div className="empty-tip">
                                        {i18next.t("upload.history-chat-tip")}
                                    </div>
                                    : null
                            }
                        </div>
                        <div className="bottom-operate">
                            <Flex gap={10}>
                                <Button onClick={() =>handleClear()} 
                                disabled={isClearAll || (activeTab === 'attachments' && fileList.length===0) || (activeTab === 'historyChat' && chatList.length===0)}
                                className="clear-btn" color="danger" variant="filled" icon={<Svgicon svgName="icon_delete" />} size="middle">
                                    {i18next.t("upload.clear")}
                                </Button>
                                {
                                    activeTab === 'attachments'?
                                    <Upload
                                        {...uploadProps}
                                        disabled={fileList.length === fileCountLimit}
                                     >
                                        <Tooltip
                                            className="noupload-trigger-tooltip"
                                            placement="top"
                                            arrow={false}
                                            title={fileList.length === fileCountLimit? i18next.t("upload.file-excced"): ""}
                                        >
                                            <Button className="plus-btn" variant="filled" color="default" icon={ <Svgicon svgName="icon_plus"/>} size="middle">
                                                {fileList.length>0 ?i18next.t("upload.continue-uploading"):i18next.t("upload.upload-file")}
                                            </Button>
                                        </Tooltip>
                                    </Upload>:null
                                }
                                {
                                    activeTab === 'historyChat'?
                                    <Button onClick={handleSelectChat} className="plus-btn" variant="filled" color="default" icon={ <Svgicon svgName="icon_plus"/>} size="middle">
                                        {chatList.length>0?i18next.t("upload.continue-to-select"):i18next.t("app.select-conversation")}</Button>:null
                                }
                            </Flex>
                        </div>
                    </div>
                }
            >
                <div>
                    { 
                        loading?
                        ( // loading
                            <div className="upload-trigger loading">
                            <Spin
                                indicator={<LoadingOutlined style={{ fontSize: 20 }} spin />}
                            />
                        </div>
                        ):
                        (// 有上传文件，并且无上传中的文件
                            fileList.length !== 0 || chatList.length !== 0 )? (
                            <div className={classnames(["upload-trigger", "document"])}>
                                <Svgicon svgName="icon_attachment" />
                            </div>
                        ) : null
                    }
                </div>
            </Popover>
        </div>
    );
});
const mapStateToProps = (state: any) => ({
    userInfo: state.app.userInfo,
    globalRegion: state.app.globalRegion,
    globalLang: state.app.globalLang,
  });
  
const mapDispatchToProps = (dispatch: any) => ({
    dispatch_api_upload_document: (data:any) => dispatch(dispatch_api_upload_document(data)),
    dispatch_api_get_user_uploaded_files: (data:any) => dispatch(dispatch_api_get_user_uploaded_files(data)),
    dispatch_api_delete_attachment: (data:any) => dispatch(dispatch_api_delete_attachment(data)),
    dispatch_api_clear_current_filebox: (data:any) => dispatch(dispatch_api_clear_current_filebox(data))
})

// 使用 connect 连接 Redux store，并启用 forwardRef
const ConnectedCounter = connect(mapStateToProps, mapDispatchToProps, null, { forwardRef: true })(FileUploadComponent);
export default ConnectedCounter;
