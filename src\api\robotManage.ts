import HTTP from "../utils/request";

// 新增机器人
export const api_create_bots_params = {
  seq: 0,
  robotType: "",
  robotName: "",
  robotLogo: "",
  robotIntroduction: "",
  robotWelcomeMsg: "",
  robotIdentityPrompt: "",
  robotLlmModel: "",
  intelligence: "",
  relevance: "",
  konwledgeCount: "",
  isIrrelevantQuestions: 0,
  irrelevantRespond: ""
};
export const api_create_bots = (data = api_create_bots_params) => HTTP.post("robot/createRobot", data);

export const api_query_robot_params = {
  seq: 0,
  robotId: ""
};
export const api_query_robot = (data = api_query_robot_params) => HTTP.post("robot/queryRobot", data);

export const api_update_robot_params = {
  seq: 0,
  robotId: "",
  robotType: "",
  robotName: "",
  robotLogo: "",
  robotIntroduction: "",
  robotWelcomeMsg: "",
  robotIdentityPrompt: "",
  robotLlmModel: [],
  intelligence: "",
  relevance: "",
  konwledgeCount: "",
  isIrrelevantQuestions: 0,
  irrelevantRespond: ""
};
export const api_update_robot = (data = api_update_robot_params) => HTTP.post("robot/updateRobot", data);

export const api_delete_robot_params = {
  seq: 0,
  robotId: ""
};
export const api_delete_robot = (data = api_delete_robot_params) => HTTP.post("robot/deleteRobot", data);

export const api_query_robot_list_params = {
  seq: 0,
  robotName: "",
  robotType: ""
};
export const api_query_robot_list = (data = api_query_robot_list_params) => HTTP.post("robot/robotList", data);

export const api_update_robot_permission_params = {
  seq: 0,
  robotId: "",
  front: {},
  back: {}
}
export const api_update_robot_permission = (data = api_update_robot_permission_params) => HTTP.post("/robot/updateRobotPermission", data);


export const api_add_robot_document_params = {
  seq: 0,
  robotId: "",
  documentName: ""
};
export const api_add_robot_document = (data = api_add_robot_document_params, config?: any) => HTTP.post("knowledge/createRobotDocument", data, config);

export const api_delete_robot_document_params = {
  seq: 0,
  robotId: "",
  documentIdList: []
};
export const api_delete_robot_document = (data = api_delete_robot_document_params) => HTTP.post("knowledge/deleteRobotDocument", data);

export const api_query_robot_document_params = {
  seq: 0,
  robotId: "",
  documentId: ""
};
export const api_query_robot_document = (data = api_query_robot_document_params) => HTTP.post("knowledge/queryRobotDocument", data);

export const api_update_robot_document_params = {
  seq: 0,
  robotId: "",
  documentId: "",
  documentName: "",
  documentContent: ""
};
export const api_update_robot_document = (data = api_update_robot_document_params) => HTTP.post("knowledge/updateRobotDocument", data);

export const api_query_robot_document_list_params = {
  seq: 0,
  robotId: "",
  documentName: "",
  status: -1
};
export const api_query_robot_document_list = (data = api_query_robot_document_list_params) => HTTP.post("knowledge/queryRobotDocumentList", data);

export const api_query_embedding_chunk_List_params = {
  seq: 0,
  robotId: "",
  text: ""
};
export const api_query_embedding_chunk_List = (data = api_query_embedding_chunk_List_params) => HTTP.post("chat/queryEmbeddingChunkList", data);


export const config_query_robot_Llm_model_params = {
  seq: 0,
  isHomeBot: false
};
export const config_query_robot_Llm_model = (data = config_query_robot_Llm_model_params) => HTTP.post("/platform/robotLlmModel", data);


export const config_query_robot_embedding_model_params = {
  seq: 0,
};
export const config_query_robot_embedding_model = (data = config_query_robot_embedding_model_params) => HTTP.post("/platform/robotEmbeddingModel", data);


export const config_query_phone_area_list_params = {
  seq: 0,
};
export const config_query_phone_area_list = (data = config_query_phone_area_list_params) => HTTP.post("/platform/phoneAreaList", data);


// 界面 配置

export const api_get_application_theme_params = {
  seq: 0,
}
export const api_get_application_theme = (data = api_get_application_theme_params) => HTTP.post("/platform/applicationTheme", data);

export const api_save_application_theme_params = {
  seq: 0,
}
export const api_save_application_theme = (data = api_save_application_theme_params) => HTTP.post("/platform/saveApplicationTheme", data);


// message
export const api_chat_message_params = {
  seq: 0,
  action: "chat",
  robotId: "",
  text: ""
};
export const api_chat_message = (data = api_chat_message_params) => HTTP.post("/chat/chatMsg", data);

export const api_query_current_chat_params = {
  seq: 0,
  robotId: ""
};
export const api_query_current_chat = (data = api_query_current_chat_params) => HTTP.post("/chat/queryCurrentChat", data);


export const api_clear_current_chat_params = {
  seq: 0,
  robotId: ""
};
export const api_clear_current_chat = (data = api_clear_current_chat_params) => HTTP.post("/chat/clearCurrentChat", data);





// permission
export const api_query_user_power_params = {
  seq: 0,
}
export const api_query_user_power = (data = api_query_user_power_params) => HTTP.post("/platform/accountPermissions", data);



export const api_query_employee_list_params = {
  seq: 0,
  searchPhoneNumber: "",
  searchAccount: "",
  searchStatus: ""
}
export const api_query_employee_list = (data = api_query_employee_list_params) => HTTP.post("/user/queryEmployeeList", data);


export const api_query_employee_tree_params = {
  seq: 0,
}
export const api_query_employee_tree = (data = api_query_employee_tree_params) => HTTP.post("/user/queryEmployeeTree", data);


export const api_update_permission_user_params = {
  seq: 0,
  editAccount: "",
  phoneArea: "",
  phoneNumber: "",
  email: "",
  status: "",
  roleIdList: ""
}
export const api_update_permission_user = (data = api_update_permission_user_params) => HTTP.post("/user/updateUserInfo", data);


export const api_create_permission_role_params = {
  seq: 0,
  roleName: "",
  description: "",
  status: ""
}
export const api_create_permission_role = (data = api_create_permission_role_params) => HTTP.post("/role/createRole", data);


export const api_update_permission_role_params = {
  seq: 0,
  roleId: "",
  roleName: "",
  description: "",
  status: ""
}
export const api_update_permission_role = (data = api_update_permission_role_params) => HTTP.post("/role/updateRole", data);


export const api_delete_permission_role_params = {
  seq: 0,
  roleId: ""
}
export const api_delete_permission_role = (data = api_delete_permission_role_params) => HTTP.post("/role/removeRole", data);


export const api_update_role_power_params = {
  seq: 0,
  roleId: "",
  powerList: []
}
export const api_update_role_power = (data = api_update_role_power_params) => HTTP.post("/role/updateRolePower", data);


export const api_query_role_list_params = {
  seq: 0,
  searchRoleName: "",
  searchStatus: ""
}
export const api_query_role_list = (data = api_query_role_list_params) => HTTP.post("/role/queryRoleList", data);


export const api_create_api_key_params = {
  seq: 0,
  robotId: "",
  name: ""
}
export const api_create_api_key = (data = api_create_api_key_params) => HTTP.post("/apiKey/createApiKey", data);


export const api_update_api_key_params = {
  seq: 0,
  robotId: "",
  keyId: "",
  name: "",
  status: 0
}
export const api_update_api_key = (data = api_update_api_key_params) => HTTP.post("/apiKey/updateApiKey", data);


export const api_remove_api_key_params = {
  seq: 0,
  robotId: "",
  keyIds: []
}
export const api_remove_api_key = (data = api_remove_api_key_params) => HTTP.post("/apiKey/removeApiKey", data);


export const api_query_api_key_list_params = {
  seq: 0,
  robotId: ""
}
export const api_query_api_key_list = (data = api_query_api_key_list_params) => HTTP.post("/apiKey/queryApiKeyList", data);


