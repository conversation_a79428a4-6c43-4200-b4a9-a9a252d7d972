import React from "react";
import { connect } from "react-redux";
import {  Flex,Modal } from "antd";
import i18next from "i18next";
import SiderMenu from "./common/siderMenu";
import Nav from "./common/nav";
import Basicinfo from "./basicinfo"; //基本信息
import Knowledge from "./knowledge"; // 机器人知识
import EmbeddingSearch from "./embeddingSearch"; // 向量搜索
import ApiManage from "./apiManage"; // API管理
import IFrame from "./iFrame"; // iFrame

import defaultLogo from "@/assets/bot_default_avatar.png";

import { api_query_robot} from "@/api/robotManage2";
import { ContextState } from "./index_type"

import './style/index.scss'
class RobotSetting extends React.Component<any, ContextState> {
  
  private basicinfoRef: React.LegacyRef<HTMLDivElement> | any;
  constructor(props: any) {
    super(props);
    this.basicinfoRef = React.createRef();
    this.state = {
      pageId:null,
      robotId: null,
      robotName: "",
      robotLogo: null, //robot图标
      robotInfo: null, //机器人详情,
      activeMenu: 'basicinfo',
      isLoading: true,
      basicInfoLeaveModal:{
        leaveType: "back",
        visible: false,
        menu: null,
        // isDecided:false
      }
    }
  };

  get navButtons(){
    // console.log('this.basicinfoRef?.current', this.basicinfoRef?.current)
    return this.state.activeMenu ==='basicinfo'? this.basicinfoRef?.current?.renderNavButton():null
  }

  // 监听切换左侧菜单事件
  handleChangeMenu= async(menu:any)=>{
    try {
      const isDirty= this.judgeBasicInfoFormDirty('toggleMenu',menu)
      if (!isDirty) this.changeMenu(menu)
    } catch (error) {
      console.log(error)
    }
  }

  // 判断 设置详情页面是否有修改
  judgeBasicInfoFormDirty(leaveType:string,menu?:any){
    const {activeMenu:currentActiveMenu,basicInfoLeaveModal } = this.state
    // console.log('currentActiveMenu', currentActiveMenu)
    // 若当前菜单是 设置详情，并且表单有修改，弹出是否退保存的确认弹窗
    if (currentActiveMenu ==='basicinfo'){
      const basicinfoRef = this.basicinfoRef?.current
      const isDirty = basicinfoRef?.on_form_is_dirty()
      // console.log('ref', isDirty)
      if (isDirty){
        basicInfoLeaveModal.visible=true
        basicInfoLeaveModal.menu=menu
        basicInfoLeaveModal.leaveType=leaveType
        this.setState({basicInfoLeaveModal})
        return true
      }
    }
    return false
  }

  // 切换菜单操作
  changeMenu = (menu:any)=>{
    const path =  menu?.slice(menu.indexOf("?"))
    const params = new URLSearchParams(path);
    const pageId = params.get("pid");
    this.props.router.navigate(menu)
    this.setState({activeMenu:pageId},()=>{
      if (pageId==='basicinfo'){
        this.handleQueryRobotInfo()
      }
    })
  }

  // 获取当前机器人详情信息
  handleQueryRobotInfo = (noRefreshChildren?:boolean) => {
    const { robotId } = this.state;
    const data = {
      robot_id:robotId,
      seq: '0'
    };
    this.setState({isLoading: true})
    api_query_robot(data).then((res: any) => {
      const result = res.data
      this.setState({isLoading: false})
      result.isIrrelevantQuestionsBoolean = result.isIrrelevantQuestions === 1;
      this.setState({ robotInfo:result,robotLogo: result?.logo, robotName: result?.name }, () => {
        // 设置和调试
        if (this.state.activeMenu==='basicinfo')this.basicinfoRef?.current?.emit_init(result, noRefreshChildren)
      });
    }).catch((err: any) => {
      console.log(err)
      this.setState({isLoading: false})
    })
  }

  // 设置详情-确认保存
  handBasicInfoLeLeaveModalOk= async()=>{
    try {
      const basicinfoRef = this.basicinfoRef?.current
      const isSaveSucess = await basicinfoRef?.onSave()
      // console.log('isSaveSucess', isSaveSucess)
      if (isSaveSucess) this.handleBasicInfoLeaveModalCancel(true)
    } catch (error) {
      console.log('save error:', error)
      // 无法保存
      const {basicInfoLeaveModal}= this.state
      basicInfoLeaveModal.visible = false
      basicInfoLeaveModal.menu = null
      this.setState({basicInfoLeaveModal})
    }
  }

  // 设置详情-取消保存
  handleBasicInfoLeaveModalCancel=(isSave?:boolean)=>{
    const {basicInfoLeaveModal}= this.state
    if (basicInfoLeaveModal.leaveType==='toggleMenu') this.changeMenu(basicInfoLeaveModal.menu) // 切换菜单
    if (basicInfoLeaveModal.leaveType==='back') this.navBack() // nav返回
    basicInfoLeaveModal.visible = false
    basicInfoLeaveModal.menu = null
    // basicInfoLeaveModal.isDecided = true
    this.setState({basicInfoLeaveModal},()=>{
      // if(basicInfoLeaveModal.leaveType==='popstate'){
      //   // 如果用户取消，阻止导航并恢复历史记录
      //   window.history.pushState(null, '', window.location.href);
      // } 
    })
  }

  // nav返回事件
  handleNavBack=()=>{
    const isDirty = this.judgeBasicInfoFormDirty('back',null)
    if(!isDirty) this.navBack()
  }

  navBack =()=>{
    // 回到 机器人列表-助手管理
    // this.props.router.navigate({
    //   path:"/robots",
    //   params: {TAB:'manage'}
    // })
    // this.props.router.navigate("/?TAB=manage",{params: { tab: 'manage' }});
    this.props.router.navigate("/",{state: { tab: 'manage' }});
    // 2. 立即清除当前页面的 state（替换当前历史记录）
    // this.props.router.navigate('.', { replace: true, state: {} });

    // this.props.router.navigate("/",{params: { tab: 'manage' }});
    // this.props.router.navigate("/?tab=manage");
  }


  checkURLParams = () => {
    const params = new URLSearchParams(window.location.search);
    const robotId = params.get("robotid");
    const pageId = params.get("pid");
    return {robotId,pageId}
  }

  componentDidMount() {
    const {robotId,pageId} = this.checkURLParams()
    if (robotId) {
      this.setState({ robotId,pageId,activeMenu:pageId }, () => {
        this.handleQueryRobotInfo()
      });
    }

    // 监听 popstate 事件
    window.addEventListener('popstate', this.handlePopState);
  }

  componentWillUnmount() {
    // 移除 popstate 事件监听器
    window.removeEventListener('popstate', this.handlePopState);
  }

  handlePopState = (event:any) => {
    // console.log('Back button pressed',event);

    // 解决浏览器URL 的pageId切换，菜单和页面不更新的问题
    const {pageId} = this.checkURLParams()
    this.setState({pageId,activeMenu:pageId });
    if (pageId==='basicinfo'){
      this.handleQueryRobotInfo()
    }
  }

  renderChildren(pageId:any){
    const map:any ={
      basicinfo: <Basicinfo router={this.props.router} ref={this.basicinfoRef} robotInfo={this.state.robotInfo} onQueryRobotInfo={()=>this.handleQueryRobotInfo(true)}/>,
      knowledge: <Knowledge robotId={this.state.robotId} />,
      embedding: <EmbeddingSearch robotId={this.state.robotId} key="RobotSettingEmbedding" />,
      // apiManage: <ApiManage robotId={this.state.robotId} />,
      iframe: <IFrame robotInfo={this.state.robotInfo} />,
    }
    return map[pageId]
  }

  render() {
    const { robotId, robotLogo, robotName,activeMenu,isLoading,basicInfoLeaveModal } = this.state;
    // console.log('activeMenu', activeMenu)
    return (
      <Flex className={"robot-setting-wrap common-wrap"} vertical>
        {/* {this.getActiveRouterName()} */}
        <Nav
          {...this.props}
            icon={robotLogo || defaultLogo}
            robotName={robotName}
            robotId={robotId}
            buttons={
              activeMenu=='basicinfo'? this.basicinfoRef?.current?.renderNavButton(isLoading):null
          }
          onBack={()=>this.handleNavBack()}
        ></Nav>
        <Flex className="wrap-main">
          {/* 左边 */}
          <Flex className="wrap-main-left">
            <SiderMenu activePath={activeMenu} {...this.props} robotId={robotId} onChange={this.handleChangeMenu} />
          </Flex>
          {/* 右边 */}
          <Flex className="wrap-main-right">
            {/* <Routes>
              <Route path="/basicinfo" element={<Basicinfo />} />
            </Routes>
            <Outlet/> */}
            {
              this.renderChildren(activeMenu)
            }
            {/* <Basicinfo ref={this.basicinfoRef} robotInfo={this.state.robotInfo}/> */}
          </Flex>
        </Flex>
        
        { // 退出设置详情页面 确认弹窗
          basicInfoLeaveModal.visible?<Modal width={272} title={i18next.t("robot-manage.save-confirm-msg")} maskClosable={false} centered closable={false}
            okText={i18next.t('robot-manage.save-changes')} cancelText={i18next.t("robot-manage.dont-save")} wrapClassName="common-confirm-modal"
            open={basicInfoLeaveModal.visible} onCancel={() => this.handleBasicInfoLeaveModalCancel()} onOk={() => this.handBasicInfoLeLeaveModalOk()}>
              <div>{i18next.t('robot-manage.save-confirm-desc')}</div>
          </Modal>:null
        }
      </Flex>
    )
  }
}

const mapStateToProps = (state: any) => ({
  userInfo: state.app.userInfo,
  globalLang: state.app.globalLang,
  globalRegion: state.app.globalRegion,
  permissionMap: state.app.permissionMap,
  themeInfo: state.app.themeInfo
});

const mapDispatchToProps = (dispatch: (e: any) => void) => ({
  // dispatch_api_query_robot: (data = api_query_robot_params) => dispatch(dispatch_api_query_robot(data)),
});

export default connect(mapStateToProps, mapDispatchToProps)(RobotSetting);
