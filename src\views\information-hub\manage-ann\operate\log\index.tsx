
import { connect } from "react-redux";
import React, { useState, useRef, useEffect, forwardRef } from "react";
import { Empty, Modal, Table } from 'antd'
import { ColumnProps } from "antd/es/table";
import { useTranslation } from 'react-i18next';
import './index.scss'
import { api_get_notification_operation_log } from '@/api/information'
import {  parseTime } from "@/utils/common";
// import SvgIcon from '@components/svgicon';

interface TableColumnsFace {
    id?: any,
    operation: any,
    operator: any,
    operationTime: any,
}

// 操作日志
const OperateLog = (props: any) => {
    const { t } = useTranslation();
    const { onClose, nid } = props

    const [dataSource, setDataSource] = useState<any>([
        {

        }
    ]);
    const [isLoading, setIsLoading] = useState(false)

    // 初始化
    useEffect(() => {
        query()
    }, [])

    const query = async()=>{
        setDataSource([])
        setIsLoading(true)
        const res:any = await api_get_notification_operation_log(nid).catch(()=>{
            setIsLoading(false)
        })
        setIsLoading(false)
        if (!res) {
            // onClose()
            return
        }
        const datas = res || []
        console.log('datas',datas)
        setDataSource(datas)
    }

    const init = () => {
        // let list = []
        // const operates = ['Create', 'Edit', 'EditValidityPeriod', 'Unpublish', 'Publish']
        // for (let i = 1; i < 16; i++) {
        //     const operation = operates[i % 5]
        //     list.push({
        //         id: i,
        //         operation,
        //         operator: '谢佳加',
        //         operationTime: '2025-05-05 12:12:12',
        //     })
        // }
        // setDataSource(list)
    }

    const onOperationLogClose = () => {
        onClose && onClose()
    }

    const getOperationName = (id: any) => {
        let name = id
        switch (id) {
            case 'CREATE':
                name = t('info-hub.add-ann')
                break;
            case 'UPDATE':
                name = t('info-hub.edit-ann')
                break;
            case 'UPDATE_VALIDITY_PERIOD':
                name = t('info-hub.update-validity')
                break;
            case 'RECALL':
                name = t('info-hub.unpublish-ann')
                break;
            case 'PUBLISH':
                name = t('info-hub.publish-ann')
                break;
            default:
                break;
        }
        return name
    }

    const columns: ColumnProps<TableColumnsFace>[] = [
        { /* 操作，包括
             Create：创建公告
             Edit：编辑公告
             Edit Validity Period：已发布公告修改有效期
             Unpublish：撤销发布
             Publish：发布公告*/
            title: t('robot-manage.document.action'),
            dataIndex: 'operationType',
            key: 'operationType',
            // fixed: 'left' as 'left',
            minWidth: 200,
            ellipsis: { showTitle: true },
            render: (text: any) => <span>{getOperationName(text)}</span>,
        },
        { /* 发布人*/
            title: t('info-hub.operator-name'),
            dataIndex: 'operatorName',
            key: 'operatorName',
            minWidth: 100,
            ellipsis: { showTitle: true },
            render: (text: any) => <span>{text}</span>,
        },
        { /* 操作时间*/
            title: t('info-hub.operator-time'),
            dataIndex: 'operationTime',
            key: 'operationTime',
            minWidth: 150,
            ellipsis: { showTitle: true },
            render: (text: any) => <span>{parseTime(text, "{y}-{m}-{d} {h}:{i}:{s}")}</span>,
        }
    ];

    return (
        <Modal 
            width={548} 
            title={t('info-hub.operation-log')}
            maskClosable={true}
            centered
            closable={true}
            footer={null} 
            className={"border no-padding"}
            wrapClassName="ann-operate-log"
            open={true} 
            onCancel={() => onOperationLogClose()}>
            <div className="table-con">
                <Table columns={columns}
                    dataSource={dataSource}
                    loading={{ spinning: isLoading, size: 'large' }}
                    rowKey={"id"}
                    locale={{
                        emptyText: !
                            isLoading ?
                            <Empty style={{height: '280px'}} image={null} description={
                                <><div>{t('common.no-data')}</div>
                                </>} /> : null
                    }}
                    pagination={false}
                />
            </div>
        </Modal>
    )
}

export default OperateLog