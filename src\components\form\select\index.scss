// select 筛选
.ant-select-dropdown.a-select{
    padding: 6px;
    background: var(--colorBgContainer);
    .ant-select-item{
        padding-top: 6px;
        padding-bottom: 6px;
    }
    .ant-select-item-option{
        font-weight: 500;
        width: 100%;
        border-radius: 12px;
        &:hover{
            &:not(.ant-select-item-option-disabled){
                background: var(--colorFillQuaternary);
            }
            border-radius: 12px;
        }
    }
    .ant-select-item-option-selected:not(.ant-select-item-option-disabled){
        background: var(--colorPrimaryBg);
        // border-radius: 12px;
    }
    .ant-select-item-option-active:not(.ant-select-item-option-disabled){
        background: var(--colorFillQuaternary);
        // border-radius: 12px;
        &.ant-select-item-option-selected{
            background: var(--colorPrimaryBg);
        }
    }
    .ant-input-affix-wrapper.select-search-input{
        // height: 34px;
        margin-bottom: 12px;
        &.ant-input-affix-wrapper-focused{
            box-shadow: none;
        }
    }
    .select-option{
        width: 100%;
        overflow: hidden;
    }
    .select-option-text{
        flex: 1;
        overflow: hidden;
        .select-option-text-inner{
            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
    .select-option-check{
        flex-shrink: 0;
        margin-left: auto;
        width: 16px;
        height: 16px;
        font-size: 16px;
        color: var(--colorPrimary);
    }
}