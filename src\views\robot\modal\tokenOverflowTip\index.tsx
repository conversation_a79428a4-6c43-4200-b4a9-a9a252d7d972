
import { useState, useEffect } from "react";
import i18next from "i18next";
import { Modal } from "antd";
// import styles  from './TokenOverflowTip.module.scss'
// token溢出
const TokenOverflowTip = (props: any) => {
    /* props */
    const overflow = props.overflow;
    const isVisible = props.visible
    /* state */
    const [overflowNumber, setOverflowNumber] = useState(0)
    /* methods */
    // 关闭弹框
    const handleClose = () => {
        // setVisible(false);
        setOverflowNumber(0)
        props.updateVisible(false)
    };

    useEffect(() => {
        // console.log('isVisible', isVisible)
        if (!isVisible) return
        if (overflow) setOverflowNumber(overflow)
    }, [isVisible, overflow])

    return (
        // <div className="token-overflow-modal">
            <Modal
                width={272}
                title={i18next.t("app.filebox-overflow")}
                centered
                closable={false}
                okText={i18next.t("app.i-know")}
                open={isVisible}
                onOk={handleClose}
                // getContainer={".token-overflow-modal"}
                wrapClassName="common-confirm-modal"
                footer={
                    (_, { OkBtn }) => (
                        <OkBtn />
                    )
                }
            >
                <div>
                    {i18next.t("app.filebox-overflow-token", {num: overflowNumber, unit: overflowNumber > 1 ? 's' : ''})}
                </div>
            </Modal>
        // </div>
    );
};
export default TokenOverflowTip;
