{"app": {"cancel": "Cancel", "ok": "Confirm", "create-dialog": "<PERSON><PERSON>", "email": "Email", "delete": "Delete", "confirm-del": "Are you sure you want to delete?", "max-msg": "You’ve reached the daily limit of 200 messages. Try again tomorrow.", "copy": "Copy message", "references": "Knowledge Base", "enhance": "<PERSON><PERSON>ce", "enhance-notice": "Please confirm whether to send the data to the external AI model:", "edit": "Edit", "edited": "Edited", "rollback": "Rest<PERSON>", "document": "Source Document", "check-agree-enhance": "I’ve reviewed the knowledge base data, confirmed it’s secure, and am ready to send it to an external AI model for a response.", "information-source": "Answer basis", "change-llm-tip": "Switching to a different AI model will remove your uploaded files and chat history.", "change-llm-confirm": "Confirm switch to a different AI model", "change-llm": "Yes", "no-change-llm": "No", "filebox-overflow": "The file is too large and exceeds the limit", "filebox-overflow-token": "Currently exceeds {{num}} token{{unit}}, please delete some files before performing enhancement.", "i-know": "I understand.", "max-input-count": "Character limit: {{n}}", "no-more-data": "No more", "select-conversation": "Select chat history", "cancel-select": "Cancel", "copyed": "Copy", "operate-success": "Operation successful", "default": "<PERSON><PERSON><PERSON>"}, "nav": {"logout": "Log out", "docs": "Docs", "drive": "Drive", "email": "Email", "approvals": "Approvals", "ann": "Announcements", "wiki": "Wiki", "project": "Project", "coming-soon": "Coming Soon", "docs-desc": "Create and collaborate on documents online.", "drive-desc": "Securely store, manage, and share your files.", "email-desc": "Professional communication with built-in security.", "approvals-desc": "Streamlined approvals for HR, finance, and admin.", "ann-desc": "Targeted push,read tracking and tag categorization.", "wiki-desc": "Centralize and share knowledge across your team.", "project-desc": "Plan, track, and manage projects efficiently."}, "modal": {"save-ok": "Save"}, "error": {"no-send-blank": "Cannot send an empty message", "ai-response": "Generating response... Please wait", "logo-timeout": "Session has timed out, please log in again", "llm-disabled-tip": "This model is not available. Please contact us for help.", "stop-generating-answers": "Stop generating (Esc)", "request-failed": "Request failed, please try again later", "request-failed-code": "Request failed, error code: {{code}}"}, "success": {"submit": "Submitted successfully"}, "placeholder": {"opinion": "Please enter your suggestions or requests", "msg-placeholder": "Talk to {{platformName}}"}, "introduction": {"default": "Welcome"}, "homebot": {"describe": "Home Bot is your go-to bot for everyday Q&A and web searches. It's designed to replace your current AI tools and transform how you search with Google. With no ties to a specific knowledge base, it's perfect for general tasks. Just remember, since it uses an external AI model, be cautious when sharing files or private data."}, "upload": {"upload-file": "Upload files", "upload-trigger-tip1": "You can upload up to 5 files (PDF, TXT, or DOCX), each no larger than 4.5MB.", "upload-trigger-tip2": "Support drag-and-drop and copy-and-paste for files.", "pdf": "PDF file", "docx": "DOCX file", "txt": "TXT file", "file-excced": "You've reached the maximum upload limit for documents.", "file-draging-tip": "Drag and drop your files here.", "upload-failed": "Failed to upload", "file-count-error": "Max files allowed: 5.", "file-format-error": "Unsupported file type", "file-size-error": "File size exceeds 4.5MB", "file-format-size-error": "Unsupported file type or file size exceeds 4.5MB.", "file-token-excced-error1": "The model has hit its word limit.", "file-token-excced-error2": "To keep uploading, please delete a few files first.", "file-empty-error": "Empty document", "file-name-long-error": "Filename is too long", "attachFile": "Attachment", "clear": "Clear", "continue-uploading": "Continue uploading", "continue-to-select": "Continue selecting", "history-chat-tip": "Add chat history to your questions"}, "robot": {"details": "Bot info", "create-by": "Created by {{user}}", "llm-model": "AI Model (default)", "llm-model-enhance": "AI Model (enhanced)", "robot-id": "Asistant ID", "create-time": "Date Created", "ai-model-creativity": "Creativity", "relevance": "Relevance", "knowlege-count": "Number of References", "my-robot": "My Bot", "share-robot": "<PERSON><PERSON> shared with me", "functions": "Functions"}, "home": {"historical-chat": "Chat History", "google-search": "Web Search", "size": "Size", "relevance": "Relevance", "upload-time": "Upload Date", "context-cleared": "Context cleared", "clearing-the-context": "Clearing the context", "delete-conversation": "Delete items selected", "confirm-delete-chat-tip": "This operation cannot be undone and both the question and answer will be deleted."}, "copied": "<PERSON>pied", "you": "You", "google-seatch": "Once activated, Google search results can enhance the quality of your generative AI responses.", "search": "Search", "saveToLocal": "Save to device", "settings": "Settings", "control-center": "Control Center", "submit": "Submit", "automatic": "Automatic", "light": "Light", "dark": "Dark", "system-setting": "Settings", "feedback": "<PERSON><PERSON><PERSON>", "feedback-tip": "We truly appreciate your feedback—it helps us make the product better!", "feedback-placeholder": "Please enter your suggestions or questions", "appearance": "Appearance", "system-language": "System language", "system-language-tip": "Choose your preferred language for the {{platformName}} interface.", "text-generation": "Text generation", "image-generation": "Image generation", "reasoning": "Reasoning", "reasoning-time": "Thought for {{time}} second{{unit}}", "logical-reasoning": "Reasoning", "login": {"sign": "Log in", "unable-login": "Unable to log in?", "sign-about": "Contact us", "sign-success": "Log in successfully", "logout": "Log out", "control-center": "User Center", "user-center": "Control Center", "login-tip2": "Access a selection of advanced, customizable bots.", "email": "Email", "email-address": "Email Address", "password": "Password", "mobile": "Mobile Number", "verify-code": "Verification Code", "get-code": "Get Code", "username": "User Name", "privacy-policy": "Privacy Policy", "terms-conditions": "Terms of Service", "agree-privacy-terms": "I agree to {{privacy}} and {{terms}}.", "agree-privacy-terms-tip": "Please read and agree to the following terms", "privacy-terms": "{{privacy}} and {{terms}}.", "agree": "Agree", "verifiy-code-login": "Log in using OTP", "password-login": "Log in using password", "forgot-password": "Forgot Password?", "register": "Register vama", "register-btn": "Register", "has-account": "Already have a vama account?", "verify-mobile": "Verify Phone Number", "continue": "Continue", "skip": "<PERSON><PERSON>", "back": "Back", "login-tip": "Let's sign you in", "joinContent": "{{userName}} invites you to join the team in vama :", "welcom-to": "Welcome to {{platName}}", "no-account": "Don't have an vama account?"}, "login-pls": {"email": "Enter your email address", "password": "Enter your password", "new-password": "Enter your new password", "mobile": "Enter your mobile number", "verify-code": "Enter the verification code", "username": "4-20 characters. e.g. <PERSON>"}, "login-validate": {"password-format-error": "Password must be 6-18 characters with letters and numbers.", "email-format-error": "Invalid email address", "mobile-format-error": "Invalid phone number", "invalid-verifiy-code-error": "Invalid verification code", "username-format-error": "Invalid user name"}, "login-msg": {"send-success": "Send successfully"}, "common": {"please-input": "Please Enter", "please-select": "Please Select", "please-search": "Please search", "ok2": "OK", "search-keyword": "Search", "input-required": "<PERSON><PERSON><PERSON>", "select-required": "Required", "no-result": "No results found", "query": "Query", "reset": "Reset", "edit": "Edit", "delete": "Delete", "edit-success": "Edited successfully", "update-success": "Modified successfully", "delete-success": "Deleted successfully", "status-all": "All status", "status-enable": "Enable", "status-disable": "Disable", "no-data": "No data yet", "warning": "Warning", "total": "Total"}, "company": {"title": "My company/team", "personal-space": "Personal Space", "create": "Create", "join": "Join", "apply": "Apply", "create-title": "Create a Company/Team", "create-tip": "You will start vama as a company administrator", "company-name": "Company Name", "location": "Location", "industry": "Industry", "employee-size": "Employee Size", "service-agreement": "vama Service Agreement", "organization-agreement": "vama Organization Agreement", "agreement": "I have read and agree to: {{service}}, {{organization}}", "service-organization": "{{service}}, {{organization}}", "join-title": "Join a Company/Team", "join-tip": "Search for and apply to join a company or team", "no-data": "No data available", "search-no-result": "Please enter the correct company name.", "apply-submitted": "Application submitted", "apply-submitted-tip": "Please wait for the organization administrator's approval", "create-success": "Create Success", "apply-failed": "Application failed", "apply-failed-reason": "You have joined the company/organization."}, "no-my-bot": "No Bots available", "no-share-bot": "No bots shared with me", "robot-manage": {"search-result": "Search results would appear here", "arrangement": "Layout", "arrangement-1": "Grid View", "arrangement-2": "List View", "robot-type": "Category", "robot-type-1": "All bots", "robot-type-2": "Bots I created", "robot-type-3": "<PERSON><PERSON> shared with me", "basic-info": "Basic Information", "ai-model": "AI Models", "create-robot": {"robot-type": "Bot Type", "personal-bot": "Personal Bot", "department-bot": "Department Bot", "department-bot-desc": "Create <PERSON><PERSON> as Department", "company-bot": "Company Bot", "company-bot-desc": "Create Bo<PERSON> as Company", "robot-id": "Bot ID", "robot-logo": "<PERSON><PERSON>", "robot-name": "Bot Name", "robot-name-hint": "Please enter bot name", "robot-introduction": "Bot Introduction", "robot-introduction-hint": "Please enter the bot introduction", "robot-introduction-hint2": "Introduce the bot functions briefly", "llm-model": "AI Model (default)", "llm-model-hint": "Please select an AI model", "escalate-llm-model": "AI Model (enhanced)", "escalate-llm-model-hint": "Please select an enhanced AI model", "intelligence": "Creativity", "intelligence-hint": "Lower values make responses more precise and stable; higher values make responses more random", "relevance": "Relevance", "relevance-hint": "The higher the value, the more accurately your question matches the content in the knowledge base (references), but the number of matching results may decrease.", "enable-conversation-memory": "Memory", "show-knowledge": "Access References", "allow": "Allow", "not-allowed": "Do not allow", "show-knowledge-notice": "Choose whether to allow the display of knowledge base content that the bot references while answering questions in the chat interface.", "empty-knowlwdge": "When the relevant content from the knowledge base cannot be found, reply will be:", "llm-response": "Selected AI model reply", "canned-response": "Default Reply", "canned-response-hit": "Please enter the default reply.", "long-term-memory": "Long-term memory", "long-term-memory-tip": "In the chat interface, long-term memory will be reset after clearing the context", "long-term-memory-desc": "Summarizes chat history to better respond to your messages.", "upload-image": "Upload picture", "bot-logo-edit": "Crop", "is-irrelevant-questions": "Greeting", "robot-welcome-msg": "Greeting message", "robot-welcome-msg-hint": "Please enter the greeting message", "robot-identity-prompt": "Identity Description", "robot-identity-prompt-hint": "The identity description determines the role and function of the robot. Please fill it in as clearly as possible.", "robot-identity-prompt-notice": "It is very important, as it will shape the assistant's identity, capabilities, boundaries, tone, etc. It’s worth spending additional time on its construction.", "knowlege-document-count": "Documents"}, "save-confirm-msg": "Leave this page?", "save-confirm-desc": "Do you want to save the changes?", "dont-save": "Don't Save", "save-changes": "Save", "create-robot-success": "Added successfully", "delete-robot-error": "Failed to delete", "upload-document-max-size-error": "The size of uploaded picture cannot exceed 1MB.", "file-format-error": "Unsupported file format", "update-robot-permission-success": "Set successfully", "llm-disabled-tip": "The model has not been installed yet, please contact support team for assistant.", "embedding-search": "Search the knowledge base", "confirm-del": "Are you sure you want to delete?", "confirm-delete-tip": "This operation cannot be undone", "setting-robot": "Settings", "publish-robot": "Publish", "share-robot": "Share", "publish-robot-info": {"front-permision": "Users who can use this robot", "back-permision": "Users who can edit this robot", "permision-0": "Owners", "permision-1": "Everyone in this department", "permision-2": "Owners of this department and subordinate departments", "permision-3": "All", "permision-4": "Customize", "select-account": "Choose organizational structure", "custom-empty-tip": "Customized user data cannot be empty"}, "create-robot-button": "Create <PERSON><PERSON>", "label": "Bots Management", "setting-debug": "Setup and debug", "debug": "Debug", "robot-knowlege": "Knowledge base", "api-manage-label": "API", "embedding-search-empty": "Search results within knowledge base will appear here", "no-knowlege": "No document available", "no-knowlege-desc": "No files have been imported yet.", "role-setting-label": "Permission", "user-manage-label": "User Management", "role-manage-label": "Role Management", "department-manage-label": "Department", "embedding-search-detail": "Source", "document": {"document-name": "Document name", "document-name-hint": "Document name", "document-id": "Document ID", "status": "Status", "status-hint": "Please select status of documents", "status-0": "Processing", "status-1": "Available", "status-2": "Deleting", "status-3": "Deleted", "status-4": "Server Error", "status-5": "The file is not in a recognizable format", "status-6": "The file is empty", "status-7": "Content is too long", "select-status-0": "Processing", "select-status-1": "Available", "select-status-2": "Failed", "characters": "Characters", "hit-count": "Hit Count", "account": "Creator", "update-time": "Last Update Date", "action": "Operation", "document-type-error": "Unsupported file format", "document-count-exceed": "Up to 10 files", "add-document": "Add", "edit-document": "Edit", "upload-document": "File Upload", "upload-document-hint": "Drag and drop your files here.", "upload-document-type-hint": "Supported file formats: .pdf、.txt、.docx", "upload-document-success": "Uploaded successfully", "document-content": "Content", "document-content-hint": "Please enter the document content", "multi-delete-document": "Batch delete"}, "api-manage": {"api-key": "KEY", "name": "Name", "name-hint": "Name", "status": "Status", "status-delete": "Deleted", "update-time": "Last Update Date", "action": "Operation", "create-api-key-success": "Created successfully", "create-api-key": "Create", "update-api-key": "Edit API", "multi-delete-api-key": "<PERSON><PERSON> Delete", "is-multi-delete-api-key": "Are you sure you want to delete KEYs in batches?", "multi-delete-api-key-notice": "This operation will delete KEYs in a batch. Please confirm this operation with caution.", "no-api": "No API available", "no-api-desc": "No API found yet"}, "chat-debug": {"content-count": "Character count", "hit-count": "Hit Count", "token": "Token", "search-hint": "Please enter a keyword", "search-notice": "The keyword cannot be empty"}}, "contacts": {"title": "Contacts", "organize": {"menu-label": "Organization Contacts", "supervisor": "Supervisor", "department": "Department", "company": "Company", "work-email": "Work Email", "work-phone": "Work Phone", "user-id": "User ID"}, "admin-panel": "Admin Panel", "leave-organize": {"menu-label": "Leave Organization", "unable-leave": "Unable to leave the organization", "unable-leave-reason": "You are currently the Primary Administrator of {{companyName}}, To ensure account security, please transfer your admin role to another user before leaving.", "leave-confirm": "Are you sure you wish to proceed?", "leave-confirm-tip": "By confirming, you will lose access to all {{companyName}} services.This action is irreversible."}, "my-groups": "My Groups", "i-created": "I created", "i-joined": "I joined", "group-members": "{{count}} member{{unit}}", "new-contacts": "New Contacts", "wait-for-verify": "Waiting for verification", "added": "Added", "rejected": "Rejected", "reject": "Reject", "agree": "Agree", "want-to-add-you": "Wants to add you", "my-contacts": "My Contacts"}, "chat": {"title": "Cha<PERSON>", "type-a-message": "Type a message", "create-group": "Create Group", "picture": "Picture", "video": "Video", "file": "File", "FILE": "FILE", "emoji": "<PERSON><PERSON><PERSON>", "you": "You", "create-group-chat": "created a group chat", "leave-group-chat": "ha{{name}} left the group chat", "kicked-out-group-chat": "{{who}} w{{title}} kicked out of the group chat by {{opUser}}", "join-group-chat": "{{opUser}} invited {{users}} to join the group chat", "change-group-name": "{{who}} modify the group name to {{name}}", "quit-group-tip": "Cannot send messages in a group chat that you have quit!", "unknown-type-message": "Unknown type message", "invite": "Invite", "group-name": "Group Name", "search-user": "Search user", "select-group-members": "Select Group Members", "Expand": "Expand", "organize-contacts": "Organization Contacts", "selected": "Selected", "select-at-least": "Please select at least one", "group-name-required": "Please enter a group name", "group-member-limit": "The maximum number of group members has exceeded 1000", "company-organize": "Company/ Organization", "messages": "Messages", "send-messages": "Send Message", "settings": "Settings", "group-members": "Group Members", "group-member-list": "Group member list", "view-more": "View more", "group-owner": "Group Owner", "kick-out-member": "Kick out member", "kick-out-member-confirm": "Are you sure you want to kick {{user}} from the group chat?", "yes": "Yes", "today": "Today", "yesterday": "Yesterday", "copy-success": "Copied Successfully", "online": "Online", "offline": "Offline", "me": "Me", "add-contact": "Add Contact", "search-user-id": "Search User ID", "search-no-user": "No relevant results found", "add": "Add", "friend-verify": "Friend verification", "verify-msg": "Verification Information", "send": "Send", "add-success": "Friend request sent successfully", "start-chat": "You are now friends and can start chatting!"}, "setting": {"title": "Settings", "user-setting": "USER SETTINGS", "my-account": "My Account", "profiles": "Profiles", "system-setting": "SYSTEM SETTINGS", "general": "General", "keybinds": "Keybinds", "other": "OTHER", "feedback": "<PERSON><PERSON><PERSON>", "accountDetails": "Account Details", "change-email": "Change Email", "change-email-ok": "Email Successfully Updated", "password-set": "Password Setted", "no-set": "Not Set", "change-mobile": "Change Mobile Number", "bind-mobile": "Bind Mobile Number", "change-mobile-ok": "Mobile Number Successfully Updated", "change-password": "Change Password", "change-password-ok": "Password Successfully Updated", "authentication": "Authentication", "delete-confirm-tip": "Delete Account - Are you sure?", "delete-confirm": "Confirm Account Deletion", "confirm-delete": "Confirm Deletion", "verify-password": "Please enter your login password to verify your identity", "delete-confirm-text1": "Deleting your account is permanent. All your personal data, personal documents, chat history, and settings will be permanently deleted and cannot be undone.", "delete-confirm-text2": "The shared files or projects you created will remain within the team until the company/team is dissolved.", "delete-confirm-text3": "Deleting your account will prevent you from accessing all vama services, including the AI assistant, chat, knowledge base, and more.", "delete-confirm-text4": "vama does not provide a grace period for account deletion. Once confirmed, your account will be deleted immediately. Please consider carefully.", "delete-confirm-text5": "If you are sure you want to proceed with account deletion, please click \"Continue\" and complete identity verification.", "cannot-delete-account": "The account cannot be deleted at the moment. You still have companies/teams that you haven't left.", "deleteAccount": "Delete Account", "deleteDes": "Once the account is deleted, all account content and data will be permanently lost.", "profilesubTitle": "You can switch and view your business card information for different companies/organizations here.", "avatar": "Avatar", "changeAvatar": "Change Avatar", "restore": "<PERSON><PERSON>", "position": "Position", "email-verifiy": "Email verification", "mobile-verifiy": "Mobile verification", "new-email": "New Email Address", "new-password": "New Password", "new-mobile": "New mobile number", "delete-account-success": "Account deletion successful", "profile-in": "Profile in {{company}}", "profile-in-text": "Profile in", "update-image": "Edit pictures", "preview": "Preview", "change-no-save": "Changes Not Saved", "save-change": "Save Changes", "keybind-desc": "All keybinds are only applicable to User Center", "ai-bot": "AI Bot", "send-message": "Send Message", "new-line-input": "New Line in Input Box", "stop-generating": "Stop Generating", "feedback-desc": "We sincerely appreciate your feedback — it helps us improve our product!"}, "invite": {"sign-as": "Signed in as", "apply-join": "Apply to join", "no-account": "Don't have an vama account?", "enter-vama": "Enter vama", "use-another-account": "Use another vama account :", "resister": "Register to join the team", "switch": "Switch", "to-vama": "Go to vama", "invitation-expired": "This invitation link has expired.", "invitation-used": "This invitation link has been used.", "contact-obtain": "Please contact your company's/organization's administrator to obtain a new one.", "register-title": "Register vama to join the team", "register-submit": "Register and Apply to Join", "have-account": "Already have a vama account?", "use-account-join": "Use another account", "reset-apply": "Reset and Apply to <PERSON><PERSON>"}, "bots-marketplace": {"title": "Market Place", "explore-bots": "Explore Bots", "explore-bots-desc": "Discover bots crafted with instructions, skills, and extra knowledge to fit your unique needs.", "search-input-placeholder": "Search bot name or creator", "owner": "Owner", "usage": "Usage", "default-model": "Default AI Model", "enhanced-model": "Enhanced AI Model", "ai-creativity": "AI Model Creativity", "relevance": "Knowledge Relevance", "files": "Files", "published": "Published", "unpublished": "Unpublished"}, "orphaned-manage": {"title": "Orphaned Management"}, "info-hub": {"title": "Information Hub", "ann": "Announcements", "ann-manage": "Manage Ann", "active": "Active", "archived": "Archived", "published": "Published", "drafts": "Drafts", "rule": {"title": "Invalid title"}, "pin": "<PERSON>n", "unpin": "Unpin", "publish": "Publish", "unpublish": "Withdraw", "activation": "Activate", "archive": "Archive", "filter-tags": "Filter tags", "search-input": "Search announcements or publishers", "show-unread": "Show unread", "add-ann": "Add announcement", "edit-ann": "Edit announcement", "delete-ann-confrim": "Confirm to delete this announcement?", "ann-title": "Title", "behalf-of": "Behalf of", "priority": "Priority", "content": "Content", "publish-type": "Publish Type", "publish-time": "Publish At", "validity-period": "Validity Period", "validity-until": "Validity Until", "attachments": "Attachments", "personal": "Personal", "personal-tip": "Published in Personal Name", "depart-tip": "Published in Department Name", "company-tip": "Published in Company Name", "critical": "Critical", "important": "Important", "normal": "Normal", "publish-at": "Publish At", "publish-now": "Publish Now", "forever": "Forever", "no-attachment": "No Attachment", "publish-success": "Published Successfully", "ann-details": "Announcement Details", "publish-ann": "Announcement Release", "file-exceeds": "File Size Exceeds Limit(20M)", "time-error": "The scheduled time is earlier than the current time. Please set a future time.", "upload-error": "Failed to upload attachment", "operation-log": "Operation Log", "operator-name": "Publisher", "operator-time": "Operation time", "update-validity": "Change validity period", "unpublish-ann": "Unpublish", "publish-range": "Users who can receive this announcement", "add-tag": "Create a tag", "add-select-tag": "Select or create an option", "tag-limit": "A maximum of {{count}} tags can be created", "tag-color": "Color", "ai-summary": "AI Summary", "ai-summary-tip": "You can click to generate an article summary", "no-summary": "No summary yet", "char-count": "Number of characters", "unsurpport-image-format": "Unsupported image format", "image-size-limit": "Image size cannot exceed {{num}}MB", "default": "<PERSON><PERSON><PERSON>", "orange": "Orange", "yellow": "Yellow", "lime": "Grass Green", "green": "Green", "cyan": "<PERSON><PERSON>", "purple": "Purple", "pink": "Pink", "red": "Red", "gray": "<PERSON>", "search-tag": "Search options"}, "robot-iframe": {"title": "iFrame", "inter-code": "Intergration code", "light-mode": "Light Mode", "dark-mode": "Dark Mode", "plugin-name": "Plugin Name", "plugin-introduction": "Plugin Introduction", "talk-to": "Talk to {{robot}}..."}}