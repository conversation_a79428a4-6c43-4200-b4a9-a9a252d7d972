import React, { useState, useEffect, forwardRef, useRef, useImperative<PERSON>andle } from "react";
import { connect } from "react-redux";
import { Modal, message, Input, Flex,Form } from "antd";
import classNames from "classnames";
import i18next from "i18next";
import {$trim} from '@/utils/common'

import { api_update_robot_document_params,api_query_robot_document_params } from "@/api/robotManage";
import { dispatch_api_update_robot_document,dispatch_api_query_robot_document } from "@/store/api";

import './index.scss'
import Svgicon from "@/components/svgicon";
const { TextArea } = Input;

// 编辑文档
const UpdateDocument = forwardRef((props: any, ref) => {
  /** 自定义暴露给父组件的实例值 **/
  // useImperativeHandle(ref, () => ({
  //   init() {
  //   }
  // }))

  /* props */
  const isVisible = props.visible
  const resource = props.resource
  const robotId = resource?.robotId
  /* state */
  const [isSubmiting, setIsSubmiting] = useState(false) // 是否提交中

  const [form] = Form.useForm(); // 表单
  const formRef = useRef<any>(null);


  /* method */
  // 确认
  const handleIsModalOk = async() => {
    try {
      const values:any = await form.validateFields();
      const {documentName,documentContent}= values
      const { userInfo, globalRegion: region } = props;
      const data = {
        ...api_update_robot_document_params,
        account: userInfo.account,
        region,
        robotId,
        documentId: resource.documentId,
        documentName: $trim(documentName),
        documentContent: $trim(documentContent)
      }
      setIsSubmiting(true)
      props.dispatch_api_update_robot_document(data).then(() => {
        setIsSubmiting(false)
        message.open({ type: "success", content: i18next.t("common.edit-success") })
        handleIsModalCancel()
        props.onSucess()
      }).catch((err: any) => {
        setIsSubmiting(false)
        console.log(err)
      })
    } catch (errorInfo) {
      console.log('Failed:', errorInfo);
    }
    
  }
  // 取消
  const handleIsModalCancel = () => {
    props.onClose()
  }


  // 表单必填校验
  const formRequiredValidator = ({}) => ({
    validator(_:any, value:any) {
      const _value = $trim(value) 
      if (_value!='' && _value!=null&& _value!=undefined) {
        return Promise.resolve();
      }
      return Promise.reject(new Error(i18next.t('common.input-required') as string));
    },
  })

  const getDetail =()=>{
    const { userInfo, globalRegion: region } = props;

    const data = {
      ...api_query_robot_document_params,
      account: userInfo.account,
      robotId,
      documentId: resource.documentId,
      region
    }

    props.dispatch_api_query_robot_document(data).then((result: any) => {
      // this.setState({ currentDoc: result, isEditModal: true })
      console.log('result', result)
      form.setFieldsValue({
        documentName:result.documentName,
        documentContent:result.documentContent
      })
    }).catch((err: any) => {
      console.log(err);
    })
  }

  useEffect(() => {
    getDetail()
  }, [])

  return (
    <>
      <Modal title={i18next.t("robot-manage.document.edit-document")}
        wrapClassName="update-robot-document-modal"
        width={780}
        open={isVisible}
        onOk={handleIsModalOk}
        onCancel={handleIsModalCancel}
        footer={undefined}
        okText={i18next.t(["app.ok"]) || ""}
        cancelText={i18next.t(["app.cancel"]) || ""}
        maskClosable={false}
        centered
        cancelButtonProps={{variant:"filled",color:"default"}}
      >
        <div>
          <Form ref={formRef} className="common-form" name="validateOnly" layout="vertical" form={form} autoComplete="off" requiredMark={false} disabled={isSubmiting}>
            {/* 文檔名稱 */}
            <Form.Item shouldUpdate={true} name="documentName" label={i18next.t("robot-manage.document.document-name")} rules={[formRequiredValidator]}>
              <Input variant="filled" showCount maxLength={60} placeholder={i18next.t("robot-manage.document.document-name-hint") as string} />
            </Form.Item>
            {/* 内容 */}
            <Form.Item name="documentContent" label={i18next.t("robot-manage.document.document-content")} rules={[formRequiredValidator]}>
              <TextArea
                placeholder={i18next.t("robot-manage.document.document-content-hint") as string}
                autoSize={{ minRows: 4, maxRows: 20 }} variant="filled" />
            </Form.Item>
          </Form>
        </div>
      </Modal>
    </>
  );
});
const mapStateToProps = (state: any) => ({
  userInfo: state.app.userInfo,
  globalRegion: state.app.globalRegion,
  globalLang: state.app.globalLang,
});
const mapDispatchToProps = (dispatch: (e: any) => void) => ({
  dispatch_api_query_robot_document: (data = api_query_robot_document_params) => dispatch(dispatch_api_query_robot_document(data)),
  dispatch_api_update_robot_document: (data = api_update_robot_document_params) => dispatch(dispatch_api_update_robot_document(data)),
});
export default connect(mapStateToProps, mapDispatchToProps)(UpdateDocument);