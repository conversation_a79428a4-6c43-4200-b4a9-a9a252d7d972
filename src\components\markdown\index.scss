@import '/src/styles/mixin.scss';
.syntax-highlighter{
    position: relative;
    >div:not(.code-lang){
      @include webkitScrollStyle($height:8px);
    }
  }
.code-top{
  position: absolute;
  top: 20px;
  left: 16px;
  right: 16px;
  .code-lang{
    background: var(--colorFillTertiary);
    color: var(--colorTextSecondary);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    line-height: 16px;
  }
  .code-copy{
    color: var(--colorIconTertiary);
    font-size: 16px;
    cursor: pointer;
    margin-left: auto;
  }
}
code{
  background: #FAFAFA;
}
.dark code{
  background: #323232!important;
  &.inline-code{
    background:transparent!important;
  }
}
code.inline-code{
  &::before,&::after{
    content: "`";
  }
}
.ai-mark-down{
  color: var(--colorText);
  font-size: var(--font-size-base);
  line-height: 24px;
  word-wrap: break-word;
  width: max-content;
  width: 100%;
  font-family: var(--font-family)!important;
  h1, h2, h3, h4, h5, h6{
    margin-top: 1em;
  }
  p {
    margin: 10px 0;
    word-break: break-word;
    max-width: 100%;
    overflow-x: hidden;
    word-wrap: break-word;
    padding-right: 4px;
  }
  img {
    max-width: 500px;
    height: auto;
    cursor: pointer;
    border-radius: 12px;
  }
  code{
    font-family: "Fira Code", "Fira Mono", Menlo, Consolas, "DejaVu Sans Mono", monospace!important;
  }
  // 表格
  table {
    // width: 100%;
    border-collapse: collapse;
    margin-top: 8px;
  }
  tbody td, th {
    border-bottom: 1px solid var(--colorBorderSecondary);
    padding: 8px;
    text-align: left;
    color: var(--colorText);
  }
  // 分割线
  hr{
    border-bottom: 0;
    border-color: var(--colorBorder);
    transform: scale(1,0.5);
  }
  a{
    color: var(--colorPrimary);
  }
} 