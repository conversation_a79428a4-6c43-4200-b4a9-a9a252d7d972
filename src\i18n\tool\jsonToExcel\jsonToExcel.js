/**
 * JSON文件生成Excel文件
 * 1、生成执行命令：node jsonToExcel.js
 * 2、特别注意：zh.json hk.json en.json 语言文件
 */
 const ExcelJS = require('exceljs');
 const fs = require('fs');
 const path = require('path');
 
 
 // 创建一个新的工作簿
 const workbook = new ExcelJS.Workbook();

 const readJsons=(workbook, basePath, sheetName)=>{
  // 读取 i18n.json 文件
  const zhData = JSON.parse(fs.readFileSync(`${basePath}/zh.json`, 'utf-8'));
  const hkData = JSON.parse(fs.readFileSync(`${basePath}/hk.json`, 'utf-8'));
  const enData = JSON.parse(fs.readFileSync(`${basePath}/en.json`, 'utf-8'));
  // 添加Sheet
  const _worksheet = workbook.addWorksheet(sheetName);
  // 设置表头
  _worksheet.columns = [
    { header: 'Key', key: 'key', width: 30, style: { alignment: { wrapText: true } } },
    { header: '简体', key: 'zh', width: 50, style: { alignment: { wrapText: true } } },
    { header: '繁體', key: 'hk', width: 50, style: { alignment: { wrapText: true } } },
    { header: '英文', key: 'en', width: 50, style: { alignment: { wrapText: true } } },
    //  { header: '英文调整（请只填写英文字符）', key: 'en_update', width: 50, style: { bold: true, alignment: { wrapText: true } } },
    //  { header: '简体调整（请只填写简体字符）', key: 'zh_update', width: 50, style: { bold: true, alignment: { wrapText: true } } },
    //  { header: '繁體调整（请只填写繁體字符）', key: 'hk_update', width: 50, style: { bold: true, alignment: { wrapText: true } } },
    { header: 'Remark', key: 'qar', width: 100, style: { alignment: { wrapText: true } } },
    //  { header: '字段出处', key: 'field_source', width: 50, style: { alignment: { wrapText: true } } },
  ];
  // 设置第一行（表头）加粗
  const headerRow = _worksheet.getRow(1);
  headerRow.font = { bold: true };
  //  const cell = headerRow.getCell('en_update');
  //  const cell2 = headerRow.getCell('zh_update');
  //  const cell3 = headerRow.getCell('hk_update');
  //  highLightFont(cell)
  //  highLightFont(cell2)
  //  highLightFont(cell3)

  function highLightFont(cell){
    cell.font = { color: { argb: 'FAAD1400' }, bold: true }; // 红色字体
    cell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFFFFF00' } // 黄色背景
    }
  }
  
  const zhFlattenedJSON = $_flattenJSON(zhData);
  const hkFlattenedJSON = $_flattenJSON(hkData);
  const enFlattenedJSON = $_flattenJSON(enData);
  $_addSheetRow(_worksheet,zhFlattenedJSON)// 先填充 简体 数据为基础
  // console.log(zhFlattenedJSON);
  // console.log(hkFlattenedJSON);
  // console.log(enFlattenedJSON);
  
  $_updateRowByLang(_worksheet,hkFlattenedJSON, "hk") // 填充繁体数据
  $_updateRowByLang(_worksheet,enFlattenedJSON, "en") // 填充英文数据
  return _worksheet
}

 // 扁平化JSON
 function $_flattenJSON(obj, prefix = '') {
   let result = {};
   
   for (let key in obj) {
     if (typeof obj[key] === 'object' && obj[key] !== null) {
       result = {
         ...result,
         ...$_flattenJSON(obj[key], `${prefix}${key}.`)
       };
     } else {
       result[`${prefix}${key}`] = obj[key];
     }
   }
   
   return result;
 }
 
 // sheet 添加行数据
 function $_addSheetRow(worksheet,jsonData){
   for (const [key, value] of Object.entries(jsonData)) {
     worksheet.addRow({
       key: key,
       zh: value
     });
   }
 }
 
 // 填充其他语言数据
 function $_updateRowByLang(worksheet,jsonData, lang){
   // 找到匹配 key, 填充对应语言数据
   for (const [key, value] of Object.entries(jsonData)) {
     const targetKey = key
     worksheet.eachRow((row, rowNumber) => {
       const keyCellValue = row.getCell('key').value; // 根据列名获取单元格值
       if (keyCellValue === targetKey) {
         // 找到匹配的行
         // console.log("找到匹配的行:" + keyCellValue, rowNumber)
         worksheet.getRow(rowNumber).getCell(lang).value = value;
         return false; // 停止遍历
       }
     })
   }
 }
 
// 1.读取locales 目录下的简繁英
readJsons(workbook,'../../locales','Translations')
// 2. 读取locales errorCode 目录下的简繁英
 readJsons(workbook,'../../locales/errorCode','Error Code')

// 指定要保存的文件名
const fileName = 'VAMA Translations.xlsx';
// 指定上上上一层目录的路径
const grandparentDirPath = path.join(__dirname); // '..' 表示上一级目录
const filePath = path.join(grandparentDirPath, 'excels', fileName); // 在当前目录下excels 文件夹中创建文件

// 保存 Excel 文件
workbook.xlsx.writeFile(filePath)
  .then(() => {
    console.log('Excel file created successfully.');
  })
  .catch((error) => {
    console.error('Error creating Excel file:', error);
});