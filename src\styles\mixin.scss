// 滚动条定制样式
@mixin webkitScrollStyle($width: 10px, $height: 4px, $thumbColor: var(--colorFillSecondary)) {
  &::-webkit-scrollbar {
    width: $width;
    height: $height;
  }
  &::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 5px;
  }
  &::-webkit-scrollbar-thumb {
    background: $thumbColor;
    border-radius: 5px;
  }
}
// 全局滚动条默认样式
@mixin globalWebkitScrollStyle($width: 10px, $height: 4px, $thumbColor: var(--colorFillSecondary)) {
  ::-webkit-scrollbar {
    width: $width;
    height: $height;
  }
  ::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 5px;
  }
  ::-webkit-scrollbar-thumb {
    background: $thumbColor;
    border-radius: 5px;
  }
}

@mixin table-filter__bottom-button_disabled-font-color {
  [data-theme="dark"] & {
    color: #909399 !important;
  }

  [data-theme="white"] & {
    color: #c0c4cc !important;
  }
}

@mixin ellipsis-multiline($line: 2) {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: $line;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
}
@mixin ellipsis() {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin flex($flex-direction: row, $align-items: center, $justify-content: normal) {
  display: flex;
  flex-direction: $flex-direction;
  align-items: $align-items;
  justify-content: $justify-content;
}

@mixin font($font-size: 14px, $line-height: 20px, $font-weight: 500, $color: var(--colorText)) {
  font-size: $font-size;
  line-height: $line-height;
  font-weight: $font-weight;
  color: $color;
}

@mixin navTitle() {
  flex-shrink: 0;
  font-weight: 600;
  font-size: 18px;
  color: var(--colorText);
  line-height: 24px;
}
