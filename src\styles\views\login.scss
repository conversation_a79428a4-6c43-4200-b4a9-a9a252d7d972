@import '../mixin.scss';
.login-wrapper {
  height: 100%;
  width: 100%;
  background: var(--colorBgLayout);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  
  .login-center {
    width: 1172px;
    height: 757px;
    background: var(--colorBgContainer);
    border-radius: 20px;
    margin: 0 auto;
    padding: 8px;
    display: flex;

    .publicity {
      .carousel {
        width: 566px;
        height: 737px;
        img {
          height: 737px;
          width: 566px;
        }
      }
    }
    .right {
      width: 566px;
      padding:60px 0;
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
  }
  .logo-tittle {
    display: flex;
    align-items: center;
    height: 46px;
    width: 355px;

    .logo {
      width: 230px;
      height: 46px;
    }

    .select-lang {
      margin-right:0;
      margin-left: auto;
      width: 120px;
      .ant-select-selector {
        padding-right: 0;
      }
      &.ant-select-open .ant-select-selection-item{
        color: var(--colorText);
      }
      .ant-select-arrow{
        inset-inline-end: 12px;
      }
      .ant-select-selection-item{
        text-align: right;
        padding-right: 32px;
        color: var(--colorText);
      }
    }
  }

  .content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
  }
  .content-tittle {
    font-size: 24px;
    font-weight: 600;
    line-height: 32px;
    color: var(--colorText);
    // word-break:break-word;
    max-width: 370px;
    @include ellipsis-multiline();
  }

  .content-describe {
    margin-top: 8px;
    font-weight: 400;
    font-size: 12px;
    color: var(--colorTextSecondary);
    line-height: 18px;
  }

  .common-form {
    margin-top: 40px;
    .ant-form-item-control-input{
      width: 370px;
    }
    .ant-form-item{
      margin-bottom: 24px;
      // &.ant-form-item-has-error,.ant-input-status-error{
      //   border-color: var(--colorError);
      // }
      &.psd-form-item{
        margin-bottom: 8px;
        .ant-form-item-explain-error{
          // margin-bottom: 8px;
        }
      }
    }

    input:-webkit-autofill {
      -webkit-box-shadow: 0 0 0px 300px var(--colorBorderSecondary) inset;
      box-shadow: 0 0 0px 1000px var(--colorBorderSecondary) inset;
      // -webkit-box-shadow: 0 0 0px 300px #E8E8E8 inset;
      // box-shadow: 0 0 0px 1000px #E8E8E8 inset;
      -webkit-text-fill-color: var(--colorText); /* 设置字体颜色 */
      transition: background-color 5000s ease-in-out 0s; /* 防止背景色闪烁 */
    }
    .ipt {
      height: 38px;
      width: 100%;
      padding-left: 14px;
      padding-right: 14px;
      border-radius: 8px;
      transition: all 200ms ease-in-out;
      
      font-weight: 400;
      font-size: 14px;
      color: var(--colorText);
      line-height: 24px;
      background: var(--colorFillSecondary);
      letter-spacing:normal;
      // &::placeholder {
      //   font-size: var(--font-size-base);
      //   color: var(--colorTextTertiary);
      // }
      &.error {
        border: 1px var(--colorError) solid;
      }
      &:focus{
        border: 1px var(--colorPrimary) solid;
      }
      &[type=password]{
        font-family: system-ui, sans-serif;
      }
    }
    // .input-suffixicon {
    //   position: absolute;
    //   right: 12px;
    //   top: 50%;
    //   transform: translateY(-50%);
    //   cursor: pointer;
    //   color: var(--colorIconQuaternary);
    //   font-size: 20px;
    //   z-index: 100;
    // }

    .form-submit {
      margin-top: 24px;
      width: 370px;
      .btn {
        width: 100%;
      }
    }
  }
  .hidden{
    display: none;
  }
  .concat-me {
    margin-left: auto;
    margin-right: auto;
    margin-top: auto;
    .unable-login {
      font-weight: 400;
      font-size: 12px;
      color: var(--colorTextTertiary);
      line-height: 17px;
    }
    .concat-link {
      font-weight: 400;
      font-size: 12px;
      line-height: 17px;
      color: var(--colorText);
      cursor: pointer;
      text-decoration-line: underline;
    }
  }
  .operate-item{
    font-size: 12px;
    line-height: 18px;
    color: var(--colorTextSecondary);
    div{
      cursor: pointer;
      &.oi-r{
        color: var(--colorPrimary);
      }
    }
  }
  .back{
    margin-bottom: 24px;
    font-size: 14px;
    line-height: 22px;
    color: var(--colorTextTertiary);
    cursor: pointer;
    .svg-icon{
      font-size: 16px;
      width: 16px;
      height: 16px;
    }
  }
  .login-meta {
    position: absolute;
    bottom: 16px;
    left: 0;
    right: 0;
    font-size: 12px;
    color: var(--colorTextTertiary);
    line-height: 16px;
    text-align: center;
    .link{
      cursor: pointer;
      text-decoration: underline;
      color: var(--colorTextSecondary);
    }
  }
  .common-tab.ant-tabs{
    .ant-tabs-tab-btn{
      font-size: 14px;
    }
    .ant-tabs-tab+.ant-tabs-tab{
      margin: 0 0 0 24px;
    }
  }
  .ant-form-item .ant-form-item-explain-error{
    max-width: 370px;
    word-break: break-word;
    // line-height: 30px;
    margin-bottom: 0;
    // padding-top: 6px;
    // margin-bottom: 24px;
    // padding-bottom: 6px;
    &:empty{
      padding-top: 0;
      margin-bottom: 0;
      padding-bottom: 0;
    }
  }
  .ant-form-item.mobile-form-item .ant-form-item-explain-error{
    padding-left: 102px;
  }
  .ant-form-item .ant-form-item-explain-warning{
    display: none;
  }
  .ant-form-item.ant-form-item-has-error{
    // margin-bottom: 42px!important;
  }
  .ant-form-item.ant-form-item-has-success{
    color: var(--colorText)!important;
    border-color: transparent!important;
  }

  input.ant-input-filled,
  input.ant-input-outlined {
      &.ant-input-status-warning:not(.ant-input-disabled) {
          border-color: transparent;
          color: var(--colorText)!important;
          input{
            color: var(--colorText)!important;
          }
      }
  }
  .ant-input-filled,
  .ant-input-outlined {
      &.ant-input-status-warning:not(.ant-input-disabled) {
          border: transparent;
          color: var(--colorText)!important;
          input{
            color: var(--colorText)!important;
          }
      }
  }
}


.login-tooltip {
  .ant-tooltip-content {
    .ant-tooltip-inner {
      color: var(--colorText);
    }
  }
}

.ant-select-dropdown.select-lang-popup {
  background: var(--colorBgElevated);
  box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.1);
  border-radius: 12px;
  width: 128px;
  padding: 8px;
  box-sizing: content-box;
  .ant-select-item {
    color: var(--colorText);
  }
  .ant-select-item-option-selected:not(.ant-select-item-option-disabled).ant-select-item-option-selected:not(.ant-select-item-option-disabled){
    background: var(--colorFillTertiary);
    border-radius: 12px;
  }
  .ant-select-item-option-active:not(.ant-select-item-option-disabled){
    border-radius: 12px;
  }
}

/*暗色 特殊处理样式*/
// .dark{
//   .login-wrapper .login-center .right .login-from .item .input-suffixicon{
//     color: var(--colorIconTertiary);
//   }
// }