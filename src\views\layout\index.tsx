import React, { useEffect, useState } from "react";
import {connect} from "react-redux";
import SideNav from './sideNav/index'
import { Outlet, useLocation,useNavigate } from 'react-router-dom';
import './index.scss'
import { useOpenIM } from "@/contexts/OpenIMContext";

// 不需要显示左侧导航的路由地址
const hideSidePaths = ['/robotSetting']

const Layout =(props:any) =>{
    const { userInfo,isLogin } = props
    const token = userInfo.imToken
    const userId = userInfo.imUserId
    const [showSideNav,setShowSideNav] = useState(true)
    const navigate = useNavigate();
    const location = useLocation();
    const {pathname}= location
    const { service, isReady, error } = useOpenIM();

    useEffect(()=>{
      // console.log('---Layout service change',service)
      if (!service || !token || !userId) return
      // console.log('++++++Layout service change',service)
      loginIm()
    },[service,token,userId])

    // useEffect(()=>{
    //   // console.log('Layout activeCompanyId',activeCompanyId)
    // },[activeCompanyId])

    useEffect(()=>{
      // console.log('path', pathname,hideSidePaths.includes('pathname'))
      setShowSideNav(!hideSidePaths.includes(pathname))
    },[pathname])

    // useEffect(()=>{
    //   // console.log('++++++Layout isLogin',isLogin)
    //   // if (!userInfo.isLogin){
    //   //   service.logout()
    //   // }
    // },[service,isLogin])

    const loginIm=()=>{
      service.login(userId, token)
    }

    return (
      <div className={"layout"}>
        {showSideNav ? <SideNav  {...props}  />:null}
        <main>
            <Outlet /> {/* 渲染子路由 */}
        </main>
      </div>
    )
}

const mapStateToProps = (state: any) => ({
  userInfo: state.app.userInfo,
  isLogin: state.app.isLogin,
  globalRegion: state.app.globalRegion,
  globalLang: state.app.globalLang,
});

const mapDispatchToProps = (dispatch: any) => ({
})

export default connect(mapStateToProps, mapDispatchToProps)(Layout);
