.create-robot-document-modal {
    .mutil-upload-content{
        padding: 27px 0 8px;
    }
    .ant-list-item{
        position: relative;
    }
    .ant-upload-list {
        padding-top: 12px;
        .ant-upload-list-item{
            padding: 8px;
            padding-left: 20px;
            font-weight: 400;
            font-size: 14px;
            color: var(--colorText);
            line-height: 20px;
            cursor: pointer;
            height: 36px;
            margin-top: 0;
            &:hover{
                background: var(--colorFillQuaternary);
                border-radius: 12px;
            }
            .ant-btn-icon img{
               width: 16px;
            }
            .ant-upload-list-item-actions{
                width: 16px;
                .ant-btn-variant-text:not(:disabled):not(.ant-btn-disabled):hover{
                    background: transparent;
                }
            }
        }
        .ant-upload-icon{
            display: none;
        }
        .icon_attachment{
            position: absolute;
            top: 10px;
            left: 8px;
            color: var(--colorIconTertiary);
            font-size: 16px;
            z-index: 10;
        }
    }
}
.mutil-upload-content{
    .mutil-upload-wrap {
      display: flex;
      width: 100%;
      position: relative;
      flex-direction: column;
      .ant-upload {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        width: 100%;
        height: 100%;
      }
      .ant-upload-drag{
        border: 0;
        background: var(--colorFillTertiary);
      }
      .ant-upload-select{
        width: 100%;
        display: flex;
        justify-content: center;
        cursor: pointer;
        background: var(--colorFillTertiary);
        border-radius: 8px;
       }
       
      .ant-upload-trigger{
        width: 100%;
        height: 100%;
        padding: 24px;
      }
      .ant-upload-text{
        width: 100%;
        display: flex;
        justify-content: center;
        font-weight: 600;
        font-size: 13px;
        color: var(--colorTextSecondary);
        line-height: 20px;
      }
      .ant-upload-drag-icon {
        font-size: 24px;
        color: var(--colorText);
        margin-bottom: 15px;
      }
      .ant-upload-hint {
        margin-top: 8px;
        font-weight: 400;
        font-size: 12px;
        color: var(--colorTextTertiary);
        line-height: 20px;
      }
    }
    }