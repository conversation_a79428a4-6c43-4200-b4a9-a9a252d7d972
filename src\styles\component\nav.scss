.nav {
  padding: 24px 0;
  height: var(--nav-height);
  .nav-content {
    display: flex;
    align-items: center;
    .left {
      .logo {
        height: 40px;
        user-select: none;
      }
    }
    .right {
      margin-left: auto;
      .right-meta {
        display: flex;
        align-items: center;
        height: 40px;
        .item {
          margin-left: 25px;
          background: #ffffff;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 25px;
          cursor: pointer;
          padding: 10px;
          .name {
            font-weight: 600;
            margin-left: 10px;
            color: var(--colorText);
          }
        }
      }
    }
  }
}
