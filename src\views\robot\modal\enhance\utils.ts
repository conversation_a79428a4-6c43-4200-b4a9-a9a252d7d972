
import docIcon from "../../../../assets/docx-icon.png";
import pdfIcon from "../../../../assets/pdf-icon.png";
import txtIcon from "../../../../assets/txt-icon.png";

// 获取文件类型，显示对应图标
const getFileType = (type: string) => {
    let image = undefined;
    switch (type) {
        case "pdf":
            image = pdfIcon;
            break;
        case "doc":
        case "docx":
            image = docIcon;
            break;
        case "txt":
            image = txtIcon;
            break;
        default:
            break;
    }
    return image
}

export { getFileType }