import React, { useState, useEffect, forwardRef, useRef, useImperativeHandle } from "react";
import { connect } from "react-redux";
import { Modal, message, Input, Flex, Form, Select } from "antd";
import classNames from "classnames";
import { useTranslation } from 'react-i18next';
import { $trim } from '@/utils/common'

import { api_create_robot } from "@/api/robotManage2";
import { api_get_my_direct_departments } from "@/api/contacts";

import './index.scss'
import Svgicon from "@/components/svgicon";
import UploadImage from "../../component/uploadImage";
const { TextArea } = Input;


// 新建机器人弹框
const CreateRobotModal = forwardRef((props: any, ref) => {
  const { t } = useTranslation();
  /** 自定义暴露给父组件的实例值 **/
  // useImperativeHandle(ref, () => ({
  //   init() {
  //   }
  // }))

  /* props */
  const { visible: isVisible, activeCompanyId, activeCompanyName, isActiveCompanyAdmin, userInfo } = props
  const { userId: currentUserId } = userInfo

  /* state */
  const [robotLogo, setRobotLogo] = useState<any>('') // 机器人logo
  const [isLoading, setIsLoading] = useState(false) // 是否提交中
  const [isSubmiting, setIsSubmiting] = useState(false) // 是否提交中
  const [submittable, setSubmittable] = useState<boolean>(false);
  const [departmentList, setDepartmentList] = useState<any>([]); // 当前用户 作为supervisor 的 直属部门 集合

  const [botTypes, setBotTypes] = useState<any>([
    {//个人机器人
      value: 'PERSONAL',
      label: t('robot-manage.create-robot.personal-bot'),
      visible: true
    },
    {//部门机器人
      value: 'DEPARTMENT', // 仅supervisor创建时显示此选项，选项显示部门名
      label: t('robot-manage.create-robot.department-bot'),
      desc: t('robot-manage.create-robot.department-bot-desc'),
      visible: false
    },
    {//公司机器人
      value: 'COMPANY', // 仅公司管理员创建时显示此选项，选项显示公司名
      label: t('robot-manage.create-robot.company-bot'),
      desc: t('robot-manage.create-robot.company-bot-desc'),
      visible: false
    }
  ]); // 机器人类型选项

  const [form] = Form.useForm(); // 表单
  const formRef = useRef<any>(null);
  // Watch all values
  const values = Form.useWatch([], form);

  useEffect(() => {
    init()
  }, []);

  useEffect(() => {
    form
      .validateFields()
      .then(() => setSubmittable(true))
      .catch(() => setSubmittable(false));
  }, [form, values]);


  /* method */
  // 初始化
  const init = async () => {
    const supervisorDepartments = await getSupervisorDepartments()
    // 表单赋值
    form.setFieldValue('robotType', 'PERSONAL') // 默认个人机器人
    form.setFieldValue('companyBot', activeCompanyName)
    // 设置机器人类型选项
    setBotTypes(botTypes.map((i: any) => {
      if (i.value === 'DEPARTMENT') {
        i.visible = supervisorDepartments && supervisorDepartments.length > 0
      }
      if (i.value === 'COMPANY') {
        i.visible = isActiveCompanyAdmin
      }
      return i
    }))
  }

  // 确认
  const handleIsRobotModalOk = async () => {
    try {
      const values: any = await form.validateFields();
      const { robotName, robotIntroduction, robotType, departmentId } = values
      const data: any = {
        seq: 0,
        robotName: $trim(robotName),
        robotLogo: robotLogo,
        robotType,
        robotIntroduction: $trim(robotIntroduction)
      }
      if (robotType === 'DEPARTMENT') {
        data.departmentId = departmentId
      }
      setIsSubmiting(true)
      api_create_robot(data).then(() => {
        setIsSubmiting(false)
        message.success(t("robot-manage.create-robot-success"))
        handleIsRobotModalCancel()
        props.onSumit()
      }).catch((err: any) => {
        setIsSubmiting(false)
        console.log(err)
      })
    } catch (errorInfo) {
      console.log('Failed:', errorInfo);
    }

  }
  // 取消
  const handleIsRobotModalCancel = () => {
    props.onClose()
  }

  // 表单必填校验
  const formRequiredValidator = ({ }) => ({
    validator(_: any, value: any) {
      const _value = $trim(value)
      if (_value != '' && _value != null && _value != undefined) {
        return Promise.resolve();
      }
      return Promise.reject(new Error(t('common.input-required') as string));
    },
  })

  const handleRoboLogoChange = (res: any) => {
    setRobotLogo(res)
  }


  // 获取当前用户是supervisor 的部门集合
  const getSupervisorDepartments = async () => {
    setIsLoading(true)
    const res: any = await api_get_my_direct_departments(null, true).catch(() => {
      setIsLoading(false)
    })
    setIsLoading(false)
    if (!res) return []
    const datas = res.data || []
    console.log('datas', datas)
    setDepartmentList(datas.map((i: any) => ({ value: i.id, label: i.name })))
    return datas
  }

  return (
    <>
      <Modal title={t(["robot-manage.create-robot-button"])}
        wrapClassName="create-robot-modal"
        open={isVisible}
        onOk={handleIsRobotModalOk}
        onCancel={handleIsRobotModalCancel}
        footer={undefined}
        okText={t("app.ok")}
        cancelText={t("app.cancel")}
        maskClosable={false}
        centered
        cancelButtonProps={{ variant: "filled", color: "default" }}
        okButtonProps={{ disabled: isSubmiting || !submittable || isLoading }}
      >
        <div>
          <div className={"upload-con"}>
            <Flex vertical align={"center"}>
              <UploadImage onUpdate={handleRoboLogoChange} icon={<Svgicon svgName="upload_avatar" />} />
              <div className="upload-text">{t('robot-manage.create-robot.robot-logo')}</div>
            </Flex>
          </div>
          <Form ref={formRef} className="common-form" name="validateOnly" layout="vertical" form={form} autoComplete="off" requiredMark={false} disabled={isSubmiting}>
            {/* 机器人名称 */}
            <Form.Item shouldUpdate={true} name="robotName" label={t("robot-manage.create-robot.robot-name")} rules={[formRequiredValidator]}>
              <Input variant="filled" showCount maxLength={40} placeholder={t("robot-manage.create-robot.robot-name-hint") as string} />
            </Form.Item>
            {/* 机器人类型 个人机器人、公司机器人、部门机器人 */}
            <Form.Item shouldUpdate={true} name="robotType" label={t("robot-manage.create-robot.robot-type")} rules={[formRequiredValidator]}>
              <Select variant="filled" popupClassName="bot-type-selector" placeholder={t("common.please-select") as string} suffixIcon={<Svgicon svgName="icon_select_suffix" />}
                options={botTypes.filter((i: any) => i.visible)}
                optionRender={(option) => (
                  <Flex className="bot-type-selector-text" vertical gap={8}>
                    <div>{option.data.label}</div>
                    {option.data.desc ? <div className="desc">{option.data.desc}</div> : null}
                  </Flex>
                )}
              />
            </Form.Item>
            {/* 部门机器人类型显示部门选择框 */}
            {
              form.getFieldValue('robotType') === 'DEPARTMENT' ?
                <Form.Item shouldUpdate={true} name="departmentId" label={t('robot-manage.create-robot.department-bot')} rules={[formRequiredValidator]}>
                  <Select variant="filled" popupClassName="departments-selector" placeholder={t("common.please-select") as string} suffixIcon={<Svgicon svgName="icon_select_suffix" />}
                    options={departmentList}
                    optionRender={(option) => (
                      <Flex className="bot-type-selector-text" vertical gap={8}>
                        <div>{option.data.label}</div>
                      </Flex>
                    )}
                  />
                </Form.Item> : null
            }
            {/* 公司机器人类型显示公司名称 */}{
              form.getFieldValue('robotType') === 'COMPANY' ?
                <Form.Item required={false} label={t('robot-manage.create-robot.company-bot')}>
                  <Input value={form.getFieldValue('companyBot')} variant="filled" disabled placeholder={t("company.company-name") as string} />
                </Form.Item> : null
            }
            {/* 机器人简介 */}
            <Form.Item name="robotIntroduction" label={t("robot-manage.create-robot.robot-introduction")}>
              <TextArea
                placeholder={t("robot-manage.create-robot.robot-introduction-hint") as string}
                autoSize={{ minRows: 4, maxRows: 8 }}
                variant="filled"
                showCount={true}
                maxLength={500} />
            </Form.Item>
          </Form>
        </div>
      </Modal>
    </>
  );
});
const mapStateToProps = (state: any) => ({
  userInfo: state.app.userInfo,
  isActiveCompanyAdmin: state.app.isActiveCompanyAdmin,
  activeCompanyId: state.app.activeCompanyId,
  activeCompanyName: state.app.activeCompanyName,
});
const mapDispatchToProps = (dispatch: (e: any) => void) => ({
});
export default connect(mapStateToProps, mapDispatchToProps)(CreateRobotModal);