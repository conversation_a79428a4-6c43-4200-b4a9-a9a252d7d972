@import '../../../styles/mixin.scss';
.robot-manage-wrap{
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    .page-nav{
        flex-shrink: 0;
        width: 100%;
        height: var(--page-nav-height);
        padding: 0 20px;
        .nav-title{
            flex-shrink: 0;
            font-weight: 600;
            font-size: 18px;
            color: var(--colorText);
            line-height: 24px;
        }
        @mixin common {
            background: var(--colorBgBase);
            border-radius: 12px!important;
            // border: 1px solid var(--colorBorderSecondary);
        }
        // .ant-input-outlined{
        //     width: 400px;
        //     font-size: 14px;
        //     line-height: 22px;
        //     &::placeholder{
        //         color: var(--colorTextQuaternary);
        //     }
        //     @include common;
        // }
        // .ant-input-prefix{
        //     color: var(--colorIconTertiary);
        //     font-size: 16px;
        //     margin-right: 10px;
        // }
        .button-icon{
            height: 40px;
            width: 40px;
            color: var(--colorIconSecondary);
            font-size: 16px;
            @include common;
        }
    }
    .segmented-types{
        margin-top: 8px;
        flex-shrink: 0;
        padding-left: 16px;
    }
    .page-main{
        flex: 1;
        overflow: auto;
        padding: 20px 40px;
        padding-right: 30px;
    }
    $space:15px;
    // 卡片/列表样式
    .card-list.grid{
        display: flex!important;
    }
    .card-list.list{
        width: 640px;
        margin: 0 auto;
        .card-list-item{
            width: 100%;
            min-height: 143px;
            border-color: var(--colorBorderSecondary );
            &:hover{
                border-color: transparent;
                .cl-operate.cl-operate-btns{
                    padding-bottom: 8px;
                }
            }
        }
        .cl-operate{
            padding-top: 14px;
        }
        .cl-operate-right-btns{
            flex-direction: row-reverse;
        }
    }
    // 卡片/网格样式
    .card-list-item{
        min-height: 180px;
        flex-shrink: 0;
        padding: 0;
        border: 1px solid transparent;
        &:hover{
            border-color: var(--colorBorder);
            .cl-operate{
                display: none;
            }
            .cl-operate.cl-operate-btns{
                display: flex;
                padding-top: 7px;
                padding-bottom: 14px;
            }
        }
    }
    .cl-content{
        flex: 1;
        padding: $space $space 0;
    }
    .cl-content-img{
        width: 48px;
        height: 48px;
        border-radius: 10px;
        border: 1px solid var(--colorBorderSecondary);
        flex-shrink: 0;
    }
    .cl-content-text{
        flex: 1;
    }
    .cl-content-title{
        margin-bottom: 4px;
        @include ellipsis-multiline(1);
    }
    .cl-content-llm{
        font-weight: 400;
        font-size: 12px;
        color: var(--colorTextTertiary);
        line-height: 20px;
        .svg-icon{
            font-size: 16px;
        }
        @include ellipsis-multiline(1);
    }
    .cl-content-bottom{
        margin: 10px 0;
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
        color: var(--colorTextSecondary);
        @include ellipsis-multiline(2);
    }
    .cl-operate{
        padding: 8px 0 $space;
        width: calc(100% - $space * 2);
        margin: 0 auto;
        border-top: 1px solid var(--colorBorderSecondary);
        font-weight: 400;
        font-size: 12px;
        color: var(--colorTextTertiary);
        line-height: 20px;
        .svg-icon{
            font-size: 16px;
        }
        .more{
            width: 32px;
            height: 32px;
            padding: 8px;
            color: var(--colorIcon);
        }
        .icon_org{
            font-size: 12px;
        }
    }
    .card-list.grid{
        .cl-operate-left{
            flex: 1;
            overflow: hidden;
            .cl-operate-left-con{
                flex: 1;
                overflow: hidden;
            }
            .cl-name{
                flex: 1;
                @include ellipsis;
            }
        }
    }
    .card-list.list{
        .cl-operate-left{
            .cl-name{
                max-width: 200px;
                @include ellipsis;
            }
        }
    }
    .cl-operate-btns{
        display: none;
    }
    .margin-left-auto{
        margin-left: auto;
    }
}

.robot-filters-popover{
    .ant-popover-inner{
        padding: 12px 8px 8px;
    }
    .rf-title{
        font-weight: 600;
        font-size: 12px;
        color: var(--colorText);
        line-height: 26px;
        padding-left: 8px;
        &:last-of-type{
            margin-top: 16px;
        }
    }
    .ant-radio-group{
        display: flex;
        flex-direction: column;
        gap: 6px;
    }
    .ant-radio-wrapper{
        height: 32px;
        margin-right: 0;
        display: flex;
        align-items: center;
        padding: 0 8px;
        font-size: 14px;
        color: var(--colorTextSecondary);
        line-height: 20px;
        &.ant-radio-wrapper-checked{
            font-weight: 500;
            font-size: 14px;
            color: var(--colorPrimary);
        }
        &:hover{
            background: var(--colorFillTertiary);
            border-radius: 12px;
            &:not(.ant-radio-wrapper-checked) .ant-radio-inner{
                border-color: var(--colorIconQuaternary);
            }
        }
    }
}
  