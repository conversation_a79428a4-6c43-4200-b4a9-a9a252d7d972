// 获取优先级标识
export const getPriorityFlag = (priority: any) => {
    // URGENT：选择后，标题框最前面固定展示两个感叹号【!】标识
    // IMPORTANT：选择后，标题框最前面固定展示一个感叹号【!】标识
    if (!priority) return ''
    if (priority === 'URGENT') {
        return '!!'
    }
    if (priority === 'IMPORTANT') {
        return '!'
    }
    return ''
}

// 过滤HTML标签，提取纯文本
export const stripHtmlTags = (html: any) => {
    const tmp = document.createElement('div');
    tmp.innerHTML = html;
    return tmp.textContent || tmp.innerText || '';
};

// 高亮显示匹配文本的函数
export const highlightText = (content: any, keyword: any) => {
    if (!keyword.trim()) return { __html: content };

    // 获取纯文本内容用于搜索
    const textContent = stripHtmlTags(content);

    // 转义正则特殊字符
    const escapedKeyword = keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const regex = new RegExp(`(${escapedKeyword})`, 'gi');

    // 在纯文本中查找匹配位置
    let match;
    const indices = [];
    while ((match = regex.exec(textContent)) !== null) {
        indices.push({
            start: match.index,
            end: match.index + match[0].length
        });
    }

    // 如果没有匹配，返回原始内容
    if (indices.length === 0) {
        return { __html: content };
    }

    // 构建高亮后的HTML
    let highlightedHtml = '';
    let lastIndex = 0;

    indices.forEach(({ start, end }) => {
        // 添加匹配前的部分
        highlightedHtml += content.substring(lastIndex, start);

        // 添加高亮部分
        highlightedHtml += `<mark>${content.substring(start, end)}</mark>`;

        lastIndex = end;
    });

    // 添加剩余部分
    highlightedHtml += content.substring(lastIndex);

    return { __html: highlightedHtml };
}