import { connect } from "react-redux";
import { useTranslation } from 'react-i18next';
import { useNavigate } from "react-router-dom";
import React, { useState, useRef, useEffect, forwardRef, useImperativeHandle } from "react";
import { Flex, Modal, message, Badge } from 'antd'
import SvgIcon from '@components/svgicon'; // 根据实际路径引入
import classnames from "classnames";
import { api_exit_tenant } from '@/api/contacts'
import { change_app_is_refresh_companies, change_app_active_company } from '@/store/action'
import { useOpenIM } from '@/contexts/OpenIMContext';

import classNames from "classnames";
import './index.scss'

const defaultmenuItemResource: any[] = [
    {
        index: 2,
        key: "NewContacts",
        label: 'contacts.new-contacts',
        icon: <SvgIcon svgName="icon_new_user" />,
        meta: {
            newCount: null // 收到的好友申请数，点击tab消失，两种更新方式：主动获取、实时回调
        }
    },
    {
        index: 3,
        key: "MyContacts",
        label: 'contacts.my-contacts',
        icon: <SvgIcon svgName="menu_mycontacts" />
    },
    {
        index: 4,
        key: "MyGroups",
        label: 'contacts.my-groups',
        icon: <SvgIcon svgName="menu_mygroups" />
    }
]
const authMenuItemResource: any[] = [
    {
        index: 1,
        key: 'Organization',
        label: 'contacts.organize.menu-label',
        icon: <SvgIcon svgName="menu_org" />,
        id: '0',
        children: [
            // 当前用户所属最小部门集合
        ]
    },
    {
        index: 6,
        key: 'Leave',
        label: 'contacts.leave-organize.menu-label',
        icon: <SvgIcon svgName="icon_signout" />,
        type: 'operate'
    }
]
// 管理员权限菜单
const adminAuthMenuItemResource: any[] = [
    {
        index: 5,
        key: 'Admin',
        label: 'contacts.admin-panel',
        icon: <SvgIcon svgName="menu_toadmin" />,
        type: 'operate'
    }
]


// 侧边菜单
const SideMenuComponent = forwardRef((props: any, ref) => {
    // 自定义暴露给父组件的实例值
    useImperativeHandle(ref, () => ({
        // 初始化
        emit_init() {
        },
        // 更新Organization 菜单选项
        emit_update_organization_children(resource: any) {
            // console.log('SideMenuComponent resource',resource)
            updateOrganizeChildren(resource)
        }
    }))
    const { service: ImService, isConnected: isConnectedIm, newContacts } = useOpenIM();
    const { t } = useTranslation();
    const navigate = useNavigate();
    const [modal, contextHolder] = Modal.useModal();
    const containerRef = useRef(null)
    const tabWindow = useRef<any>(null)

    const { isPrivate, activeCompanyId, isActiveCompanyAdmin, userInfo, onChangeMenu, activeCompanyName, onUpdateDepartment } = props
    const { userId: currentUserId } = userInfo

    const [menuItems, setMenuItems] = useState<any>(defaultmenuItemResource); // 菜单选项
    const [activeMenu, setActiveMenu] = useState<any>(null); // 当前选中的菜单
    const [isAdmin, setIsAdmin] = useState<any>(false); // 是否是当前企业的管理员

    const [showNewCounts, setShowNewCounts] = useState<any>(null); // 收到的好友申请数，点击 '新的好友tab'消失，两种更新方式：主动获取、实时回调

    useEffect(() => {
        init()
    }, [])

    useEffect(() => {
        // 根据企业 初始化菜单：个人空间 没有组织联系人、admin面板、退出企业菜单
        let items = [...defaultmenuItemResource]
        let _isAdmin = isActiveCompanyAdmin
        setIsAdmin(isActiveCompanyAdmin)
        // console.log('_isAdmin',_isAdmin)
        setIsAdmin(_isAdmin)
        if (!isPrivate && String(activeCompanyId) !== '0') {
            items = [...items, ...authMenuItemResource]
            if (_isAdmin) {
                items = [...items, ...adminAuthMenuItemResource]
            }
        }
        items = items.sort((a: any, b: any) => a.index - b.index) // 排序
        const _activeMenu = items[0].key // 选中菜单
        setActiveMenu(_activeMenu)
        setMenuItems(items)
        onChangeMenu && onChangeMenu(_activeMenu)
    }, [activeCompanyId, isActiveCompanyAdmin, ImService, isPrivate])

    useEffect(() => {
        if (!ImService || !isConnectedIm) return
    }, [ImService, isConnectedIm])

    useEffect(() => {
        // console.log('sidemenu newContacts or activeMenu change', newContacts, activeMenu)
        if (newContacts.length > 0) { // 新的好友
            // 获取最新缓存 加好友申请的时间
            const latestAddFrinedCreateTimeStore = localStorage.getItem('AMA_LST_AD_TM')
            const newApplyCount = !latestAddFrinedCreateTimeStore ? newContacts.length : newContacts.filter((i: any) => {
                return (i.createTime > latestAddFrinedCreateTimeStore)
            }).length
            // console.log('newApplyCount', newApplyCount)
            // 更新标记 好友申请数
            setShowNewCounts(newApplyCount)
        }

        // 若当前菜单选中 "新的好友",标记好友申请数已读 , 更新缓存 加好友申请的时间
        if (activeMenu === 'NewContacts' && newContacts.length > 0) {
            // 更新缓存 加好友申请的时间
            const latestAddFrinedCreateTimeStore = localStorage.getItem('AMA_LST_AD_TM')
            let latestAddFrinedCreateTime: any = null
            newContacts.forEach((i: any) => {
                if (latestAddFrinedCreateTime === null) {
                    latestAddFrinedCreateTime = i.createTime
                }
                if (latestAddFrinedCreateTime < i.createTime) {
                    latestAddFrinedCreateTime = i.createTime
                }
            });
            if (latestAddFrinedCreateTimeStore !== latestAddFrinedCreateTime) {
                // console.log('更新缓存 latestAddFrinedCreateTime', latestAddFrinedCreateTime)
                localStorage.setItem('AMA_LST_AD_TM', latestAddFrinedCreateTime)
            }
            setShowNewCounts(0) // 标记好友申请数已读
        }
    }, [activeMenu, newContacts])

    // 初始化
    const init = async () => {
    }

    // 组织架构菜单 子菜单 显示部门信息（当前所属最小部门的名称）
    const updateOrganizeChildren = (resource: any) => {
        // resource = []
        // for (let i = 1;i<50;i++){
        //     resource.push({
        //         id: i,
        //         name: '测试部门' + i
        //     })
        // }
        let items = [...menuItems]
        let orgTarget: any = items.filter((i: any) => i.key === 'Organization')
        if (orgTarget.length > 0) {
            orgTarget = orgTarget[0]
            orgTarget.children = resource.map((ci: any) => {
                return {
                    key: `Organization-children-${ci.id}`,
                    label: ci.name,
                    id: ci.id,
                    type: "Department"
                }
            })
            setMenuItems(items)
        }
    }

    // 选择菜单
    const handleSelectMenu = async (target?: any, e?: any) => {
        e && e.stopPropagation()
        if (target.key === 'Organization') {
            onUpdateDepartment && onUpdateDepartment(target)
        }
        if (target.type === 'Department') {
            setActiveMenu(target.key)
            onChangeMenu && onChangeMenu('Organization')
            onUpdateDepartment && onUpdateDepartment(target)
            return
        }
        if (target.type !== 'operate') {
            setActiveMenu(target.key)
            // console.log(target.key)
            onChangeMenu && onChangeMenu(target.key)
            return
        }

        if (target.key === 'Admin') {
            // 跳转到Admin管理平台的Organization菜单
            openAdminPage()
        }
        if (target.key === 'Leave') {
            // 如用户是管理员，不允许用户退出企业
            if (isAdmin) {
                modal.error({
                    width: 548,
                    title: t('contacts.leave-organize.unable-leave'),
                    icon: null,
                    content: t('contacts.leave-organize.unable-leave-reason', { companyName: activeCompanyName as string }),
                    okText: t('common.ok2'),
                    className: 'no-icon mediumn',
                    centered: true,
                    closable: true,
                    onCancel: () => {
                    },
                    onOk: () => {
                    }
                });
            } else {// 反之提示 
                modal.confirm({
                    width: 548,
                    title: t('contacts.leave-organize.leave-confirm'),
                    icon: null,
                    content: t('contacts.leave-organize.leave-confirm-tip', { companyName: activeCompanyName as string }),
                    okText: t('app.ok'),
                    okButtonProps: { color: "danger", variant: "filled" },
                    centered: true,
                    className: 'no-icon mediumn',
                    closable: true,
                    onCancel: () => {
                        // onCancel()
                    },
                    onOk: () => {
                        quit_company()
                    }
                });
            }

        }
    }

    // 跳转到Admin管理平台的Organization菜单
    const openAdminPage = () => {
        const location = window.location
        // 设置存储 admin 用户登录信息、企业ID、Token
        const userInfo: any = localStorage.getItem('AMA_USER_INFO')
        const token: any = localStorage.getItem('AMA_USER_TOKEN')
        localStorage.setItem('AD_AMA_USER_INFO', userInfo)
        localStorage.setItem('AD_AMA_USR_TKN', token)
        localStorage.setItem('AD_IS_AMA_USR_LG', 'true')
        localStorage.setItem('AD_ACTCID', activeCompanyId)

        // const adminUrl = location.origin + '/admin/page/organization/user'
        const adminUrl = location.origin + '/admin/page'
        // const adminUrl = 'http://localhost:3022' + '/admin/page/organization/user'
        try {
            // 检查窗口是否存在且未关闭
            if (tabWindow.current && !tabWindow.current?.closed) {
                tabWindow.current?.focus(); // 聚焦到已有标签页
            } else {
                // 打开新标签页并保存引用
                tabWindow.current = window.open(adminUrl, adminUrl);
                // 监听窗口关闭事件（可选：用于清理引用）
                if (tabWindow) {
                    tabWindow.current.addEventListener('beforeunload', () => {
                        tabWindow.current = null;
                    });
                }
            }
        } catch (error) {
            console.log('error', error)
            window.open(adminUrl, adminUrl)
        }
    }

    // 退出企业
    const quit_company = async () => {
        const res: any = await api_exit_tenant().catch(() => {
        })
        if (!res) return
        if (res?.data?.success) {
            // 设置选中公司为 "个人空间"
            await props.dispatch_change_app_is_refresh_companies(true)
            await props.dispatch_change_app_active_company({ id: '0', name: '个人空间' })
            message.success(t('success.submit'))
            // 马上退出，需要刷新企业列表，
            navigate('/')
        } else {
            message.error(t('error.request-failed'))
        }
    }

    return (
        <div className="contact-side-menu">
            <div className="side-menu-title">
                <span>{t('contacts.title')}</span>
            </div>
            {/* <Menu
                defaultSelectedKeys={['Organization-Product']}
                defaultOpenKeys={['Organization']}
                mode="inline"
                inlineCollapsed={false}
                items={menuItems}
            /> */}
            {
                menuItems ?
                    <div className="contact-menu-container" ref={containerRef}>
                        {menuItems.map((item: any, hIndex: number) => {
                            return (
                                <React.Fragment key={item.key}>
                                    <Flex align="center" onClick={(e) => handleSelectMenu(item, e)}
                                        className={classNames(["contact-menu-item-title", item.key, { active: activeMenu === item.key }])} gap={8}>
                                        {item.icon && item.icon}
                                        <span className="menu_label">{t(item.label)}</span>
                                        {
                                            item.key === 'NewContacts' && showNewCounts ?
                                                // <div className="new-count">{showNewCounts || ''}</div>
                                                <Badge className="new-count" count={showNewCounts} size="small" />
                                                : null
                                        }
                                    </Flex>
                                    {
                                        item.children?.length > 0 ?
                                            <Flex className="a-menu-item-list" vertical gap={4}>
                                                {
                                                    item.children ? item.children.map((citem: any) => {
                                                        return (
                                                            <Flex align="center" onClick={(e) => handleSelectMenu(citem, e)} key={citem.key}
                                                                className={classNames(["a-menu-item-submenu-content", { active: activeMenu === citem.key }])} gap={8}>
                                                                <SvgIcon className="sublevel" svgName="menu_sublevel" />
                                                                <span className="menu_label">{citem.label}</span>
                                                            </Flex>
                                                        )
                                                    })
                                                        : null
                                                }
                                            </Flex>
                                            : null
                                    }

                                </React.Fragment>
                            )
                        })}
                    </div>
                    : null
            }
            {contextHolder}
        </div>
    );
});
const mapStateToProps = (state: any) => ({
    isPrivate: state.app.isPrivate,
    userInfo: state.app.userInfo,
    isActiveCompanyAdmin: state.app.isActiveCompanyAdmin,
    activeCompanyId: state.app.activeCompanyId,
    activeCompanyName: state.app.activeCompanyName
})
const mapDispatchToProps = (dispatch: any) => ({
    dispatch_change_app_active_company: (data: any) => dispatch(change_app_active_company(data)),
    dispatch_change_app_is_refresh_companies: (data: any) => dispatch(change_app_is_refresh_companies(data))
})
const ConnectedCounter = connect(mapStateToProps, mapDispatchToProps, null, { forwardRef: true })(SideMenuComponent);
export default ConnectedCounter;
