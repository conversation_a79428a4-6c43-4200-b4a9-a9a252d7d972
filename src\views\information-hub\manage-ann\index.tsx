import { connect } from "react-redux";
import React, { useState, useRef, useEffect, forwardRef } from "react";
import { Badge, Button, DatePicker, Empty, Flex, Input, message, Modal, Pagination, Popover, Segmented, Select, Spin, Tabs, TabsProps, Tag, Tooltip } from 'antd'
import { useTranslation } from 'react-i18next';
import SvgIcon from '@components/svgicon';
import { getPriorityFlag, highlightText } from '../common'
import AnnOperate from './operate'
import AnnPublish from './publish'
import moment from 'moment';
import { api_get_admin_notification, api_publish_notification, api_delete_notification, api_recall_notification } from '@/api/information'
import { $isNull, $trim, parseTime } from "@/utils/common";

import './index.scss'
import classNames from "classnames";
import { calculatePageCount } from "@/utils/pagination";
const { RangePicker } = DatePicker;
const DateFormat = 'YYYY-MM-DD'


// 公告管理
const ManageAnn = forwardRef((props: any, ref: any) => {
    const { t } = useTranslation();
    const { activeCompanyName } = props

    const [panel, setPanel] = useState<any>('List'); // List 列表 Operate编辑
    const [annType, setAnnType] = useState<any>('Published'); // 当前选中的公告类型 Published Drafts
    const [searchValue, setSearchValue] = useState<any>(''); // 关键字输入框
    const [searchDate, setSearchDate] = useState<any>([]); // 日期输入框
    const [isLoading, setIsLoading] = useState<any>(false);
    const [isSearching, setIsSearching] = useState<any>(false);
    const [isResting, setIsResting] = useState<any>(false);
    const [currentPage, setCurrentPage] = useState<any>(1); // 分页-当前页
    const [totalCount, setTotalCount] = useState<any>(0); // 分页-总页数
    const [pageSize, setPageSize] = useState<any>(10); // 分页-一页数目
    const [dataList, setDataList] = useState<any>([]); // 列表数据
    const [operateResource, setOperateResource] = useState<any>({ visible: false, type: 'create', data: {} }); // 公告操作
    const [deleteResource, setDeleteResource] = useState<any>({ visible: false, id: null }); // 删除
    const [publishResource, setPublishResource] = useState<any>({ visible: false, id: null }); // 发布
    const [isPublishing, setIsPublishing] = useState<any>(false);

    const annTypes: TabsProps['items'] = [
        {
            key: 'Published',
            label: t('info-hub.published'),
            children: null,
        },
        {
            key: 'Drafts',
            label: t('info-hub.drafts'),
            children: null,
        }
    ];

    // 初始化
    useEffect(() => {
        init()
    }, [])

    const init = async () => {
        queryList()
        // setIsLoading(true)
        // let _list: any = []
        // for (let i = 1; i < 50; i++) {
        //     _list.push({
        //         id: i,
        //         title: "公告标题" + i,
        //         content: '公告内容公告内容公告内容公告内容公告内容公告内容公告内容公告内容公告内容公告内容公告内容公告内容公告内容公告内容公告内容公告内容公告内容' + i,
        //         createTime: '2025-05-05',
        //         priority: i == 1 ? 'Critical' : i == 2 ? 'Important' : 'Normal'
        //     })
        // }
        // setTotalCount(_list.length)
        // setTimeout(() => {
        //     setIsLoading(false)
        //     setDataList(_list)
        // }, 200)
    }

    const queryList = async (pageNo = currentPage, _annType?: any, isReset?: boolean) => {
        // console.log('_annType', _annType)
        const type = _annType ? _annType : annType
        const p: any = {
            pageNo,
            pageSize: pageSize,
            status: type === 'Drafts' ? 'DRAFT' : 'PUBLISHED'
        }
        setDataList([])
        if (!isReset) {
            if (!$isNull(searchValue)) { // 关键词
                p.keyword = $trim(searchValue)
            }
            if (searchDate.length > 0 && searchDate.join('') !== '') {
                const startDate = searchDate[0].format(DateFormat);
                const endDate = searchDate[1].format(DateFormat);
                p.publishDateStart = startDate// 发布开始时间
                p.publishDateEnd = endDate // 发布结束时间
            }
        }

        // console.log('p', p)
        setIsLoading(true)
        const res: any = await api_get_admin_notification(p).catch(() => {
            setIsLoading(false)
        })
        setIsLoading(false)
        if (!res || res.code !== 0) return
        const total = res?.data?.total
        setTotalCount(total)
        let list = res?.data?.list || []
        const pageCount = calculatePageCount(total, pageSize)
        console.log('pageCount', pageCount)
        //  如果当前页大于总页数, 重新请求
        if (total > 0 && pageNo > pageCount) {
            const page = currentPage - 1
            setCurrentPage(page)
            queryList(page)
            return
        }
        list = list.map((item: any) => {
            item.createTime = parseTime(item.createTime, "{y}-{m}-{d} {h}:{i}")
            item.publishedTime = item.publishedTime ? parseTime(item.publishedTime, "{y}-{m}-{d} {h}:{i}") : ''
            item.content = item.contentText
            item.operator = item.behalfOfName
            return item
        })
        setDataList(list)
        // console.log('res', res)
    }

    // 类型切换
    const onChangeTab = (key: any) => {
        setCurrentPage(1)
        setTotalCount(0)
        setAnnType(key)
        queryList(1, key)
    }

    // 关键字输入框输入
    const onSearchValueChange = (e: any) => {
        setSearchValue(e.target.value)
    }

    // 发布日期筛选
    const onChangeDate = (dates: any, dateStrings: any) => {
        // console.log('dateStrings',dateStrings)
        setSearchDate(dates);
    }

    // 搜索
    const onSearch = () => {
        setCurrentPage(1)
        queryList(1)
    }
    // 重置
    const onReset = () => {
        setSearchValue(null)
        setCurrentPage(1)
        setSearchDate([])
        // setSelectedTag()
        queryList(1, undefined, true)
    }

    // 禁用当天之后的日期
    const disabledDate = (current: any) => {
        // 返回 true 表示禁用该日期
        return current && current > moment().endOf('day');
    }

    // 操作返回
    const onBackList = () => {
        setPanel('List')
        setOperateResource({ ...operateResource, data: {}, visible: false })
    }

    // operate 返回刷新
    const onRefresh = (operateType: string) => {
        let tab = annType
        if (operateType === 'SAVE') {
            // 保存公告后，切换到草稿标签页
            tab = 'Drafts'
        } else {
            tab = 'Published'
        }
        setCurrentPage(1)
        setAnnType(tab)
        setPanel('List')
        setOperateResource({ ...operateResource, data: {}, visible: false })
        queryList(1, tab)
    }

    const toOperate = (type: any, data?: any) => {
        setPanel('Operate')
        setOperateResource({ ...operateResource, data: data ? { ...data } : {}, type: type, visible: true })
    }

    // 新增
    const onCreate = () => {
        toOperate('create')
    }

    // 查看详情
    const onViewDetail = (e: any, item: any) => {
        e.preventDefault()
        toOperate('detail', item)
    }

    // 发布状态-点击编辑
    const onClickPublishEdit = (e: any, item: any) => {
        e.stopPropagation()
        toOperate('publishEdit', item)
    }

    // 发布状态-点击撤回
    const onClickUnpublish = async (e: any, item: any) => {
        e.stopPropagation()
        const res: any = await api_recall_notification(item.id).catch(() => {
        })
        if (!res) return
        message.success(t('app.operate-success'))
        queryList()
    }

    // 草稿状态-点击编辑
    const onClickDraftsEdit = (e: any, item: any) => {
        e.stopPropagation()
        toOperate('edit', item)
    }

    // 点击删除
    const onClickDelete = (e: any, item: any) => {
        e.stopPropagation()
        setDeleteResource({ visible: true, id: item.id })
    }

    //取消删除
    const onDeleteCancel = () => {
        setDeleteResource({ visible: false, id: null })

    }

    // 确认删除
    const onDeleteOk = async () => {
        const res: any = await api_delete_notification(deleteResource.id).catch(() => {
        })
        if (!res) return
        setDeleteResource({ visible: false, id: null })
        message.success(t('app.operate-success'))
        queryList()
    }

    // 点击发布
    const onClickPublish = (e: any, item: any) => {
        e.stopPropagation()
        setPublishResource({ visible: true, id: item.id })
    }

    // 取消发布
    const onPublishCancel = () => {
        setPublishResource({ visible: false, id: null })

    }

    // 发布请求
    const onPublishSubmit = async (audiences: any) => {
        setIsPublishing(true)
        const res = await api_publish_notification({ id: publishResource.id, audiences }).catch(() => {
            setIsPublishing(false)
        })
        setIsPublishing(false)
        if (!res) return
        // console.log('res', res)
        message.success(t('app.operate-success'))
        setPublishResource({ visible: false, id: null })
        // 请求接口
        queryList()
    }

    // 切换分页-当前页
    const onChangePagination = (page: any) => {
        // console.log('page',page)
        setCurrentPage(page)
        queryList(page)
    }

    // // 获取发布者名称
    // const getOperatorName = (item: any) => {
    //     let name = item.behalfOfName
    //     if (item.behalfOfType === 'DEPARTMENT') {
    //         name = item.behalfOfName
    //     }
    //     if (item.behalfOfType === 'COMPANY') {
    //         name = activeCompanyName
    //     }
    //     return name
    // }

    return (
        <Flex className="manage-ann-page common-ann-page" vertical>
            <Flex className={`list-panel-con common-wrap ${panel === 'Operate' ? "hidden" : ""}`} vertical>
                <Flex className="page-nav" gap={20} align="center" justify="space-between" wrap={true}>
                    <span className="nav-title">{t('info-hub.ann-manage')}</span>
                    {/* 搜索 */}
                    <Flex className="search-area" gap={20} align="center">
                        <Input
                            className='search-input'
                            prefix={<SvgIcon svgName="icon_search" />}
                            placeholder={t('info-hub.search-input') as string}
                            value={searchValue}
                            onChange={onSearchValueChange}
                            onKeyDown={(e) => e.stopPropagation()}
                        // variant="filled"
                        />
                        {/* 时间必须小于当前时间节点 */}
                        {/* <DatePicker className='search-date'
                                onChange={onChangeDate}
                                disabledDate={disabledDate}
                                suffixIcon={<SvgIcon svgName="icon_datepicker" />}
                            /> */}
                        <RangePicker
                            className='search-date'
                            value={searchDate}
                            format={DateFormat}
                            onChange={onChangeDate}
                            disabledDate={disabledDate}
                            suffixIcon={<SvgIcon svgName="icon_datepicker" />}
                        />
                        <Button type="primary" onClick={() => onSearch()} disabled={isSearching}>{t('search')}</Button>
                        <Button color="default" variant="filled" onClick={() => onReset()} disabled={isResting}>{t('common.reset')}</Button>
                    </Flex>
                    <Button type="primary" onClick={() => onCreate()} icon={<SvgIcon svgName="icon_plus" />} disabled={isSearching}>{t('info-hub.add-ann')}</Button>
                </Flex>
                <div className="segmented-types">
                    <Segmented
                        className="common-segmented"
                        value={annType}
                        options={annTypes.map((i: any) => {
                            i.value = i.key
                            return i
                        })}
                        onChange={onChangeTab}
                    />
                </div>

                <Flex className={`list-panel page-main`} vertical>
                    <>
                        {
                            isLoading ?
                                <Spin className="full-spin" size="large" /> :
                                <>
                                    {
                                        dataList.length > 0 ?
                                            <Flex className="ann-list-con" vertical gap={20}>
                                                {dataList?.map((item: any) => (
                                                    <Flex onClick={(e) => onViewDetail(e, item)} className="list-item" align="center" gap={20} key={item.id}>
                                                        <div className={`li-card ${annType}`}>
                                                            <Flex className="li-card-top" gap={8} align="center">
                                                                <div className="li-card-priority">{getPriorityFlag(item.priority)}</div>
                                                                <div className="li-card-title" dangerouslySetInnerHTML={{ __html: item.title }} />
                                                                <div className="li-card-time">{item.publishedTime}</div>
                                                            </Flex>
                                                            {/* 发布者 */}
                                                            <div className="li-card-operator" dangerouslySetInnerHTML={{ __html: item.operator }}></div>
                                                            <div className="li-card-content" dangerouslySetInnerHTML={{ __html: item.content }} />
                                                            {/* 标签 */}
                                                            <Flex className="li-card-bottom" gap={40} align="center">
                                                                <Flex className="more" justify="flex-end">
                                                                    <SvgIcon svgName="more_dot" />
                                                                </Flex>
                                                                <Flex className="li-card-operate" justify="flex-end" align="center" gap={10}>
                                                                    {
                                                                        annType === 'Published' ?
                                                                            <>
                                                                                <Button onClick={(e) => onClickUnpublish(e, item)} type="primary">{t('info-hub.unpublish')}</Button>
                                                                                <Button onClick={(e) => onClickPublishEdit(e, item)} color="default" variant="filled">{t('app.edit')}</Button>
                                                                            </> : null
                                                                    }
                                                                    {
                                                                        annType === 'Drafts' ?
                                                                            <>
                                                                                <Button onClick={(e) => onClickPublish(e, item)} type="primary">{t('info-hub.publish')}</Button>
                                                                                <Button onClick={(e) => onClickDraftsEdit(e, item)} color="default" variant="filled">{t('app.edit')}</Button>
                                                                                <Button onClick={(e) => onClickDelete(e, item)} color="danger" variant="filled">{t('app.delete')}</Button>
                                                                            </> : null
                                                                    }
                                                                </Flex>
                                                            </Flex>
                                                        </div>
                                                    </Flex>
                                                ))}
                                            </Flex>
                                            :
                                            // 空数据
                                            <Empty className="page-empty" image={null} description={
                                                <><div>{t('common.no-result')}</div>
                                                    <div>{t('robot-manage.search-result')}</div>
                                                </>} />
                                    }
                                </>
                        }
                    </>
                    {/* 分页 */}
                    <Flex className="pagition" justify="flex-end">
                        {
                            totalCount > 0 ?
                                <Pagination
                                    current={currentPage}
                                    onChange={onChangePagination}
                                    total={totalCount}
                                    // showQuickJumper
                                    showSizeChanger={false}
                                    showTotal={(total) => `${t('common.total')} ${total}`}
                                    size="small"
                                />

                                : null}
                    </Flex>
                </Flex>
            </Flex>
            {
                panel === 'Operate' && operateResource.visible ?
                    <AnnOperate onRefresh={onRefresh} resource={operateResource} onBack={onBackList} />
                    :
                    null
            }
            {
                // 发布弹窗
                publishResource.visible ?
                    <AnnPublish visible={publishResource.visible} onClose={onPublishCancel} onSumit={onPublishSubmit}
                        resource={publishResource} /> : null
            }
            { // 删除确认弹窗
                <Modal width={272} title={t('info-hub.delete-ann-confrim')} maskClosable={false} centered closable={false}
                    okText={t("app.ok")} cancelText={t("app.cancel")} wrapClassName="common-confirm-modal"
                    open={deleteResource.visible} onCancel={() => onDeleteCancel()} onOk={() => onDeleteOk()}>
                </Modal>
            }
        </Flex>
    )
})


const mapStateToProps = (state: any) => ({
    companies: state.app.companies,
    activeCompanyId: state.app.activeCompanyId,
    activeCompanyName: state.app.activeCompanyName,
})
const mapDispatchToProps = (dispatch: any) => ({
    // dispatch_api_: (data:any) => dispatch(dispatch_api_(data))
})
const ConnectedCounter = connect(mapStateToProps, mapDispatchToProps, null, { forwardRef: true })(ManageAnn);
export default ConnectedCounter;