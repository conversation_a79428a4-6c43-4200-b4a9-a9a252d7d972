import React, { useEffect, useRef, useState } from "react";
import { connect } from "react-redux";
import { useTranslation } from 'react-i18next';
import classNames from "classnames";
import Svgicon from "@/components/svgicon"
import { Flex, Popover, Button, Radio, Modal, message, Empty, Spin, Input, Divider } from "antd";
import { StarFilled, StarOutlined, LoadingOutlined } from '@ant-design/icons';
import { api_get_bot_marketplace_datas } from "@/api/robot";
import InfiniteScroll from 'react-infinite-scroll-component';
import MyInput from './component/keyWordInput'
// import CreateRobotModal from './modal/createRobot/index';
import RobotDetails from './modal/robotDetails/index';
import RobotChat from '@/views/robot/robotChat/index';

import defaultLogo from "@/assets/bot_default_avatar.png";
import { api_set_robot_favorite, api_remove_robot_favorite } from '@/api/robot'
import { api_query_robot } from "@/api/robotManage2";

import './index.scss'

const pageSize = 20 // 分页size

// 机器人广场
const BotsMarketplace = (props: any) => {
  const { t, i18n } = useTranslation();
  const { wss: websocketService, onUpdateRobotList, activeCompanyId,activeCompanyName, userInfo } = props

  const RobotChatRef = useRef<any>(null) // 机器人聊天
  const scrollTopMemoryRef = useRef<any>(null) // 滚动条位置
  const isQueryRobotInfoRef = useRef<any>(false) // 是否查询机器人详情中

  const [isLoading, setIsLoading] = useState(false) // 页面加载 
  const [keyWord, setKeyWord] = useState("") // 模糊搜索 
  const [robotList, setRobotList] = useState<any>([]) // 机器人数据
  const [pageStart, setPageStart] = useState<any>(0) // 数据起始下标
  const [totalCount, setTotalCount] = useState<any>(0) // 数据总数目
  // const [isLoadingMore, setIsLoadingMore] = useState(false) // 下拉加载更多中
  const [loadAllFinished, setLoadAllFinished] = useState<any>(false) // 加载所有完成
  const [hasMore, setHasMore] = useState(true); // 是否有更多数据可加载


  // const [isCreateRobotModalOpen, setIsCreateRobotModalOpen] = useState(false) // 新建机器人弹窗 
  const [robotDetailModal, setRobotDetailModal] = useState({ visible: false, robotId: null }) // 机器人详情弹窗 
  const [panel, setPanel] = useState('RobotList') // 显示面板：机器人列表 RobotList(默认)、机器人聊天会话 RobotChat
  const [targetRobotChat, setTargetRobotChat] = useState<any>({}) // 选中的机器人聊天

  // 初始化
  useEffect(() => {
    queryList(true)
  }, [])
  
  useEffect(() => {
    if (panel === 'RobotChat' && targetRobotChat){
      RobotChatRef?.current?.$_handleToggleDialog(targetRobotChat) // 通知组件更新
    }
  }, [panel,targetRobotChat])

  // 获取所有的机器人
  const queryList = async (isInit = false, _keyword?: any) => {
    const data = {
      seq: 0,
      searchKeywords: _keyword !== undefined ? _keyword : keyWord,
      start: pageStart,
      count: pageSize
    }
    toggleLoading(isInit, true)
    // if (_keyword) return
    let res: any = await api_get_bot_marketplace_datas(data).catch(() => {
      toggleLoading(isInit, false)
    })
    if (isInit) {
      setIsLoading(false)
    } else {
      toggleLoading(isInit, false)
    }
    // console.log('res', res)
    if (!res) return
    let list = res?.robotList || []
    // list = []
    // list.push({})
    list = [...robotList, ...list]
    setTotalCount(res?.total) // 总数
    const nextStart = pageStart + res?.robotList.length
    setPageStart(nextStart) // 下一页开始下标
    // loadAllFinishedRef.current = list.length === res?.total// 判断是否加载所有
    setLoadAllFinished(list.length === res?.total) // 判断是否加载所有
    //collect: Collectible"表示可收藏 collected 表示已收藏 NotCollectible表示不可收藏
    list.forEach((item: any, index: number) => {
      // console.log('item.collect',item.collect)
      item.canCollect = item.collect !== 'NotCollectible'
      item.isCollected = item.collect === 'collected'
      item.isCollecting = false
    });
    // console.log('list', list)
    setRobotList(list)
  }

  const toggleLoading = (isInit: boolean, show: boolean) => {
    // console.log('isInit', isInit, show)
    if (isInit) {
      setIsLoading(show)
    } else {
      // setTimeout(()=>{
      // isLoadingMoreRef.current=show
      // setIsLoadingMore(show)
      // }, 1000)
      // if (show) isLoadingMoreRef.current=show
    }
  }

  // 发起机器人会话
  const onStartRobotCaht = async(e: any, robot: any) => {
    e.stopPropagation()
    if (isQueryRobotInfoRef.current) return
    isQueryRobotInfoRef.current = true
    // 先获取机器人详情
    const robotInfo = await getRobotDetails(robot.robot_id).catch(()=>{
      isQueryRobotInfoRef.current = false
    })
    isQueryRobotInfoRef.current = false
    // console.log('robotInfo',robotInfo)
    if (!robotInfo) return
    // 记录滚动条位置
    try {
      const targetContainer = document.getElementById('scrollableContainer')
      scrollTopMemoryRef.current = targetContainer?.scrollTop
      // console.log('scrollTopMemoryRef.current', scrollTopMemoryRef.current)
    } catch (error) {
    }

    setTargetRobotChat(robotInfo)
    setPanel('RobotChat')
  }

  // 发起机器人会话 返回 机器人列表
  const onRobotChatBack = async () => {
    setTargetRobotChat({})
    setPanel('RobotList')
    const robotInfo = await getRobotDetails(targetRobotChat.robot_id)
    // console.log('robotInfo', robotInfo)
    if (robotInfo) { // 刷新当前机器人数据-使用数量字段
      setRobotList(robotList.map((item: any) => {
        if (item.robot_id === targetRobotChat.robot_id) {
          // console.log('找到该机器人', item)
          item.chat_count = robotInfo.chat_count
        }
        return item
      }))
    } else { // 刷新所有数据
      queryList(true)
    }
    // 恢复滚动条位置
    // console.log('scrollTopMemoryRef.current', scrollTopMemoryRef.current)
    const targetContainer = document.getElementById('scrollableContainer')
    if (targetContainer && scrollTopMemoryRef.current) {
      targetContainer.scrollTop = scrollTopMemoryRef.current
    }
  }


  // 详情
  const onViewRobotDetail = (robot: any) => {
    setRobotDetailModal({ visible: true, robotId: robot.robot_id })
  }

  const setIsCollecting = (robotIndex:any, show:boolean)=>{
    setRobotList(robotList.map((r: any, ri: number) => {
      if (robotIndex === ri) {
        r.isCollecting = show
      }
      return r
    }))
  }

  // 收藏机器人
  const onCollectBot = async (e: any, robot: any, robotIndex: number) => {
    e.stopPropagation()
    // 请求接口
    let api: any = null
    const p = {
      seq: 0,
      robot_id: robot.robot_id
    }
    if (robot.isCollected) {
      // 取消收藏
      api = api_remove_robot_favorite
    } else {
      // 收藏
      api = api_set_robot_favorite
    }
    setIsCollecting(robotIndex, true)
    const res = await api(p).catch(() => {
      setIsCollecting(robotIndex, false)
    })
    setIsCollecting(robotIndex, false)
    if (!res || !res.result) return
    message.success(t("robot-manage.update-robot-permission-success"));
    // 通知父组件 更新 Bots shared with me 列表
    emitUpdateRobotList()
    setRobotList(robotList.map((r: any, ri: number) => {
      if (robotIndex === ri) {
        r.isCollected = !r.isCollected
      }
      return r
    }))
  }

  // 修改关键词
  const onChangeKeyWord = (value: any) => {
    // console.log('value', value)
    setKeyWord(value)
    queryList(true, value)// 发起请求
  }

  // 通知父组件robot更新机器人列表
  const emitUpdateRobotList = () => {
    onUpdateRobotList && onUpdateRobotList()
  }

  // 关闭 机器人详情弹窗
  const onCloseRobotDeailModal = () => {
    setRobotDetailModal({ visible: false, robotId: null })
  }

  const loadMoreData = () => {
    if (loadAllFinished) return
    // console.log('loadMoreData')
    queryList()
  }

  // 滚动事件
  const onListScroll = (e: any) => {
    try {
      // 记录滚动条位置
      const { scrollTop } = e.target
      // setScrollTopMemory(scrollTop)
      console.log('scrollTop', scrollTop)
    } catch (error) {
      console.log(error);
    }
  }

  // 获取机器人详情
  const getRobotDetails = async (robotId: any) => {
    const data = {
      seq: 0,
      robot_id: robotId
    }
    const res: any = await api_query_robot(data).catch(() => { return false })
    return !res ? false : res.data || {}
  }

  // 获取名称
  const getName = (robot:any)=>{
    let name = robot.username
    if (robot.robot_type ==='DEPARTMENT'){
      name = robot.department_name
    }
    if (robot.robot_type ==='COMPANY'){
      name = activeCompanyName
    }
    return name
  }

  return (
    <div className="bots-marketplace gray-bg">
      <div className={`page-nav-area ${panel === 'RobotChat' ? "hidden" : ""}`}>
        <Flex className={`page-nav ${panel === 'RobotChat' ? "hidden" : ""}`} align="center" justify="space-between">
          <span className="nav-title">{t('bots-marketplace.title')}</span>
          {/* <Button onClick={onCreateRobot} icon={<Svgicon svgName="icon_plus" />} type="primary">Create Bot</Button> */}
        </Flex>
        <Flex className="top-area" vertical gap={16} align="center">
          <div className="title">{t('bots-marketplace.explore-bots')}</div>
          <div className="txt">{t('bots-marketplace.explore-bots-desc')}</div>
          <MyInput onChange={onChangeKeyWord} placeholder={t('bots-marketplace.search-input-placeholder')} />
        </Flex>
      </div>
      { // 机器人列表
        panel === 'RobotList' ?
          <>
            <div className="page-main" id="scrollableContainer">
              {
                isLoading ?
                  <Spin className="full-spin" size="large" /> :
                  <>
                    {
                      robotList?.length > 0 ?
                        <InfiniteScroll
                          dataLength={robotList.length}
                          next={loadMoreData}
                          hasMore={robotList.length < totalCount}
                          loader={<div className="card-loading-area">
                            <div className="loading-more"><Spin indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />} /></div>
                          </div>}
                          endMessage={robotList.length > pageSize?<Divider className="card-no-more-data no-more-data" plain>{t("app.no-more-data")}</Divider>:null}
                          scrollableTarget="scrollableContainer"
                          className="infinite-scroll"
                        >
                          {
                            robotList?.map((robot: any, robotIndex: number) => {
                              return (
                                <Flex onClick={() => onViewRobotDetail(robot)} key={robot.robot_id + '' + robotIndex} className="card-list-item common-card" vertical justify={"space-between"}>
                                  <div className="cl-content">
                                    <Flex className="cl-content-top" gap={10} align="center">
                                      <img className="cl-content-img" src={robot.logo || defaultLogo} alt="" />
                                      <div className="cl-content-text">
                                        <div className="cl-content-title card-title">{robot.name}</div>
                                      </div>
                                      {
                                        robot.canCollect ?
                                          <>
                                            {
                                              robot.isCollecting ? <Spin indicator={<LoadingOutlined style={{ fontSize: 16 }} spin />} />
                                                : <Flex onClick={(e) => onCollectBot(e, robot, robotIndex)}
                                                  align="center" justify="center"
                                                  className={`icon-collect ${robot.isCollected ? 'filled' : ''}`}>
                                                  {
                                                    robot.isCollected ? <StarFilled /> : <StarOutlined />
                                                  }
                                                </Flex>
                                            }
                                          </> : null
                                      }
                                    </Flex>
                                    <div className="cl-content-bottom">{robot.introduction_message}</div>
                                  </div>
                                  <>
                                    <Flex className="cl-operate" justify={"space-between"} gap={20} align="center">
                                      <Flex gap={20} className="cl-operate-left">
                                        <Flex align="center" gap={4}>
                                          <Svgicon svgName="files" />
                                          <span>{robot.chat_count}</span>
                                        </Flex>
                                        <Flex className="cl-operate-left-con" align="center" gap={4}>
                                          {
                                            robot.robot_type === 'PERSONAL' ? <Svgicon svgName="founder" /> : <Svgicon className="icon_org" svgName="icon_company" />
                                          }
                                          <div className="cl-name">{getName(robot)}</div>
                                        </Flex>
                                      </Flex>
                                      <span onClick={(e) => onStartRobotCaht(e, robot)} className="margin-left-auto more"><Svgicon svgName="icon_chat" /></span>
                                    </Flex>
                                    {/* <Flex className="cl-operate cl-operate-btns" justify={"space-between"} gap={10} align="center" wrap={false}>
                              {renderOperateBtns(robot)}
                            </Flex> */}
                                  </>
                                </Flex>
                              )
                            })
                          }
                        </InfiniteScroll>
                        :
                        // 空数据
                        <Empty className="page-empty" image={null} description={
                          <><div>{t('common.no-result')}</div>
                            <div>{t('robot-manage.search-result')}</div>
                          </>} />
                    }
                  </>
              }
            </div>
          </>
          : null
      }
      { // 机器人会话
        panel === 'RobotChat' ?
          <RobotChat ref={RobotChatRef} pageOriginId="MARKET" targetRobot={targetRobotChat} onBack={onRobotChatBack} />
          : null
      }
      {/* { // 新建机器人弹窗 
        isCreateRobotModalOpen ?
          <CreateRobotModal visible={isCreateRobotModalOpen} onClose={() => onToggleIsRobotModal(false)} onSumit={onSumitCreate} />
          : null
      } */}
      { // 机器人详情弹窗 
        robotDetailModal.visible ?
          <RobotDetails visible={robotDetailModal.visible} resource={robotDetailModal} onClose={() => onCloseRobotDeailModal()} />
          : null
      }
    </div>
  )
}
const mapStateToProps = (state: any) => ({
  userInfo: state.app.userInfo,
  activeCompanyId: state.app.activeCompanyId,
  activeCompanyName: state.app.activeCompanyName,
});

const mapDispatchToProps = (dispatch: (e: any) => void) => ({
  // dispatch_api_delete_robot: (data = api_delete_robot_params) => dispatch(dispatch_api_delete_robot(data)),
});

export default connect(mapStateToProps, mapDispatchToProps)(BotsMarketplace);
