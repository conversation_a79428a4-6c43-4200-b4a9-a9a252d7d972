import React, { useState, useEffect } from "react";
import { Form, Space, Input, Flex, Button, message, Modal, Checkbox } from 'antd';
import { connect } from "react-redux";
import { useNavigate, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import Layout from '../login/layout' // 公共布局
import type { FormInstance, FormProps } from 'antd';
import { PasswordRules, EmailRules, MobileRules, UsernameRules, VerifyCodeRules } from '@/views/common/formRule'
import ErrorCodeMap from '@/views/common/formErrorCodeMap'
import FormUtils from '@/views/common/formUtils'
import classnames from "classnames";
import PasswordInput from '@/components/form/passwordInput'
import MobileInput from '@/components/form/mobileInput'
import VerifyCode from '@/components/form/verifyCode'
import { CountdownProvider } from '@/components/form/verifyCode/CountdownProvider';
import './index.scss'
import { api_registrations, api_get_email_code, api_get_mobile_code, api_bind_user_phone } from '@/api/user'
import { change_app_userInfo } from "@/store/action"
import { Encrypt } from '@/utils/forge'
import { RequestFailedMessage } from '@/utils/userRequest'
import { loginedDefaultPath } from "@/routes";
import SvgIcon from '@components/svgicon'; // 根据实际路径引入
import { refreshTheme } from "@/theme";

// 注册
type FieldType = {
    username?: string;
    password?: string;
    mobile?: string;
    email?: string;
    code?: string;
};

let initialValues: any = {
    email: '',
    username: '',
    password: '',
    code: '',
    verify_mobile: {
        number: '', // 手机号
        // code: {label: 1, value: 'US'} // 区号
        // code: null// 区号
        code: { label: null, value: null }// 区号
    },
    verify_code: ''
}
const Register = (props: any) => {
    const { isLogin } = props
    const location = useLocation();
    const { email, inviteCode } = location.state || {}; // 获取隐性参数
    // console.log('email: ',email )
    // console.log('accountType: ',accountType )

    const { t, i18n } = useTranslation();
    const navigate = useNavigate();
    const [modal, contextHolder] = Modal.useModal();

    const [form] = Form.useForm();
    const [formValues, setFormValues] = useState<any>({});
    const [step, setStep] = useState(1)// 步骤
    // const [registerType, setRegisterTypee] = useState(accountType || 'Email') // 注册类型：邮箱 或 手机号, 'Email' 'Mobile'
    const [userId, setUserId] = useState('') // 用户ID
    const [isSubmiting, setIsSubmiting] = useState(false)

    const [isAgree, setIsAgree] = useState(false)
    const [showPrivacyAgree, setShowPrivacyAgree] = useState(false) // 隐私政策同意弹窗显示
    const privacyRender = (`<a href="/privacyPolicy" target={"_blank"}>${t('login.privacy-policy')}</a>`)
    const termsRender = (`<a href="/termsConditions" target={"_blank"}>${t('login.terms-conditions')}</a>`)

    // 初始化
    useEffect(() => {
        // console.log('inviteCode', inviteCode, isLogin)
        if (inviteCode && isLogin) {
            navigate('/invite?code=' + inviteCode)
        }
        // 获取系统默认主题
        getThemeConfig()
    }, [])

    useEffect(() => {
        // console.log('useEffectuseEffectuseEffect',email)
        if (email) form.setFieldValue('email', email)
    }, [email])

    // 获取邀请企业主题配置
    const getThemeConfig = async () => {
        refreshTheme(true)
    }
    
    // 步骤1提交
    const handleSubmit1 = (ignoreAgree?: boolean) => {
        FormUtils.resetValidate(form)
        if (!ignoreAgree && !isAgree) {
            setShowPrivacyAgree(true)
            return
        }
        const values: any = form.getFieldsValue()
        const { email, username, code } = values
        // console.log('values', values)
        const password = Encrypt(values.password) // 加密
        // const password = values.password
        // 创建用户记录
        const data: any = {
            "registrationType": "email",  /*注册类型 email mobile*/
            "email": email, /*Email地址*/
            "emailCode": code,  /*Email 验证码*/
            "password": password,  /*密码*/
            "nickname": username, /*昵称*/
        }
        if (inviteCode) {
            data.invitationCode = inviteCode
        }
        setIsSubmiting(true)
        api_registrations(data).then(async (res: any) => {
            setIsSubmiting(false)
            // console.log('res',res)
            const code = res?.code
            if (code == 0) { // 成功
                const result = res.data || {}
                const { accessToken, ...otherInfo } = result
                await props.change_app_userInfo({ isLogin: true, userInfo: otherInfo, token: accessToken })
                if (!inviteCode) {
                    // 注册
                    if (result?.accessToken) {
                        setUserId(result?.userId)
                        setStep(2) // 跳转到第二步
                    }
                } else {
                    navigate('/inviteSuccess?code=' + inviteCode)
                }

            } else {
                const emailErrors = ErrorCodeMap.email // 邮件错误码
                const codeErros = ErrorCodeMap.code// 验证码错误码
                const allCodes = [...emailErrors, ...codeErros]
                if (!allCodes.includes(code)) {
                    const msg = RequestFailedMessage(res?.msg, code)
                    message.error(msg)
                    return
                }
                let errMsg = t(`errorCode.${code}`)
                form.setFields([{ name: emailErrors.includes(code) ? 'email' : 'code', errors: [errMsg], warnings: [`errorCode.${code}`] }])
            }

        }).catch((err: any) => {
            setIsSubmiting(false)
            console.log('err', err)
        })
    }

    // 步骤2提交
    const handleSubmit2 = () => {
        FormUtils.resetValidate(form)
        // 绑定手机 并登录
        const values: any = form.getFieldsValue()
        const { verify_mobile, verify_code } = values
        // console.log('values',values)
        // 创建用户记录
        const data: any = {
            userId,
            areaCode: '+' + verify_mobile.code.label,
            mobile: verify_mobile.number,
            code: verify_code
        }
        // console.log('data',data)
        api_bind_user_phone(data).then(async (res: any) => {
            // console.log('res',res)
            const code = res?.code
            if (code == 0 && res.data.success) { // 成功直接登录
                navigate(loginedDefaultPath)
            } else {
                const mobileErrors = ErrorCodeMap.mobile // 手机错误码
                const codeErros = ErrorCodeMap.code// 验证码错误码
                const allCodes = [...mobileErrors, ...codeErros]
                if (!allCodes.includes(code)) {
                    const msg = RequestFailedMessage(res?.msg, code)
                    message.error(msg)
                    return
                }
                let errMsg = t(`errorCode.${code}`)
                form.setFields([{ name: mobileErrors.includes(code) ? 'verify_mobile' : 'verify_code', errors: [errMsg], warnings: [`errorCode.${code}`] }])
            }

        }).catch((err: any) => {
            console.log('err', err)
        })
    }

    // 步骤2 跳过
    const onSkip = () => {
        // 直接登录
        navigate(loginedDefaultPath)
        // props.change_app_userInfo({ isLogin: true, userInfo: result.data.data })
    }

    const handleOnFormItemKeyUp = (e: any) => {
    }

    // 用户名输入限制
    const onChangeUsername = (e: any) => {
        // const inputValue = e.target.value;
        // // console.log('inputValue',inputValue)
        // // 允许输入中文、英文、特殊字符、空格
        // const filteredValue:any = inputValue.replace(/[^\u4e00-\u9fa5a-zA-Z0-9 　!@#$%^&*()_+=\[\]{};':"\\|,.<>/?~！@#￥%…&*（）—；：‘’“”、。《》？，．／－]/g, '');
        // // console.log('filteredValue',filteredValue)
        // form.setFieldValue('username',filteredValue)
    }

    // 区号切换时需要
    const onValidteMobile = (field: string) => {
        form.validateFields([field])
    }

    // 表单项 输入改变事件
    const onFiledValueChange = (changedFields: any, allValues: any) => {
        try {
            // console.log('changedFields', changedFields)
            setFormValues({ ...formValues, ...allValues });
            Object.keys(changedFields).forEach((fieldName: any) => {
                const fieldError = form?.getFieldError(fieldName)
                if (fieldError.length > 0) FormUtils.clearValidate(form, fieldName)
            })
        } catch (error) {

        }
    }

    // 处理步骤1 邮箱 获取验证码失败 对应表单输入框错误提示
    const onVerifyCodeError1 = (err: any) => {
        // console.log('onVerifyCodeError1 err',err)
        const _code = err?.code
        // const codeErrors =  ErrorCodeMap.code
        const emailErrors = ErrorCodeMap.email
        if (!emailErrors.includes(_code)) return
        const errMsg = t(`errorCode.${_code}`)
        form.setFields([{ name: 'email', errors: [errMsg], warnings: [`errorCode.${_code}`] }])
    }

    // 处理步骤2 手机号 获取验证码失败 对应表单输入框错误提示
    const onVerifyCodeError2 = (err: any) => {
        // console.log('onVerifyCodeError2 err',err)
        const _code = err?.code
        const mobileErrors = ErrorCodeMap.mobile
        if (!mobileErrors.includes(_code)) return
        const errMsg = t(`errorCode.${_code}`)
        form.setFields([{ name: 'verify_mobile', errors: [errMsg], warnings: [`errorCode.${_code}`] }])
    }

    // 语言切换时，重新渲染表单校验信息
    useEffect(() => {
        const fun: any = () => FormUtils.refreshValidate(form, t)
        // 监听语言变化
        i18n.on('languageChanged', fun);

        // 清理监听器
        return () => {
            i18n.off('languageChanged', fun);
        };
    }, [i18n]);

    const onChangeAgree = (e: any) => {
        setIsAgree(e.target.checked)
    }

    // 同意用户协议
    const onPrivacyAgreeOk = () => {
        setIsAgree(true)
        setShowPrivacyAgree(false)
        // 登录
        handleSubmit1(true)
    }

    const onPrivacyAgreeCancel = () => {
        setShowPrivacyAgree(false)
    }
    const onToLogin = () => {
        if (inviteCode) {
            navigate('/invite?code=' + inviteCode)
        } else {
            navigate('/login')
        }
    }

    return (
        <CountdownProvider>
            <Layout>
                <>
                    <div className="register-page content">
                        <div className="content-tittle">{
                            step == 2 ? t('login.verify-mobile') :
                                inviteCode ? t('invite.register-title') : t('login.register')}</div>
                        <Form
                            form={form}
                            className={classnames(["login-from", "common-form", `step${step}`])}
                            name="register_basic"
                            autoComplete="off"
                            onValuesChange={onFiledValueChange}
                            validateTrigger="onBlur"
                            layout="vertical"
                            requiredMark={false}
                            initialValues={initialValues}
                        >
                            {
                                step == 1 ?
                                    <>
                                        {/* 用户名 */}
                                        <Form.Item name="username" label={t('login.username')} rules={UsernameRules(t)}>
                                            <Input onChange={(e) => onChangeUsername(e)}
                                                className="ipt" variant="filled" maxLength={20} placeholder={t('login-pls.username') as string} />
                                        </Form.Item>
                                        {/* 密码 */}
                                        <Form.Item name="password" label={t('login.password')}
                                            rules={[{ required: true, whitespace: true, message: '' }, ...PasswordRules(t)]}>
                                            <PasswordInput key="register-password" onKeyUp={(e: any) => handleOnFormItemKeyUp(e)} />
                                        </Form.Item>
                                        {/* 邮箱 */}
                                        {
                                            <Form.Item name="email" label={t('login.email-address')} rules={[{ required: true, whitespace: true, message: '' }, ...EmailRules(t)]}>
                                                <Input onKeyUp={(e) => handleOnFormItemKeyUp(e)}
                                                    className="ipt" variant="filled" maxLength={300} placeholder={t('login-pls.email') as string} />
                                            </Form.Item>
                                        }

                                        {/* 验证码 */}
                                        <Form.Item name="code" label={t('login.verify-code')}
                                            rules={VerifyCodeRules(t)}>
                                            <VerifyCode api={api_get_email_code} target="email" email={form.getFieldValue('email')} scene="MEMBER_REGISTER"
                                                targetValidate={form.validateFields} onKeyUp={(e: any) => handleOnFormItemKeyUp(e)}
                                                // onOk={onVerifyCodeOk1}
                                                onError={onVerifyCodeError1} />
                                        </Form.Item>
                                        {/* 隐私政策 */}
                                        <Space className='privacy-term' align='center'>
                                            <Checkbox checked={isAgree} onChange={onChangeAgree}></Checkbox>
                                            <div dangerouslySetInnerHTML={{
                                                __html: t('login.agree-privacy-terms',
                                                    { privacy: privacyRender, terms: termsRender }) as string,
                                            }} />
                                        </Space>
                                        <div className={"form-submit"}>
                                            <SubmitButton1 form={form} disabled={isSubmiting} onClick={() => handleSubmit1()}>{
                                                inviteCode ? t('invite.register-submit') : t('login.register-btn')
                                            }</SubmitButton1>
                                        </div>
                                    </>
                                    : null
                            }
                            {
                                step == 2 ?
                                    <>
                                        {// 手机号
                                            <Form.Item name="verify_mobile" label={t('login.mobile')} rules={MobileRules(t)} className="mobile-form-item">
                                                <MobileInput onValidte={() => onValidteMobile('verify_mobile')} />
                                            </Form.Item>
                                        }

                                        {/* 验证码 */}
                                        <Form.Item name="verify_code" label={t('login.verify-code')}
                                            rules={[{ required: true, whitespace: true, message: '' }]}
                                        >
                                            <VerifyCode api={api_get_mobile_code} target="mobile" mobile={form.getFieldValue('verify_mobile')} scene="MEMBER_UPDATE_MOBILE"
                                                targetValidate={form.validateFields} onKeyUp={(e: any) => handleOnFormItemKeyUp(e)}
                                                // onOk={onVerifyCodeOk2}
                                                onError={onVerifyCodeError2} />
                                        </Form.Item>
                                        <Flex className={"form-submit"} vertical gap={24}>
                                            <SubmitButton2 form={form} onClick={handleSubmit2}>{t('app.ok')}</SubmitButton2>
                                            <Button size="large" onClick={onSkip} color="primary" variant="link" className={"btn"}>{t('login.skip')}</Button>
                                        </Flex>
                                    </>
                                    : null
                            }

                        </Form>
                    </div>

                    {step == 1 ?
                        <Space className={"concat-me"} align="center">
                            <div className={"unable-login"}>{inviteCode ? t('invite.have-account') : t('login.has-account')}</div>
                            <span onClick={onToLogin} className="concat-link">{
                                inviteCode ? t('invite.use-account-join') : t('login.sign')
                            }</span>
                        </Space> : null
                    }
                    {/* 隐私政策 同意弹窗 */}
                    <Modal width={272} title={t('login.agree-privacy-terms-tip')} maskClosable={false} centered closable={false} okText={t('login.agree')} cancelText={t("app.cancel")}
                        open={showPrivacyAgree} wrapClassName="common-confirm-modal" onOk={onPrivacyAgreeOk} onCancel={onPrivacyAgreeCancel}>
                        <div className='modal-privacy-content'>
                            <div dangerouslySetInnerHTML={{
                                __html: t('login.privacy-terms',
                                    { privacy: privacyRender, terms: termsRender }) as string,
                            }} />
                        </div>
                    </Modal>
                    {contextHolder}
                </>
            </Layout>
        </CountdownProvider>
    )
}
interface SubmitButtonProps {
    form: FormInstance;
    disabled?: boolean;
    onClick: any
}

// 步骤1 提交按钮
const SubmitButton1: React.FC<React.PropsWithChildren<SubmitButtonProps>> = ({ form, children, onClick, disabled }) => {
    const [submittable, setSubmittable] = React.useState<boolean>(false);

    // Watch all values
    const values = Form.useWatch([], form);
    // console.log('Watch values', values)
    let fields: any = ['username', "password", "email", "code"]
    // console.log('fields', fields)
    useEffect(() => {
        form
            .validateFields(fields, { validateOnly: true })
            .then(() => setSubmittable(true))
            .catch((err) => {
                // console.log('提交 catch errerr:', err)
                setSubmittable(false)
            });
    }, [form, values]);

    useEffect(() => {
        // console.log('disabled,',disabled)
        setSubmittable(!disabled)
    }, [disabled])

    return (
        <Button size="large" type="primary" onClick={onClick} disabled={!submittable} className={"btn"}>
            {children}
        </Button>
    );
};
// 步骤2 提交按钮
const SubmitButton2: React.FC<React.PropsWithChildren<SubmitButtonProps>> = ({ form, children, onClick }) => {
    const [submittable, setSubmittable] = React.useState<boolean>(false);

    // Watch all values
    const values = Form.useWatch([], form);
    // console.log('Watch values', values)
    let fields: any = ['verify_code', "verify_mobile"]
    // console.log('fields', fields)
    React.useEffect(() => {
        form
            .validateFields(fields, { validateOnly: true })
            .then(() => setSubmittable(true))
            .catch((err) => {
                // console.log('errerr:', err)
                setSubmittable(false)
            });
    }, [form, values]);

    return (
        <Button size="large" type="primary" onClick={onClick} disabled={!submittable} className={"btn"}>
            {children}
        </Button>
    );
};
const mapStateToProps = (state: any) => ({
    globalLang: state.app.globalLang,
    globalRegion: state.app.globalRegion,
    isLogin: state.app.isLogin,
})

const mapDispatchToProps = (dispatch: any) => ({
    change_app_userInfo: (data: any) => dispatch(change_app_userInfo(data)),
})

export default connect(mapStateToProps, mapDispatchToProps)(Register);
