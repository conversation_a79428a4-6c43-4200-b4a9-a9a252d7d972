@import '../../../styles/mixin.scss';
.robot-embedding-wrap{
    .common-card{
        height: 238px;
    }
    .card-top{
        padding-bottom: 5px;
        border-bottom: 1px solid var(--colorBorderSecondary);
    }
    .ct-text{
        @include ellipsis-multiline(1);
    }
    .ct-more{
        margin-left: auto;
        width: 32px;
        height: 32px;
        font-size: 16px;
        cursor: pointer;
        &:hover{
            background: var(--colorFillTertiary);
            border-radius: 50%;
        }
    }
    .card-content{
        font-weight: 400;
        font-size: 12px;
        color: var(--colorTextSecondary);
        line-height: 20px;
        padding-top: 10px;
        @include ellipsis-multiline(8);
    }
}
.ant-dropdown.robot-embedding-more-dropdown .ant-dropdown-menu .ant-dropdown-menu-item-icon {
    font-size: 16px;
    margin-right: 10px;
}