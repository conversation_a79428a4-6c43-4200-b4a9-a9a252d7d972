@import "@/styles/mixin.scss";

.information-side-menu {
    @include flex($flex-direction: column);
    flex-shrink: 0;
    width: 280px;
    background: var(--colorBgNav);
    height: 100%;
    $align-space: 16px;
    // padding: 0 $align-space;

    .side-menu-title {
        width: 100%;
        flex-shrink: 0;
        @include flex($justify-content: space-between);
        @include font(18px, 24px, 600);
        padding: 19px $align-space;
        span {
            margin-right: 4px;
        }

        .anticon {
            font-size: 16px;
            cursor: pointer;
        }
    }

    .information-menu-container{
        position: relative;
        padding: 8px $align-space 0;
        width: 100%;
        flex: 1 1;
        overflow-y: auto;
    }
    @mixin active(){
        background: var(--colorPrimary);
        color: var(--colorPrimaryContrasting);
        border-radius: 12px;
    }
    @mixin common_title {
        padding: 6px 8px;
        margin: 0 auto;
        @include font(14px, 22px, 500);
        cursor: pointer;
        width: 100%;
        &:not(.active):hover {
            // background: var(--colorFillQuaternary);
            // border-radius: 12px;
        }

        &.active{
            @include active;
        }
    }
    
    .information-menu-item-title{
        @include common_title();
        // position: sticky;
        $h:34px;
        height: $h;
        // bottom: 0;
        z-index: 555;
        background: var(--colorBgNav);
        // @for $i from 1 through 3 {
        //     &:nth-last-of-type(#{$i}) {
        //       top: auto;
        //       bottom: calc((#{$i} - 1) * 34px);
        //     }
        // }
        // &:first-of-type{
        //     bottom: auto;
        //     top: 0;
        // }
        &:not(:last-of-type){
            margin-bottom: 4px;
        }
        .svg-icon {
            font-size: 16px;
            flex-shrink: 0;
        }
        // &.MyGroups {
        //     margin-bottom: 16px;
        // }
        &:not(.active):hover {
            background: var(--colorFillQuaternary);
            border-radius: 12px;
        }
        &.NewContacts {
            margin-top: 16px;
        }
        &.MyGroups {
            margin-bottom: 16px;
        }
        &:first-of-type{
            margin-top: 0!important;
        }
    }
    
    .menu_label {
        flex: 1;
        @include ellipsis-multiline(1)
    }
    .new-count{
        // color: #fff;
        // background: var(--colorError);
        // font-size: 10px;
        // line-height: 10px;
        // font-weight: 500;
        // border-radius: 16px;
        // padding: 2px 4px;
        flex-shrink: 0;
        // text-align: center;
    }
    
    .a-menu-item-list{
        // padding-top: 4px;
        // padding-bottom: 16px;
        .a-menu-item-submenu-content{
            @include common_title();
            padding-left: 32px;
            // padding-bottom: 6px;
            .svg-icon{
                font-size: 16px;
                flex-shrink: 0;
                color: var(--colorIconQuaternary);
            }
            
            &:not(.active):hover {
                background: var(--colorFillQuaternary);
                border-radius: 12px;
            }
        }

    }
}