import { connect } from "react-redux";
import { Input } from "antd";
import { useTranslation } from 'react-i18next';
import { useState, useRef, useEffect, useCallback, forwardRef } from "react";
import './index.scss'
import Svgicon from "@/components/svgicon";
import debounce from 'lodash/debounce';

const InputComponent = forwardRef((props: any, ref) => {
    const { onChange: onChangeEvent, dispatch, placeholder, ...resProps } = props
    const { t } = useTranslation();
    const [value, setValue] = useState('')

    const emitChange = (newValue: any) => {
        onChangeEvent && onChangeEvent(newValue)
    }

    const debouncedOnChange = useCallback(
        debounce((newValue: string) => {
            emitChange(newValue);
        }, 300), // 设置延迟
        []);


    const onChange = (e: any) => {
        const newValue = e.target.value;
        setValue(newValue)
        debouncedOnChange(newValue)
    }

    const onClear = () => {
        setValue("")
    }
    return (
        <Input {...resProps} className="keyword-input" variant="filled" value={value} onChange={onChange} onClear={onClear}
            allowClear maxLength={1000} placeholder={ placeholder || t('common.search-keyword') as string} size="mediumn" suffix={<Svgicon svgName="search" />}></Input>
    );
});
const mapStateToProps = (state: any) => ({
});
// 使用 connect 连接 Redux store，并启用 forwardRef
const ConnectedCounter = connect(mapStateToProps, null, null, { forwardRef: true })(InputComponent);
export default ConnectedCounter;
