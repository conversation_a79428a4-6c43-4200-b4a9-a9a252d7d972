import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate } from "react-router-dom";
import { Space, Spin, Flex, Button, Modal, message } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';
import { connect } from "react-redux";
import { change_app_globalLang } from "@/store/action";
import Layout from '@/views/login/layout'
import PublicLogin from '@/views/login/panel/publicLogin'
import { dispatch_api_get_configurations } from '@/api/platform'
import { api_get_dept_invitation, api_accept_invitation, dispatch_api_logout } from '@/api/user'
import Avatar from '@/components/avatar'
import { api_get_my_profile } from '@/api/setting'
import { loginedDefaultPath } from '@/routes';
import {refreshTheme} from '@/theme'

import './index.scss'

const InviteErrorCodes = [1002033000, **********, **********] // 邀请码不存在、邀请码已过期、邀请码已被使用

/* 邀请页面
* 存在状态：登录、非登录
*/
const Invite = (props: any) => {
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const location = useLocation();

  const searchParams = new URLSearchParams(location.search);
  const inviteCode = searchParams.get('code') || ''; // 获取查询参数  

  const [modal, contextHolder] = Modal.useModal();

  const { isLogin, isPrivate } = props;
  const [isLogined, setIsLogined] = useState(false) // 登录状态，是否已登录
  const [acceptInviteType, setAcceptInviteType] = useState<any>(null) // 接受邀请方式：手动输入账号 AccountEnterApply、 已登录账号 AccountLoginedApply
  const [inviteData, setInviteData] = useState<any>({}); // 邀请详情
  const [loginedUserInfo, setLoginedUserInfo] = useState<any>({ userName: "", corpEmail: "" }); // 已登录账号信息
  const [isLoading, setIsLoading] = useState(true) // 加载中
  // const [isQuerySuccess, setIsQuerySuccess] = useState(true) // 初始化查询成功
  const [isSubmiting, setIsSubmiting] = useState(false) // 提交中
  const [showInviteInfo, setShowInviteInfo] = useState(true) // 是否显示邀请消息，默认是
  const [happenError, setHappenError] = useState(false) // 出现错误
  const [inviteExist, setInviteExist] = useState(true) // 邀请存在
  const [inviteErrorCode, setInviteErrorCode] = useState<any>(null) // 邀请错误码

  // 初始化
  useEffect(() => {
    init()
  }, [isLogin])

  // 监听登录状态
  // useEffect(() => {
  //   // // 登录状态，获取已登录账号信息
  //   // if (isLogin) {
  //   //   getLoginedUserInfo()
  //   // }
  //   if (showInviteInfo){
  //     console.log('showInviteInfo',showInviteInfo)
  //     setAcceptInviteType(isLogin ? 'AccountLoginedApply' : 'AccountEnterApply') // 根据登录方式 设置 接受邀请方式
  //   }
  // }, [isLogin])

  const init = async () => {
    // 私有化部署 没有此功能
    if (isPrivate) {
      // console.log('isPrivate', isPrivate)
      navigate('/login', { replace: true })
    }
    setIsLogined(isLogin)
    setAcceptInviteType(isLogin ? 'AccountLoginedApply' : 'AccountEnterApply') // 根据登录方式 设置 接受邀请方式
    // 获取邀请详情
    let result = true
    if (inviteCode) {
      result = await getInvitorInfo()
      // console.log('result', result)
    }
    // 登录状态，获取已登录账号信息
    if (isLogin && result) {
      getLoginedUserInfo()
    }
    // console.log('isLogin', isLogin, inviteCode)
    // 获取系统主题配置
    getConfig()
  }

  // 获取用户信息
  const getLoginedUserInfo = async () => {
    setIsLoading(true)
    const res: any = await api_get_my_profile('0', true).catch(() => {
      setIsLoading(false)
    })
    setIsLoading(false)
    if (!res) return
    // console.log('res',res)
    setLoginedUserInfo(res)
  }
  
  // 获取系统配置
  const getConfig = async () => {
    setIsLoading(true)
    const res = await props.dispatch_api_get_configurations().catch((err: any) => {
      console.log('err')
      setIsLoading(false)
    })
    setIsLoading(false)
    if (!res) return
    // console.log('res',res)
  }

  // 获取邀请企业主题配置
  const getThemeConfig = async (isDefault=false, tenantId?:any) => {
    refreshTheme(isDefault, false, tenantId)
  }

  // 获取邀请详情
  const getInvitorInfo = async () => {
    setIsLoading(true)
    const res: any = await api_get_dept_invitation(inviteCode).catch((err: any) => {
      setIsLoading(false)
      // setIsQuerySuccess(false)
    })
    if (!res) {
      getThemeConfig(true)
      return false
    }
    const code = res?.code
    // console.log('--------res', res,InviteErrorCodes.includes(code))
    // setIsQuerySuccess(code == 0)
    // 邀请码不存在，跳转到登录页面
    getThemeConfig(false, res.data?.tenantId)
    setInviteErrorCode(code)
    if (code == 1002033000) {
      setInviteExist(false)
      message.error(res.msg)
      navigate('/login', { replace: true })
      setIsLoading(false)
      return false
    }
    setIsLoading(false)
    setHappenError(InviteErrorCodes.includes(code))
    // setHappenError(true)
    // const = InviteErrorCodes
    setInviteData(res?.data)
    return true
  };

  // 操作
  const onRegister = () => {
    // 跳转到注册
    navigate('/register', { state: { inviteCode } });
  }

  // 切换账号，接受邀请
  const onSwitch = async () => {
    // 1.退出当前账号
    await props.dispatch_api_account_logout().catch((err: any) => {
      console.log(err)
    })
    // 2. 切换 接受邀请方式为 手动输入
    setAcceptInviteType('AccountEnterApply')
  }

  // 申请加入
  const onApplyJoin = async () => {
    setIsSubmiting(true)
    const res: any = await api_accept_invitation(inviteCode).catch(() => {
      setIsSubmiting(false)
    })
    setIsSubmiting(false)
    if (!res) return
    const success = res.success
    if (success) {
      navigate('/inviteSuccess?code='+inviteCode)
    }
  }

  // enter vama
  const onEnterSystem = () => {
    navigate(loginedDefaultPath, { replace: true })
  }

  // 忘记密码
  const onForgotPassword = (show: boolean) => {
    // 忘记密码不显示邀请信息
    // console.log('show', show)
    setShowInviteInfo(show)
  }

  // 邀请链接出错原因
  const getInviteErrorReason = () => {
    let text = ''
    if (inviteErrorCode == **********) {
      text = t('invite.invitation-expired')
    } else if (inviteErrorCode == **********) {
      text = t('invite.invitation-used')
    }
    return text
  }

  // 渲染 邀请信息
  const renderInviteInfo = () => {
    return (
      <Flex className='invite-info' vertical align='center' style={{ margin: '0 auto' }} gap={4}>
        <Avatar name={inviteData?.invitorName} src={inviteData?.avatar} />
        <div className='join-content'>{t('login.joinContent', { userName: inviteData?.invitorName })}</div>
        <div className='join-info'>{inviteData?.companyName}</div>
        <div className='join-info'>{inviteData?.deptNames}</div>
      </Flex>
    )
  }

  return (
    <Flex className={`invite-container ${happenError ? "has-error" : ''}`} align="center" justify="center">
      {
        !isLoading ?
          <>
            {
              // 邀请链接有问题
              happenError ?
                <Layout isInvite={true} hideCarousel={true}>
                  <Flex className="invite-error" vertical gap={40}>
                    {inviteExist ? renderInviteInfo() : null}
                    <Flex className="invite-error-text" vertical gap={8} align="center">
                      <div>{getInviteErrorReason()}</div>
                      <div className="invite-error-desc">{t('invite.contact-obtain')}</div>
                    </Flex>
                  </Flex>
                </Layout>
                :
                <Layout isInvite={true}>
                  <>
                    {/*  邀请信息 */}
                    {
                      showInviteInfo ? renderInviteInfo() : null
                    }
                    {
                      acceptInviteType && acceptInviteType === 'AccountLoginedApply' ?
                        <Flex className="logined-user-info" vertical gap={24}>
                          <div className="lu-title">{t('invite.sign-as')}</div>
                          <Flex className="lu-user" gap={12} align="center">
                            <Avatar size="48" name={loginedUserInfo.userName} />
                            <Flex className="lu-user-text" vertical gap={4}>
                              <div>{loginedUserInfo.userName}</div>
                              <div>{loginedUserInfo.corpEmail}</div>
                            </Flex>
                          </Flex>
                          <Button size="large" type="primary" disabled={isSubmiting} onClick={onApplyJoin} className={"btn"}>
                            {t('invite.apply-join')}
                          </Button>
                          <Button size="large" onClick={onEnterSystem} color="primary" variant="link" className={"btn"}>{t('invite.enter-vama')}</Button>
                        </Flex>
                        :
                        <PublicLogin
                          isInvite={true}
                          inviteCode={inviteCode}
                          onForgotPassword={(e: any) => onForgotPassword(e)}
                        />
                    }

                    {
                      acceptInviteType && showInviteInfo ?
                        <>
                          {
                            acceptInviteType === 'AccountLoginedApply' ?
                              // 邀请-注册
                              <Space className={"concat-me"} align="center">
                                <div className={"unable-login"}>{t('invite.use-another-account')}</div>
                                <span onClick={onSwitch} className="concat-link">{t('invite.switch')}</span>
                              </Space> : null
                          }
                          {
                            acceptInviteType === 'AccountEnterApply' ?
                              <Flex className={"concat-me"} align="center">
                                <div className={"unable-login"}>{t('login.no-account')}&nbsp;</div>
                                <span onClick={onRegister} className="concat-link">{t('login.register-btn')}</span>
                              </Flex> : null
                          }
                        </>
                        : null
                    }

                  </>
                  {contextHolder}
                </Layout>
            }
          </>
          : <Spin className="modal-spin" size="large" indicator={<LoadingOutlined style={{ fontSize: 48 }} spin />} />
      }

    </Flex>
  )
}

const mapStateToProps = (state: any) => ({
  isLogin: state.app.isLogin,
  globalLang: state.app.globalLang,
  globalRegion: state.app.globalRegion,
  themeInfo: state.app.themeInfo,
  isPrivate: state.app.isPrivate,
})

const mapDispatchToProps = (dispatch: any) => ({
  dispatch_api_account_logout: (data: any) => dispatch(dispatch_api_logout(data, null, true)),
  change_app_globalLang: (globalLang: string) => dispatch(change_app_globalLang(globalLang)),
  dispatch_api_get_configurations: () => dispatch(dispatch_api_get_configurations()),
})

export default connect(mapStateToProps, mapDispatchToProps)(Invite);
// export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(Login));
