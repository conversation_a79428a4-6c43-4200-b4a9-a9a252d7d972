import axios, { AxiosInstance } from "axios";
import { message } from "antd";
import router from "../routes";
import store from "../store";
import { change_app_userInfo } from "../store/action";
import i18next from "i18next";
import errorCodeJson from '@/i18n/locales/errorCode/en.json'
import { refreshTheme } from "@/theme";
import { openIMService } from '@/services/ImChatService'

let LoginExpiredCode = 401 // 登录失效的code
const Timeout = 150000

// 处理系统 已定义的接口错误码，提示对应信息
let ErrorCodes: any = []
ErrorCodes = Object.keys(errorCodeJson.errorCode)

// 请求失败提示信息，优先提示 已定义错误码信息，其次提示默认信息
const RequestFailedMessage = (requestMsg: any, code?: any) => {
  // const msg = ErrorCodes.includes(code.toString()) ? i18next.t(`errorCode.${code}`) : i18next.t('error.request-failed')
  const msg = requestMsg ? requestMsg : i18next.t('error.request-failed')
  return msg
}

// 设置企业ID请求头
const $_SetTenantId = (config:any)=>{
  /*规则：
  *1.Sass登录方式：所有要传tenant-id的接口, 个人空间tenant-id 传0，其他传 企业id
  *2.私有化登录方式： 所有要传tenant-id的接口，都传1
  */
  const isPrivate= config.isPrivate ? config.isPrivate : store.getState().app.isPrivate
  const isLogin = store.getState().app.isLogin
  let activeCompanyId = config.tenantId || store.getState().app.activeCompanyId
  // console.log('isPrivate',isPrivate)
  // console.log('isLogin',isLogin)
  // console.log('activeCompanyId',activeCompanyId)
  if (!config?.noTenant && isLogin){
    // console.log('请求配置里：isPrivate',isPrivate)
    if (isPrivate){
      config.headers['tenant-id']= config.tenantId || 1
    }else{
      config.headers['tenant-id'] = activeCompanyId || 0
    }
  }
  // console.log('config.tenantId',config.tenantId)
  if (config.tenantId){
    config.headers['tenant-id']= config.tenantId
  }
}

// 公共拦截器模块
const AddCommonInterceptors = (instance: AxiosInstance) => {
  instance.interceptors.request.use(
    (config: any) => {
      // console.log('config:', config)
      // 添加 token 请求头
      const token = localStorage.getItem('AMA_USER_TOKEN')
      // console.log('token',token)
      if (!config?.noToken && token) config.headers['Authorization'] = `${token}`
      // 添加 language 请求头
      const lang = localStorage.getItem("i18nextLng") || 'en'
      const langMap: any = { en: "en-us", zh: "zh-cn", hk: "zh-hk" }
      config.headers['Accept-Language'] = langMap[lang]
      // 添加企业ID 请求头
      $_SetTenantId(config)
      return config;
    }, (error) => {
      return Promise.reject(error);
    });
  instance.interceptors.response.use(
    (response) => {
      // let requestData= response.config.data
      const {request}= response
      // console.log('request config',request)
      if (request.responseType ==='blob'){
        return response;
      }
      const config: any = response.config
      const requestUrl = response.config.url
      const statusCode = Number(response?.data?.code)
      const isLogin = store.getState().app.isLogin
      // 1.成功响应
      if (statusCode === 0 || (config?.handleCode && statusCode !== LoginExpiredCode)) {
        // handleCode为true，表示需要 自行获取并处理请求code，默认为false
        return response;
      }
      // 2.登录失效,退出登录
      if (statusCode === LoginExpiredCode) {
        
        const pathname = window.location.pathname
        const dontGoToLoginPaths = ['/invite', '/inviteSuccess'] // 不要跳转到login页面的路径
        try {
          // 保证退出IM服务
          openIMService.then(async (im: any) => {
            console.log('🥶🥶🥶🥶🥶获取IM实例', im, im.isLogouting)
            if (!im.isLogouting) await im.logout()
            
            const goToLogin = !dontGoToLoginPaths.includes(pathname)
            // console.log('goToLogin',goToLogin)
            if (requestUrl !== '/system/auth/session') {
              // 1）登录失效提醒
              if (goToLogin){
                const key = 'loginExpired'
                message.destroy(key)
                message.open({
                  type: 'error',
                  content: i18next.t("error.logo-timeout"),
                  key: key
                })
                refreshTheme(true, true)
              }
            }
            if (store.getState().app.isLogin){
              // 2）跳转到登录页面
              store.dispatch(change_app_userInfo({ isLogin: false, userInfo: {} }))
              if (goToLogin){
                router.navigate("/login")
              }
            }
          }).catch((err) => {
            console.log('err', err)
          })
        } catch (error) {
          console.log('error', error)
        }
      }
      // 公司不存在，刷新页面
      else if ([1002015000].includes(statusCode) && isLogin){
        const key = 'CompanyNoExist'
        message.destroy(key)
        message.open({
          type: 'error',
          content: RequestFailedMessage(response?.data?.msg),
          key: key,
          duration: 2000,
          onClose:()=>{
            window.location.reload() // 刷新页面
          }
        })
      }
      // 4.接口报错，提示报错信息
      else if (statusCode !== 0) {
        if (!config?.hideTip) {
          let errorMsg = RequestFailedMessage(response?.data?.msg)
          message.error(errorMsg);
        }
      }

      return Promise.reject(response.data);
    }, (error) => {
      const errorCode = error.response.status
      // 请求异常，提示错误
      message.open({
        type: 'error',
        content: i18next.t('error.request-failed-code', { code: errorCode })
      });
      return Promise.reject(error);
    })
  return instance
};

// 创建不同实例
const HTTP = AddCommonInterceptors(
  axios.create({ baseURL: '/api', timeout: Timeout })
);

const ADMIN_HTTP = AddCommonInterceptors(
  axios.create({ baseURL: '/gateway/admin-api', timeout: Timeout })
);

const APP_HTTP = AddCommonInterceptors(
  axios.create({ baseURL: '/gateway/app-api', timeout: Timeout })
);

export default HTTP;

export { APP_HTTP, ADMIN_HTTP, ErrorCodes,Timeout, LoginExpiredCode, RequestFailedMessage, AddCommonInterceptors }
