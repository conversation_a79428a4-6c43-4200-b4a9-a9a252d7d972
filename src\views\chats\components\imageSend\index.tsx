
import { useState } from "react";
import {  Upload,Flex } from "antd"
import type { UploadProps } from 'antd';
import SvgIcon from '@components/svgicon'; 
import './index.scss'

const ImageSend = (props: any) => {
    const {onUpdate} = props

    // 视频上传
    const Uploadprops: UploadProps = {
        name: 'chatImage',
        action: '#',
        accept: 'image/*',
        showUploadList: false,
        // 上传前检验
        beforeUpload(file: any, uploadFileList: any) {
            return true
        },
        customRequest: async (options: any) => {
             // console.log('customRequest uploadingFileTotal', uploadingFileTotal)
            const { onSuccess, onError, file, onProgress } = options;
            let fileType = file.name.substring(file.name.lastIndexOf(".") + 1);
            fileType = fileType.toLowerCase()
            // 通知上传
            onUpdate && onUpdate(file,fileType)
        }
    };

    return (
            <Upload className="chat-image-upload" {...Uploadprops}>
                <Flex  className="operate-item" align="center" justify="center">
                    <SvgIcon svgName="send_image"/>
                </Flex>
            </Upload>
    )
}

export default ImageSend