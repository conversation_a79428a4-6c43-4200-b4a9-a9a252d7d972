

import React,{ useEffect, useState,useRef } from "react";
import {connect} from "react-redux";
import i18next from 'i18next'
import ReactMarkdown from "react-markdown";
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import remarkGfm from 'remark-gfm'
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { oneLight, oneDark  } from "react-syntax-highlighter/dist/esm/styles/prism";
import {Tooltip,Flex} from 'antd'
import {CheckCircleFilled} from '@ant-design/icons';
import CopyToClipboard from 'copy-to-clipboard';
import Svgicon from "@/components/svgicon"
import "./index.scss"
import 'katex/dist/katex.min.css'; // 引入KaTeX样式

const MarkdownComponent = (props:any) => {
  /**props **/
  let content = props.content;
  // 为公式插件，格式化\\\\为\\
  if (typeof content=='string'){
    // 使用正则表达式匹配以$$开头和结尾的部分，并替换\\\\为\\
     content = content.replace(/\$\$([^$]*?)\$\$/g, (match:any, p1:any) => {
      return `$$${p1.replace(/\\\\/g, '\\')}$$`;
    })
    // 使用正则表达式匹配 转义$ 字符，```代码块除外
    // content = content.replace(/(?<!`)(\$)(\d+(\.\d+)?)(?![^`]*`)/g, '\\$1$2');
    content = content.replace(/(?<!`)(\$)(\d+(\.\d+)?)(?![^`]*`)/g, (match:any, p1:any, p2:any, p3:any, p4:any) => {
      // console.log('---match',match)
      // console.log('p1',p1)
      // console.log('p2',p2)
      // console.log('p3',p3)
      // console.log('p4',p4)
      return `\\${p1}${p2}`;
    })
  }
  
  // console.log('content',  content)
  
  const theme = props.themeInfo.theme
  const markdownRef = useRef(null);

  /*state */
  const [copyFinished,setCopyFinished] = useState(false)
  const [copyFinishedTimer,setCopyFinishedTimerd] = useState<any>(null)

  // const fontFamily=getCSSVariablesValue('--font-family')
  /**method */
  const handleClickCopy = (str:any)=>{
    CopyToClipboard(str);
    setCopyFinished(true)
    const timer = window.setTimeout(()=>{
      setCopyFinished(false)
    },2000)
    setCopyFinishedTimerd(timer)
  }

  useEffect(() => {
    return () => {
      window.clearTimeout(copyFinishedTimer)
    };
  }, []); // 空依赖数组表示只在挂载和卸载时执行

  const handleImageClick = (e:any) => {
    if (!props.onViewImage || props.imageViewDisabled) return
    const src =e?.target?.getAttribute("src")
    props.onViewImage(src)
  };

  useEffect(() => {
    // 查找目标元素markdownRef
    const targetElement:any = markdownRef.current;
    if (targetElement) {
      // 查找该元素下的所有 img 标签
      const imgElements = targetElement.getElementsByTagName('img');
      // 禁用状态
      if (props.imageViewDisabled){
        Array.from(imgElements).forEach((img:any) => {
          img.removeEventListener('click', handleImageClick);
        });
        return
      }
      // 为每个 img 标签添加点击事件
      Array.from(imgElements).forEach((img:any) => {
        img.addEventListener('click', handleImageClick);
      });
      // 清理事件监听器
      return () => {
        Array.from(imgElements).forEach((img:any) => {
          img.removeEventListener('click', handleImageClick);
        });
      };
    }
  }, [props.imageViewDisabled]); // 空依赖数组确保只在组件挂载时运行一次

  return (
    <div ref={markdownRef}>
      <ReactMarkdown
        className={"ai-mark-down "}
        children={content}
        linkTarget="_blank"
        components={{
          code({ node, inline, className, children, ..._props }) {
            const regex = /^language-(\w+)$/; // 匹配语言
            let lang:string = ''
            if (className){
              const match = className.match(regex); // 尝试匹配字符串
              if(match) lang = match[1]
            }
            return (
              !inline ? <div className="syntax-highlighter">
                {/* 语言 */}
                <Flex className="code-top" justify="space-between">
                  {lang? <span className="code-lang">{lang}</span>:null}
                  <Tooltip placement="top" className="copy-tooltip" title={copyFinished? (<div className="copyed">
                    <CheckCircleFilled />{i18next.t("copied")}</div>):i18next.t('app.copy')} arrow={false}>
                  <span className="code-copy" onClick={() => handleClickCopy(String(children))}><Svgicon svgName="copy"/></span>
                </Tooltip>
                </Flex>
                {
                  <SyntaxHighlighter
                    {..._props}
                    children={String(children)}
                    style={theme =="dark"? oneDark : oneLight}
                    // customStyle={{paddingTop:lang ? "2em" : "1em"}}
                    customStyle={{background: theme =="dark" ? "#323232" : '#FAFAFA',paddingTop:"54px",borderRadius: "12px", margin:0}}
                    language={lang}
                    // language={_syntaxSupportedLanguages.includes(lang) ? lang : "jsx"}
                    PreTag="div"
                  />
                }
              </div> :
                <code className={`className ${'inline-code'}`} {..._props}>
                  {String(children)}
                </code>
            )
          }
          // img:({src,alt})=> <CustomImage src={src} />
        }}
        remarkPlugins={[remarkGfm,remarkMath]}// 处理 表格、数学公式
        rehypePlugins={[rehypeKatex as any]} // 数学公式渲染为HTML
      />
    </div>
  );
};

const mapStateToProps =(state:any) =>({
  themeInfo: state.app.themeInfo
})
const mapDispatchToProps = (dispatch: any) => ({
  })
export default connect(mapStateToProps,mapDispatchToProps)(MarkdownComponent);
