import React from "react";
import {connect} from "react-redux";
import {LoadingOutlined} from '@ant-design/icons';
import {Spin} from 'antd';

interface ContextState {
  
}
class Home extends React.Component<any, ContextState> {

  constructor(props: any) {
    super(props);
    this.state = {
      
    }
  }
  render() {
    const {userInfo} = this.props;

    return (
      <div className={"home-page"}>
        home page
      </div>
    )
  }
}

const mapStateToProps = (state: any) => ({
  userInfo: state.app.userInfo,
  globalRegion: state.app.globalRegion,
  globalLang: state.app.globalLang,
});

const mapDispatchToProps = (dispatch: any) => ({
  // dispatch_api_xxx:(data: any) => dispatch(dispatch_api_xxx(data)),
})

export default connect(mapStateToProps, mapDispatchToProps)(Home);
