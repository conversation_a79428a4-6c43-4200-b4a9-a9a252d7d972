/* 
*主题默认配置
*1、目前模式有2种：亮色模式、暗色模式、
*2、支持配置的颜色：品牌主色、品牌辅助色、文本对比色
*3、这是系统默认配置，优先使用 用户在admin 配置的
*/
//内置配置
export const theme = "light" // 主题模式，默认亮色模式
export const colorPrimary = "#252526" // 品牌主色-亮白模式（亮白和暗黑模式 色值可不一致，根据字节palette算法生成替换）
export const colorPrimarySecondary = "#AC39F6" // 主色輔助色 colorPrimarySecondary
export const colorPrimaryContrasting = "#FFFFFF" // 主色對比色 colorPrimaryContrasting
export const themeOptions = ["light", "dark"] // 主题模式选项

const common={
    '--colorPrimarySecondary': colorPrimarySecondary, // 主色輔助色
    '--colorPrimaryContrasting': colorPrimaryContrasting, // 主色對比色
}
const colorPrimaryDark ="#929293"// 品牌主色-暗黑模式
const fixedConfig ={
    '--font-size-small'        : '12px',
    '--font-size-base'         : '14px',
    '--font-size-large'        : '16px',
    '--footer-height'          : '32px',
    '--robot-right-header'     : '62px',
    '--robot-footer-space'     : '16px',
    '--font-family'            : '-apple-system, BlinkMacSystemFont, "PingFang SC", "PingFang TC", "Noto Sans SC", "Noto Sans TC", "Source Han Sans SC", "Source Han Sans TC", "Microsoft YaHei", "Microsoft JhengHei", "Helvetica Neue", Helvetica, Arial, sans-serif',
    '--formitem-border-radius' : '8px',
    
    '--page-nav-height'        : '62px',
    '--layout-content-space'   : '20px', 
    '--sider-menu-width'       : '260px',
    '--formitem-default-height': '34px',
    '--formitem-large-height'  : '38px',
    '--formitem-small-height'  : '30px'
}
export interface themeConfigType  {
    light: object,
    dark: object
}

export const themeDefaultConfig:any = {
    // 亮色模式
    light:{
        /*-----动态变量开始-----*/
        ...common,
        /*品牌色 */
        '--colorPrimary'          : colorPrimary, // 品牌主色 colorPrimary (arco-ama-6)
        /*品牌派生色 */
        '--colorPrimaryBg'         :'#F8F8F8',// 主色淺色背景色 colorPrimaryBg(arco-ama-1)
        '--colorPrimaryBgHover'    :'#CECFD4',// 主色淺色背景懸浮態 colorPrimaryBgHover(arco-ama-2)
        '--colorPrimaryHover'      :'#4F4F51',// 主色懸浮態 colorPrimaryHover (arco-ama-5)
        '--colorPrimaryActive'     :'#1C1C26',// 主色激活態 colorPrimaryActive(arco-ama-7)
        '--colorPrimaryBorder'     :'#A4A4A8',// 主色描邊色 colorPrimaryBorder (arco-ama-3)
        '--colorPrimaryBorderHover':'#7A7A7D',// 主色描邊色懸浮態 colorPrimaryBorderHover(arco-ama-4)
        '--colorPrimaryTextHover'  :'#4F4F51',// 主色文本懸浮態 colorPrimaryTextHover(arco-ama-5)
        '--colorPrimaryText'       : colorPrimary,// 主色文本色 colorPrimaryText (arco-ama-6)
        '--colorPrimaryTextActive' :'#1C1C26',// 主色文本激活態 colorPrimaryTextActive(arco-ama-7)
        /*-----动态变量结束-----*/
        /*-----静态变量开始-----*/
        /*填充色 */
        '--colorFill'              :'rgba(0, 0, 0, 0.15)',// 一級填充色 colorFill
        '--colorFillSecondary'     : 'rgba(0, 0, 0, 0.05)', // 二級填充色 colorFillSecondary (导航栏背景色) 
        '--colorFillTertiary'      : 'rgba(0, 0, 0, 0.04)', // 三級填充色 colorFillTertiary
        '--colorFillQuaternary'    : 'rgba(0, 0, 0, 0.02)', // 四級填充色 colorFillQuaternary
        /*文本色 */
        '--colorText'              : 'rgba(0, 0, 0, 0.88)', // 一級文本色 colorText
        '--colorTextLightSolid'    : '#fff',
        '--colorTextSecondary'     : 'rgba(0, 0, 0, 0.65)', // 二級文本色 colorTextSecondary
        '--colorTextTertiary'      : 'rgba(0, 0, 0, 0.45)', // 三級文本色 colorTextTertiary
        '--colorTextQuaternary'    : 'rgba(0, 0, 0, 0.25)', // 四級文本色 colorTextQuaternary
        /*背景色 */
        '--colorBgContainer'       : '#FEFEFE', // 組件容器背景色colorBgContainer
        '--colorBgElevated'        : '#FFFFFF',// 浮層容器背景色colorBgElevated
        '--colorBgLayout'          : '#F4F4F5',// 佈局背景色 colorBgLayout
        '--colorBgMask'            : 'rgba(0, 0, 0, 0.45)',// 浮層的背景蒙層顏色 colorBgMask
        '--colorBgSpotlight'       : 'rgba(0, 0, 0, 0.85)',// 引起注意的背景色 colorBgSpotlight
        /*功能色 */
        '--colorSuccess'           : '#52C41A', // 成功色 colorSuccess
        '--colorWarning'           : '#FAAD14', // 警戒色 colorWarning
        '--colorError'             : '#F53F3F', // 錯誤色 colorError
        '--colorErrorBg'           : '#FFF1F0',
        '--colorErrorBgHover'      : '#FFCCC7', 
        '--colorErrorBorder'       : '#FFA39E', 
        '--colorErrorBorderHover'  : '#FF7875', 
        '--colorErrorHover'        : '#FF4D4F', 
        '--colorErrorActive'       : '#CF1322', 
        '--colorLink'              : '#1677FF', // 鏈接色 colorLink
        /*中性色 */
        '--colorTextBase'          : '#000000', // 基础文本色 colorTextBase
        '--colorBgBase'            : '#FFFFFF', // 基礎背景色 colorBgBase
        /*图标色 */
        '--colorIcon'              : '#1F1F1F', // 一級圖標色 colorIcon
        '--colorIconSecondary'     : '#595959', // 二級圖標色 colorIconSecondary
        '--colorIconTertiary'      : '#8C8C8C', // 三級圖標色 colorIconTertiary 
        '--colorIconQuaternary'    : '#BFBFBF', // 四級圖標色 colorIconQuaternary
        /*描边色 */
        '--colorBorder'            : '#D9D8D8', // 一級邊框色 colorBorder
        '--colorBorderSecondary'   : '#E8E8E8', // 二級邊框色 colorBorderSecondary
        '--colorBorderTertiary'    : '#F6F6F6', // 三級邊框色 colorBorderTertiary
        '--colorBorderImage'       : 'rgba(0,0,0,0.04)', // colorBorderImage   
        /*其他 */
        '--colorBgNav'             : '#FAFAFA', // 导航栏背景色
        '--colorBgMark'            : '#f3f791', // 高亮背景

        ...fixedConfig
        /*-----静态变量结束-----*/
    },
    // 暗色模式
    dark:{
        /*-----动态变量开始-----*/
        ...common,
        /*品牌色 */
        '--colorPrimary'           : colorPrimaryDark, // 品牌主色 colorPrimary (arco-ama-6)
        /*品牌派生色 */
        '--colorPrimaryBg'         :'#17171A',// 主色淺色背景色 colorPrimaryBg(arco-ama-1)
        '--colorPrimaryBgHover'    :'#2E2E30',// 主色淺色背景懸浮態 colorPrimaryBgHover(arco-ama-2)
        '--colorPrimaryHover'      :'#78787A',// 主色懸浮態 colorPrimaryHover (arco-ama-5)
        '--colorPrimaryActive'     :'#ABABAC',// 主色激活態 colorPrimaryActive(arco-ama-7)
        '--colorPrimaryBorder'     :'#484849',// 主色描邊色 colorPrimaryBorder (arco-ama-3)
        '--colorPrimaryBorderHover':'#5F5F60',// 主色描邊色懸浮態 colorPrimaryBorderHover(arco-ama-4)
        '--colorPrimaryTextHover'  :'#78787A ',// 主色文本懸浮態 colorPrimaryTextHover(arco-ama-5)
        '--colorPrimaryText'       : colorPrimaryDark,// 主色文本色 colorPrimaryText (arco-ama-6)
        '--colorPrimaryTextActive' :'#ABABAC',// 主色文本激活態 colorPrimaryTextActive(arco-ama-7)
        /*-----动态变量结束-----*/
        /*-----静态变量开始-----*/
        /*填充色 */
        '--colorFill'              : 'rgba(255, 255, 255, 0.18)',// 一級填充色 colorFill
        '--colorFillSecondary'     : 'rgba(255,255,255,0.12)', // 二級填充色 colorFillSecondary (导航栏背景色) 
        '--colorFillTertiary'      : 'rgba(255,255,255,0.08) ', // 三級填充色 colorFillTertiary
        '--colorFillQuaternary'    : 'rgba(255,255,255,0.04)', // 四級填充色 colorFillQuaternary
        /*文本色 */ 
        '--colorText'              : 'rgba(255,255,255,0.85)', // 一級文本色 colorText
        '--colorTextLightSolid'    : '#fff',
        '--colorTextSecondary'     : 'rgba(255,255,255,0.65)', // 二級文本色 colorTextSecondary
        '--colorTextTertiary'      : 'rgba(255, 255, 255, 0.45)', // 三級文本色 colorTextTertiary
        '--colorTextQuaternary'    : 'rgba(255,255,255,0.25) ', // 四級文本色 colorTextQuaternary
        /*背景色 */
        '--colorBgContainer'       : '#202020', // 組件容器背景色colorBgContainer
        '--colorBgElevated'        : '#191919',// 浮層容器背景色colorBgElevated
        '--colorBgLayout'          : '#101010',// 佈局背景色 colorBgLayout
        '--colorBgMask'            : 'rgba(0, 0, 0, 0.8)',// 浮層的背景蒙層顏色 colorBgMask
        '--colorBgSpotlight'       : 'rgba(66,66,66,0.85) ',// 引起注意的背景色 colorBgSpotlight
        /*功能色 */
        '--colorSuccess'           : '#52C41A', // 成功色 colorSuccess
        '--colorWarning'           : '#FAAD14', // 警戒色 colorWarning
        '--colorError'             : '#F53F3F', // 錯誤色 colorError
        '--colorErrorBg'           : '#3F2125',
        '--colorErrorBgHover'      : '#431418', 
        '--colorErrorBorder'       : '#58181C', 
        '--colorErrorBorderHover'  : '#791A1F', 
        '--colorErrorHover'        : '#A61D24', 
        '--colorErrorActive'       : '#F98D86', 
        '--colorLink'              : '#1677FF', // 鏈接色 colorLink
        /*中性色 */
        '--colorTextBase'          : '#FFFFFF', // 基础文本色 colorTextBase
        '--colorBgBase'            : '#101010', // 基礎背景色 colorBgBase
        /*图标色 */
        '--colorIcon'              : '#DBDBDB', // 一級圖標色 colorIcon
        '--colorIconSecondary'     : '#ACACAC', // 二級圖標色 colorIconSecondary
        '--colorIconTertiary'      : '#7C7C7C', // 三級圖標色 colorIconTertiary
        '--colorIconQuaternary'    : '#4B4B4B', // 四級圖標色 colorIconQuaternary
        /*描边色 */
        '--colorBorder'            : '#525252', // 一級邊框色 colorBorder
        '--colorBorderSecondary'   : '#404040', // 二級邊框色 colorBorderSecondary
        '--colorBorderTertiary'    : '#303030', // 三級邊框色 colorBorderTertiary
        '--colorBorderImage'       : 'rgba(255,255,255,0.06)', // colorBorderImage   
        /*其他 */
        '--colorBgNav'             : '#191919', // 导航栏背景色
        '--colorBgMark'            : '#3a3d07', // 高亮背景

        ...fixedConfig
        /*-----静态变量结束-----*/
    }
}