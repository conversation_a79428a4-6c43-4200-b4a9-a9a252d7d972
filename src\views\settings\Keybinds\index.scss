@import "../../../styles/mixin.scss";
.keybinds-wrapper {
  height: 100%;
  .sub-title {
    @include font(12px, 18px, 600);
    margin-top: 24px;
  }
  .keys-list {
    margin-top: 20px;
  }
  .key-name {
    @include font(14px, 22px, 400, var(--colorTextSecondary));
  }
  .key-item {
    padding-bottom: 20px;
    border-bottom: 1px solid var(--colorBorderSecondary);
  }
  .key-btns {
    margin-left: auto;
    @include font(14px, 22px, 400);
  }
  .key-btn {
    border-radius: 6px;
    border: 0.5px solid var(--colorBorderSecondary);
    background: var(--colorFillSecondary);
    padding-bottom: 6px;
    > div {
      border-radius: 6px;
      background: var(--colorBgBase);
      padding: 0 6px;
    }
   
  }
}
