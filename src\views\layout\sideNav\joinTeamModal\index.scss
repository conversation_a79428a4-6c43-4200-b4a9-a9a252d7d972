@import "../../../../styles/mixin.scss";
.join-team-modal{
    .ant-modal-body{
        padding-right: 0!important;
    }
    .modal-tip{
        // flex-shrink: 0;
        font-size: 12px;
        line-height: 18px;
        color: var(--colorTextSecondary);
        margin-bottom: 8px;
    }
    .modal-middle{
        margin-top: 24px;
        // flex: 1;
        // overflow-y: auto;
        .ant-input-affix-wrapper{
            flex-shrink: 0;
            width: calc(100% - 20px);
        }
        .result-area{
            // flex: 1;
            overflow-y: auto;
            height: 400px;
            width: 100%;
            padding: 6px;
            padding-right: 26px;
        }
        .no-result{
            width: 100%;
            height: 100%;
            >div:first-child{
                font-size: 14px;
                line-height: 22px;
                font-weight: 600;
                color: var(--colorTextSecondary);
            }
            >div:last-child{
                font-size: 12px;
                line-height: 18px;
                font-weight: 400;
                color: var(--colorTextTertiary);
            }
        }
        .result-item{
            padding: 8px;
            cursor: pointer;
            .team-logo{
                flex-shrink: 0;
                width: 32px;
                height:32px;
                border-radius: 10px;
            }
            .team-name{
                flex: 1;
                overflow: hidden;
                >div:first-child{
                    font-size: 14px;
                    line-height: 22px;
                    font-weight: 500;
                    color: var(--colorText);
                }
                >div:last-child{
                    font-size: 12px;
                    line-height: 18px;
                    font-weight: 400;
                    color: var(--colorTextTertiary);
                }
                >div{
                    width: 100%;
                    @include ellipsis();
                }
            }
            .team-selected{
                flex-shrink: 0;
            }
        }
    }
    .ant-modal-footer{
        border-top: 1px solid var(--colorBorderSecondary);
    }
    .modal-bottom{
        // flex-shrink: 0;
        .ant-btn{
            // height: 34px;
        }
    }
    // .search-input{
    //     width: calc(100% - 16px);
    // }
}