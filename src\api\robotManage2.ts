/* 助手管理模块*/
import HTTP from "@/utils/request";
import * as actions from "@/store/action";
import { AxiosRequestConfig } from 'axios';

const commonParams = {
  seq: '0'
}

// 获取机器人列表
export const api_get_robot_list = (data:any): Promise<unknown>  => new Promise((resolve, reject) => {
  HTTP({
    method: 'post',
    url: "/robot/robotList",
    data,
  }as AxiosRequestConfig).then(result => {
    resolve(result?.data || {});
  }).catch(err => reject(err))
})

// 新增机器人
export const api_create_robot = (data:any): Promise<unknown>  => new Promise((resolve, reject) => {
  HTTP({
    method: 'post',
    url: "/robot/createRobot",
    data,
  }as AxiosRequestConfig).then(result => {
    resolve(result?.data || {});
  }).catch(err => reject(err))
})

// 更新机器人
export const api_update_robot = (data:any): Promise<unknown>  => new Promise((resolve, reject) => {
  HTTP({
    method: 'post',
    url: "/robot/updateRobot",
    data,
  }as AxiosRequestConfig).then(result => {
    resolve(result?.data || {});
  }).catch(err => reject(err))
})

// 删除机器人
export const api_delete_robot = (data:any): Promise<unknown>  => new Promise((resolve, reject) => {
  HTTP({
    method: 'post',
    url: "/robot/deleteRobot",
    data,
  }as AxiosRequestConfig).then(result => {
    resolve(result?.data || {});
  }).catch(err => reject(err))
})

// 机器人详情
export const api_query_robot = (data:any): Promise<unknown>  => new Promise((resolve, reject) => {
  HTTP({
    method: 'post',
    url: "/robot/queryRobot",
    data,
  }as AxiosRequestConfig).then(result => {
    resolve(result?.data || {});
  }).catch(err => reject(err))
})

// 知识文档-查询
export const api_query_robot_document = (data:any): Promise<unknown>  => new Promise((resolve, reject) => {
  HTTP({
    method: 'post',
    url: "/knowledge/queryRobotDocumentList",
    data,
  }as AxiosRequestConfig).then(result => {
    resolve(result?.data?.data || {});
  }).catch(err => reject(err))
})

// 知识文档-新增
export const api_add_robot_document = (data:any): Promise<unknown>  => new Promise((resolve, reject) => {
  HTTP({
    method: 'post',
    url: "/knowledge/createRobotDocument",
    data,
  }as AxiosRequestConfig).then(result => {
    resolve(result?.data || {});
  }).catch(err => reject(err))
})

// 知识文档-删除
export const api_delete_robot_document = (data:any): Promise<unknown>  => new Promise((resolve, reject) => {
  HTTP({
    method: 'post',
    url: "/knowledge/deleteRobotDocument",
    data,
  }as AxiosRequestConfig).then(result => {
    resolve(result?.data || {});
  }).catch(err => reject(err))
})

// 获取llm模型数据
export const api_get_admin_robot_llmmodel = (): Promise<unknown>  => new Promise((resolve, reject) => {
  HTTP({
    method: 'post',
    url: "/robot/robotLlmModel",
    data:{ seq: 0, isHomeBot: false }
  }as AxiosRequestConfig).then(result => {
    resolve(result?.data?.data || {});
  }).catch(err => reject(err))
})

// 获取机器人权限
export const api_get_robot_permission = (data:any): Promise<unknown>  => new Promise((resolve, reject) => {
  HTTP({
    method: 'post',
    url: "/robot/getPermission",
    data
  }as AxiosRequestConfig).then(result => {
    resolve(result?.data?.data || {});
  }).catch(err => reject(err))
})

// 修改机器人权限
export const api_update_robot_permission = (data:any): Promise<unknown>  => new Promise((resolve, reject) => {
  HTTP({
    method: 'post',
    url: "/robot/updateRobotPermission",
    data
  }as AxiosRequestConfig).then(result => {
    resolve(result?.data?.data || {});
  }).catch(err => reject(err))
})