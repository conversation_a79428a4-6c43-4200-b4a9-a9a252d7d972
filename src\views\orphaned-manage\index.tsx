import React, { useEffect, useState } from "react";
import { connect } from "react-redux";
import { useTranslation } from 'react-i18next';
import classNames from "classnames";
import Svgicon from "@/components/svgicon"
import { Flex, Empty, Spin } from "antd";
// import { StarFilled, StarOutlined, LoadingOutlined } from '@ant-design/icons';
import { api_get_robot_list } from "@/api/robotManage2";
import { api_get_orphaned_robots } from "@/api/robot";
import MyInput from '@/views/robot-manage/component/Input'
import RobotDetails from '@/views/bots-marketplace/modal/robotDetails/index';

import defaultLogo from "@/assets/bot_default_avatar.png";

import './index.scss'

// const pageSize = 20 // 分页size

// 机器人回收站
const OrphanedManage = (props: any) => {
  const { t, i18n } = useTranslation();
  const { onUpdateRobotList, activeCompanyId, activeCompanyName, userInfo } = props

  const [isLoading, setIsLoading] = useState(false) // 页面加载 
  const [keyWord, setKeyWord] = useState("") // 模糊搜索 
  const [robotList, setRobotList] = useState<any>([]) // 机器人数据
  // const [totalCount, setTotalCount] = useState<any>(0) // 数据总数目
  // const [isLoadingMore, setIsLoadingMore] = useState(false) // 下拉加载更多中
  // const [loadAllFinished, setLoadAllFinished] = useState<any>([]) // 加载所有完成

  // const [isCreateRobotModalOpen, setIsCreateRobotModalOpen] = useState(false) // 新建机器人弹窗 
  const [robotDetailModal, setRobotDetailModal] = useState({ visible: false, robotId: null }) // 机器人详情弹窗 
  const [panel, setPanel] = useState('RobotList') // 显示面板：机器人列表 RobotList(默认)、机器人聊天会话 RobotChat
  // const [targetRobotChat, setTargetRobotChat] = useState<any>({}) // 选中的机器人聊天

  // 初始化
  useEffect(() => {
    queryList(true)
  }, [])

  // 获取所有的机器人
  const queryList = async (isInit = false, _keyword?: any) => {
    const data = {
      seq: 0,
      searchKeywords: _keyword !== undefined ? _keyword : keyWord,
    }
    setIsLoading(true)
    let res: any = await api_get_orphaned_robots(data).catch(() => {
      setIsLoading(false)
    })
    setIsLoading(false)
    if (!res) return
    let list = res?.robotList || []
    setRobotList(list)
  }

  // 详情
  const onViewRobotDetail = (robot: any) => {
    setRobotDetailModal({ visible: true, robotId: robot.robot_id })
  }

  // 修改关键词
  const onChangeKeyWord = (value: any) => {
    // console.log('value', value)
    setKeyWord(value)
    queryList(false, value)// 发起请求
  }

  // 关闭 机器人详情弹窗
  const onCloseRobotDeailModal = () => {
    setRobotDetailModal({ visible: false, robotId: null })
  }

  // 获取名称
  const getName = (robot: any) => {
    let name = robot.username
    if (robot.robot_type === 'DEPARTMENT') {
      name = robot.department_name !== null ? robot.department_name : activeCompanyName
    }
    if (robot.robot_type === 'COMPANY') {
      name = activeCompanyName
    }
    return name
  }

  return (
    <div className="orphaned-manage gray-bg">
      { // 机器人列表
        panel === 'RobotList' ?
          <>
            <Flex className="page-nav" align="center" justify="space-between">
              <span className="nav-title">{t('orphaned-manage.title')}</span>
              <MyInput onChange={onChangeKeyWord} />
              <span></span>
            </Flex>
            <div className="page-main">
              {
                isLoading ?
                  <Spin className="full-spin" size="large" /> :
                  <>
                    {
                      robotList?.length > 0 ?
                        <Flex className="card-list" gap={20} wrap={true}>
                          {
                            robotList?.map((robot: any, robotIndex: number) => {
                              return (
                                <Flex onClick={() => onViewRobotDetail(robot)} key={robot.robot_id + '' + robotIndex} className="card-list-item common-card" vertical justify={"space-between"}>
                                  <div className="cl-content">
                                    <Flex className="cl-content-top" gap={10} align="center">
                                      <img className="cl-content-img" src={robot.logo || defaultLogo} alt="" />
                                      <div className="cl-content-text">
                                        <div className="cl-content-title card-title">{robot.name}</div>
                                      </div>
                                    </Flex>
                                    <div className="cl-content-bottom">{robot.introduction_message}</div>
                                  </div>
                                  <>
                                    <Flex className="cl-operate" gap={20} align="center">
                                      <Flex gap={20} className="cl-operate-left">
                                        <Flex align="center" gap={4}>
                                          <Svgicon svgName="files" />
                                          <span>{robot.chat_count}</span>
                                        </Flex>
                                        <Flex className="cl-operate-left-con" align="center" gap={4}>
                                          {
                                            robot.robot_type === 'PERSONAL' ? <Svgicon svgName="founder" /> : <Svgicon className="icon_org" svgName="icon_company" />
                                          }
                                          <div className="cl-name">{getName(robot)}</div>
                                        </Flex>
                                      </Flex>
                                    </Flex>
                                  </>
                                </Flex>
                              )
                            })
                          }
                        </Flex>
                        :
                        // 空数据
                        <Empty className="page-empty" image={null} description={
                          <><div>{t('common.no-result')}</div>
                            <div>{t('robot-manage.search-result')}</div>
                          </>} />
                    }
                  </>
              }
            </div>
          </>
          : null
      }
      { // 机器人详情弹窗 
        robotDetailModal.visible ?
          <RobotDetails visible={robotDetailModal.visible} resource={robotDetailModal} onClose={() => onCloseRobotDeailModal()} />
          : null
      }
    </div>
  )
}
const mapStateToProps = (state: any) => ({
  userInfo: state.app.userInfo,
  activeCompanyId: state.app.activeCompanyId,
  activeCompanyName: state.app.activeCompanyName,
});

const mapDispatchToProps = (dispatch: (e: any) => void) => ({
});

export default connect(mapStateToProps, mapDispatchToProps)(OrphanedManage);
