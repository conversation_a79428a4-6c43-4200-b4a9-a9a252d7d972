
import { useState } from "react";
import {  Flex, Upload } from "antd"
import type { UploadProps } from 'antd';
import SvgIcon from '@components/svgicon'; 
import './index.scss'

const FileSend = (props: any) => {
    const {onUpdate} = props
    
    // 文件上传
    const Uploadprops: UploadProps = {
        name: 'chatFile',
        action: '#',
        // accept: 'image/*',
        showUploadList: false,
        // 上传前检验
        beforeUpload(file: any, uploadFileList: any) {
            return true
        },
        customRequest: async (options: any) => {
             // console.log('customRequest uploadingFileTotal', uploadingFileTotal)
            const { onSuccess, onError, file, onProgress } = options;
            // 通知上传
            onUpdate && onUpdate(file)
        }
    };

    return (
        <Upload className="chat-file-upload" {...Uploadprops}>
            <Flex className="operate-item" align="center" justify="center">
                <SvgIcon svgName="send_file"/>
            </Flex>
        </Upload>
    )
}

export default FileSend