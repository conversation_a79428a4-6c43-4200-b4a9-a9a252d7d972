
import { connect } from "react-redux";
import { useEffect, useState, useRef, forwardRef } from "react";
import { Modal, message, Flex, Button, Input, Form, Tabs, TabsProps } from "antd"
import { useTranslation } from 'react-i18next';
import { $trim, $isNull } from '@/utils/common'
import './index.scss'
import MobileInput from "@/components/form/mobileInput";
import ErrorCodeMap from '@/views/common/formErrorCodeMap'
import FormUtils from '@/views/common/formUtils'
import classnames from "classnames";
import { EmailRules, MobileRules, VerifyCodeRules } from "@/views/common/formRule";
import { api_get_email_code, api_get_mobile_code } from '@/api/user'
import { api_check_verify_code, api_change_contact } from '@/api/setting'
import VerifyCode from "@/components/form/verifyCode";
import { GetMobileFormValue } from '../EditMobile/index'
import { RequestFailedMessage } from "@/utils/userRequest";

const scence1: any = {
    Email: 'MEMBER_CHANGE_EMAIL',
    Mobile: 'MEMBER_CHANGE_MOBILE',
}
const scence2: any = {
    Email: 'MEMBER_CHANGE_NEW_EMAIL',
    // Mobile: 'MEMBER_CHANGE_NEW_MOBILE',
}

// const scence1 ='MEMBER_CHANGE_EMAIL'
// const scence2 ='MEMBER_CHANGE_NEW_EMAIL'

const TabItems: any = [
    {
        key: 'Email',
        label: 'email-verifiy',
        children: null,
    },
    {
        key: 'Mobile',
        label: 'mobile-verifiy',
        children: null,
        visible: false // // 用户绑定过手机号,才有手机号 tab
    }
];
// const OtherItems: any = [

// ]

// 修改邮箱
const EditEmail = forwardRef((props: any, ref: React.LegacyRef<HTMLDivElement> | any) => {
    const { t } = useTranslation()

    const { userInfo, email, mobile, onUpdate } = props

    const [step, setStep] = useState(1)// 步骤
    const [tabItems, setTabItems] = useState([...TabItems]) //tabs
    const [accountType, setAccountType] = useState('Email') // 邮箱 Email 或  手机号 Mobile
    const [isOkBtnAble, setIsOkBtnAble] = useState(false) // 按钮是否可用
    const [form] = Form.useForm();
    const [formValues, setFormValues] = useState<any>({});
    const [step1CheckCode, setStep1CheckCode] = useState<any>(null);


    // 初始化
    useEffect(() => {
    }, [])

    useEffect(() => {
        // console.log('email', email)
        form.setFieldValue('email', email)
    }, [email])

    useEffect(() => {
        // console.log('mobile', mobile)
        setTabItems(tabItems.map((i: any) => {
            i.visible = !$isNull(mobile)
            return i
        }))
        // if ($isNull(mobile)){
        //     setTabItems(OtherItems)
        // }
        setMobileFormValue()
    }, [mobile])

    // 表单项输入值监听, 控制提交按钮状态
    const values = Form.useWatch([], form);
    useEffect(() => {
        let fields: any = []
        if (step === 1) {
            fields = accountType == 'Email' ? ['email', "code"] : ['mobile', "code"]
        } else {
            fields = ["newEmail", "newCode"]
        }
        // console.log('fields', fields)
        form.validateFields(fields, { validateOnly: true }).then(() => {
            setIsOkBtnAble(true)
        }).catch((err) => {
            // console.log('SubmitButton1 errerr:', err)
            setIsOkBtnAble(false)
        });
    }, [step, accountType, form, values])


    const setMobileFormValue = () => {
        const { number, codeLabel } = GetMobileFormValue(mobile)
        form.setFieldValue('mobile', {
            number: number,
            code: { label: codeLabel, value: '' }
        })
    }

    const onOk = async () => {
        if (step === 1) {
            onSubmitStep1()
            return
        }
        if (step === 2) {
            onSubmitStep2()
        }
    }

    // 步骤1 提交
    const onSubmitStep1 = async () => {
        // setStep(2)
        // return
        let { email, mobile, code } = formValues
        const data: any = {
            scene: scence1[accountType],
            code,
            // email,
            // areaCode: '+' + mobile?.code?.label,
            // mobile: mobile?.number

        }
        if (accountType === 'Email') {
            data.email = email
        } else {
            data.areaCode = '+' + mobile?.code?.label
            data.mobile = mobile?.number
        }
        // console.log('data', data)
        setIsOkBtnAble(false)
        const res: any = await api_check_verify_code(data).catch(() => {
            setIsOkBtnAble(true)
        })
        setIsOkBtnAble(true)
        // console.log('res', res)
        if (!res) return
        let _code = res.code
        setStep1CheckCode(res?.data?.tempCode)
        if (_code === 0) {
            setStep(2)
        } else {
            const codeErros = ErrorCodeMap.code// 验证码错误码
            const errors = [...codeErros]
            if (!errors.includes(_code)) {
                const msg = RequestFailedMessage(res?.msg, _code)
                message.error(msg);
                return
            }
            const errMsg = t(`errorCode.${_code}`)
            form.setFields([{ name: "code", errors: [errMsg], warnings: [`errorCode.${_code}`] }]) //warnings 为了解决 setFields 检验信息 国际化问题
        }
    }

    // 步骤2 提交
    const onSubmitStep2 = async () => {
        FormUtils.resetValidate(form)
        let { newEmail, newCode } = formValues
        const data: any = {
            scene: scence2.Email,
            email: newEmail,
            code: newCode,
            tempCode: step1CheckCode
        }
        // console.log('data', data)
        // return
        setIsOkBtnAble(false)
        const res: any = await api_change_contact(data).catch(() => {
            setIsOkBtnAble(true)
        })
        setIsOkBtnAble(true)
        // console.log('res', res)
        if (!res) return
        let _code = res.code
        if (_code === 0 && res?.data?.success) {
            message.success(t('setting.change-email-ok'))
            onUpdate && onUpdate()
            onCancel()
        } else {
            const codeErros = ErrorCodeMap.code// 验证码错误码
            const emailErros = ErrorCodeMap.email// 邮件错误码
            const errors = [...codeErros,...emailErros]
            if (!errors.includes(_code)) {
                const msg = RequestFailedMessage(res?.msg, _code)
                message.error(msg);
                return
            }
            const errMsg = t(`errorCode.${_code}`)
            const field = codeErros.includes(_code) ? "newCode": 'newEmail'
            form.setFields([{ name: field, errors: [errMsg], warnings: [`errorCode.${_code}`] }]) //warnings 为了解决 setFields 检验信息 国际化问题
        }
    }

    // 取消
    const onCancel = () => {
        props.onClose()
    }

    // tab切换
    const onChangeTab = (key: string) => {
        // console.log(key);
        setAccountType(key)
    };

    // 区号切换时需要校验手机号
    const onValidteMobile = () => {
        form.validateFields(["mobile"])
    }

    // 表单项 输入改变事件
    const onFiledValueChange = (changedFields: any, allValues: any) => {
        try {
            // console.log('changedFields', changedFields)
            // console.log('allValues',allValues)
            setFormValues({ ...formValues, ...allValues });
            Object.keys(changedFields).forEach((fieldName: any) => {
                const fieldError = form?.getFieldError(fieldName)
                if (fieldError.length > 0) FormUtils.clearValidate(form, fieldName)
            })
        } catch (error) {

        }
    }
    // const EmailDiffRules = {
    //     whitespace: true,
    //     validator: async (_: any, value: any) => {
    //         if (value === null || value === undefined || value?.length === 0) return Promise.resolve() // 有值才校验
    //         const oldValue = form.getFieldValue('email')
    //         if (oldValue === value) {
    //             return Promise.reject('新邮箱不能和旧邮箱相同');
    //         }
    //         return Promise.resolve();
    //     }
    // }

     // 处理步骤1  获取验证码失败 对应表单输入框错误提示
     const onVerifyCodeError1 = (err: any) => {
        const _code =err?.code
        const emailErrors = ErrorCodeMap.email
        const mobileErrors = ErrorCodeMap.mobile
        const errMsg = t(`errorCode.${_code}`)
        let targetField:any = null
        if (emailErrors.includes(_code)) targetField = 'email'
        if (mobileErrors.includes(_code)) targetField = 'mobile'
        if(!targetField) return
        form.setFields([{ name: targetField, errors: [errMsg], warnings:[`errorCode.${_code}`]}])
    }

    // 处理步骤2  获取验证码失败 对应表单输入框错误提示
    const onVerifyCodeError2 = (err: any) => {
        const _code = err?.code
        const Errors = ErrorCodeMap.email
        if (!Errors.includes(_code)) return
        const errMsg = t(`errorCode.${_code}`)
        form.setFields([{ name: 'newEmail', errors: [errMsg], warnings: [`errorCode.${_code}`] }])
    }

    return (
        <div className="setting-edit-email">
            <Modal
                className={`setting-edit-email-modal border no-padding step${step}`}
                title={t('setting.change-email')}
                open={true}
                onOk={onOk}
                onCancel={onCancel}
                mask={true}
                maskClosable={true}
                centered={true}
                width={450}
                okText={step === 1 ? t('login.continue') : t('app.ok')}
                cancelText={t("app.cancel")}
                cancelButtonProps={{ variant: "filled", color: "default" }}
                okButtonProps={{ disabled: !isOkBtnAble }}
                footer={undefined}
            // footer={null}
            >
                <Form
                    form={form}
                    className="setting-edit-email-form common-form mediumn"
                    name="basic"
                    autoComplete="off"
                    layout="vertical"
                    onValuesChange={onFiledValueChange}
                    validateTrigger="onBlur"
                    requiredMark={false}
                    initialValues={{
                        email: '',
                        mobile: {
                            number: '', // 手机号
                            code: { label: '', value: '' }// 区号 label: 1, value: 'US'
                        },
                        code: '',
                        newEmail: '',
                        newCode: ''
                    }}
                >

                    {
                        step === 1 ?
                            <>
                                {
                                    tabItems.filter(i => i.visible).length > 0 ?
                                        <Tabs className="common-tab without-border" activeKey={accountType} defaultActiveKey="Email"
                                            items={tabItems.filter(i => i.visible)?.map((item: any) => ({
                                                key: item.key,
                                                label: t(`setting.${item.label}`), // 确保这里使用翻译函数
                                                children: item.children,
                                            }))} onChange={onChangeTab} />
                                        : null}
                                {/* 邮箱 */}
                                <Form.Item name="email"
                                    label={t('login.email-address')} rules={[{ required: true, whitespace: true, message: '' }, ...EmailRules(t)]}
                                    className={accountType == 'Mobile' ? 'hidden' : ''}>
                                    <Input disabled autoComplete="new-email" variant="filled" maxLength={300} placeholder={t('login-pls.email') as string} />
                                </Form.Item>
                                {/* 手机号 */}
                                <Form.Item shouldUpdate name="mobile" label={t('login.mobile')} help={null} rules={MobileRules(t)}
                                    className={classnames(['mobile-form-item', { hidden: accountType == 'Email' }])}>
                                    <MobileInput disabled onValidte={onValidteMobile} height={34} />
                                </Form.Item>
                                {/* 验证码 */}
                                <Form.Item name="code"
                                    className={classnames(["psd-form-item"])} label={t('login.verify-code')} rules={VerifyCodeRules(t)}>
                                    <VerifyCode
                                        step={step}
                                        id="edit-email-code1"
                                        api={accountType == 'Email' ? api_get_email_code : api_get_mobile_code}
                                        scene={scence1[accountType]}
                                        target={accountType.toLocaleLowerCase()}
                                        {
                                        ...
                                        (accountType == 'Email' ? { email: form.getFieldValue("email") } : { mobile: form.getFieldValue("mobile") })
                                        }
                                        targetValidate={form.validateFields}
                                        onError={onVerifyCodeError1}
                                    />
                                </Form.Item>
                            </> : null
                    }
                    {
                        step === 2 ? <>
                            {/*新邮箱 */}
                            <Form.Item name="newEmail"
                                label={t('setting.new-email')}
                                rules={[{ required: true, whitespace: true, message: '' }, ...EmailRules(t)]}
                            >
                                <Input autoComplete="new-email"
                                    variant="filled" maxLength={300} placeholder={t('login-pls.email') as string} />
                            </Form.Item>

                            {/* 验证码 */}
                            <Form.Item name="newCode" className={classnames(["psd-form-item"])} label={t('login.verify-code')} rules={VerifyCodeRules(t)}>
                                <VerifyCode
                                    id="edit-email-code2"
                                    step={step}
                                    api={api_get_email_code}
                                    scene={scence2["Email"]}
                                    target={'newEmail'}
                                    email={form.getFieldValue("newEmail")}
                                    targetValidate={form.validateFields}
                                    onError={onVerifyCodeError2}
                                />
                            </Form.Item>
                        </> : null
                    }
                    {/* </div> */}
                </Form>
            </Modal>

        </div>
    )
})

const mapStateToProps = (state: any) => ({
    userInfo: state.app.userInfo,
})
const mapDispatchToProps = (dispatch: any) => ({
    // dispatch_api_: (data:any) => dispatch(dispatch_api_(data))
})
const ConnectedCounter = connect(mapStateToProps, mapDispatchToProps, null, { forwardRef: true })(EditEmail);
export default ConnectedCounter;