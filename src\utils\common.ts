import * as math from "mathjs";
/**
 * @param value: number | string            需要被转换的数字
 * 千分位转换
 * 业务场景 => 123456789 转成 123,456,789
 * demo:
 *     formatThousands(123456) = 123,456,789;
 * */

export const formatThousands = (value: number = 0) => (value).toString().replace(/\d+/, (n) => {
  let len = n.length;

  if(len % 3 === 0) {
    return n.replace(/(\d{3})/g, ',$1').slice(1);
  } else {
    return n.slice(0, len % 3) + n.slice(len % 3).replace(/(\d{3})/g, ',$1');
  }

});

export const isJson = (str: string) => {
  try {
    let obj = JSON.parse(str);
    if (obj && typeof obj === "object") {
      return true;
    } else {
      return false;
    }
  } catch (e) {
    return false;
  }
}

export const getUUid = () => {
  let s: any[] = [];
  let hexDigits = '0123456789';
  for (let i = 0; i < 10; i++) {
    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
  }
  s[7] = '4';
  s[9] = hexDigits.substr((s[4] & 0x3) | 0x8, 1);
  s[8] = s[13] = s[18] = s[23] = '-';
  return s.join('');
}

export const formatNumberKMB = (value: number | string) => {
  if(typeof value === 'string') {
    value = Number(value);
  }

  let billion = 1000000000;
  let million = 1000000;
  let theThousand = 1000;
  let reg = /^\d+(?:\.\d{0,2})?/;


  if (value >= billion) {
    return Number(String(value / billion).match(reg)).toFixed(2) + "B";
  }

  if (value >= million) {
    return Number(String(value / million).match(reg)).toFixed(2) + "M";
  }

  if (value >= theThousand) {
    return Number(String(value / theThousand).match(reg)).toFixed(2) + "K";
  }

  return value;
}

export function debounce(func: any, wait: number) {
  let timeout: any = null;
  return function() {
    // @ts-ignore
    let context = this;

    if(!timeout) {
      timeout = setTimeout(() => {
        // @ts-ignore
        func.apply(context, arguments);
        timeout = null;
      }, wait)
    }
  }
}

export function throttle(func: any, wait: number) {
  let timeout: any = null;
  return function() {
    // @ts-ignore
    let context = this;

    if(timeout) return;
    timeout = setTimeout(() => {
      // @ts-ignore
      func.apply(context, arguments);
      timeout = null;
    }, wait)
  }
}

// 转换日期：时间戳转为日期字符串
export function formatDatetime(timestamp:any) {
  if (timestamp=="" || timestamp==undefined || timestamp == null) return ""
  return new Date(timestamp).toISOString().slice(0, 19).replace('T', ' ');
}

/**
 * Parse the time to string
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string}
 */
 export function parseTime(time:any, cFormat?:any) {
  if (arguments.length === 0) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if ((typeof time === 'string') && (/^[0-9]+$/.test(time))) {
      time = parseInt(time)
    }
    if ((typeof time === 'number') && (time.toString().length === 10)) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj:any = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result:any, key:any) => {
    let value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value] }
    if (result.length > 0 && value < 10) {
      value = '0' + value
    }
    return value || 0
  })
  return time_str
}

// 格式化日期 当日显示HH:MM；昨天及以前显示MM-DD 
export function formatTimestamp(timestamp:any) {
  // 将时间戳转换为Date对象（假设传入的是毫秒时间戳，如果是秒级需要*1000）
  const date = new Date(timestamp);
  // console.log('date',date)
  const now = new Date();
  
  // 判断是否是今天
  const isToday = date.getDate() === now.getDate() && 
                 date.getMonth() === now.getMonth() && 
                 date.getFullYear() === now.getFullYear();

  // 格式时间数字补零
  const padZero = (num:any) => String(num).padStart(2, '0');

  if (isToday) {
    // 当天显示 HH:MM
    return `${padZero(date.getHours())}:${padZero(date.getMinutes())}`;
  } else {
    // 昨天及以前显示 MM-DD
    return `${padZero(date.getMonth() + 1)}/${padZero(date.getDate())}`;
  }
}

// 去除字符串前后空格
export const $trim = (val:any)=> {
  return typeof val == 'string' ? val.replace(/^\s*|\s*$/g, '') : val
}

export const $isNull = function(val:any) {
  if (val instanceof Array) {
    return val.length > 0
  }
  return val === null || val === undefined || val === ''
}

// 对象数组按 某字段首字母排序方法
export const sortObjectArrayByFistWord =(data:any[], prop: string)=>{
  // 按 某字段 的首字母排序
  const _data:any[] = JSON.parse(JSON.stringify(data))
  const result = _data.sort((a, b) => {
      const aValue = a[prop].toUpperCase(); // 忽略大小写
      const bValue = b[prop].toUpperCase(); // 忽略大小写
      if (aValue < bValue) {
          return -1; // a 排在 b 前面
      }
      if (aValue > bValue) {
          return 1; // a 排在 b 后面
      }
      return 0; // 保持顺序不变
  });
  return result
}

// 对象数组按某字段 拼音首字母排序方法
export const sortObjectArrayByPingyin =(data:any[], prop: string)=>{
  const _data:any[] = JSON.parse(JSON.stringify(data))
  const result = _data.sort((a, b) => a[prop].localeCompare(b[prop], 'zh-CN'));
  return result
}

// 首字母大写
export const capitalize = (str:any) => str.toLowerCase().replace(/\b\w/g, (c:any) => c.toUpperCase());

/**
 * 根据对象属性对数组去重
 * @param {Array} arr - 待去重的对象数组
 * @param {string} prop - 去重依据的属性名
 * @param {boolean} [keepLast=false] - 是否保留最后一次出现的元素（默认保留第一个）
 * @returns {Array} 去重后的新数组
 */
 export function $deduplicateByProp(arr:any, prop:any, keepLast = false) {
  const seen = new Map(); // 使用 Map 记录属性值和最后一次出现的元素
  
  arr.forEach((item:any) => {
    const key = item[prop];
    if (keepLast || !seen.has(key)) {
      seen.set(key, item);
    }
  });

  // 转换为数组时保持原数组顺序（若需保留最后一个，需要重新遍历）
  return keepLast 
    ? Array.from(seen.values()) 
    : arr.filter((item:any) => {
        const key = item[prop];
        return seen.get(key) === item && seen.delete(key);
      });
}