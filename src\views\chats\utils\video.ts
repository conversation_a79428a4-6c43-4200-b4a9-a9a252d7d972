// 视频处理方法
const VideoUtils ={
    // 获取视频第一帧作为缩略图
  generateThumbnail: (videoFile: any) => {
    try {
        return new Promise((resolve) => {
            const video = document.createElement('video');
            video.crossOrigin = 'anonymous'; // 解决火狐浏览器兼容行
            const canvas = document.createElement('canvas');
            const ctx: any = canvas.getContext('2d');
      
            video.src = URL.createObjectURL(videoFile);
      
            video.onloadedmetadata = () => {
              // 设置canvas尺寸与视频相同
              canvas.width = video.videoWidth;
              canvas.height = video.videoHeight;
      
              // 在视频可以播放时捕获第一帧
              video.addEventListener('canplay', function () {
                // 跳转到视频的10%处（避免黑屏）
                video.currentTime = Math.min(0.1, video.duration * 0.1);
              });
      
              video.onseeked = () => {
                // 绘制视频帧到canvas
                ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
      
                // 将canvas转为Blob对象
                canvas.toBlob((blob: any) => {
                  const thumbnailFile = new File([blob], `thumbnail_${videoFile.name.replace(/\.[^/.]+$/, '')}.jpg`, {
                    type: 'image/jpeg',
                    lastModified: Date.now()
                  });
      
                  // 释放内存
                  URL.revokeObjectURL(video.src);
      
                  resolve({
                    thumbnailUrl: URL.createObjectURL(blob),
                    thumbnailFile,
                    duration: video.duration,
                    width: video.videoWidth,
                    height: video.videoHeight
                  });
                }, 'image/jpeg', 0.8); // 0.8是JPEG质量
              };
            };
          });
    } catch (error) {
        console.log('error',error)
        return {}
    }
  },
  // 获取视频时长
  getVideoDuration: (file: any): Promise<number> => {
    return new Promise((resolve, reject) => {
      // 创建视频元素来获取时长
      const video = document.createElement('video');
      video.preload = 'metadata';

      video.onloadedmetadata = () => {
        window.URL.revokeObjectURL(video.src);
        const duration = Math.floor(video.duration);
        console.log('----视频时长:', duration, '秒');
        // 可以将时长附加到file对象上
        file.duration = duration;
        resolve(file);
      };

      video.onerror = () => {
        reject(new Error('无法获取视频元数据'));
      };

      video.src = URL.createObjectURL(file);
    });
  },
  formatSeconds(seconds:any) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
}
export default VideoUtils