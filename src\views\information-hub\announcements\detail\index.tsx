import { connect } from "react-redux";
import React, { useState, useRef, useEffect } from "react";
import { Badge, Button, Dropdown, Empty, Flex, Input, Menu, message, Pagination, Popover, Segmented, Select, Spin, Tabs, TabsProps, Tag, Tooltip } from 'antd'
import { LoadingOutlined } from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { useTranslation } from 'react-i18next';
import SvgIcon from '@components/svgicon';
import { getPriorityFlag } from '../../common'
import TagManage from '../../components/tagManage'
import { getMenuItem } from "@components/base";
import { api_get_user_notification_details, api_generate_ann_summary, api_update_notification_pin, api_update_notification_archive, api_download_attachment } from '@/api/information'
import { $isNull, $trim, parseTime } from "@/utils/common";

import { formatFileSize, downloadBlobFile, getFileTypeByName } from "@/utils/file";

import './index.scss'
import classNames from "classnames";
import RichTextEditor from "@/components/richTextEditor/index2";


// 公告详情
const AnnouncementDetail = (props: any) => {
    const { t } = useTranslation();
    const editorRef = useRef<any>(null)

    const { resource, onBack, onRefreshTarget, onRefreshList } = props

    const [isLoading, setIsLoading] = useState<any>(false);
    const [isPining, setIsPining] = useState<any>(false);
    const [isArchiving, setIsArchiving] = useState<any>(false);
    const [details, setDetails] = useState<any>({
        content: ''
    }); // 公告详情
    const [attachments, setAttachments] = useState<any>([]); // 附件数据
    const [summary, setSummary] = useState<any>(null); // 摘要
    const [isGenerating, setIsGenerating] = useState<any>(false);
    const [isLoadFailed, setIsLoadFailed] = useState<any>(false);

    // 初始化
    useEffect(() => {
        getDeails()
        init()
    }, [])

    const init = () => {
        // setIsLoading(true)
        // let attachment = []
        // const types = ['pdf', 'doc', 'docx', 'pptx', 'xls', 'xlsx', 'jpeg', 'png']
        // for (let i = 1; i < 11; i++) {
        //     const fileType = types[i % 8]
        //     attachment.push(
        //         {
        //             fileId: i,
        //             fileType: fileType,
        //             fileName: '附件名称附件名称sdgsdgsdgsgsdgsdgsgsdgsgsdgsdgsgsdgsdgsdgsdgsdg.' + fileType,
        //             filePath: `/${i}.` + fileType,
        //             fileSize: i + 'KB'
        //         }
        //     )
        // }
        // setDetails({
        //     title: '2025 年艾德第三季 · 聚力同行-金科聚餐活动',
        //     // content: 'Dear&nbsp;全体同事 And&nbsp;ALL</div><div><br></div><p>关于第三季度活动聚餐费用同步如下：</p><div>&nbsp; &nbsp;&nbsp;&nbsp; 时&nbsp; &nbsp; 间：2025/08/01--18:30-21:30</div><div>&nbsp; &nbsp; &nbsp; 地&nbsp; &nbsp; 址：广东省深圳市南山区粤海街道鹏润达广场西座(后海地铁站F口）-铭门盛宴·艺术海鲜姿造（南山店）</div><p>&nbsp; &nbsp; &nbsp; 消费形式：<strong>自助餐</strong></p><p><span style="background-color: transparent">&nbsp; &nbsp; &nbsp; 参加人数</span><span style="background-color: transparent">：共32人</span></p><p><span style="background-color: transparent">&nbsp; &nbsp; &nbsp; 消费明细：249*28&nbsp;+294*4=8148</span></p><p><span style="background-color: transparent"><span style="color: rgb(255, 153, 0)">注：有28张券为249元/人，有4张券294元/人（美团购买）</span></span></p><p><span style="background-color: transparent">消费总计：共消费<strong>8148</strong>元</span></p><p><span style="background-color: transparent">&nbsp; &nbsp; &nbsp; 部门团队费用剩余：<strong>1752</strong>元</span></p><p><br></p><div>附件可附餐厅消费账单发票</div><div class="se-component se-image-container __se__float- __se__float-none"><figure><img src="/vama-avatar/629fb1a88e68c5b0c738f515fd1e7f264a050c106a3881255fdc9c6d63dea461.gif" alt="" data-align="none" data-file-name="Image.gif" data-proportion="true" data-file-size="0" data-origin="," data-size="," data-rotate="" data-percentage="auto,auto" style=""></figure></div><p>啊哈哈哈哈哈哈哈</p>',
        //     content: 'Dear&nbsp;全体同事 And&nbsp;ALL</div><div><br></div><p>关于第三季度活动聚餐费用同步如下：</p><div>&nbsp; &nbsp;&nbsp;&nbsp; 时&nbsp; &nbsp; 间：2025/08/01--18:30-21:30</div><div>&nbsp; &nbsp; &nbsp; 地&nbsp; &nbsp; 址：广东省深圳市南山区粤海街道鹏润达广场西座(后海地铁站F口）-铭门盛宴·艺术海鲜姿造（南山店）</div><p>&nbsp; &nbsp; &nbsp; 消费形式：<strong>自助餐</strong></p><p><span style="background-color: transparent">&nbsp; &nbsp; &nbsp; 参加人数</span><span style="background-color: transparent">：共32人</span></p><p><span style="background-color: transparent">&nbsp; &nbsp; &nbsp; 消费明细：249*28&nbsp;+294*4=8148</span></p><p><span style="background-color: transparent"><span style="color: rgb(255, 153, 0)">注：有28张券为249元/人，有4张券294元/人（美团购买）</span></span></p><p><span style="background-color: transparent">消费总计：共消费<strong>8148</strong>元</span></p><p><span style="background-color: transparent">&nbsp; &nbsp; &nbsp; 部门团队费用剩余：<strong>1752</strong>元</span></p><p><br></p><div>附件可附餐厅消费账单发票</div><div class="se-component se-image-container __se__float- __se__float-none"><figure>',
        //     behalfOfName: '谢佳佳',
        //     publishedTime: '2025-05-05 05:00:00',
        //     tags: [{ id: 1, name: 'vama', color: 'pink' },
        //     { id: 2, name: 'vintech', color: 'blue' },
        //     { id: 3, name: 'vintech2', color: 'gray' }],
        //     attachment
        // })
        // setTimeout(() => {
        //     setIsLoading(false)
        // }, 200)
    }

    const getDeails = async () => {
        setIsLoading(true)
        let res: any = await api_get_user_notification_details(resource?.id).catch(() => {
            setIsLoading(false)
        })
        setIsLoading(false)
        setIsLoadFailed(!res)
        if (!res) return
        // 未归档状态-通知列表页面，未读状态的公告 更新为已读状态
        if (!res?.archivedStatus) onRefreshTarget(resource?.id, { isUnread: false })
        // 转换数据为前端格式
        res.tags = res.tags || []
        let attachments = res.attachments || []
        attachments = attachments.map((file: any) => {
            file.isDownloading = false
            return file
        })
        setAttachments(attachments)
        if (res.publishedTime) {
            res.publishedTime = res.publishedTime ? parseTime(res.publishedTime, "{y}-{m}-{d} {h}:{i}") : ''
        }
        // 判断公告是否已过期
        res.isExpired = res?.validityAt === 'TIMED' && Date.now() >= res?.expireTime
        const result = setStatus(res)
        setDetails(result)
        // console.log('result', result)
    }

    const setStatus = (res: any) => {
        // 是否能 （取消）置顶操作: 未归档
        res.canPinOperate = !res?.archivedStatus
        // 是否能 （取消）归档操作: 已归档但公告未过期 or 未归档
        res.canArchiveOperate = !res?.archivedStatus || !res.isExpired
        return res
    }

    // 生成摘要
    const onGenerateSummary = async () => {
        setSummary(null)
        setIsGenerating(true)
        let res: any = await api_generate_ann_summary(resource?.id).catch(() => {
            setIsGenerating(false)
        })
        setIsGenerating(false)
        if (!res) return
        console.log('res', res)
        setSummary(res?.summary)
    }

    // 返回
    const onBackPage = async () => {
        onBack && onBack()
    }

    const getFileTypeIcon = (file: any) => {
        let name = ''
        // const [_, type] = file.fileType.split('/')
        // const fileType = file.fileName.splice
        // let fileType = file.fileName.substring(file.fileName.lastIndexOf(".") + 1);
        // fileType = fileType ? fileType.toLowerCase() :fileType
        const fileType = getFileTypeByName(file.fileName)
        switch (fileType) {
            case 'pdf':
                name = 'icon_file_pdf'
                break
            case 'doc':
            case 'docx':
                name = 'icon_file_doc'
                break
            case 'pptx':
                name = 'icon_file_ppt'
                break
                break
            case 'xlsx':
            case 'xls':
                name = 'icon_file_xls'
                break
                break
            case 'jpeg':
            case 'png':
                name = 'icon_file_image'
                break
            default:
                break
        }
        return <div className="ann-file-icon"><SvgIcon svgName={name} /></div>
    }

    const onOperate = (operate: any) => {
        // console.log('operate', operate)
        if (['Active', 'Archive'].includes(operate.key)) {
            onToggleArchived(operate.key == 'Archive')
            return
        }
        if (['Pin', 'Unpin'].includes(operate.key)) {
            onTogglePin(operate.key == 'Pin')
            return
        }
    }
    // 归档-取消归档
    const onToggleArchived = async (isArchived: boolean) => {
        //  isArchiveRequest
        const data = {
            id: resource?.id,
            archived: isArchived
        }
        const res: any = await api_update_notification_archive(data).catch(() => {
        })
        if (!res) return
        if (res.success) {
            if (isArchived && !details?.readStatus) {
                // 归档需要更新未读数 
                onRefreshTarget(resource?.id, { isUnread: false })
            }
            message.success(t('app.operate-success'))
            // getDeails()
            let result = setStatus({ ...details, archivedStatus: isArchived })
            setDetails(result)
            // 通知列表刷新
            onRefreshList()
        }

    }

    // 置顶-取消置顶
    const onTogglePin = async (isPin: boolean) => {
        const data = {
            id: resource?.id,
            pinned: isPin
        }
        const res: any = await api_update_notification_pin(data).catch(() => {
        })
        if (!res) return

        if (res.success) {
            message.success(t('app.operate-success'))
            // getDeails()
            let result = setStatus({ ...details, pinnedStatus: isPin })
            setDetails(result)
            // 通知列表刷新
            onRefreshList()
        }
    }

    // 菜单
    const renderTopMenu = () => {

        // 归档 or 取消归档
        let result: any = []
        if (details?.canPinOperate) {
            // 置顶 or 取消置顶: 已归档不显示此按钮
            const item1 = details?.pinnedStatus ? getMenuItem(t('info-hub.unpin'), "Unpin", <SvgIcon svgName="icon_unpin" />)
                : getMenuItem(t('info-hub.pin'), "Pin", <SvgIcon svgName="icon_pin" />)
            result.push(item1)
        }
        if (details?.canArchiveOperate) {
            const item2 = details?.archivedStatus ? getMenuItem(t('info-hub.activation'), "Active", <SvgIcon svgName="icon_active" />)
                : getMenuItem(t('info-hub.archive'), "Archive", <SvgIcon svgName="icon_archive" />)
            result.push(item2)
        }
        // console.log('result', details?.canArchiveOperate, result)
        const items: MenuProps['items'] = [...result]

        return <Menu onClick={(e) => onOperate(e)} style={{ minWidth: 200 }} items={items} selectedKeys={[props.globalLang]} />
    }
    // tag管理通知 刷新目标通知-标签
    const onRefreshListItemTag = (targetId: any, tags: any) => {
        // 通知列表
        props.onRefreshListItemTag(targetId, tags)
        // 更新当前页面
        const _details = { ...details }
        _details.tags = [...tags]
        setDetails(_details)

    }

    // tag管理通知 刷新 相关标签
    const onRefreshListTargetTag = (targetTag: any) => {
        // 通知列表
        props.onRefreshListTargetTag(targetTag)
        // 更新当前页面
        updateCurrentTag(targetTag)
    }

    // tag管理通知  移除相关标签
    const onDeleteListTargetTag = (targetTag: any) => {
        // 通知列表
        props.onDeleteListTargetTag(targetTag)
        // 更新当前页面
        const _details = { ...details }
        _details.tags = _details.tags.filter((i: any) => i.id != targetTag.id)
        setDetails(_details)
    }

    // tag管理通知 刷新标签选项数据
    const onRefreshTags = (tags: any = []) => {
        // console.log('onRefreshTags', tags)
        // 通知列表
        props.onRefreshTags(tags)
        // // 更新当前页面
        // const _details = { ...details }
        // _details.tags = tags.map((i:any)=>{
        //     return {
        //         id: i.id,
        //         tagName: i.name,
        //         tagColor: i.color
        //     }
        // })
        // setDetails(_details)
    }

    const updateCurrentTag = (targetTag: any) => {
        const _details = { ...details }
        _details.tags = _details.tags.map((i: any) => {
            if (i.id === targetTag.id) {
                // console.log('找到了')
                i.tagName = targetTag.name
                i.tagColor = targetTag.color
            }
            return i
        })
        // console.log('_details',_details, targetTag)
        setDetails(_details)
    }

    const setIsDownloading = (fileIndex: number, isDownloading: boolean) => {
        const _attachments = [...attachments]
        const target = _attachments[fileIndex]
        // console.log('target', target)
        if (target) {
            target.isDownloading = isDownloading
            setAttachments(_attachments)
        }
    }

    const onDownloadAttachment = async (file: any, fileIndex: number) => {
        if (file.isDownloading) return
        // console.log('请求', file.isDownloading)
        setIsDownloading(fileIndex, true)
        const res: any = await api_download_attachment(details?.id, file.id).catch(() => {
            setIsDownloading(fileIndex, false)
        })
        setIsDownloading(fileIndex, false)
        if (!res) return
        // console.log('res', res)
        downloadBlobFile(res, file.fileName, 'none')
    }

    return (
        <Flex className="ann-detail-page common-wrap space-layout2" vertical>
            {
                // isLoading ?
                // <Spin className="full-spin" size="large" /> :
                <>
                    <Flex className="wrap-nav" align="center" gap={4}>
                        <div onClick={onBackPage} className="icon-back" ><SvgIcon svgName="icon_back" /></div>
                        <div className="ann-title">{details?.title}</div>
                    </Flex>
                    <div className="ann-detail wrap-content">
                        <Flex gap={20} justify="space-between">
                            <div className="ann-operator">{details.behalfOfName}</div>
                            <Flex className="ann-time-operate" gap={14} align="center">
                                <div className="ann-operate-time">{details.publishedTime}</div>
                                {
                                    // 已归档状态 不支持操作
                                    details && (details?.canPinOperate || details?.canArchiveOperate) ?
                                        <Dropdown className="ann-operate-menu" placement="bottomRight" overlayClassName="chats-new-dropdown" dropdownRender={() => renderTopMenu()} arrow={false}>
                                            <Flex className="ann-operate-menu-trigger" align="center" justify="center">
                                                {/* <Button className="tertiary" color="default" variant="filled">
                                            <Flex gap={4} align="center">More actions<SvgIcon svgName="arrow_down"/></Flex>
                                        </Button> */}
                                                <SvgIcon svgName="icon_setting3" />
                                            </Flex>
                                        </Dropdown> : null
                                }

                            </Flex>
                        </Flex>
                        {/* 标签集合 */}
                        {
                            <Flex className="ann-tags" gap={8} align="center" wrap={true}>
                                {
                                    details.tags ?

                                        <TagManage
                                            options={details.tags}
                                            notificationId={details.id}
                                            onRefreshListItemTag={onRefreshListItemTag}
                                            onRefreshListTargetTag={onRefreshListTargetTag}
                                            onDeleteListTargetTag={onDeleteListTargetTag}
                                            onRefreshTags={onRefreshTags}
                                        >
                                            {
                                                details?.tags && details.tags.map((tag: any) => (
                                                    <Tag className={`"ann-tag" ${tag.tagColor}`} key={tag.id} bordered={false}>{tag.tagName}</Tag>
                                                ))
                                            }
                                        </TagManage>
                                        : null
                                }
                            </Flex>
                        }
                        {/* AI摘要 */}
                        {
                            isLoadFailed ? null :
                                <Flex className="ann-ai-summary" vertical gap={10}>
                                    <Flex onClick={onGenerateSummary} className="ai-summary" gap={10} align="center">
                                        <div className="title">{t('info-hub.ai-summary')}</div>
                                        <div className="ai-icon">
                                            {
                                                isGenerating ? <Spin indicator={<LoadingOutlined style={{ fontSize: 16 }} spin />} /> :
                                                    <SvgIcon svgName="ai-summary" />
                                            }
                                        </div>

                                    </Flex>
                                    {
                                        summary !== null ?
                                            <div className="summary">{summary === '' ? t('info-hub.no-summary'): summary}</div> :
                                            <div className="placehoder">{t('info-hub.ai-summary-tip')}</div>
                                    }
                                </Flex>
                        }

                        {/* 内容 */}
                        <Flex vertical gap={20}>
                            <div className="ann-content">
                                <RichTextEditor
                                    // ref={editorRef}
                                    disabled={true}
                                    readOnly={true}
                                    contents={details?.content || ''}
                                />
                            </div>
                            {/* 附件 */}
                            {
                                attachments && attachments.length > 0 ?
                                    <Flex className="ann-files" gap={'4px 20px'} align="center" wrap={true}>
                                        {
                                            details.attachments.map((file: any, fileIndex: number) => (
                                                <Flex onClick={() => file.isDownloading ? null : onDownloadAttachment(file, fileIndex)} className="ann-file-item" align="center" key={file.id} gap={4}>
                                                    {
                                                        file.isDownloading ?
                                                            <Spin indicator={<LoadingOutlined style={{ fontSize: 14 }} spin />} />
                                                            : getFileTypeIcon(file)
                                                    }
                                                    <div className="ann-file-name">{file.fileName}</div>
                                                    <div className="ann-file-size">({formatFileSize(file.fileSize)})</div>
                                                </Flex>
                                            ))
                                        }
                                    </Flex>
                                    : null
                            }
                        </Flex>
                    </div>
                </>

            }
        </Flex>
    )
}


const mapStateToProps = (state: any) => ({
    companies: state.app.companies,
    activeCompanyId: state.app.activeCompanyId
})
const mapDispatchToProps = (dispatch: any) => ({
    // dispatch_api_: (data:any) => dispatch(dispatch_api_(data))
})
export default connect(mapStateToProps, mapDispatchToProps)(AnnouncementDetail);