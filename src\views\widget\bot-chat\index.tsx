import React, { useEffect, useRef, useState } from "react";
import { connect } from "react-redux";
import { useTranslation } from 'react-i18next';
import { Flex, Input, Tooltip } from "antd";

import SvgIcon from "@/components/svgicon"
import classNames from "classnames";
import './index.scss'
import defaultLogo from "@/assets/bot_default_avatar.png";
import BotChatInput from './input'
import { $isNull } from "@/utils/common";
import Markdown from "@/components/markdown";
import { LoadingOutlined, UndoOutlined, CheckCircleFilled } from '@ant-design/icons';
import CopyToClipboard from 'copy-to-clipboard';


// 机器人聊天小插件
const BotChat = (props: any) => {
    const { t } = useTranslation()

    const botChatInputRef = useRef<any>(null)
    const charCon = useRef<any>(null)

    const [robotInfo, setRobotInfo] = useState<any>({
        name: 'Grammar-Checker-Pro',
        introduction: "Ask the research, chat directly with the world's scientific literature. Search references, get simple explanations, write articles backed by academic papers.",
        robotId: 1,
        appearance: 'light',
        icon: defaultLogo
    }) // 机器人信息
    const [isOpen, setIsOpen] = useState(true) // 是否展开
    const [copyFinished, setCopyFinished] = useState(false) // 
    const [copyFinishedTimer, setCopyFinishedTimer] = useState<any>(null) // 
    const [chatList, setChatList] = useState<any>([]); // 会话
    // let copyFinishedTimer:any = null

    useEffect(() => {
        return () => {
            clearTimeout(copyFinishedTimer)
        }
    }, [])

    // 发送
    const onSend = async (sendString: string) => {
        console.log('sendString', sendString)
        const list = [...chatList]
        list.push(
            {
                messageId: Date.now(),
                seq: Date.now(),
                content: sendString,
                role: "user",
                isLoading: true
            },
            {
                messageId: Date.now() + 1,
                seq: Date.now() + 1,
                content: "我是回答我是回答我是回答我是回答我是回答我是回答我是回答我是回答...",
                role: "bot",
                isLoading: true
            },
        )
        console.log('list', list)
        setChatList(list)
        // 清空输入框值
        botChatInputRef?.current?.emit_clear_input_value()
        // 滚动条滚动到底部
        scrollToBottom()
        return true
    }

    // 复制回答
    const onCopyAnswer = (assistant: any) => {
        CopyToClipboard(assistant);
        setCopyFinished(true)
        if (copyFinishedTimer) clearTimeout(copyFinishedTimer)
        const timer = setTimeout(() => {
            setCopyFinished(false)
        }, 2000)
        setCopyFinishedTimer(timer)
    }

    // 删除对话
    const onDelteChat = (item: any, index: number) => {
        const list = [...chatList]
        const prev = index - 1
        if (prev != -1) {
            if (index) list.splice(prev, 2)
            setChatList(list)
        }

    }
    // 问答时保持滚动条位置
    const scrollToBottom = () => {
        try {
            setTimeout(() => {
                if (charCon?.current) {
                    let scrollTop = charCon.current.scrollHeight
                    console.log('scrollTop', scrollTop)
                    charCon.current.scrollTop = scrollTop
                }
            }, 10)

        } catch (error) {
            console.log(error)
        }
    }

    // const onToggle = () => {
    //     setIsOpen(!isOpen)
    //     // window.parent.postMessage({ data: { name: 'WindowOpenStatus', value: !isOpen } }, '*')
    // }

    // // 最小化
    // const onMinimize = () => {
    //     setIsOpen(false)
    //     window.parent.postMessage({ data: { name: 'WindowOpenStatus', value: false } }, '*')
    // }

    return (
        <Flex vertical justify="flex-end" className={`widget-bot-con ${robotInfo?.appearance}`}>
            {/* <div style={{width:'500px',height:'700px'}}>
                <iframe width="100%" height="100%" allow="microphone *" src="https://www.gptbots.ai/widget/eembtledigcccgflsbuti3i/chat.html"></iframe>
            </div> */}
            {
                isOpen ?
                    <Flex className="is-open" vertical>
                        {/* 展开 */}
                        <Flex className="nav" gap={12}>
                            <img className="bot-icon" src={defaultLogo} alt="" />
                            <div className="bot-name">{robotInfo.name}</div>
                            {/* <div className="zoom-out-icon" onClick={onMinimize}>
                                <SvgIcon svgName="icon_minus" />
                            </div> */}
                        </Flex>
                        {/* 对话 */}
                        <Flex ref={charCon} vertical gap={12} className="chat-list">
                            {
                                chatList && chatList.length > 0 ?
                                    chatList.map((item: any, index: number) => (
                                        <Flex className="chat-list-item" vertical key={item.seq}>
                                            {
                                                item.role === 'user' ?
                                                    <div className="chat-question">{item.content}</div> : null
                                            }
                                            {
                                                item.role === 'bot' ?
                                                    <div className="chat-answer">
                                                        <Markdown key={index}
                                                            content={item.content}
                                                        />
                                                        <Flex className="chat-operation">
                                                            {
                                                                item.messageId ?
                                                                    <Tooltip placement="top" title={t('app.delete')} arrow={false}>
                                                                        <span onClick={() => onDelteChat(item, index)}>
                                                                            <SvgIcon svgName="icon_delete" />
                                                                        </span>
                                                                    </Tooltip> : null
                                                            }
                                                            {
                                                                !$isNull(item.content) ? <>
                                                                    { // 图片 类型不显示复制按钮
                                                                        item.isImage ? null :
                                                                            <Tooltip placement="top" rootClassName="copy-tooltip"
                                                                                title={copyFinished ? (
                                                                                    <div className="copyed"><CheckCircleFilled />{t("copied")}</div>) : t('app.copy')} arrow={false}>
                                                                                <span onClick={() => onCopyAnswer(item.content)}>
                                                                                    <SvgIcon svgName="copy" /></span>
                                                                            </Tooltip>
                                                                    }
                                                                </> : null
                                                            }
                                                        </Flex>
                                                    </div> : null
                                            }
                                        </Flex>
                                    ))
                                    :
                                    <Flex gap={12} vertical justify="center" className="preview-text">
                                        <div className="preview-icon">
                                            <img src={robotInfo.icon} alt="" />
                                        </div>
                                        <div className="preview-title">{robotInfo.name}</div>
                                        {
                                            $isNull(robotInfo.introduction) ? null :
                                                <div className="preview-desc">
                                                    {robotInfo.introduction}
                                                </div>
                                        }
                                    </Flex>
                            }
                        </Flex>
                        {/* 对话输入框 */}
                        < Flex className="chat-input-area" >
                            {/* <TextArea 
                                placeholder={t('robot-iframe.talk-to', { robot: robotInfo.name }) as string}
                                variant="borderless"
                            /> */}
                            < BotChatInput
                                ref={botChatInputRef}
                                onSend={onSend}
                                robotName={robotInfo.name}
                            />
                        </Flex>
                    </Flex> :
                    null
            }
            {/* 展开收缩操作 */}
            {/* <Flex className="toggle-area" justify="flex-end" align="center">
                <Flex onClick={onToggle} justify="center" align="center">
                    <SvgIcon className="zoom-out-icon" svgName={isOpen ? "arrow_down_line" : "icon_ai_chat"} />
                </Flex>
            </Flex> */}

        </Flex>
    )
}
const mapStateToProps = (state: any) => ({
    userInfo: state.app.userInfo,
    activeCompanyId: state.app.activeCompanyId,
    activeCompanyName: state.app.activeCompanyName,
});

const mapDispatchToProps = (dispatch: (e: any) => void) => ({
    // dispatch_api_delete_robot: (data = api_delete_robot_params) => dispatch(dispatch_api_delete_robot(data)),
});

export default connect(mapStateToProps, mapDispatchToProps)(BotChat);