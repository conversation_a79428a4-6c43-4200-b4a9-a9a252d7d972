import React, { useEffect, useState } from "react";
import { Menu, Dropdown, Space, Spin,Skeleton} from 'antd';
import type { MenuProps } from 'antd';
import {LoadingOutlined} from '@ant-design/icons';
import { connect } from "react-redux";
import i18next from "i18next";
import { getMenuItem } from "@/components/base";
import { change_app_globalLang } from "@/store/action";
import Layout from './layout'
import PrivateLogin from './panel/privateLogin'
import PublicLogin from './panel/publicLogin'
import {dispatch_api_get_configurations} from '@/api/platform'
import { useOpenIM } from "@/contexts/OpenIMContext";
import { refreshTheme } from "@/theme";

export const renderConcatMe =()=> {
  const items: MenuProps['items'] = [
    getMenuItem(`${i18next.t(["app.email"])}: <EMAIL>`, "en", null),
  ];
  return (
    <Menu style={{ minWidth: 160 }} items={items} />
  )
}

const Login=(props:any)=> {
  const { isPrivate } = props;
  const [isLoading,setIsLoading] =useState(true)
  const [hideConTact,setHideConTact] =useState(false)
  const { service:ImService,isConnected:isConnectedIm } = useOpenIM()

  const onUpdateConTact=(visible:boolean)=>{
    setHideConTact(visible)
  }
  
  const getConfig = async()=>{
    setIsLoading(true)
    const res = await props.dispatch_api_get_configurations().catch((err:any)=>{
      console.log('err')
      setIsLoading(false)
    })
    setIsLoading(false)
    if(!res) return
    // console.log('res',res)
  }

  useEffect(()=>{
    getLoginThemeConfig()
    getConfig()
  },[])

  useEffect(()=>{
    if (ImService&&isConnectedIm){
      // console.log('IM需要退出登录')
      ImService.logout()
    }
  },[ImService,isConnectedIm])

  // 获取登录主题配置
  const getLoginThemeConfig = async () => {
    refreshTheme()
}

  return(
    <Layout>
      <>
        {
          !isLoading?<>
            { // 私有化登录
            isPrivate?<PrivateLogin  onUpdateConTact={onUpdateConTact}/>:null
            }
            { // Sass登录
              !isPrivate?<PublicLogin />:null
            }
          </>:
          <Spin className="modal-spin" size="large" indicator={<LoadingOutlined style={{fontSize: 24}} spin/>}/>
        }
        

        {isPrivate && !hideConTact?
          <Space className={"concat-me"} align="center">
            <div className={"unable-login"}> {i18next.t(["login.unable-login"])}</div>
            <Dropdown className={"concat-link"} dropdownRender={() => renderConcatMe()} placement="topRight" trigger={["click", "hover"]} arrow>
              <span className="link">{i18next.t(["login.sign-about"])}</span>
            </Dropdown>
        </Space>:null
        }
      </>
    </Layout>
  )
}

const mapStateToProps = (state: any) => ({
  globalLang: state.app.globalLang,
  globalRegion: state.app.globalRegion,
  themeInfo: state.app.themeInfo,
  isPrivate: state.app.isPrivate,
})

const mapDispatchToProps = (dispatch: any) => ({
  change_app_globalLang: (globalLang: string) => dispatch(change_app_globalLang(globalLang)),
  dispatch_api_get_configurations: () => dispatch(dispatch_api_get_configurations()),
})

export default connect(mapStateToProps, mapDispatchToProps)(Login);
// export default connect(mapStateToProps, mapDispatchToProps)(withTranslation()(Login));
