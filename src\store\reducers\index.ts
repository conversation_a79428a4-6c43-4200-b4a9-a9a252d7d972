import * as types from "../constant";
import i18next from "i18next";
import { combineReducers } from "redux";
import { theme as themeDefault, colorPrimary, colorPrimarySecondary, colorPrimaryContrasting } from '@/theme/config'

export interface ThemeInfoFace {
  theme: string,
  brandColor: string, // 品牌主色
  colorPrimary: string, // 主色：基于品牌色 和 外观模式（亮色或暗色） 生成的主色，亮色下 与 品牌主色相同，暗色可能不同
  colorPrimarySecondary: string // 主色辅助色 
  colorPrimaryContrasting: string, // 主色對比色
  // 主色派生色
  colorPrimaryBg?: String, // 主色淺色背景色 colorPrimaryBg
  colorPrimaryBgHover?: String, //主色淺色背景懸浮態 colorPrimaryBgHover
  colorPrimaryHover?: String, //主色懸浮態 colorPrimaryHover
  colorPrimaryActive?: String,  // 主色激活態 colorPrimary 
  colorPrimaryBorder?: String,  // 主色描邊色 colorPrimaryBorder
  colorPrimaryBorderHover?: String,  // 主色描邊色懸浮態 colorPrimaryBorderHover
  colorPrimaryTextHover?: String,  // 主色文本懸浮態 colorPrimaryTextHover
  colorPrimaryText?: String,// 主色文本色 colorPrimaryText
  colorPrimaryTextActive?: String,// 主色文本激活態 colorPrimaryTextActive
}

export interface StateValidator {
  isLogin: boolean,
  globalLang: WindowLocalStorage | string,
  userInfo: any,
  userProfile: any,
  userProfilePersonal: any,
  globalRegion: string,
  defaultThemeInfo: any, // 默认配置
  themeInfo: ThemeInfoFace,
  isPrivate: any, // 是否是私有化部署，true 私有化部署，false sass部署
  ipAreaCode: any, // IP 对应地区码名称
  companies: any, // 当前用户加入的 企业列表数据
  isRefreshCompanies: boolean, //是否需要刷新企业列表
  activeCompanyId: any,// 当前选中的 企业ID
  activeCompanyName: any,// 当前选中的 企业名称
  isActiveCompanyAdmin: boolean // 登录用户是否是当前公司的管理员
  isRefreshAnnNotify: boolean // 公告未读数 是否要更新
}

const themeInfoDefault = {
  theme: themeDefault,
  brandColor: colorPrimary, // 品牌主色
  colorPrimary,
  colorPrimarySecondary,
  colorPrimaryContrasting
}

const isLogin = localStorage.getItem("IS_AMAUSER_LOGIN") ? JSON.parse(localStorage.getItem("IS_AMAUSER_LOGIN") as string) : false;
const userInfo = localStorage.getItem("AMA_USER_INFO") ? JSON.parse(localStorage.getItem("AMA_USER_INFO") as string) : {};
// const userProfile = localStorage.getItem("AMA_USR_POF") ? JSON.parse(localStorage.getItem("AMA_USR_POF") as string) : {};
const activeCompanyId = localStorage.getItem("ACTCID")
const activeCompanyName = localStorage.getItem("ACTCID_NM")
const isPrivate = localStorage.getItem("A_PRT") === '1'
// console.log('isPrivate',isPrivate)

const themeInfo = { ...themeInfoDefault };
const defaultThemeInfo = localStorage.getItem("ATDEFAULT") ? JSON.parse(localStorage.getItem("ATDEFAULT") as string) : { ...themeInfoDefault };

const initState: StateValidator = {
  isLogin,
  userInfo,
  userProfile: {}, // userProfile, //用户个人资料：用户名+头像
  userProfilePersonal: {}, // userProfile, //用户 个人空间 个人资料：用户名+头像
  globalLang: localStorage.getItem("i18nextLng") || "en",
  globalRegion: "",
  defaultThemeInfo,
  themeInfo, // 主题色信息
  // globalConfig: {isSaaS: false},
  isPrivate: isPrivate,
  ipAreaCode: null,
  companies: null,
  isRefreshCompanies: false,
  activeCompanyId: activeCompanyId || null,
  activeCompanyName: activeCompanyName || null,
  isActiveCompanyAdmin: false,
  isRefreshAnnNotify: false
};

function app(state = initState, action: any) {
  switch (action.type) {
    // 更改userInfo
    case types.CHANGE_APP_USERINFO:
      if (action.isLogin && Object.keys(action.userInfo).length) {
        localStorage.setItem("AMA_USER_INFO", JSON.stringify(action.userInfo));
        localStorage.setItem("AMA_USER_TOKEN", action.token);
      } else {
        localStorage.removeItem("AMA_USER_INFO");
        localStorage.removeItem("AMA_USER_TOKEN");
        localStorage.removeItem("AMA_USR_POF");
        localStorage.removeItem("AMA_USR_POF_PS");
      }

      localStorage.setItem("IS_AMAUSER_LOGIN", JSON.stringify(action.isLogin));
      return { ...state, isLogin: action.isLogin, userInfo: action.userInfo };
    // 更改多语言
    case types.CHANGE_APP_GLOBALANG:
      i18next.changeLanguage(action.globalLang);
      localStorage.setItem("i18nextLng", action.globalLang);
      return { ...state, globalLang: action.globalLang };
    // 更改globalRegion
    case types.CHANGE_APP_GLOBALREGION:
      return { ...state, globalRegion: action.globalRegion };
    // 更改系统主题配置
    case types.CHANGE_APP_THEMEINFO:
      const newThemeInfo = { ...state.themeInfo, ...action.themeInfo }
      // console.log('更改系统主题配置 ----newThemeInfo',newThemeInfo)
      // localStorage.setItem("AMA_THEME_INFO", JSON.stringify(newThemeInfo)); // 缓存  
      return { ...state, themeInfo: newThemeInfo }
    // 更改系统默认主题配置
    case types.CHANGE_APP_DEFALUT_THEMEINFO:
      const newThemeInfo2 = { ...state.defaultThemeInfo, ...action.themeInfo }
      localStorage.setItem('ATDEFAULT', JSON.stringify(newThemeInfo2))
      return { ...state, defaultThemeInfo: newThemeInfo2 }
    // 更改系统全局配置
    case types.CHANGE_APP_CONFIG:
      // const config = {...state.globalConfig, ...action.config}
      const config = { ...action.config }
      const _isp = !config.isSaaS
      // console.log('config',config)
      localStorage.setItem("A_PRT", _isp ? '1' : '0')
      return { ...state, isPrivate: _isp } // , globalConfig: config
    case types.CHANGE_APP_IPAREA:
      return { ...state, ipAreaCode: action.code || 'US' }
    // 获取 企业 列表
    case types.GET_APP_COMPANIES:
      return { ...state, companies: action.list };
    // 是否刷新 企业 列表
    case types.REFRESH_APP_COMPANIES:
      // console.log('action.isRefresh',action.isRefresh)
      return { ...state, isRefreshCompanies: action.isRefresh };
    // 更新当前选中企业
    case types.CHANGE_APP_ACTIVE_COMPANY:
      // console.log('action.info', action.info)
      const id = action.info.id
      const name = action.info.name
      localStorage.setItem("ACTCID", id)
      localStorage.setItem("ACTCID_NM", name)
      return { ...state, activeCompanyId: id, activeCompanyName: name };
    // 更新 登录账号 在 当前企业 中的 个人资料
    case types.CHANGE_APP_USERPROFILE:
      // console.log('action.info',action.info)
      localStorage.setItem("AMA_USR_POF", JSON.stringify(action.info));
      return { ...state, userProfile: { ...action.info } };
    // 更新 登录账号 在 个人空间 中的 个人资料
    case types.CHANGE_APP_PERSONAL_USERPROFILE:
      // console.log('action.info',action.info)
      localStorage.setItem("AMA_USR_POF_PS", JSON.stringify(action.info));
      return { ...state, userProfilePersonal: { ...action.info } };
    case types.REMOVE_APP_ACTIVE_COMPANY:
      localStorage.removeItem("ACTCID");
      localStorage.removeItem("ACTCID_NM");
      return { ...state, activeCompanyId: '', activeCompanyName: '' };
    // 更新 登录用户是否是当前公司的管理员
    case types.CHANGE_APP_IS_ACTIVE_COMPANY_ADMIN:
      // console.log('---action.isAdmin',action.isAdmin)
      return { ...state, isActiveCompanyAdmin: action.isAdmin };
    // 是否 更新 公告未读数
    case types.REFRESH_APP_ANN_UNREAD:
      // console.log('action.isRefresh',action.isRefresh)
      return { ...state, isRefreshAnnNotify: action.isRefresh };
    default:
      return state;
  }
}

export default combineReducers({ app });
