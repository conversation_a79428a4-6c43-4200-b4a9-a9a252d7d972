import * as types from "../constant";
import { ThemeInfoFace } from "../reducers";

export const change_app_globalLang = (globalLang: string) => ({
  type: types.CHANGE_APP_GLOBALANG,
  globalLang
});

export const change_app_userInfo = ({ isLogin, userInfo, token } : { isLogin: boolean, userInfo: any, token?: null|string }) => ({
  type: types.CHANGE_APP_USERINFO,
  isLogin,
  token,
  userInfo
});


export const change_app_global_region = (globalRegion: string) => ({
  type: types.CHANGE_APP_GLOBALREGION,
  globalRegion
});

export const change_app_themeInfo = (themeInfo: object ) => ({
  type: types.CHANGE_APP_THEMEINFO,
  themeInfo
});

export const change_app_default_themeInfo = (themeInfo: object ) => ({
  type: types.CHANGE_APP_DEFALUT_THEMEINFO,
  themeInfo
});


// 全局配置
export const change_app_config = (config: object ) => ({
  type: types.CHANGE_APP_CONFIG,
  config
});

// IP地区码
export const change_app_ip_areacode = (code: any ) => ({
  type: types.CHANGE_APP_IPAREA,
  code
});

// 更新企业列表
export const change_app_companies = (list: object[]) => ({
  type: types.GET_APP_COMPANIES,
  list
});

// 更新 是否刷新企业列表 
export const change_app_is_refresh_companies = (isRefresh: boolean) => ({
  type: types.REFRESH_APP_COMPANIES,
  isRefresh
});

// 更新选中企业
export const change_app_active_company = (info:object) => ({
  type: types.CHANGE_APP_ACTIVE_COMPANY,
  info
});

// 更新登录账号 非个人空间个人资料
export const change_app_user_profile = (info: any) => ({
  type: types.CHANGE_APP_USERPROFILE,
  info
});

// 更新登录账号 个人空间 资料
export const change_app_personal_user_profile = (info: any) => ({
  type: types.CHANGE_APP_PERSONAL_USERPROFILE,
  info
});

export const remove_app_active_company = () => ({
  type: types.REMOVE_APP_ACTIVE_COMPANY,
});

export const change_is_active_company_admin = (isAdmin:boolean) => ({
  type: types.CHANGE_APP_IS_ACTIVE_COMPANY_ADMIN,
  isAdmin
});

// 更新 公告未读数
export const change_app_is_refresh_ann_unread = (isRefresh: boolean) => ({
  type: types.REFRESH_APP_ANN_UNREAD,
  isRefresh
});