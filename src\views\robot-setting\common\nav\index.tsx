import React, { useState } from "react";
import { connect } from "react-redux";
import i18next from "i18next";
import classNames from "classnames";
import Svgicon from "@/components/svgicon"
import { Flex } from "antd";

import './index.scss'

const Nav = ((props: any) => {

    const { themeInfo, children } = props;

    // 返回
    const handleBack = (item: any) => {
        props?.onBack(item.key)
    }


    return (
        <Flex className="page-detail-nav" align={"center"} gap={10}>
            <Svgicon className="reback" svgName="reback"  onClick={handleBack}/>
            <img src={props.icon} alt="" />
            <Flex className="name-id" vertical gap={6} justify="center">
                <div>{props.robotName}</div>
                <div>{i18next.t('robot-manage.create-robot.robot-id')}：{props.robotId}</div>
            </Flex>
            {props.buttons ?
                <Flex className="btns" gap={10}>{props.buttons}</Flex>
            :null}
            {/* {children} */}
        </Flex>
    )
})

const mapStateToProps = (state: any) => ({
    userInfo: state.app.userInfo,
    themeInfo: state.app.themeInfo
});

const mapDispatchToProps = (dispatch: (e: any) => void) => ({
    //   dispatch_api_query_robot_list: (data = api_query_robot_list_params) => dispatch(dispatch_api_query_robot_list(data)),
});

export default connect(mapStateToProps, mapDispatchToProps)(Nav);
