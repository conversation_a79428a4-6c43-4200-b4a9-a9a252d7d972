/*
* 重定义antd组件 基础样式，配合主题切换
*/
@import "@/styles/mixin.scss";
.ant-app {
    height: 100%;
}
// tooltip
.ant-tooltip,
.ant-tooltip .ant-tooltip-inner {
    background: var(--colorBgElevated);
    color: var(--colorText);
    border-radius: 10px;
}
.ant-tooltip .ant-tooltip-arrow:before {
    background: var(--colorBgElevated);
}
.ant-tooltip .ant-tooltip-inner {
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    line-height: 20px;
    padding-top: 8px;
    padding-bottom: 8px;
}
.ant-tooltip.dark {
    &,
    .ant-tooltip-inner {
        border-radius: 8px;
        background: var(--colorBgSpotlight);
        box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.1);
        color: var(--colorTextLightSolid);
    }
    .ant-tooltip-inner {
        padding: 6px 8px;
    }
    .ant-tooltip-arrow:before {
        background: var(--colorBgSpotlight);
    }
}

// button 默认尺寸medium(middle)
@mixin filled-default-style {
    color: var(--colorPrimary);
    background: var(--colorPrimaryBg);
}
.ant-btn {
    box-shadow: none;
    font-weight: 500;
    border-radius: 8px !important;
    gap: 4px;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 22px;
    height: 34px;
    .ant-btn-icon {
        font-size: 16px;
    }
    &:not(:disabled):focus-visible {
        outline: none !important;
    }
    // 尺寸
    &.ant-btn-sm {
        padding: 4px 12px;
        height: 30px;
        @include font(12px, 18px, 500);
    }
    &.ant-btn-lg {
        padding: 8px 14px;
        height: 38px;
    }
    &.ant-btn-variant-solid,
    &.ant-btn-variant-filled {
        border: 0;
    }

    // primary solid
    @mixin solid-default-style {
        color: var(--colorPrimaryContrasting);
        background: var(--colorPrimary);
    }
    &.ant-btn-color-primary.ant-btn-variant-solid {
        @include solid-default-style;
        &:hover,
        &:not(:disabled):not(.ant-btn-disabled):hover {
            @include solid-default-style;
            background: var(--colorPrimaryHover);
        }
        &:disabled {
            background: var(--colorFillQuaternary);
            color: var(--colorTextQuaternary);
        }
    }
    // default(Secondary) filled
    // @mixin filled-default-style {
    //     color: var(--colorPrimary);
    //     background: var(--colorPrimaryBg);
    // }
    &.ant-btn-color-default.ant-btn-variant-filled {
        @include filled-default-style;
        &:hover,
        &:not(:disabled):not(.ant-btn-disabled):hover {
            background: var(--colorPrimaryBgHover);
        }
        &:disabled {
            background: var(--colorFillQuaternary);
            color: var(--colorTextQuaternary);
        }
    }
    // Tertiary filled
    @mixin filled-tertiary-style {
        color: var(--colorText);
        background: var(--colorFillSecondary);
    }
    &.ant-btn-color-default.ant-btn-variant-filled.tertiary {
        @include filled-tertiary-style;
        &:hover,
        &:not(:disabled):not(.ant-btn-disabled):hover {
            background: var(--colorFill);
        }
        &:disabled {
            background: var(--colorFillQuaternary);
            color: var(--colorTextQuaternary);
        }
    }

    // Quaternary filled
    @mixin filled-quaternary-style {
        color: var(--colorText);
        background: transparent;
    }
    &.ant-btn-color-default.ant-btn-variant-filled.quaternary {
        @include filled-quaternary-style;
        &:hover,
        &:not(:disabled):not(.ant-btn-disabled):hover {
            background: var(--colorFillTertiary);
        }
        &:disabled {
            background: transparent;
            color: var(--colorTextQuaternary);
        }
        &.primary {
            color: var(--colorPrimary);
        }
    }

    // Danger
    // Danger Primary solid
    @mixin solid-danger-primary-style {
        color: var(--colorTextLightSolid);
        background: var(--colorError);
    }
    &.ant-btn-color-dangerous.ant-btn-variant-solid {
        @include solid-danger-primary-style;
        &:hover,
        &:not(:disabled):not(.ant-btn-disabled):hover {
            background: var(--colorErrorHover);
        }
        &:disabled {
            background: var(--colorFillQuaternary);
            color: var(--colorTextQuaternary);
        }
    }

    // Danger Secondary filled
    @mixin filled-danger-secondary-style {
        color: var(--colorError);
        background: var(--colorErrorBg);
    }
    &.ant-btn-color-dangerous.ant-btn-variant-filled {
        @include filled-danger-secondary-style;
        &:hover,
        &:not(:disabled):not(.ant-btn-disabled):hover {
            background: var(--colorErrorBgHover);
        }
        &:disabled {
            background: var(--colorFillQuaternary);
            color: var(--colorTextQuaternary);
        }
    }
}

// cascader
.ant-cascader {
    &.ant-select-open .ant-select-selection-item {
        color: var(--colorText);
    }
}
.ant-cascader-dropdown {
    background: var(--colorBgElevated);
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    .ant-cascader-menu {
        padding: 8px;
    }
    .ant-cascader-menu:not(:last-child) {
        border-inline-end: 1px solid var(--colorBorderSecondary);
    }
    .ant-cascader-menu-item {
        font-weight: 500;
        font-size: 14px;
        line-height: 20px;
    }
    .ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled),
    .ant-cascader-menu-item-active:not(.ant-cascader-menu-item-disabled):hover,
    .ant-cascader-menu-item:not(.ant-cascader-menu-item-disabled):hover {
        background: var(--colorFillTertiary);
        color: var(--colorText);
        border-radius: 12px;
    }
    .ant-cascader-menu-item-disabled {
        color: var(--colorTextTertiary);
    }
    .ant-cascader-menu-item.ant-cascader-menu-item-expand .ant-cascader-menu-item-expand-icon {
        font-size: 16px;
    }
}
.pointer-cascader-dropdown.ant-cascader-dropdown .ant-cascader-menu-item-disabled {
    cursor: pointer;
}

$modalBorderRadius: 12px;
$footerButtonspace: 12px;
// 确认弹窗
.common-confirm-modal {
    .ant-modal {
        .ant-modal-content {
            padding: 16px;
            border-radius: $modalBorderRadius;
            box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
            background: var(--colorBgElevated);
        }
        .ant-modal-header {
            background: transparent;
            margin-bottom: 12px;
        }
        .ant-modal-title {
            font-weight: 600;
            font-size: 16px;
            color: var(--colorText);
            line-height: 24px;
        }

        .ant-modal-body {
            font-weight: 400;
            font-size: 14px;
            color: var(--colorTextSecondary);
            line-height: 22px;
            margin: 0;
        }

        .ant-tabs-nav .ant-tabs-ink-bar {
            height: 4px;
            border-radius: 2px;
        }

        .ant-btn-default {
            @include filled-default-style;
            &:hover,
            &:not(:disabled):not(.ant-btn-disabled):hover {
                background: var(--colorPrimaryBgHover);
            }
            &:disabled {
                background: var(--colorFillQuaternary);
                color: var(--colorTextQuaternary);
            }
        }

        .ant-btn {
            box-shadow: none;
            border: 0;
        }

        .ant-btn + .ant-btn {
            margin-inline-start: 0;
        }

        .ant-modal-footer,
        .ant-modal-confirm-btns {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            gap: $footerButtonspace;
        }
        .ant-modal-confirm-paragraph {
            max-width: 100%;
        }
    }

    .ant-tabs-nav .ant-tabs-ink-bar {
        height: 4px;
        border-radius: 2px;
    }

    .ant-tabs-tab {
        padding-bottom: 8px;
    }

    .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
        color: var(--colorText);
        font-weight: 600;
    }

    .ant-tabs-tab-btn {
        font-weight: 400;
        font-size: 15px;
        line-height: 22px;
    }
}
// 功能弹窗
.ant-modal-wrap:not(.common-confirm-modal) {
    .ant-modal.no-padding {
        .ant-modal-content {
            padding: 0;
        }
        .ant-modal-header {
            padding: 16px 16px 0;
            border-radius: $modalBorderRadius;
        }
        .ant-modal-body {
            padding: 0 20px;
            border-radius: 0 0 $modalBorderRadius $modalBorderRadius;
            overflow: hidden;
        }
        .ant-modal-footer {
            margin-top: 0;
            padding: 12px 20px;
            border-radius: 0 0 $modalBorderRadius $modalBorderRadius;
        }
    }
    .ant-modal.border {
        .ant-modal-header {
            padding-bottom: 12px;
            border-bottom: 1px solid var(--colorBorderSecondary);
            border-bottom-right-radius: 0;
            border-bottom-left-radius: 0;
        }
    }
    .ant-modal {
        .ant-modal-content {
            padding: 16px;
            border-radius: $modalBorderRadius;
        }
        .ant-modal-header {
            .ant-modal-title {
                font-size: 20px;
                color: var(--colorText);
                line-height: 28px;
                font-weight: 600;
            }
            margin-bottom: 0;
        }
        .ant-modal-footer {
            > .ant-btn + .ant-btn {
                margin-inline-start: $footerButtonspace;
            }
        }
        .ant-modal-close {
            top: 20px !important;
            &:hover {
                background: var(--colorFillQuaternary);
                border-radius: 12px;
            }
        }

        // 确认提醒
        &.ant-modal-confirm {
            &.no-icon {
                .ant-modal-confirm-title {
                    padding-left: 0;
                }
            }
            // 不同尺寸
            &.mediumn,
            &.large {
                .ant-modal-content {
                    padding: 16px 0;
                }
                .ant-modal-confirm-title {
                    padding: 0 16px 12px;
                    border-bottom: 1px solid var(--colorBorderSecondary);
                    border-bottom-right-radius: 0;
                    border-bottom-left-radius: 0;
                }
                .ant-modal-confirm-content,
                .ant-modal-confirm-btns {
                    padding: 0 16px;
                }
                .ant-modal-confirm-paragraph {
                    row-gap: 16px;
                    max-width: 100%;
                }
            }
            &.mediumn {
                .ant-modal-content {
                    padding-top: 12px;
                }
                .ant-modal-close {
                    top: 14px !important;
                }
                .ant-modal-confirm-title {
                    @include font(18px, 24px, 600);
                }
            }
            &.large {
                .ant-modal-confirm-btns {
                    padding-top: 12px;
                    margin-top: 16px;
                    border-top: 1px solid var(--colorBorderSecondary);
                }
                .ant-modal-close {
                    top: 20px !important;
                }
                .ant-modal-confirm-title {
                    @include font(20px, 28px, 600);
                }
            }
            // 默认尺寸
            .ant-modal-content {
                padding: 16px;
                border-radius: $modalBorderRadius;
            }
            .modal-icon,
            .anticon {
                font-size: 20px;
                width: 20px;
                height: 20px;
                position: absolute;
                top: 18px;
                left: 16px;
            }
            .ant-modal-confirm-paragraph {
                row-gap: 12px;
            }
            .ant-modal-confirm-title {
                padding-left: 28px;
            }
            .ant-modal-confirm-content {
                @include font(14px, 22px, 400);
            }
            .ant-modal-close {
                top: 18px !important;
            }
            .ant-modal-confirm-btns {
                .ant-btn {
                    box-shadow: none;
                    border: 0;
                }
                .ant-btn + .ant-btn {
                    margin-inline-start: 12px;
                }
                .ant-btn-default {
                    @include filled-default-style;
                    &:hover,
                    &:not(:disabled):not(.ant-btn-disabled):hover {
                        background: var(--colorPrimaryBgHover);
                    }
                    &:disabled {
                        background: var(--colorFillQuaternary);
                        color: var(--colorTextQuaternary);
                    }
                }
            }
        }
    }
}
.ant-modal-wrap {
    @include webkitScrollStyle($thumbColor: var(--colorFill));
    .ant-modal-close {
        color: var(--colorText);
        right: 16px;
        top: 20px !important;
        width: auto;
        height: auto;
        &:hover {
            background: var(--colorFillQuaternary);
            border-radius: 12px;
        }
        .ant-modal-close-x {
            font-size: 20px;
        }
    }
    // 上传图片裁剪弹窗

    &.img-crop-modal {
        .ant-modal-body {
            padding-top: 20px;
        }
    }
}

// notification
.ant-notification .ant-notification-notice-wrapper {
    border-radius: 12px;
    background: var(--colorBgElevated);
    box-shadow: none;
    @mixin customIcon($url) {
        svg {
            display: none;
        }
        background: url($url) no-repeat center;
        background-size: 100%;
        display: block;
        width: 16px;
        height: 16px;
    }
    // 自定义图标
    .ant-notification-notice-error .ant-notification-notice-with-icon > .anticon {
        @include customIcon("../../assets/error.png");
    }
    .ant-notification-notice-info .ant-notification-notice-with-icon > .anticon {
        @include customIcon("../../assets/info.png");
    }
    .ant-notification-notice-success .ant-notification-notice-with-icon > .anticon {
        @include customIcon("../../assets/success.png");
    }
    .ant-notification-notice-warning .ant-notification-notice-with-icon > .anticon {
        @include customIcon("../../assets/warning.png");
    }
}
.ant-notification-notice-wrapper .common-notification-error {
    // width: 470px !important;
    $--colorError: #f53f3f; // 跟var(--colorError) 保持一致, 因为rgba无法使用var变量
    background: rgba($color: $--colorError, $alpha: 0.1);
    border-radius: 8px;
    border: 0;
    border-radius: 12px;
    box-shadow: none;
    padding: 15px 36px 15px 10px !important;

    .ant-notification-notice-icon {
        font-size: 16px !important;
        margin-top: 2px !important;
    }

    .ant-notification-notice-message {
        margin-left: 20px !important;
        padding-inline-end: 0 !important;
        font-weight: 500 !important;
        font-size: 14px !important;
        color: var(--colorText) !important;
        line-height: 20px !important;
        //   max-width: 390px !important;
        margin-bottom: 0 !important;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .ant-notification-notice-description {
        margin-left: 20px !important;
        margin-top: 6px !important;
        color: var(--colorTextSecondary);
        font-size: 12px !important;
        line-height: 16px !important;
    }

    .ant-notification-notice-close {
        top: 13px !important;
        right: 10px !important;
        color: var(--colorIconSecondary);
        &:hover {
            color: var(--colorIconSecondary);
        }
    }
}

// message
.ant-message {
    // background: var(--colorBgElevated);
    div.ant-message-notice-wrapper {
        .ant-message-notice-content {
            background: var(--colorBgElevated);
            color: var(--colorText);
            box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.1);
            border-radius: 12px;
            font-weight: 400;
            font-size: 14px;
            line-height: 24px;
            padding: 8px 16px;
        }
    }
    @mixin customIcon($url) {
        svg {
            display: none;
        }
        background: url($url) no-repeat center;
        background-size: 100%;
        display: block;
        width: 16px;
        height: 16px;
    }
    // 自定义图标
    .ant-message-error > .anticon {
        @include customIcon("../../assets/error.png");
    }
    .ant-message-info > .anticon {
        @include customIcon("../../assets/info.png");
    }
    .ant-message-success > .anticon {
        @include customIcon("../../assets/success.png");
    }
    .ant-message-warning > .anticon {
        @include customIcon("../../assets/warning.png");
    }
}

// drawer
//公共抽屉drawer样式
.common-antd-drawer {
    .ant-drawer-header {
        padding: 20px 24px;
        border-bottom: 0;
    }

    .ant-drawer-header-title {
        flex-direction: row-reverse;

        .ant-drawer-title {
            font-weight: 600;
            font-size: 18px;
            color: var(--colorText);
            line-height: 24px;
        }
    }
    .ant-drawer-close {
        width: 32px;
        height: 32px;
        padding: 8px;
        margin-right: 0;
        color: var(--colorIcon);
        .svg-icon {
            width: 16px;
            height: 16px;
        }
        &:hover {
            background: var(--colorFillQuaternary);
            border-radius: 12px;
        }
    }
    .ant-drawer-body {
        padding: 0 24px 24px;
    }
}

.common-antd-drawer-card {
    display: flex;
    flex-direction: column;
    background: var(--colorFillQuaternary);
    border-radius: 10px;
    margin-bottom: 15px;
    padding: 15px 10px;
    color: var(--colorText);
    cursor: pointer;

    &:hover {
        box-shadow: 1px 2px 7px 0px rgba(0, 0, 0, 0.16);
    }

    .card-title {
        font-size: 16px;
        line-height: 22px;
        margin-bottom: 10px;
        font-weight: 500;
        display: flex;
        word-break: break-word;
    }

    .card-text {
        font-size: 14px;
        line-height: 20px;
        word-break: break-word;
    }

    .card-file {
        display: flex;
        align-items: center;
        font-weight: 500;
        font-size: 16px;
        color: var(--colorText);
        line-height: 22px;
        margin-bottom: 10px;

        img {
            width: 20px;
            height: 20px;
            margin-right: 6px;
        }
    }

    .card-msg {
        display: flex;
        align-items: center;
        font-size: 12px;
        color: #83909d;
        line-height: 16px;
        gap: 4px 16px;
        margin-top: 10px;
        flex-wrap: wrap;
    }
}
.drawer-append-parent .ant-drawer-content-wrapper {
    border-top: 1px solid var(--colorBorderSecondary);
    border-left: 1px solid var(--colorBorderSecondary);
    box-shadow: none;
}
// tabs
.common-tab.ant-tabs {
    &.without-border {
        .ant-tabs-nav::before {
            border: 0;
        }
    }
    .ant-tabs-nav .ant-tabs-ink-bar {
        width: 16px !important;
        height: 4px;
        border-radius: 2px;
        background: var(--colorPrimary);
    }
    .ant-tabs-tab {
        padding-top: 0;
        padding-bottom: 8px;
        color: var(--colorTextTertiary);
    }
    .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
        color: var(--colorText);
        font-weight: 400;
    }
    .ant-tabs-tab-btn {
        font-weight: 400;
        font-size: 15px;
        line-height: 22px;
    }
}

// radio
.ant-radio-wrapper {
    .ant-radio-inner {
        border-width: 2px;
        border-color: var(--colorIconQuaternary);
    }
    .ant-radio-checked {
        background: transparent;
        .ant-radio-inner {
            border-color: var(--colorPrimary);
            background: transparent;
            &::after {
                transform: scale(0.5);
                background: var(--colorPrimary);
                border-radius: 50%;
            }
        }
    }
}
// checkbox
.extend-common-checkbox-inner {
    border-color: var(--colorIconQuaternary);
    border-width: 1.5px;
    border-radius: 3px;
    transition: none;
    &::after {
        transition: none;
    }
}
.extend-common-checkbox-indeterminate-inner {
    background-color: var(--colorPrimary) !important;
    border-color: transparent !important;
    &:after {
        background-color: var(--colorPrimaryContrasting);
        height: 2px;
        border-radius: 2px;
    }
}
.extend-common-checkbox-checked-inner {
    background-color: var(--colorPrimary);
    border-color: var(--colorPrimary);
    &::after {
        border-radius: 1px;
        width: 0;
        left: 6px;
        border-color: var(--colorPrimaryContrasting);
    }
    &::before {
        display: block;
        content: "";
        border-radius: 1px;
        border: 2px solid var(--colorPrimaryContrasting);
        transform: rotate(45deg) scale(1) translate(-50%, -50%);
        border-right: 0;
        border-top: 0;
        border-left: 0;
        width: 5px;
        position: absolute;
        top: 70%;
        z-index: 50;
        inset-inline-start: 25%;
    }
}
.ant-checkbox-wrapper:not(.radio-style) {
    .ant-checkbox,
    .ant-checkbox .ant-checkbox-inner {
        @extend .extend-common-checkbox-inner;
    }
    .ant-checkbox-indeterminate .ant-checkbox-inner {
        @extend .extend-common-checkbox-indeterminate-inner;
    }
    .ant-checkbox-checked .ant-checkbox-inner {
        @extend .extend-common-checkbox-checked-inner;
    }
}

.ant-checkbox-wrapper {
    &.radio-style {
        .ant-checkbox-inner {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 2px solid var(--colorIconQuaternary);
        }
        .ant-checkbox-checked .ant-checkbox-inner {
            background-color: transparent;
            border-color: var(--colorPrimary);
            &:after {
                transform: translate(-50%, -50%);
                width: calc(100% - 8px);
                height: calc(100% - 8px);
                background: var(--colorPrimary);
                border-radius: 50%;
                top: 50%;
                left: 50%;
                border: 0;
            }
        }

        &:not(.ant-checkbox-wrapper-disabled):hover
            .ant-checkbox-checked:not(.ant-checkbox-disabled)
            .ant-checkbox-inner {
            background-color: transparent;
            border-color: var(--colorPrimary);
        }
        &:not(.ant-checkbox-wrapper-disabled):hover .ant-checkbox-inner,
        .ant-checkbox:not(.ant-checkbox-disabled):hover .ant-checkbox-inner {
            border-color: var(--colorIconQuaternary);
        }
    }
}

// dropdwon
.ant-dropdown .ant-dropdown-menu {
    // background: var(--colorBgElevated);
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    padding: 8px;
    .ant-dropdown-menu-item {
        &:not(:first-of-type) {
            margin-top: 6px;
        }
        &:hover {
            border-radius: 12px;
        }
        &.ant-dropdown-menu-item-selected {
            background: var(--colorFillTertiary);
            color: var(--colorText);
            border-radius: 12px;
        }
    }
}

// skeleton
.ant-skeleton {
    .ant-skeleton-image {
        border-radius: 12px;
    }
}

// switch
.ant-switch {
    background: var(--colorFill);
    &.ant-switch-checked {
        background: var(--colorPrimary);
    }
}

/*form*/
$borderRadius: var(--formitem-border-radius);
.disabled-filled {
    border: 0;
    color: var(--colorTextTertiary) !important;
    background: var(--colorFillTertiary) !important;
}
.ant-form-item .ant-form-item-explain-error {
    color: var(--colorError);
    //   word-wrap: break-word;
    font-size: 12px;
    line-height: 18px;
}
// input
span.ant-input-affix-wrapper {
    height: var(--formitem-default-height); // 默认尺寸
    &.ant-input-affix-wrapper-md {
        height: var(--formitem-default-height); // 中尺寸
    }
    &.ant-input-affix-wrapper-lg {
        height: var(--formitem-large-height); // 大尺寸
    }
    &.ant-input-affix-wrapper-sm {
        height: var(--formitem-small-height); // 小尺寸
    }
}
.ant-input-affix-wrapper.ant-input-filled .ant-input-prefix {
    color: var(--colorIconTertiary);
    font-size: 16px;
    margin-right: 8px;
}
.ant-input-filled:not(.custom-input) {
    background: var(--colorFillTertiary) !important;
    border-radius: $borderRadius !important;
    &::placeholder,
    .ant-input::placeholder {
        color: var(--colorTextTertiary);
    }
    &.ant-input-disabled {
        @extend .disabled-filled;
    }
}

// form 中尺寸
.ant-form.mediumn {
    .ant-form-item .ant-form-item-label > label {
        @include font(12px, 18px, 500);
    }
    &.ant-form-vertical .ant-form-item:not(.ant-form-item-horizontal) .ant-form-item-label {
        padding-bottom: 6px;
        line-height: 1;
    }
    .ant-input,
    .ant-select,
    .ant-input-affix-wrapper {
        height: 34px;
    }
}
.ant-form {
    .ant-form-item .ant-form-item-explain-warning {
        display: none;
    }
    input.ant-input-filled,
    input.ant-input-outlined {
        &.ant-input-status-warning:not(.ant-input-disabled) {
            border-color: transparent;
            color: var(--colorText) !important;
            input {
                color: var(--colorText) !important;
            }
        }
    }
    .ant-input-filled,
    .ant-input-outlined {
        &.ant-input-status-warning:not(.ant-input-disabled) {
            border: transparent;
            color: var(--colorText) !important;
            input {
                color: var(--colorText) !important;
            }
        }
    }
    .ant-form-item.mobile-form-item .ant-form-item-explain-error {
        padding-left: 102px;
    }
}

// input 搜索
.ant-input-filled.search {
    border-color: var(--colorBorderSecondary);
    .ant-input-suffix {
        font-size: 16px;
        color: var(--colorIconTertiary);
    }
    &:not(.ant-input-disabled):hover {
        background: var(--colorFillTertiary) !important;
        border-color: var(--colorBorder);
    }
    &:not(.ant-input-disabled):focus,
    &:not(.ant-input-disabled):focus-within {
        border-color: var(--colorPrimary);
    }
}

.ant-input-outlined {
    border-radius: $borderRadius !important;
    border-color: var(--colorBorder);
    &::placeholder,
    .ant-input::placeholder {
        color: var(--colorTextQuaternary);
    }
    .ant-input-suffix {
        font-size: 16px;
        color: var(--colorIcon);
    }
}

input.ant-input-filled,
input.ant-input-outlined {
    &.ant-input-status-error:not(.ant-input-disabled) {
        border: 1px solid var(--colorError);
        color: var(--colorText) !important;
        input {
            color: var(--colorText) !important;
        }
    }
}
.ant-input-filled,
.ant-input-outlined {
    &.ant-input-status-error:not(.ant-input-disabled) {
        border: 1px solid var(--colorError);
        color: var(--colorText) !important;
        input {
            color: var(--colorText) !important;
        }
    }
}
span.ant-input-suffix .ant-input-show-count-suffix,
span.ant-input-suffix .ant-input-data-count {
    font-size: 10px;
    color: var(--colorTextSecondary);
    line-height: 14px;
}
// 清空样式
.ant-input-suffix .ant-input-clear-icon {
    font-size: 16px;
    color: var(--colorIconTertiary);
}
.ant-input-suffix .ant-input-clear-icon:hover {
    color: var(--colorIconTertiary);
}
.icon_input_search {
    font-size: 16px;
    color: var(--colorIconSecondary);
}

// select
div.ant-select {
    height: var(--formitem-default-height); // 默认尺寸
    &.ant-select-md {
        height: var(--formitem-default-height); // 中尺寸
    }
    &.ant-select-lg {
        height: var(--formitem-large-height); // 大尺寸
    }
    &.ant-select-sm {
        height: var(--formitem-small-height); // 小尺寸
    }
    .ant-select-selector {
        border-radius: $borderRadius !important;
    }
    &.ant-select-filled:not(.ant-select-customize-input) {
        &.ant-select-disabled {
            .ant-select-selector {
                @extend .disabled-filled;
            }
        }
        .ant-select-selector {
            background: var(--colorFillTertiary) !important;
            border-radius: $borderRadius !important;
            padding-left: 14px;
            padding-right: 14px;
            color: var(--colorText);
        }
    }
    .ant-select-arrow {
        font-size: 16px;
        color: var(--colorIcon);
    }
    // 禁用
    &.ant-select-disabled {
        .ant-select-arrow {
            color: var(--colorTextTertiary);
        }
    }
    // // 多选
    // &.ant-select-multiple{
    //   .ant-select-selector{
    //     padding-left: 4px;
    //   }
    //   .ant-select-selection-item{
    //     background: var(--colorBgBase);
    //     border-radius: 6px;
    //     color: var(--colorText);
    //     border: 0;
    //   }
    //   .ant-select-selection-item-remove{
    //     color: var(--colorText);
    //     font-size: 16px;
    //   }
    // }
}
.ant-select-dropdown {
    .ant-select-item-option:not(:last-child) {
        margin-bottom: 4px;
    }
}

// table
.ant-table-wrapper {
    $br: 12px;
    border-radius: $br;
    .ant-table-container {
        border-radius: $br;
        border: 1px solid var(--colorBorderSecondary);
    }
    &.table-with-pagination {
        .ant-spin-container {
            height: 100%;
            display: flex;
            flex-direction: column;
            gap: 24px;
        }
        .ant-pagination {
            margin-top: auto;
            margin-bottom: 40px;
            .ant-pagination-item {
                margin-inline-end: 4px;
            }
            .ant-pagination-item-active {
                border: 0;
                background: var(--colorFillTertiary);
                font-weight: 400;
                font-size: 14px;
                color: var(--colorText);
                line-height: 32px;
                border-radius: 8px;
                // display: flex;
                // align-items: center;
                // justify-content: center;
            }
            .ant-pagination-options-quick-jumper {
                color: var(--colorText);
                input {
                    border: 0;
                    background: var(--colorFillTertiary);
                    border-radius: 8px;
                }
            }
            .ant-pagination-options {
                margin-left: 10px;
            }
            .ant-pagination-item-link .ant-icon {
                font-size: 16px;
            }
        }
    }
    .ant-table {
        .ant-table-content {
            scrollbar-color: initial;
            @include webkitScrollStyle($height: 10px);
            border-radius: $br;
        }
        .ant-table-thead {
            > tr > th {
                background: var(--colorBgLayout);
                border-bottom-color: transparent;
                @include font(12px, 18px, 600, var(--colorTextSecondary));
                padding-top: 8px;
                padding-bottom: 8px;
                height: 34px;
                &::before {
                    height: 0 !important;
                }
            }
        }
        .ant-table-tbody {
            > tr > td {
                border-bottom-color: var(--colorBorderSecondary);
                @include font(14px, 22px, 400);
                padding-top: 16px;
                padding-bottom: 16px;
            }
            .ant-table-row.ant-table-row-selected > .ant-table-cell {
                background: var(--colorPrimaryBg);
            }
            .ant-table-row:last-of-type {
                > td {
                    border-bottom: 0;
                }
            }
        }
        &.ant-table-empty {
            .ant-table-tbody td {
                border-bottom-color: transparent;
            }
        }
    }
    .ant-table-container table > thead {
        > tr:first-child > *:last-child {
            border-start-end-radius: $br;
        }
        > tr:first-child > *:first-child {
            border-start-start-radius: $br;
        }
    }
    &.table-border {
        .ant-table .ant-table-thead {
            > tr > th {
                padding-left: 10px;
                background: var(--colorFillQuaternary);
                border-start-end-radius: 0 !important;
                &::before {
                    height: 22px !important;
                    background-color: var(--colorBorderSecondary) !important;
                }
            }
        }
    }
}

// 分页
.ant-pagination {
    .ant-select-selector,
    .ant-select-outlined:not(.ant-select-customize-input) .ant-select-selector {
        background: var(--colorFillTertiary);
        border: 0 !important;
        box-shadow: none !important;
    }
    .ant-pagination-item {
        margin-inline-end: 4px;
    }
    .ant-pagination-item-active {
        border: 0;
        background: var(--colorFillTertiary);
        font-weight: 400;
        font-size: 14px;
        color: var(--colorText);
        line-height: 32px;
        border-radius: 8px;
    }
    .ant-pagination-options-quick-jumper {
        color: var(--colorText);
        input {
            border: 0;
            background: var(--colorFillTertiary);
            border-radius: 8px;
        }
    }
    .ant-pagination-options {
        margin-left: 10px;
    }
    .ant-pagination-item-link .ant-icon {
        font-size: 16px;
    }
}

// table里的tag
.ant-table .ant-tag {
    padding: 1px 8px;
    font-size: 13px;
    line-height: 20px;
}
//empty
.page-empty {
    height: 618px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    .ant-empty-description {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        > div:first-child {
            font-weight: 600;
            font-size: 13px;
            color: var(--colorTextSecondary);
            line-height: 20px;
        }
        > div:last-child {
            margin-top: 10px;
            font-weight: normal;
            font-size: 12px;
            color: var(--colorTextTertiary);
            line-height: 20px;
        }
    }
    &.full {
        height: 100%;
    }
}

//badge
.ant-badge {
    .ant-badge-count {
        color: #fff;
        background: var(--colorError);
        border-radius: 16px;
    }
    .ant-badge-count-sm {
        font-size: 10px;
        // line-height: 10px;
        font-weight: 500;
        padding-left: 4px;
        padding-right: 4px;
    }
    .ant-badge-dot{
        background: var(--colorError);
    }
}

//segmented
.ant-segmented.common-segmented {
    background: var(--colorFillTertiary);
    padding: 6px;
    border-radius: 10px;
    .ant-segmented-item-label {
        min-height: 30px;
        line-height: 30px;
    }
    .ant-segmented-group {
        gap: 0 8px;
    }
    .ant-segmented-item {
        flex: 1;
        $br: 8px;
        border-radius: $br;
        @include font(14px, 22px, 400, var(--colorTextSecondary));
        &.ant-segmented-item-selected {
            box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.05);
            border-radius: 8px;
            .ant-segmented-item-label {
                background: var(--colorBgBase);
                border-radius: 8px;
                color: var(--colorPrimary);
                font-weight: 500;
            }
        }
        &::after {
            border-radius: $br;
        }
    }
}

// tag 重定义颜色
.ant-tag {
    // 默认蓝色
    &.default,
    &.blue {
        background: #e8f3ff;
        color: #165dff;
    }
    // 橙色
    &.orange {
        background: #fff7e6;
        color: #fa8c16;
    }
    // 黄色
    &.yellow {
        background: #fffbe6;
        color: #faad14;
    }
    // 绿色
    &.green {
        background: #ebffee;
        color: #009951;
    }
    // 红色
    &.red {
        background: #fff1f0;
        color: #f5222d;
    }
    // 紫色
    &.purple {
        background: #f9f0ff;
        color: #722ed1;
    }
    // 青色
    &.cyan {
        background: #e6fffb;
        color: #13c2c2;
    }
    // 灰色
    &.gray {
        background: rgba(0, 0, 0, 0.04);
        color: rgba(0, 0, 0, 0.45);
    }
    // 粉色
    &.pink {
        background: #fff0f6;
        color: #eb2f96;
    }
    // 草绿
    &.lime {
        background: #fcffe6;
        color: #a0d911;
    }
}

// datePicker
div.ant-picker {
    border-radius: 8px !important;
    height: var(--formitem-default-height); // 默认尺寸
    &.ant-picker-middle {
        height: var(--formitem-default-height); // 中尺寸
    }
    &.ant-picker-large {
        height: var(--formitem-large-height); // 大尺寸
    }
    &.ant-picker-small {
        height: var(--formitem-small-height); // 小尺寸
    }
}
