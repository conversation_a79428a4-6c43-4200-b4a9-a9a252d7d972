@import "@/styles/mixin.scss";
.ann-detail-page {
    height: 100%;
    width: 100%;
    background: var(--colorBgBase) !important;

    position: relative;

    &.space-layout2  .wrap-nav{
        min-height: var(--page-nav-height);
        padding: 10px 20px;
        height: auto;
    }

    &.space-layout{
        background: var(--colorBgLayout)!important;
        gap: 10px;
        .wrap-nav{
            background: var(--colorBgBase) !important;
        }
        .wrap-content{
            width: calc(100% - 40px);
            margin: 0 auto;
            padding-top: 20px;
            padding-left: 20px;
            padding-right: 20px;
            background: var(--colorBgBase) !important;
            border-radius: 8px;
            .ann-content{
                padding: 0 20px;
                // padding-bottom: 100px;
            }
        }
    }


    .ann-title {
        @include font(20px, 28px, 400);
        @include ellipsis-multiline();
    }
    .wrap-nav{
        // box-shadow: 0px 1px 0px 0px var(--colorBorderSecondary);
    }
    .icon-back {
        cursor: pointer;
        font-size: 20px;
        padding: 8px;
    }
    .ann-operate-time {
        @include font(12px, 20px, 400, var(--colorTextSecondary));
    }
    .ann-detail {
        padding-top: 0;
        // position: relative;
        .ann-content{
            // padding-bottom: 100px;
            flex: 1;
        }
    }
    .ann-tags {
        padding-top: 12px;
    }
    .ann-files {
        // position: sticky;
        // left: 0;
        // background: var(--colorBgNav);
        // border-radius: 8px;
        // right: 0;
        // width: 100%;
        // padding: 10px 16px;
        flex-shrink: 0;
        margin-top: auto;

        border-top: 1px solid var(--colorBorderSecondary);
        position: absolute;
        padding: 10px 0;
        left: 40px;
        right: 40px;
        background: var(--colorBgBase) !important;

        bottom: 0;
        z-index: 100;

        .ann-file-icon {
            font-size: 14px;
            line-height: 14px;
        }
        .ann-file-item{
            @include font(12px, 18px, 400,var(--colorTextSecondary));
            cursor: pointer;
            padding: 4px 6px;
            &:hover{
                background: var(--colorFillTertiary);
                border-radius: 8px;
            }
        }
        .ann-file-name{
            max-width: 228px;
            @include ellipsis();
        }
        .ann-file-size{
            color: var(--colorTextTertiary);
        }
    }
    .ann-operator{
        flex: 1;
        @include ellipsis-multiline();
    }
    .ann-time-operate{
        flex-shrink: 0;
    }
    .ann-operate-menu-trigger{
        cursor: pointer;
        font-size: 18px;
        width: 36px;
        // height: 36px;
        color: var(--colorTextSecondary);
    }
    .ann-ai-summary{
        @include font(14px, 22px, 400,var(--colorText));
        background: var(--colorBgLayout);
        border-radius: 8px;
        padding: 10px 16px;
        margin-top: 20px;
        min-height: 72px;
        
        border: 1px solid var(--colorBorderSecondary);
        // border-bottom: 1px solid var(--colorBorderSecondary);
        .placehoder{
            @include font(10px, 16px, 400,var(--colorTextSecondary));
        }
        .ai-icon{
            font-size: 16px;
            // width: 36px;
            color: var(--colorText);
        }
        .ai-summary{
            width: 120px;
            cursor: pointer;
        }
        .summary{
            @include font(12px, 18px, 400,var(--colorText));
            word-wrap: break-word;
            word-break: break-word;
        }
    }
}

.ann-page div.detail-panel{
    width: calc(100% - 32px);
    height: calc(100% - 32px);
    margin: auto;
    border-radius: 8px;
    .ann-detail-page.space-layout2{
        border-radius: 8px;
        padding-right: 4px;
        .common-wrap .wrap-nav{
            border-radius: 8px 8px 0 0;
        }
        .wrap-content{
            padding-right: 36px;
        }
    }
}
