import { connect } from "react-redux";
import { useState, useEffect, forwardRef, useRef } from "react";
import { Flex, Button, Popover, Skeleton, Tooltip, message, Badge } from "antd";
import UserMenu from "./userMenu";
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';
import SvgIcon from '@components/svgicon'; // 根据实际路径引入
import classnames from "classnames";
import CreateTeamModal from './createTeamModal'
import JoinTeamModal from './joinTeamModal'
import { api_get_mycompanies } from '@/api/company'
import { api_get_my_profile } from '@/api/setting'
import {
    change_app_active_company, change_app_is_refresh_companies,
    change_app_user_profile, change_app_personal_user_profile, change_is_active_company_admin, change_app_is_refresh_ann_unread
} from '@/store/action'
import { api_get_notification_unread_num } from '@/api/information'
import { refreshTheme } from '@/theme'
import { useOpenIM } from '@/contexts/OpenIMContext'
import UserAvatar from '@/components/userAvatar'

import navLogoDefault from '@/icons/png/nav_logo_default.png'
import './index.scss'
import { routeMenuItemsDefault } from './menuConfig'

// 默认企业-个人企业
const defaultTeam = {
    id: '0',
    companyName: 'company.personal-space',
    logo: '',
    isPersonal: true
}// 默认有个人空间

// 侧边导航栏
const SideNavComponent = forwardRef((props: any, ref) => {
    const { t } = useTranslation()
    const navigate = useNavigate()
    const location = useLocation();

    const { service: ImService, isConnected: isConnectedIm, unreadMsgCount } = useOpenIM()
    const { globalLang: lang, activeCompanyId, themeInfo, isPrivate, defaultThemeInfo, isRefreshCompanies, activeCompanyName, companies = [], isLogin, isRefreshAnnNotify } = props
    const { theme, logoUserCenterLight, logoUserCenterDark } = themeInfo
    const [routeMenuItems, setRouteMenuItems] = useState([...routeMenuItemsDefault])
    const [openCompanyMenu, setOpenCompanyMenu] = useState(false)
    const [teamData, setTeamData] = useState([defaultTeam]) // 组织数据
    const [activeTeam, setActiveTeam] = useState(0) //当前选择组织
    const [createModalVisible, setCreateModalVisible] = useState(false) // 新增弹窗显隐
    const [joinModalVisible, setJoinModalVisible] = useState(false) // 加入弹窗显隐
    const [isLoading, setIsLoading] = useState(true)
    const [chatUnread, setChatUnread] = useState<any>(null) // 聊天-未读数
    const [isNotificationUnread, setIsNotificationUnread] = useState<any>(false) // 公告-是否存在未读数
    const [activeMenu, setActiveMenu] = useState('robotpage')

    const timerRef = useRef<any>(null);
    const tabWindows = useRef<any>({})
    const notificationTimerRef = useRef<any>(null); // 公告未读数 计时器

    //初始化
    useEffect(() => {
        getPersonalUserProfile()
    }, [])

    // 监听部署方式变化
    useEffect(() => {
        try {
            if (isPrivate) {
                refreshTheme(true)
                // props.dispatch_change_app_active_company({id:1,name: ''})
                setIsLoading(false)
                return
            }
            // 获取公司数据
            // getMyCompanies(activeCompanyId)

            // 设置激活路由
            const pathname = props.router.location.pathname
            setActiveMenuByLocation(pathname)
        } catch (error) {

        }
        return () => {
            if (timerRef.current) {
                clearTimeout(timerRef.current);
            }
        };
    }, [isPrivate])

    // 获取公司数据
    useEffect(() => {
        // console.log('companies changed', companies)
        setActiveTeam(activeCompanyId)
        if (isLogin) refreshTheme(false)
        if (companies) {
            setTeamData([defaultTeam, ...companies])
        }
        setIsLoading(false)
    }, [companies, isLogin])


    //监听 是否需要刷新企业列表
    useEffect(() => {
        // console.log('监听 是否需要刷新企业列表 isRefreshCompanies',isRefreshCompanies)
        if (isRefreshCompanies) {
            getMyCompanies()
            props.dispatch_change_app_is_refresh_companies(false)
        }
    }, [isRefreshCompanies])

    //  初始化获取聊天未读数
    useEffect(() => {
        console.log('+++++侧边栏 ImService isConnectedIm 变了', ImService)
        if (!ImService || !isConnectedIm) return
        // 获取聊天未读数
        ImService.getTotalUnreadMsgCount().then((res: any) => {
            if (res) {
                console.log('获取聊天未读数成功', res)
                updateChatUnreadCount(res)
            }
        }).catch((err: any) => {
            console.log('获取聊天未读数失败', err)
        })

    }, [ImService, isConnectedIm])

    // 监听路由地址变化
    useEffect(() => {
        if (location.pathname) {
            setActiveMenuByLocation(location.pathname)
        }
    }, [location.pathname]);


    useEffect(() => {
        console.log('-----侧边栏未读数需要变化', unreadMsgCount)
        updateChatUnreadCount(unreadMsgCount)
    }, [unreadMsgCount])

    // 监听切换企业
    useEffect(() => {
        console.log('选择企业变了', activeCompanyId)
        // 获取个人信息
        getUserProfile()
        const isPersonal = activeCompanyId == '0'
        // console.log('isPersonal', isPersonal)
        if (!isPersonal) {
            // 非个人空间的企业，每隔半个小时获取 公告未读数
            if (notificationTimerRef.current) {
                clearTimeout(notificationTimerRef.current);
            }
            getNotificationUnreadNum()
            // 新增定时器，30分钟 查询一次
            notificationTimerRef.current = setInterval(() => {
                getNotificationUnreadNum()
            }, 30 * 60 * 1000)
        }
        // 设置左侧菜单属性，针对 个人空间 和 普通企业
        setRouteMenuItems(routeMenuItems.map((i: any) => {
            if (!['robotpage', 'chatspage', 'contactpage'].includes(i.key)) {
                i.isHidden = isPersonal
            }
            if (i.key === 'ann') {
                i.isBottom = !isPersonal
            }
            // console.log('----i',i)
            return i
        }))

        return () => {
            if (notificationTimerRef.current) {
                clearTimeout(notificationTimerRef.current);
            }
        }
    }, [activeCompanyId])

    // 监听 是否需要 刷新 公告未读数
    useEffect(() => {
        console.log('isRefreshAnnNotify change', isRefreshAnnNotify)
        if (isRefreshAnnNotify) {
            getNotificationUnreadNum()
            props.dispatch_change_app_is_refresh_ann_unread(false)
        }
    }, [isRefreshAnnNotify])

    const setActiveMenuByLocation = (pathname: any) => {
        const targetMenu = routeMenuItems.filter((item: any) => item.path === pathname)
        if (targetMenu && targetMenu.length > 0) {
            setActiveMenu(targetMenu[0].key)
        }
    }

    // 修改聊天tab 未读数
    const updateChatUnreadCount = (count: any) => {
        let unread = count > 99 ? '99+' : count
        if (String(count) === '0') unread = null
        setChatUnread(unread)
    }

    // 获取 个人空间 个人资料
    const getPersonalUserProfile = async () => {
        try {
            const info: any = await api_get_my_profile('0').catch(() => { })
            if (!info) return
            // console.log('info', info)
            props.change_app_personal_user_profile({ userName: info.userName, avatar: info.avatar })
        } catch (error) {

        }
    }

    // 获取个人资料
    const getUserProfile = async () => {
        try {
            const info: any = await api_get_my_profile(activeCompanyId).catch(() => { })
            if (!info) return
            // console.log('info', info)
            props.change_app_user_profile({ userName: info.userName, avatar: info.avatar })
            if (String(activeCompanyId) === '0') {
                props.change_app_personal_user_profile({ userName: info.userName, avatar: info.avatar })
            }
        } catch (error) {

        }
    }

    // 获取公告未读数
    const getNotificationUnreadNum = async () => {
        const res: any = await api_get_notification_unread_num().catch(() => {
        })
        if (!res) return
        console.log('----公告未读数更新：', res)
        setIsNotificationUnread(res && res > 0)
    }

    // 获取公司数据
    const getMyCompanies = async (targetId?: any) => {
        setIsLoading(true)
        const res: any = await props.dispath_api_get_mycompanies().catch((err: any) => {
            setIsLoading(false)
            // console.log(err)
        })
        setIsLoading(false)
        // console.log(res)
        if (!res) return
        let datas = res?.data || []
        datas = [defaultTeam, ...datas]
        setTeamData(datas)
        let activeId = datas.length > 0 ? datas[0]['id'] : '0'
        let activeName = ''
        if (targetId) {
            const target = datas.filter((i: any) => (i.id + '') === (targetId + ''))
            if (target.length > 0) {
                activeId = targetId
                activeName = target[0].companyName
            }
        }
        setActiveTeam(activeId)
        emit_change_active_company({ id: activeId, name: activeName })
    }

    const emit_change_active_company = async (activeObject: any, refreshAfter?: boolean) => {
        await props.dispatch_change_is_active_company_admin(false)
        await props.dispatch_change_app_active_company(activeObject)
        // console.log('initTheme', props.themeInfo)

        if (refreshAfter) {
            if (timerRef.current) {
                clearTimeout(timerRef.current);
            }

            timerRef.current = setTimeout(() => {
                refreshTheme(false)
            }, 200); // 200毫秒防抖
        } else {
            refreshTheme(false)
        }

    }
    // 路由菜单选择
    const handleRouterMenuClick = (menu: any) => {
        if (menu.disabled) {
            message.info(t('nav.coming-soon'))
            return
        }
        message.destroy()
        setActiveMenu(menu.key)
        props.router.navigate(menu.path)
    }

    // logo
    const getNavLogo = () => {
        try {
            const navLogo = theme === 'dark' ? logoUserCenterDark : logoUserCenterLight;
            return navLogo
        } catch (error) {
            return navLogoDefault
        }

    }

    // 个人空间头像
    const getPersonalDefaultLogo = (size = 32) => {
        return <UserAvatar isLoginUser={true} isPersonal={true} key="personal" size={size} borderRadius="10px" />
    }

    const getNavDefaultLogo = () => {
        try {
            const navLogo = theme === 'dark' ? defaultThemeInfo.logoUserCenterDark : defaultThemeInfo.logoUserCenterLight;
            return navLogo
        } catch (error) {
            return navLogoDefault
        }
    }

    // 企业logo
    const getCompanyLogo = (item: any) => {
        const prop = theme === 'dark' ? 'darkLogo' : 'lightLogo'
        return item[prop] || getNavLogo()
    }

    const onClickCreate = () => {
        setCreateModalVisible(true)
    }

    const onClickJoin = () => {
        setJoinModalVisible(true)
    }

    const onCreateTeamCancel = () => {
        setCreateModalVisible(false)
    }

    const onJoinTeamCancel = () => {
        setJoinModalVisible(false)
    }

    // 切换公司
    const onSelectTeam = async(team: any) => {
        setOpenCompanyMenu(false)
        // 根据企业配置域名，新开浏览器tab窗口
        let domainUrl = team?.domainName
        // domainUrl = ''
        const wholeDomainUrl = domainUrl ? ensureHttps(domainUrl) : domainUrl
        // domainUrl = 'http://localhost:3023' // TEST
        const currentDomain = window.location.origin
        if (!team.isPersonal && domainUrl && currentDomain !== wholeDomainUrl) {
            domainUrl = ensureHttps(domainUrl)
            // 打开窗口，传递企业ID
            domainUrl = `${domainUrl}?CID=${team.id}&CNAME=${team.companyName}`
            // console.log('domainUrl', domainUrl)
            if (!tabWindows.current) {
                tabWindows.current = {}
            }
            // 尽量避免打开多个窗口
            try {
                // 检查窗口是否存在且未关闭
                const targetWindow = tabWindows?.current?.[team.id]
                // console.log('targetWindow', targetWindow)
                if (tabWindows.current && targetWindow && !targetWindow?.closed) {
                    // console.log('聚焦到已有标签页')
                    targetWindow?.focus(); // 聚焦到已有标签页
                } else {
                    // 打开新标签页并保存引用
                    // tabWindow.current = window.open(domainUrl, domainUrl);
                    // console.log('打开新标签页并保存引用')
                    tabWindows.current[team.id] = window.open(domainUrl, domainUrl)
                    // console.log('tabWindows.current',tabWindows.current)
                }
            } catch (error) {
                console.log('error', error)
                window.open(domainUrl, domainUrl)
            }
        } else {
            setActiveTeam(team.id)
            const activeObject = { id: team.id, name: team.companyName }
            if (team.isPersonal){ // 切换到"个人空间"，需要判断当前路径，是否有权限
                const pathname = props.router.location.pathname
                const noAuthPaths = routeMenuItemsDefault.filter((menu:any)=>menu.personalHide).map((menu:any)=>menu.path)
                if (noAuthPaths.includes(pathname)){
                    // console.log('没权限拉')
                    await props.dispatch_change_app_active_company(activeObject)
                    navigate('/')
                    return
                }
            }
            emit_change_active_company(activeObject, true)
        }

    }

    // 确保https
    const ensureHttps = (url: string) => {
        // 如果 URL 没有协议（http/https），则添加 https://
        if (!url.startsWith('http://') && !url.startsWith('https://')) {
            return 'https://' + url;
        }
        // 如果已经是 https://，直接返回
        else {
            return url;
        }
    }

    // 更新当前选中公司
    const onUpdateActiveCompany = (targetId: any) => {
        getMyCompanies(targetId)
        // setOpenCompanyMenu(true)
    }

    const onOpenChangeCompanyMenu = (open: boolean) => {
        setOpenCompanyMenu(open)

    }

    // 获取导航栏logo
    const getActiveNavLogo = () => {
        if (activeCompanyId == '0') {
            return getNavDefaultLogo()
        }
        // console.log('activeTeam',activeTeam)
        if (activeTeam && teamData.length > 1) {
            const target = teamData.filter((i: any) => i.id == activeTeam)
            return target.length > 0 ? getCompanyLogo(target[0]) : getNavLogo()
        } else {
            return getNavLogo()
        }
    }

    // 导航logo: 如状态为企业，logo显示该企业logo，个人显示AMA默认logo
    const navLogo = <img className={"user-avatar"} src={getNavLogo()} alt="" draggable="false" />

    // 用户菜单
    const renderCompanyMenu = () => {
        return (
            <Flex vertical className="user-center-popover-content">
                <div className="menu-title">{t('company.title')}</div>
                <Flex className="menu-content" vertical gap={8}>
                    {
                        teamData.map((item: any, index: number) => {
                            return (
                                <Flex onClick={() => onSelectTeam(item)} className="menu-content-item" key={index} gap={8} align="center">
                                    {
                                        item.isPersonal ?
                                            getPersonalDefaultLogo()
                                            :
                                            <img className="team-logo" src={getCompanyLogo(item)} alt="" ></img>
                                    }
                                    <div className="team-name">{item.isPersonal ? t(item.companyName) : item.companyName}</div>
                                    {
                                        String(activeTeam) == String(item.id) ? <SvgIcon className="team-selected" svgName="icon_check" /> : null
                                    }
                                </Flex>
                            )
                        })
                    }

                </Flex>
                <Flex className="menu-operate" gap={8}>
                    <Button onClick={() => onClickCreate()} className="tertiary" color="default" variant="filled">{t('company.create')}</Button>
                    <Button onClick={() => onClickJoin()} className="tertiary" color="default" variant="filled">{t('company.join')}</Button>
                </Flex>
            </Flex>
        )
    }

    const renderMenuItemTrigger = (menu: any) => {
        return <div key={menu.key} className={classnames(['nav-menu-item', { active: menu.key === activeMenu, 'is-bottom': menu.isBottom }])} onClick={() => handleRouterMenuClick(menu)}>
            {
                menu.key === 'ann' && isNotificationUnread ?
                    <Badge dot className="nav-ann-unread" offset={[-6, 4]}>
                        <SvgIcon svgName={menu.key === activeMenu ? menu.iconActive : menu.icon} />
                    </Badge> :
                    <>
                        <SvgIcon svgName={menu.key === activeMenu ? menu.iconActive : menu.icon} />
                        {menu.key === 'chatspage' && chatUnread ? <div className="nav-chat-unred">{chatUnread}</div> : null}
                    </>
            }
        </div>
    }

    return (
        <div className="side-nav-container">
            <div className="side-nav-logo">
                {
                    isLoading ? <Skeleton.Node style={{ width: 46, height: 30 }} active /> :
                        <>
                            {isPrivate ? navLogo : null}
                            {/* open={true} */}
                            {!isPrivate ?
                                <Popover open={openCompanyMenu} onOpenChange={onOpenChangeCompanyMenu} content={() => renderCompanyMenu()}
                                    // destroyTooltipOnHide={true}
                                    trigger="click"
                                    placement="topLeft" overlayClassName="user-center-popover" arrow={false}>
                                    <Flex className="pointer" align="center" justify="center" onClick={(e) => e.preventDefault()}>
                                        <img className={"user-avatar"} src={getActiveNavLogo()} alt="" draggable="false" />
                                        <SvgIcon svgName="arrow_down" />

                                    </Flex>
                                </Popover>
                                : null}
                        </>
                }
            </div>
            <div className="side-nav-menu">
                {
                    routeMenuItems.filter((menu: any) => !menu.isHidden).map((menu: any) => {
                        return (
                            menu.content ?
                                <Popover key={menu.key} placement="rightTop" overlayClassName="side-nav-menu-item-popover"
                                    content={
                                        <Flex className="menu-item-popover-detail" vertical gap={4}>
                                            <div className="title">{t(menu.tooltip)}</div>
                                            <div className="content">{t(menu.content)}</div>
                                        </Flex>
                                    }>
                                    {renderMenuItemTrigger(menu)}
                                </Popover> :
                                <Tooltip key={menu.key} overlayClassName='side-nav-menu-item-tooltip dark' placement="right" title={t(menu.tooltip)} >
                                    {renderMenuItemTrigger(menu)}
                                </Tooltip>
                        )
                    })
                }
            </div>
            <div className="side-nav-operate">
                <UserMenu {...props} />
            </div>
            {
                createModalVisible ?
                    <CreateTeamModal visible={createModalVisible} lang={lang} onClose={onCreateTeamCancel} onActiveCompany={onUpdateActiveCompany} /> : null
            }
            {
                joinModalVisible ?
                    <JoinTeamModal visible={joinModalVisible} lang={lang} onClose={onJoinTeamCancel} /> : null
            }
        </div>
    );
});

const mapStateToProps = (state: any) => ({
    globalLang: state.app.globalLang,
    themeInfo: state.app.themeInfo,
    isLogin: state.app.isLogin,
    companies: state.app.companies,
    isRefreshCompanies: state.app.isRefreshCompanies,
    isPrivate: state.app.isPrivate,
    activeCompanyId: state.app.activeCompanyId,
    defaultThemeInfo: state.app.defaultThemeInfo,
    isRefreshAnnNotify: state.app.isRefreshAnnNotify,
})
const mapDispatchToProps = (dispatch: any) => ({
    dispath_api_get_mycompanies: () => dispatch(api_get_mycompanies()),
    dispatch_change_app_active_company: (data: any) => dispatch(change_app_active_company(data)),
    dispatch_change_app_is_refresh_companies: (data: any) => dispatch(change_app_is_refresh_companies(data)),
    change_app_user_profile: (data: any) => dispatch(change_app_user_profile(data)),
    change_app_personal_user_profile: (data: any) => dispatch(change_app_personal_user_profile(data)),
    dispatch_change_is_active_company_admin: (data: any) => dispatch(change_is_active_company_admin(data)),
    dispatch_change_app_is_refresh_ann_unread: (data: any) => dispatch(change_app_is_refresh_ann_unread(data)),
})
export default connect(mapStateToProps, mapDispatchToProps)(SideNavComponent);
