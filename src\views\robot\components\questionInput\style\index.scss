
@import "../../../../../styles/mixin.scss";
$minHeight:50px;
.question-input-send {
  display: flex;
  // align-items: center;
  flex: 1;
  min-height: $minHeight;
  .text-area{
    flex: 1;
    display: flex;
    flex-direction: column;
    max-height: 200px;
    position: relative;
  }
  .ant-input {
    position: relative;
    z-index: 100;

    // flex: 1;
    border: 0;
    padding: 4px 10px;
    border-radius: 25px;
    font-size: 14px;
    line-height: 24px;
    margin-top: 9px;
    outline: none;
    box-shadow: none;
    // resize: none;
    // &::-webkit-scrollbar { 
    //   display: none; 
    // }
    // max-height: 147px!important;
    color: var(--colorText);
    background: transparent;
    &:focus {
      box-shadow: none;
    }
    &.ant-input-out-of-range {
      color: var(--colorText);
    }
    &::placeholder{
      background: transparent;
      color: var(--colorTextTertiary);
    }
  }
  .placeholder {
    position: absolute;
    z-index: 10;
    top: 0; 
    left: 0;
    width: 100%;
    padding: 4px 10px;
    color: var(--colorTextTertiary);
    font-size: 14px;
    line-height: 24px;
    margin-top: 9px;
    @include ellipsis;
    pointer-events: none; /* 禁止事件 */
    /* 当输入框有值时，隐藏占位符 */
    &.hidden {
      opacity: 0;
    }
  }
  img {
    cursor: pointer;
    width: $minHeight;
    height: $minHeight;
    user-select: none;
  }
  .switch{
    display: flex;
    align-items: center;
    height: 50px;
    .ant-switch{
      min-width: 30px;
      height: 18px;
      margin-right: 10px;
      .ant-switch-handle{
        width:14px;
        height: 14px;
      }
      &.ant-switch-checked .ant-switch-handle{
        inset-inline-start: calc(100% - 16px);
      }
    }
  }
  .max-count-error{
    text-align: right;
    color: var(--colorError);
    font-size: 12px;
    line-height: 17px;
    margin-bottom: 16px;
  }
  img{
    user-select: none;
  }
  .enter-img{
    width: 32px;
    height: 32px;
    background: var(--colorPrimarySecondary);
    border-radius: 12px;
    margin-top: 9px;
    margin-right: 10px;
    cursor: pointer;
    .svg-icon{
      color: var(--colorPrimaryContrasting);
    }
  }
  .enter-img-disabled{
    opacity: 0.2;
  }
  .pause{
    width: calc($minHeight - 18px);
    height: calc($minHeight - 18px);
    border-radius: 50%;
    border: 1px solid var(--colorBorderSecondary);
    background: var(--colorBgElevated);
    margin: 9px 10px 0 0;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    
    .svg-icon{
      color: var(--colorPrimary);
    }
  }
}

.google-search-popover{
  width: 242px;
  background: var(--colorBgElevated);
  box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.1);
  .ant-popover-title{
    font-size: 16px;
    line-height: 22px;
    margin-bottom: 4px;
  }
  .ant-popover-inner-content{
    font-size: 14px;
    line-height: 20px;
    color: var(--colorTextSecondary);
  }
  .ant-popover-inner{
    border-radius: 10px;
  }
}
.pause-trigger-tooltip-overlay{
  background: var(--colorBgElevated);
  color: var(--colorText);
  .ant-tooltip-inner{
    padding: 8px 10px;
  }
}