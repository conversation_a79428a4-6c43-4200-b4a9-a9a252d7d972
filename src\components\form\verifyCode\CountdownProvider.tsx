import { createContext, useContext, useState, useEffect,useRef } from 'react';

// const CountdownContext = createContext({countdown:0,isCounting:false,startCountdown:()=>{}});
// interface MyContextType { value: any }
// const CountdownContext = createContext<MyContextType | null>(null);
const CountdownContext = createContext({countdowns:new Map(), startCountdown:(key:any, initialCount = 60)=>{}});

export function CountdownProvider({ children }:{children:any}) {
  // 使用 Map 存储多个倒计时状态（格式：{ key: { countdown: number, isCounting: boolean } }）
  const [countdowns, setCountdowns] = useState(new Map());
  // 存储定时器的引用（避免闭包问题）
  const timersRef = useRef(new Map());

  // 启动/重置倒计时
  const startCountdown = (key:any, initialCount = 60) => {
    setCountdowns(prev => new Map(prev).set(key, {
      countdown: initialCount,
      isCounting: true
    }));

    // 清理旧的定时器（如果存在）
    if (timersRef.current.has(key)) {
      clearInterval(timersRef.current.get(key));
    }

    // 创建新定时器
    const timer = setInterval(() => {
      setCountdowns(prev => {
        const newMap = new Map(prev);
        const current = newMap.get(key);
        
        if (!current || current.countdown <= 1) {
          newMap.delete(key);
          return newMap;
        }

        newMap.set(key, {
          ...current,
          countdown: current.countdown - 1
        });
        return newMap;
      });
    }, 1000);

    timersRef.current.set(key, timer);
  };

  // 组件卸载时清理所有定时器
  useEffect(() => {
    return () => {
      timersRef.current.forEach(timer => clearInterval(timer));
    };
  }, []);

  return (
    <CountdownContext.Provider value={{ countdowns, startCountdown }}>
      {children}
    </CountdownContext.Provider>
  );
}

export const useCountdown = () => useContext(CountdownContext);