/***chat模块***/
import HTTP from "@/utils/userRequest";
import { AxiosRequestConfig } from 'axios';

// 获取所属公司、部门、用户
export const api_get_tenant_depts_users = (params:any): Promise<unknown>  => new Promise((resolve, reject) => {
  HTTP({
    method: 'get',
    url: "/system/dept/tenant-depts-users",
    params,
    noTenant: true,
  }as AxiosRequestConfig).then(result => {
    resolve(result?.data?.data || {});
  }).catch(err => reject(err))
})

// 获取联系人用户基本信息
export const api_all_users_profile = (params:any): Promise<unknown>  => new Promise((resolve, reject) => {
  HTTP({
    method: 'get',
    url: "/system/user/profile/all-user-profile",
    params,
    noTenant: true,
  }as AxiosRequestConfig).then(result => {
    resolve(result?.data?.data || {});
  }).catch(err => reject(err))
})

// 联系人 - 查看多个用户名片
export const api_get_user_profile = (data:any, hideTip= false): Promise<unknown>  => new Promise((resolve, reject) => {
  HTTP({
    method: 'post',
    url: "/system/user/profile/multi-user-profile",
    data,
    noTenant: true,
    hideTip,
  }as AxiosRequestConfig).then(result => {
    resolve(result?.data?.data || {});
  }).catch(err => reject(err))
})

// 获取公司部门面包屑
export const api_get_tenant_depts_crumbs = (params:any): Promise<unknown>  => new Promise((resolve, reject) => {
  HTTP({
    method: 'get',
    url: "/system/dept/tenant-depts-crumbs",
    params,
    noTenant: true,
  }as AxiosRequestConfig).then(result => {
    resolve(result?.data?.data || {});
  }).catch(err => reject(err))
})

// 搜索用户
export const api_search_user = (params:any): Promise<unknown>  => new Promise((resolve, reject) => {
  HTTP({
    method: 'get',
    url: "/system/user/profile/search-user",
    params,
  }as AxiosRequestConfig).then(result => {
    resolve(result?.data?.data || {});
  }).catch(err => reject(err))
})
