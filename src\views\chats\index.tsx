import React, { useState, useRef, useEffect, forwardRef, useImperative<PERSON>andle } from "react";
import { connect } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import { Input, Button, Flex, Dropdown, Menu, Empty, Spin, message } from 'antd';
import { LoadingOutlined } from "@ant-design/icons";
import { DownloadOutlined } from "@ant-design/icons";
import type { MenuProps } from 'antd';

import { ConversationItem, MessageItem } from '@openim/wasm-client-sdk';
import { useOpenIM } from '@/contexts/OpenIMContext';
import SvgIcon from '@components/svgicon'; // 根据实际路径引入
import VideoSend from './components/videoSend';
import ImageSend from './components/imageSend';
import FileSend from './components/fileSend';
import StickerSelector from './components/stickerSelector';
import VideoPlayer from './components/videoPlayer';
import UserInfoCard from '@/components/userInfoCard';
import ViewImage from '@/views/robot/components/viewImage';
import CreateGroup from './createGroup';
import AddFriend from './addFriend';
import SettingGroup from './settingGroup';

import "./index.scss";
import { useTranslation } from 'react-i18next';
import classNames from "classnames";
import PlayIcon from '@/icons/png/chats/icon_video_play.png';
import groupDefaultAvatar from '@/icons/png/chats/icon_group_default_avatar.png';
import UserAvatar from "@/components/userAvatar";
import { getMenuItem } from "@components/base";

import { api_all_users_profile } from '@/api/chat'

import VideoUtils from './utils/video'
import FileUtils from './utils/file'
import { formatTimestamp } from '@/utils/common'
import { downloadFile } from '@/utils/download'
import { FormatTimestamp } from './utils/date'
import { api_get_user_profile } from '@/api/chat'


interface ConversationItemType extends ConversationItem {
  name?: string // 显示的名称:用户名 or 群名
  avatar?: any // 用户自定义头像
}

interface MessageItemType extends MessageItem {
  name?: string // 显示的名称:用户名 or 群名
}

const { TextArea } = Input;

const Chats = (props: any) => {
  const { activeCompanyId, userInfo, userProfilePersonal, userProfile, isPrivate } = props
  const currentUserId = String(userInfo.imUserId)
  const { service: ImService, isConnected: isConnectedIm } = useOpenIM();
  const { t } = useTranslation()
  const navigate = useNavigate();
  const location = useLocation();
  // 获取路由传递的 state
  const locationStateGroupID = location.state?.groupID; // 群组ID，通讯录-我的群组，选择群组可跳转到此聊天页面
  const locationStateUserID = location.state?.userID; // 用户ID，通讯录-用户，选择用户可跳转到此聊天页面

  /* state实时取值 */
  const allConversationListRef = useRef<any>(null)
  const messageListRef = useRef<any>(null)
  const currentConversationRef = useRef<any>(null)
  const messageListConRef = useRef<any>(null)
  const usersInfoMapRef = useRef<any>(null)
  const chatInputRef = useRef<any>(null)
  const settingGroupRef = useRef<any>(null)
  const isCreatingNewConversationRef = useRef<any>(null)
  const scrollTimer = useRef<any>(null)
  const isGetGroupLoadingRef = useRef<any>(null)
  const isChangeConversation = useRef<any>(null)

  const [allConversationList, setAllConversationList] = useState<Array<any>>([]) // 所有消息列表
  const [currentConversation, setCurrentConversation] = useState<any>(null) // 当前会话
  const [currentConversationUserInfo, setCurrentConversationUserInfo] = useState<any>(null) // 当前会话-用户信息（企业、部门、职位）
  const [currentConversationUserStatus, setCurrentConversationUserStatus] = useState<any>(null) // 当前会话-用户在线状态
  const [messageList, setMessageList] = useState<Array<any>>([]) // 当前会话消息列表
  const [isLoadingMessageList, setIsLoadingMessageList] = useState(false); // 当前会话消息列表 加载中
  const [chatInputValue, setChatInputValue] = useState<string>("") //对话输入框
  const [chatInputSelection, setChatInputSelection] = useState({ start: 0, end: 0 });
  const [hasChatInputValue, setHasChatInputValue] = useState(false); // 输入框是否有输入值
  const [usersInfoMap, setUsersInfoMap] = useState<any>({}) // 已有的用户信息映射
  const [currentUserName, setCurrentUserName] = useState<any>('') // 当前登录的用户名
  const [currentUserAvatar, setCurrentUserAvatar] = useState<any>('') // 当前登录的用户头像
  const [isCurrentConversationDisabled, setIsCurrentConversationDisabled] = useState<any>(false) // 当前会话是否可用
  const [isScrolling, setisScrolling] = useState<any>(false) // 当前会话容器是否滚动中

  // const [thumbnail, setThumbnail] = useState<any>(null);

  const [viewVideoInfo, setViewVideoInfo] = useState<any>({ visible: false, url: '' }); // 预览视频
  const [viewImageInfo, setViewImageInfo] = useState<any>({ visible: false, url: '' }); // 预览图片
  const [createGroupResource, setCreateGroupResource] = useState<any>({ visible: false, groupedUsers: null, groupId: null, singleUser: null }); // 新增群聊
  const [settingGroupResource, setSettingeGroupResource] = useState<any>({ visible: false, groupId: null, groupName: '', groupAvatar: null }); // 设置群
  const [addFriendResource, setAddFriendResource] = useState<any>({ visible: false }); // 添加好友

  // 页面挂载
  useEffect(() => {
    // chatInputRef.current?.focus() // 输入框自动聚焦
    return () => {
      // console.log('定时器已清理',scrollTimer.current);
      clearInterval(scrollTimer.current);
    }
  }, [])

  // 读取路由参数
  useEffect(() => {
    if (locationStateGroupID || locationStateUserID) {
      navigate(location.pathname, { replace: true, state: {} });// 自动清除 state（替换当前历史记录）
    }
  }, [locationStateGroupID, locationStateUserID, navigate, location.pathname]);

  // 监听企业ID变化
  useEffect(() => {
    // 不同企业 userName存在不同情况，切换企业需要刷新userName
    initData()
    if (currentConversationRef.current?.conversationType === 1) {
      getCurrentConversationUserInfo()
    }
    onCloseGroupSetting() // TODO 优化
  }, [activeCompanyId]) // activeCompanyId

  // 监听企业ID+用户个人资料变化
  useEffect(() => {
    // 实时刷新当前登录用户 用户名和 头像
    let storeProfile = String(activeCompanyId) === '0' ? userProfilePersonal : userProfile // 设置能修改用户名，实时刷新当前用户
    if (storeProfile) {
      const name = storeProfile.userName
      const avatar = storeProfile.avatar
      // console.log('刷新name avatar ',name)
      setCurrentUserName(name)
      setCurrentUserAvatar(avatar)
    }
  }, [activeCompanyId, userProfile, userProfilePersonal])

  // 监听 IM服务变化
  useEffect(() => {
    // console.log('+++++聊天页面 ImService 变了',ImService)
    console.log('+++++聊天页面 ImService isConnectedIm 变了', isConnectedIm)
    if (!ImService) return
    // IM登录后，获取消息列表
    if (ImService.isLogin) {
      // getAllConversationList(true)
      initIM()
    }

    // 接收到新消息时的回调，回调中可能携带多条消息。
    ImService.registerCallBack("OnRecvNewMessages", async (data: any) => {
      // console.log(`📢📢📢📢接收到新消息：`, data);
      handleRecvNewMessages(data)
    });

    // 会话信息发生变化时的回调（如未读数变化、会话的最后一条消息变化）
    ImService.registerCallBack("OnConversationChanged", async (data: any) => {
      console.log(`📢📢📢📢会话信息发生变化：`, data);
      changeConversationList(data);
    });

    ImService.registerCallBack("OnNewConversation", async (data: any) => {
      console.log(`📢📢📢📢有新会话产生：`, data);
      getAllConversationList();
    });

    return () => {
      ImService.unRegisterCallBack("OnRecvNewMessages");
      ImService.unRegisterCallBack("OnConversationChanged");
      ImService.unRegisterCallBack("OnNewConversation");
    };

  }, [ImService, isConnectedIm])

  const initIM = async () => {
    const userMap = await initData()
    getAllConversationList(true, userMap)
  }

  useEffect(() => {
    // allConversationListRef.current = JSON.parse(JSON.stringify([...allConversationList]))
    allConversationListRef.current = [...allConversationList]
    // console.log('+++++allConversationList 变了',allConversationList.map(i=>i.latestMsgSendTime))
  }, [allConversationList])

  useEffect(() => {
    messageListRef.current = [...messageList]
    ScrollMessageToBottom()
    // console.log('+++++messageList 变了',messageList)
  }, [messageList])

  useEffect(() => {
    currentConversationRef.current = { ...currentConversation }
    // ScrollMessageToBottom()
    // console.log('+++++currentConversation 变了',currentConversation)
    getCurrentConversationStatus()
  }, [currentConversation])

  useEffect(() => {
    setHasChatInputValue(chatInputValue?.trim() !== "")
  }, [chatInputValue])

  useEffect(() => {
    usersInfoMapRef.current = { ...usersInfoMap }
  }, [usersInfoMap])

  useEffect(() => {
    // console.log('+++++isCurrentConversationDisabled 变了',isCurrentConversationDisabled,chatInputRef.current)
    if (!isCurrentConversationDisabled) chatInputRef.current?.focus() // 输入框自动聚焦
  }, [isCurrentConversationDisabled])


  // 页面初始化
  async function initData() {
    // 获取用户信息Map
    const res: any = await api_all_users_profile({ currentTenantId: activeCompanyId }).catch((err: any) => {
      console.log(err)
      return usersInfoMap
    })
    if (!res) return usersInfoMap
    const infoMap = { ...usersInfoMap, ...res }
    setUsersInfoMap(infoMap)
    if (allConversationListRef.current && allConversationListRef.current.length > 0) { // username
      const list = setConversationName(allConversationListRef.current, infoMap)
      // 修改当前会话 头部用户名称
      const _currentConversation = currentConversationRef.current
      if (_currentConversation && _currentConversation.conversationType !== 3) {
        // _currentConversation.name = infoMap[_currentConversation.userID]?.userName
        _currentConversation.name = getUserName(_currentConversation.userID, _currentConversation.showName, infoMap)
        _currentConversation.avatar = getUserProfileAvatar(_currentConversation.userID, infoMap)
        setCurrentConversation(_currentConversation)
      }
    }
    let storeProfile = String(activeCompanyId) === '0' ? userProfilePersonal : userProfile // 设置能修改用户名，实时刷新当前用户
    setCurrentUserName(infoMap[currentUserId]?.userName || storeProfile?.userName || '')
    setCurrentUserAvatar(infoMap[currentUserId]?.avatar || null)
    return infoMap
  }

  const setConversationName = (conversationList: any, _usersInfoMap = usersInfoMapRef.current) => {
    conversationList?.forEach((conversationItem: ConversationItemType) => {
      conversationItem.name = conversationItem.showName
      if ([1, 2].includes(conversationItem.conversationType)) {
        conversationItem.name = getUserName(conversationItem.userID, conversationItem.showName, _usersInfoMap)
        conversationItem.avatar = getUserProfileAvatar(conversationItem.userID, _usersInfoMap)
      }
    });
    return conversationList
  }

  // 获取用户名, 取值逻辑：1.先去匹配usersInfoMap的userName；2.若匹配不到取IM 的用户名（nickname、showName、senderNickname）3.都匹配不到显示userId
  const getUserName = (userID: any, userName: any, userMap = usersInfoMap) => {
    let name = (userMap[userID]?.userName || userName || userID)
    if (userID == currentUserId) {
      name = currentUserName || name
    }
    return name
  }

  // 获取用户profile 头像
  const getUserProfileAvatar = (userID: any, userMap = usersInfoMap) => {
    return userID == currentUserId ? currentUserAvatar : userMap[userID]?.avatar
  }

  // 获取所有消息列表
  const getAllConversationList = async (isInit = false, userMap = usersInfoMap) => {
    try {
      const { data: conversationList = [] } = await ImService.getAllConversationList();

      const _conversationList = setConversationName(conversationList, userMap)

      console.log('👻👻👻获取所有消息列表:', _conversationList)
      setAllConversationList([..._conversationList]);
      const stateUserID = location.state?.userID
      // 默认显示第一个会话数据
      if ((isCreatingNewConversationRef.current || isInit) && _conversationList.length > 0) {
        isCreatingNewConversationRef.current = false
        let selectConversation = _conversationList[0] // 默认读取第一个会
        if (locationStateGroupID) { // 路由参数包含群组ID，读取该群组
          const existConversion = _conversationList.filter((i: any) => i.groupID === locationStateGroupID)
          selectConversation = existConversion.length > 0 ? existConversion[0] : selectConversation
        }
        // console.log('************stateUserID', stateUserID)
        if (stateUserID) { // 路由参数包含用户ID，读取该用户
          const existConversion = _conversationList.filter((i: any) => i.userID === stateUserID)
          if (existConversion.length > 0) { // 会话列表 已存在该用户
            selectConversation = existConversion[0]
          } else { // 找不到该用户，需要发起会话
            // console.log('************找不到该用户，需要发起会话', stateUserID)
            initiateUserConversation(stateUserID)
          }
        }
        setCurrentConversation(selectConversation)
        handleSelectConversation(selectConversation, isInit)
        // 若新消息是当前会话的,标记会话已读
        if (selectConversation.unreadCount > 0) {
          ImService.markConversationMessageAsRead(selectConversation.conversationID)
        }
      } else { // 消息列表为空
        if (stateUserID){// 路由参数包含用户ID，读取该用户
          initiateUserConversation(stateUserID)
        }
      }

    } catch (error) {
      console.log('error', error)
    }
  }

  //发起 用户新会话
  const initiateUserConversation =(userID:any)=>{
    isCreatingNewConversationRef.current = true
    ImService.sendCustomMessage('user', userID, { data: 'CustomConnectionMessage', extension: '', description: 'Make a connection' })
    initData() // 刷新userMap
  }

  // 更新消息列表
  const changeConversationList = async (data: ConversationItem[]) => {
    try {
      let allList: any[] = [...allConversationListRef.current]// 所有会话
      const _currentConversation = currentConversationRef.current // 当前会话
      data = setConversationName(data)
      for (let i = 0; i < data.length; i++) {
        const conversationItem = data[i]
        // 刷新当前会话信息
        if (conversationItem.conversationID === _currentConversation.conversationID) {
          if ([1, 3].includes(_currentConversation.conversationType)) { // user group
            // 按照时间从新到老，获取会话中的历史聊天记录
            const conversation: any = await ImService.getAdvancedHistoryMessageList(_currentConversation.conversationID, "").catch((err: any) => { });
            conversation?.messageList && setMessageList(conversation?.messageList);
          }
          setCurrentConversation(conversationItem);
          // 若新消息是当前会话的,标记会话已读
          if (conversationItem.unreadCount > 0) {
            ImService.markConversationMessageAsRead(conversationItem.conversationID)
          }
        }

        // 刷新所有会话中的 消息
        allList = allList.map(i => {
          if (i.conversationID === conversationItem.conversationID) {
            return conversationItem
          }
          return i
        })
      }
      // 所有会话 本地排序
      allList = allList.sort((a: any, b: any) => {
        const key = 'latestMsgSendTime'
        // 统一转换为时间戳毫秒数
        const timeA = a[key] //new Date(a[key]).getTime();
        const timeB = b[key] // new Date(b[key]).getTime();
        return timeB - timeA;// 升序排列（从早到晚）
      })
      console.log('---更新消息总列表 allList', allList)
      setAllConversationList([...allList])
    } catch (error) {
      console.log('error', error)
    }
  }

  // 处理新消息
  const handleRecvNewMessages = (datas: any) => {

  }

  // 选中某个会话
  const handleSelectConversation = async (_conversationItem: any, isClickConversation = false) => {
    try {
      setMessageList([]); // 清空当前会话对应的 MessageList
      if (isClickConversation) { //切换对话 才显示loading
        setIsLoadingMessageList(true) // 开启loading
        isChangeConversation.current = true // 标记是切换对话操作
      }
      const conversationItem = JSON.parse(JSON.stringify(_conversationItem))
      onCloseGroupSetting() // 关闭群聊设置弹窗
      setChatInputValue('') // 清空输入框
      setCurrentConversation({}) // 清空当前会话
      setCurrentConversationUserInfo(null) // 清空当前会话用户信息
      // console.log('conversationItem', conversationItem)
      if (conversationItem?.unreadCount > 0) { // 标记会话已读
        ImService.markConversationMessageAsRead(conversationItem.conversationID)
      }
      // const currentInputValue = $trim(chatInputValue)
      // if (currentInputValue!==''){ 
      //   // 如果输入框有值,发送草稿
      //   await ImService.setConversationDraft(currentConversation.conversationID,currentInputValue)
      // } else if(!$isNull(currentConversation.draftText)){ // 清空草稿
      //   await ImService.setConversationDraft(currentConversation.conversationID,'')
      // }
      setCurrentConversation(conversationItem);
      // if (!$isNull(conversationItem.draftText)){
      //   setChatInputValue(conversationItem.draftText)
      // }
      console.log(`---------handleSelectConversation conversationItem, `, conversationItem);
      if (conversationItem.conversationType === 1) { // user
        const conversation: any = await ImService.getAdvancedHistoryMessageList(conversationItem.conversationID, "");
        console.log(`---------handleSelectConversation conversationItem, `, conversation?.messageList);
        setMessageList(conversation?.messageList);
        // 1.订阅用户在线状态
        ImService.subscribeUsersStatus([conversationItem.userID]).then((userStatus: any) => {
          if (userStatus) {
            const status = userStatus[0].status
            console.log('setCurrentConversationUserStatus status', status)
            setCurrentConversationUserStatus(status);
          }
        })
        // 2.获取用户企业-部门-职业信息
        getCurrentConversationUserInfo(conversationItem.userID)
      } else if (conversationItem.conversationType === 3) { // group
        // 获取群信息
        // const groupInfo = await ImService.getSpecifiedGroupsInfo(conversationItem.groupID)
        // console.log('groupInfo',groupInfo)
        const conversation: any = await ImService.getAdvancedHistoryMessageList(conversationItem.conversationID, "");
        console.log(`---------handleSelectConversation get group message list: `, conversation);
        setMessageList(conversation?.messageList);
      } else if (conversationItem.conversationType === 4) { // notifications
      }
      setIsLoadingMessageList(false)
      chatInputRef.current?.focus() // 输入框自动聚焦
    } catch (error) {
      setIsLoadingMessageList(false)
      console.log('error', error)
    }
  }

  /// 获取当前会话-用户类型，用户信息：企业-部门-职业信息
  const getCurrentConversationUserInfo = async (userID = currentConversationRef.current.userID) => {
    // 2.获取用户企业-部门-职业信息
    let userInfo: any = await api_get_user_profile({
      currentTenantId: activeCompanyId,
      "imUserList": [userID]
    }, true).catch(() => {
    })
    if (!userInfo) return
    userInfo = userInfo[userID] || null
    // console.log('userInfo', userInfo)
    setCurrentConversationUserInfo(userInfo);
  }

  // 处理生产环境IM 图片视频 url协议问题
  const removeHttpProtocol = (url: any) => {
    // if (process.env.NODE_ENV !=='production' || typeof url !=='string') return url
    return url.replace(/^https?:/i, '');
  }

  // 根据类型渲染消息列表信息
  const getContentFormMsg = (msgItem: any, isConversationDetails = false) => { // isConversationDetails 是否是消息详情
    try {
      // console.log(`---------getContentFormMsg : `, msgItem);
      const msg = isConversationDetails ? msgItem : JSON.parse(msgItem.latestMsg) // 最新消息
      // console.log('---msg',msg)
      // if (msgItem.draftText!=='' && msgItem.draftText !==undefined&& msgItem.draftText !==undefined){
      //   return (<div className="msg-draft"><span >[草稿]</span>{msgItem.draftText}</div>);
      // }
      const contentType = msg?.contentType
      let result: any = ''
      const lasestSenderNickname = msg.sendID === currentUserId || msgItem?.conversationType === 1 ? '' :
        (getUserName(msg.sendID, msg.senderNickname)) + ':' // 最新消息发送人
      // console.log('senderNickname',senderNickname)
      switch (contentType) {
        case 101:// 文本
          result = !isConversationDetails ? `${lasestSenderNickname} ${msg?.textElem?.content}` :
            <div className="msg-text">{msg?.textElem?.content}</div>
            ;
          break;

        case 102:// 图片
          const pictureElem = msg?.pictureElem
          // console.log('pictureElem',pictureElem)
          const sourcePicture = pictureElem?.sourcePicture.url
          result = !isConversationDetails ? `${lasestSenderNickname} [${t('chat.picture')}]` :
            (
              msg.isSubmiting === true ?
                (
                  <Flex className="msg-submiting" align="center" justify="center">
                    <Spin className="msg-spin" size="large" indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />} />
                  </Flex>
                )
                :
                <div onClick={() => onClickPreviewImage(sourcePicture)} className="msg-image">
                  <img src={pictureElem.snapshotPicture.url} alt="" />
                  {/* <div className="msg-image-view"><SvgIcon svgName="eye_open" /></div> */}
                </div>
            )
          break;

        case 103:
          result = `${lasestSenderNickname} [语音消息]`
          break

        case 104:// 视频
          const videoElem = msg?.videoElem
          // console.log('videoElem',videoElem)
          result = !isConversationDetails ? `${lasestSenderNickname} [${t('chat.video')}]` :
            (
              msg.isSubmiting === true ?
                (
                  <Flex className="msg-submiting" align="center" justify="center">
                    <Spin className="msg-spin" size="large" indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />} />
                    {/* {
                    msg.thumbnailUrl?
                    <div className="video-thumbnai">
                      <img src={msg.thumbnailUrl} alt="" />
                    </div>:null
                  } */}
                  </Flex>
                )
                :
                <div className="msg-video" onClick={() => viewVideo(videoElem)}>
                  <img src={videoElem?.snapshotUrl} alt="" />
                  <div className="duration">{videoElem?.duration ? VideoUtils.formatSeconds(videoElem?.duration) : ''}</div>
                  <img src={PlayIcon} className="icon_play" alt="" />
                </div>
            )
          break;
        case 105: //文件消息 
          const fileElem = msg?.fileElem
          // console.log('fileElem',fileElem)
          result = !isConversationDetails ? `${lasestSenderNickname} [${t('chat.file')}]${fileElem.fileName}` :
            (msg.isSubmiting === true ?
              (
                <Flex className="msg-submiting" align="center" justify="center">
                  <Spin className="msg-spin" size="large" indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />} />
                </Flex>
              )
              : <Flex className="msg-file" gap={15} onClick={() => onDownloadFile(fileElem.fileName, fileElem.sourceUrl)}>
                <Flex className="msg-file-icon" align="flex-end" justify="center">
                  <DownloadOutlined className="dl-icon" />
                  {t('chat.FILE')}
                </Flex>
                <Flex className="msg-file-text" vertical gap={4}>
                  <div>{fileElem.fileName}</div>
                  <div>{FileUtils.formatFileSize(fileElem.fileSize)}</div>
                </Flex>
              </Flex>)
          break;

        case 110: //自定义消息
          result = ''
          break;
        case 1201:// 通知：已经成为好友
          const notifyText = t('chat.start-chat')
          result = !isConversationDetails ? notifyText : (<Flex justify="center" className="msg-notify">{notifyText}</Flex>)
          break;

        case 1501: // 创建了群聊
          const detail1 = JSON.parse(msg?.notificationElem?.detail);
          // console.log('detail1',detail1)
          const createUserId = detail1?.group?.creatorUserID
          const createUserName = createUserId === currentUserId ? t('chat.you') : getUserName(createUserId, detail1?.opUser.nickname)
          const content1 = createUserName + `${t('chat.create-group-chat')}`
          // console.log('detail1',detail1)
          result = isConversationDetails ? <Flex className="msg-group-operate" align="center" justify="center" gap={6}>
            <span className="name">{createUserName}</span>
            <span>{t('chat.create-group-chat')}</span>
          </Flex> : content1;
          break;

        case 1504: // 退出群聊
          const detail5 = JSON.parse(msg?.notificationElem?.detail);
          // console.log('detail5',detail5)
          let quitUser = detail5.quitUser // TODO
          const isCurrentUser5 = msg.userID === currentUserId
          let quitUserName = isCurrentUser5 ? t('chat.you') : getUserName(msg.userID, quitUser.nickname)
          const quitText = `${t('chat.leave-group-chat', { name: isCurrentUser5 ? 've' : 's' })}`
          const content5 = `${quitUserName}${quitText}`
          result = isConversationDetails ? <Flex className="msg-group-operate" align="center" justify="center" gap={6}>
            <span className="name">{quitUserName} </span>
            <span>{quitText}</span>
          </Flex> : content5
          break;

        case 1508: // 踢出群聊
          const detail2 = JSON.parse(msg?.notificationElem?.detail);
          // console.log('detail2',detail2)
          let opUserName = detail2?.opUser?.nickname
          let opUserId = detail2?.opUser.userID
          opUserName = opUserId === currentUserId ? t('chat.you') : getUserName(opUserId, opUserName)
          let memberList: string[] = [];
          const memberIds = detail2?.kickedUserList?.map((member: any) => {
            memberList.push(member?.userID === currentUserId ? t('chat.you') : getUserName(member?.userID, member?.nickname))
            return member?.userID
          })
          const members = memberList.join(","); // 被踢的人
          const title = (memberIds.length === 1 && memberIds[0] === currentUserId) || memberIds.length > 1 ? 'ere' : 'as' // 人称 were 还是was
          const content2 = t('chat.kicked-out-group-chat', { who: members, opUser: opUserName, title: title })

          result = isConversationDetails ? <Flex className="msg-group-operate" align="center" justify="center" gap={6}
            dangerouslySetInnerHTML={{
              __html: t('chat.kicked-out-group-chat',
                {
                  who: (`<span class="name">${members}</span>`), opUser: `<span class="name">${opUserName}</span>`,
                  title: title
                }) as string,
            }}
          >
          </Flex> : content2
          break;

        case 1509: // 邀请加入群聊
          const detail3 = JSON.parse(msg?.notificationElem?.detail);
          // console.log('detail3',detail3)
          let memberList2: string[] = [];
          let opUserName2 = detail3?.opUser?.nickname
          let opUserId2 = detail3?.opUser.userID
          opUserName2 = opUserId2 === currentUserId ? t('chat.you') : getUserName(opUserId2, opUserName2)
          detail3?.invitedUserList?.map((member: any) =>
            memberList2.push((member?.userID === currentUserId ? t('chat.you') : getUserName(member?.userID, member?.nickname)))
          )
          const members2 = memberList2.join(" , ");
          // const content3 = `${opUserName2}邀请了${members2}加入群聊`
          const content3 = t('chat.join-group-chat', { opUser: opUserName2, users: members2 })
          result = isConversationDetails ? <Flex className="msg-group-operate" align="center" justify="center" gap={6}
            dangerouslySetInnerHTML={{
              __html: t('chat.join-group-chat',
                { users: (`<span class="name">${members2}</span>`), opUser: `<span class="name">${opUserName2}</span>` }) as string,
            }}
          >
          </Flex> : content3
          break;

        case 1520: //修改群名
          const detail4 = JSON.parse(msg?.notificationElem?.detail);
          let opUser = detail4?.opUser?.nickname
          opUser = detail4?.opUser.userID === currentUserId ? t('chat.you') : getUserName(detail4?.opUser.userID, opUser)
          const groupName = detail4?.group?.groupName
          const content4 = t('chat.change-group-name', { who: opUser, name: groupName })
          result = isConversationDetails ? <Flex className="msg-group-operate" align="center" justify="center" gap={6}
            dangerouslySetInnerHTML={{
              __html: t('chat.change-group-name',
                { who: (`<span class="name">${opUser}</span>`), name: groupName }) as string,
            }}
          >
          </Flex> : content4
          break;

        case 2101:// 通知：撤回
          result = "通知：撤回";
          break;

        default:
          result = `${t('chat.unknown-type-message')} ${msg?.contentType}`
          break
      }
      return result
    } catch (error) {
      console.log('error', error)
    }
  }

  // 判断会话是群聊，当前用户是否存在该群聊
  const getCurrentConversationStatus = () => {
    try {
      const _currentConversation = currentConversationRef.current
      const msg = JSON.parse(_currentConversation.latestMsg)
      if (_currentConversation.conversationType === 3 && msg.contentType === 1508) { // 用户被踢出群聊
        const detail = JSON.parse(msg?.notificationElem?.detail);
        const kickedUserIds = detail?.kickedUserList?.map((member: any) => {
          return member?.userID
        })
        // console.log('---isUserInCurrentGroup kickedUserIds', kickedUserIds)
        setIsCurrentConversationDisabled(kickedUserIds.includes(currentUserId))
      } else {
        setIsCurrentConversationDisabled(false)
      }
    } catch (error) {

    }
  }

  const getUserAvatar = (conversationItem: ConversationItemType, avatarSize = 32) => {
    const conversationType = conversationItem.conversationType
    let name = conversationItem.name || conversationItem || ''
    let avatar = null
    const target = usersInfoMapRef.current?.[conversationItem.userID] || {}
    let avatarUrl = target?.avatar
    if (currentUserId == conversationItem.userID) {
      name = currentUserName
      avatarUrl = currentUserAvatar
    }
    if (conversationType === 1) { // 单聊
      avatar = getAvatar(name || '', avatarUrl, conversationItem.userID ,avatarSize)
    } else {
      avatar = <img className="group-avatar" style={{ width: avatarSize + 'px', height: avatarSize + 'px' }} src={conversationItem.faceURL || groupDefaultAvatar} alt="" />
    }
    return avatar
  }

  // 左侧消息列表 的会话项
  const conversationItemView = (conversationItem: ConversationItemType) => {
    // console.log('左侧消息列表左侧消息列表左侧消息列表')
    // const conversationType = conversationItem.conversationType
    const avatar = getUserAvatar(conversationItem, 48)
    const name = currentUserId == conversationItem.userID ? currentUserName : conversationItem.name
    return (
      <div key={conversationItem.conversationID}
        className={classNames(["conversation-item", { active: currentConversation.conversationID === conversationItem.conversationID }])}
        id={conversationItem.conversationID}
        onClick={() => handleSelectConversation(conversationItem, true)}>
        {avatar}
        <div className="content">
          <span className="content-tittle">{conversationItem.userID === currentUserId ? `${t('chat.me')} (${name})` : conversationItem.name}</span>
          <span className="content-msg">{getContentFormMsg(conversationItem)}</span>
          {/* <ContentMsgComponent item={conversationItem}/> */}
        </div>
        <Flex className="content-right" vertical gap={8} align="flex-end">
          {/* 消息时间当日显示HH:MM；昨天及以前显示MM-DD */}
          <div className="c-date">{formatTimestamp(conversationItem.latestMsgSendTime)}</div>
          {conversationItem?.unreadCount > 0 ? <div className="c-unread">{conversationItem.unreadCount > 99 ? '99+' : conversationItem.unreadCount}</div>
            : null}
        </Flex>
      </div>
    )
  }

  const onClickPreviewImage = (url: any) => {
    setViewImageInfo({ visible: true, url: removeHttpProtocol(url) })
  }

  // 预览视频
  const viewVideo = (target: any) => {
    // console.log('target',target)
    setViewVideoInfo({ visible: true, url: removeHttpProtocol(target.videoUrl) })
  }

  const addSubmitStatus = (contentType: any,) => {
    const currentMessageList = JSON.parse(JSON.stringify(messageListRef.current))
    if (!currentMessageList[currentMessageList.length - 1].isSubmiting) {
      currentMessageList.push({
        sendID: currentUserId,
        contentType: contentType,
        isSubmiting: true,
        clientMsgID: Date.now()
      })
      setMessageList(currentMessageList)
    }
  }

  const removeSubmitStatus = () => {
    try {
      const currentMessageList = JSON.parse(JSON.stringify(messageListRef.current))
      delete currentMessageList[currentMessageList.length - 1].isSubmiting
      delete currentMessageList[currentMessageList.length - 1].thumbnailUrl
      // console.log('currentMessageList', currentMessageList)
      setMessageList(currentMessageList)
    } catch (error) {

    }
  }

  // 发送视频
  const onVideoSend = async (file: any, thumbnailFile: any) => {
    const currentConversation = currentConversationRef.current
    let fileType = file.name.substring(file.name.lastIndexOf(".") + 1);
    fileType = fileType.toLowerCase()
    // console.log('fileType', fileType)
    if (fileType !== 'mp4') {
      onImageSend(file, fileType)
      return
    }
    // console.log('currentConversation?.userID, file, thumbnailFile',currentConversation)
    // return
    if (currentConversation.conversationType === 1) {
      await ImService.sendVideoMessage('user', currentConversation?.userID, file, thumbnailFile).catch((error: any) => {
        console.log(error)
      })
    } else if (currentConversation.conversationType === 3) {
      await ImService.sendVideoMessage('group', currentConversation?.groupID, file, thumbnailFile).catch((error: any) => {
        console.log(error)
      })
    }
    removeSubmitStatus()
  }


  // 发送视频中
  const onVideoSubmiting = (isSubmiting: boolean, thumbnailUrl?: any) => {
    // console.log('isSubmiting',isSubmiting)
    // console.log('thumbnailUrl',thumbnailUrl)
    addSubmitStatus(104)
  }

  // 发送图片
  const onImageSend = async (file: any, fileType: any) => {
    //  png jpg jpeg gif 用图片上传，其他用文件上传
    const imageFormat = ['png', 'jpg', 'jpeg', 'gif']
    if (!imageFormat.includes(fileType)) {
      onFileSend(file)
      return
    }
    const currentConversation = currentConversationRef.current
    const type = currentConversation.conversationType === 1 ? 'user' : 'group'
    const targetId = type === 'user' ? currentConversation?.userID : currentConversation?.groupID
    addSubmitStatus(102)
    await ImService.sendPitcture(type, targetId, file);
  }

  // 发送文件
  const onFileSend = async (file: any) => {
    const currentConversation = currentConversationRef.current
    const type = currentConversation.conversationType === 1 ? 'user' : 'group'
    const targetId = type === 'user' ? currentConversation?.userID : currentConversation?.groupID
    await addSubmitStatus(105)
    await ImService.sendFile(type, targetId, file);
    removeSubmitStatus()
  }

  // 选择表情
  const onStickerUpdate = (emoji: any) => {
    const newInputValue = chatInputValue
    const { start, end } = chatInputSelection;
    const newText =
      newInputValue.slice(0, start) +
      emoji +
      newInputValue.slice(end);

    // console.log('newText',newText)
    setChatInputValue(newText)

    // 异步更新光标位置
    setTimeout(() => {
      const textarea = chatInputRef.current.resizableTextArea.textArea;
      const newPos = start + emoji.length;
      textarea.focus();
      textarea.setSelectionRange(newPos, newPos);
    }, 10);
  }

  // 创建群组
  const createGroup = async (groupName: string, memberUserIDs: any, isInvite: boolean, groupId: any) => {
    if (isInvite) {
      // console.log('groupId, memberUserIDs', groupId, memberUserIDs)
      const isOK = await ImService.inviteUserToGroup(groupId, memberUserIDs)
      if (!isOK) return false
      settingGroupRef?.current?.emit_refresh && settingGroupRef.current.emit_refresh()
      initData() // 刷新userMap
      return true
    } else {
      onCloseGroupSetting()
      isCreatingNewConversationRef.current = true
      const isOK = await ImService.createGroup(groupName, memberUserIDs)
      if (!isOK) return false
      initData() // 刷新userMap
      return true
    }

  }

  // 与用户发起单聊
  const onCreateOneConversation = (targetUser: any, isInvite: boolean) => {
    // console.log('currentConversation', currentConversation)
    // console.log('targetUser', targetUser)
    let existUser = allConversationList.filter((i: ConversationItemType) => i.userID === targetUser.imUserId)
    setMessageList([])
    // if (isInvite) {
    onCloseGroupSetting()
    // }
    if (existUser.length > 0) { // 会话列表存在该用户，切换到当前会话为该用户
      // console.log('---existUser', existUser)
      handleSelectConversation(existUser[0])
    } else { // 创建临时联系
      isCreatingNewConversationRef.current = true
      const isOK = ImService.sendCustomMessage('user', targetUser.imUserId, { data: 'CustomConnectionMessage', extension: '', description: 'Make a connection' })
      if (!isOK) return false
      initData() // 刷新userMap
    }
  }

  // 点击群组设置
  const onClickGroupSetting = async () => {
    if (currentConversation.conversationType !== 3) {
      return;
    }
    // console.log('currentConversation',currentConversation)
    const { showName: groupName, groupID: groupId } = currentConversation
    const avatar = getUserAvatar(currentConversation, 48)
    setSettingeGroupResource({ visible: true, groupId, groupName, groupAvatar: avatar })
  }

  // 聊天会话头部-点击加入群组
  const onClickConversationAddGroup = async (_currentConversation: any) => {
    // console.log('currentConversation', _currentConversation)
    if (_currentConversation.conversationType === 3) { // 群聊需要先获取成员IDs
      // console.log('isGetGroupLoadingRef.current',isGetGroupLoadingRef.current)
      if (isGetGroupLoadingRef.current === true) return
      isGetGroupLoadingRef.current = true
      const groupId = _currentConversation.groupID
      let memberList: any = await ImService.getGroupMemberList(groupId, 0, 1000).catch(() => {
        isGetGroupLoadingRef.current = false
      });
      isGetGroupLoadingRef.current = false
      if (!memberList) return
      const groupExistUserIds = memberList.map((i: any) => i.userID)
      // console.log('groupExistUserIds',groupExistUserIds)
      setCreateGroupResource({ visible: true, groupedUsers: groupExistUserIds, groupId: groupId })
    } else {//单聊
      setCreateGroupResource({ visible: true, singleUser: { userID: _currentConversation.userID, userName: _currentConversation.name, avatar: _currentConversation.avatar } })
    }
  }

  // 群设置 关闭
  const onCloseGroupSetting = () => {
    setSettingeGroupResource({ visible: false, groupId: null, groupName: '', groupAvatar: null })
  }

  // 群聊邀人
  const onInviteUser = (groupId: any, groupExistUserIds: any[]) => {
    // console.log('groupExistUserIds', groupExistUserIds, groupId)
    setCreateGroupResource({ visible: true, groupedUsers: groupExistUserIds, groupId: groupId })
  }

  // 下载文件
  const onDownloadFile = async (fileName: any, url: string) => {
    downloadFile(fileName, removeHttpProtocol(url))
  }
  // 消息列表滚动置底 
  const ScrollMessageToBottom = () => {
    try {
      if (scrollTimer.current) clearTimeout(scrollTimer.current)
      // 等待内容加载后，滚动到底部
      if (isChangeConversation.current) {
        setisScrolling(true)
        isChangeConversation.current = false
      }
      scrollTimer.current = setTimeout(() => {
        const target = messageListConRef?.current
        // console.log('target',target)
        if (target) {
          let scrollTop = target.scrollHeight
          target.scrollTop = scrollTop
        }
        setisScrolling(false)
      }, 100) // 适当增加延迟确保兼容性

      // 左侧会话列表 滚动到目标元素
      if (!currentConversationRef.current?.conversationID) return
      const childElement: any = document.querySelector(`#${currentConversationRef.current.conversationID}`);
      childElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    } catch (error) {
      console.log(error)
    }
  }

  // 输入框Selection
  const handleChatInputSelection = () => {
    // console.log('selectionEnd',chatInputRef.current)
    if (chatInputRef.current) {
      const target = chatInputRef.current.resizableTextArea.textArea
      // console.log('selectionStart',target?.selectionStart)
      setChatInputSelection({
        start: target?.selectionStart || 0,
        end: target?.selectionEnd || 0
      });
    }
  }

  // 输入框值改变
  const onChangeChatInputValue = (e: any) => {
    const inputValue = e.target.value;
    // console.log(`---------onChangeMsgInput, ${inputValue}`);
    setChatInputValue(inputValue)
    setHasChatInputValue(inputValue?.trim() !== "")
  }

  // 输入框KeyDown
  const handleChatInputKeyDown = (e: any) => {
    // console.log('allConversationList',allConversationList)
    // handleSendText(e)
    if (e.shiftKey && e.keyCode === 13) { // shift+enter换行
      return;
    } else if (e.keyCode === 13) { // enter发送
      e.preventDefault();
      const inputValue = e.target.value;
      if (inputValue?.trim() === "") return
      handleSendText(e);
    }
  }

  // 发送文本
  const handleSendText = async (e: any) => {
    const inputValue = chatInputValue;
    // console.log('inputValue',chatInputValue);
    // return
    let targetId = null
    if (currentConversation.conversationType === 1) {
      targetId = currentConversation?.userID
      await ImService.sendTextMessageToUser(targetId, inputValue);
    } else if (currentConversation.conversationType === 3) {
      targetId = currentConversation?.groupID
      await ImService.sendTextMessageToGroup(targetId, inputValue);
    }
    // const conversationItem = await ImService.getOneConversation(targetId, currentConversation.conversationType)
    // console.log('当前最新消息conversationItem',conversationItem)
    // changeConversationList([conversationItem])
    setChatInputValue('')
  }

  // 新增操作
  const handleCreate = (e: any) => {
    if (e.key === 'createGroup') { // 添加群聊
      setCreateGroupResource({ visible: true, groupedUsers: null, groupId: null })
    } else if (e.key === 'addFriend') { // 添加好友
      setAddFriendResource({ visible: true })
    }
  }

  // 用户名片-发起单聊
  const onSendMessageToUser = (userId: any) => {
    const targetUser = { imUserId: userId }
    // console.log('targetUser', targetUser)
    onCreateOneConversation(targetUser, false)
  }

  const onCloseAddFriend = () => {
    setAddFriendResource({ visible: false })
  }

  // 新增菜单
  const renderTopMenu = () => {
    const items: MenuProps['items'] = [
      getMenuItem(t(["chat.add-contact"]), "addFriend", <SvgIcon svgName="icon_new_user" />), // 添加好友
      getMenuItem(t(["chat.create-group"]), "createGroup", <SvgIcon svgName="icon_new_group" />), // 添加群聊
    ]

    return <Menu onClick={(e) => handleCreate(e)} style={{ minWidth: 200 }} items={items} selectedKeys={[props.globalLang]} />
  }

  // 聊天框 头部用户信息：企业-部门-职位
  const renderHeaderUserPostInfo = () => {
    try {
      // console.log('currentConversationUserInfo',currentConversationUserInfo)
      let dom = ''
      const userInfo = currentConversationUserInfo
      if (userInfo) {
        if (userInfo.tenantFullName) { // 企业名称，后端接口只返回一个
          dom += userInfo.tenantFullName
        }
        if (userInfo.departments && userInfo.departments.length > 0) { // 部门，参照钉钉 获取最后一个
          dom += ` - ${userInfo.departments[userInfo.departments.length - 1]['name']}`
        }
        if (userInfo.positions && userInfo.positions.length > 0) { // 职位，获取最后一个
          dom += ` | ${userInfo.positions[userInfo.positions.length - 1]['name']}`
        }
      }
      return dom
    } catch (error) {
      console.log(error)
    }
  }

  // 聊天框 头部用户信息
  const renderHeaderUserInfo = () => {
    // console.log('currentConversation',currentConversation)
    const name = currentUserId == currentConversation.userID ? currentUserName : currentConversation.name
    let avatar = getUserAvatar(currentConversation, 32)
    return (<Flex gap={8} className="conversation-tittle-user" align="center">
      {
        currentConversation.conversationType === 1 ?
          <UserInfoCard onSendMessageToUser={onSendMessageToUser} placement="rightBottom" userInfo={{ userID: currentConversation.userID, userName: name, status: currentConversationUserStatus }}>
            <div>{avatar}</div>
          </UserInfoCard> : avatar
      }
      <Flex vertical className="user-info" justify="center">
        <Flex className="user-name-status" gap={9} align="center">
          <div className="user-name">{currentConversation.userID === currentUserId ? `${t('chat.me')} (${name})` : name}</div>
          {// 在线状态0 表示离线，1 表示在线
            currentConversation.conversationType === 1 && currentConversationUserStatus !== null ? <div className="user-status">{
              <SvgIcon svgName={currentConversationUserStatus === 1 ? 'icon_inline' : "icon_outline"} />
            }</div>
              : null}
        </Flex>
        <div className="user-post">
          {renderHeaderUserPostInfo()}
        </div>
      </Flex>
    </Flex>)
  }

  // 用户头像
  const getAvatar = (name: any, src: any, userId:any, size?: any) => {
    return <UserAvatar className="user-avatar" size={size} name={name} src={src} userId={userId} />
  }

  // 聊天框 消息中用户信息
  const renderMessageItem = (msgItem: MessageItemType) => {
    // console.log('msgItem',msgItem)
    let name = getUserName(msgItem.sendID, msgItem.senderNickname)
    const target = usersInfoMapRef.current?.[msgItem.sendID]
    let avatarUrl = target && target?.avatar ? target.avatar : null
    if (msgItem.sendID == currentUserId) { // 当前用户
      // console.log('currentUserAvatar',currentUserAvatar)
      avatarUrl = currentUserAvatar
    }
    const avatar = <UserInfoCard onSendMessageToUser={onSendMessageToUser} placement="right"
      userInfo={{ userID: msgItem.sendID, userName: name }}>
      <div>
        {getAvatar(name || '', avatarUrl, msgItem.sendID )}
      </div>
    </UserInfoCard>
    return (
      <Flex className="msg-detail" gap={12} align="flex-start">
        {
          [101, 102, 103, 104, 105, 115].includes(msgItem.contentType) ?
            <>
              {avatar}
              <Flex className="msg-detail-right" vertical align="flex-start" gap={4}>

                <Flex className="msg-user" align="center" gap={8}>
                  <div className="user-name">{name}</div>
                  {/* {msgItem.sendTime ? <div className="send-time">{parseTime(msgItem.sendTime, "{y}/{m}/{d} {h}:{i}")}</div> : null} */}
                  {msgItem.sendTime ? <div className="send-time">{FormatTimestamp(msgItem.sendTime, t)}</div> : null}
                </Flex>
                {/* <div >{FormatTimestamp(new Date(2025, 4, 11).getTime(),t)}</div> */}
                <div className="msg-content">{getContentFormMsg(msgItem, true)}</div>
              </Flex>
            </>
            : <div className="msg-content no-avatar">{getContentFormMsg(msgItem, true)}</div>
        }
      </Flex>)
  }
  return (
    <div className="chats">
      {/* 侧边栏-会话列表 */}
      <Flex className="left-area" vertical>
        <Flex className="title" justify="space-between">
          <span>{t('chat.title')}</span>
          <Dropdown className="chats-new" placement="topRight" overlayClassName="chats-new-dropdown" dropdownRender={() => renderTopMenu()} arrow={false}>
            <Flex className="chats-new-icon" align="center" justify="center"><SvgIcon svgName="icon_new" /></Flex>
          </Dropdown>
        </Flex>
        <div className="chats-conversation-list">
          {
            allConversationList?.map((conversationItem: any) => {
              return conversationItemView(conversationItem);
            })
          }
          {
            !allConversationList || allConversationList.length === 0 ?
              <Empty className="page-empty full" image={null} description={
                <>
                  <div>{t('common.no-data')}</div>
                  <div></div>
                </>}
              /> : null
          }
        </div>
      </Flex>
      {/* 会话-聊天详情 */}
      <Flex className="right-area" vertical>
        {
          currentConversation ?
            <>
              <Flex className="conversation-tittle" gap={8} align="center">
                {renderHeaderUserInfo()}
                <Flex className="conversation-tittle-operate">
                  {
                    !isCurrentConversationDisabled && [1, 3].includes(currentConversation.conversationType) ?
                      <>
                        {
                          isGetGroupLoadingRef.current ?
                            <Flex align="center" justify="center"><LoadingOutlined /></Flex>
                            :
                            <Flex onClick={() => onClickConversationAddGroup(currentConversation)} align="center" justify="center">
                              <SvgIcon svgName="icon_new_user">
                              </SvgIcon></Flex>
                        }
                      </>
                      : null
                  }
                  {
                    !isCurrentConversationDisabled && currentConversation.conversationType === 3 ?
                      <Flex onClick={onClickGroupSetting} align="center" justify="center"><SvgIcon svgName="icon_settings"></SvgIcon></Flex>
                      : null
                  }
                </Flex>

              </Flex>
              <div ref={messageListConRef} className="conversation-content-area">
                {
                  <>
                    <Spin className={classNames(["content-area-spin", { 'show-loading': isScrolling || isLoadingMessageList }])} size="large" />
                    <Flex className="conversation-content" vertical gap={20}>
                      {messageList ? messageList.map((msgItem: any, msgIndex: number) =>
                        <Flex className={"conversation-msg"} key={msgItem.clientMsgID} id={msgIndex === messageList.length - 1 ? "conversationMsgLast" : ''} gap={12}>
                          {/* 头像 */}
                          {/* 名称 + 信息详情 */}
                          {renderMessageItem(msgItem)}
                          {/* <Flex className="msg-item" gap={12}>
                          <div>{getContentFormMsg(msgItem, true)}</div>
                        </Flex> */}
                        </Flex>
                      ) : null}
                    </Flex>
                  </>
                }
              </div>
              {
                isCurrentConversationDisabled ?
                  <Flex className="conversation-disabled" align="center" justify="center" gap={8}>
                    <SvgIcon svgName="icon_info2" />
                    <span>{t('chat.quit-group-tip')}</span>
                  </Flex>
                  :
                  <Flex className="conversation-send" vertical gap={4}>
                    <TextArea ref={chatInputRef}
                      maxLength={3000} placeholder={t('chat.type-a-message') as string}
                      onChange={(e: any) => onChangeChatInputValue(e)}
                      onClick={handleChatInputSelection}
                      onSelect={handleChatInputSelection}
                      onKeyUp={handleChatInputSelection}
                      onKeyDown={(e: any) => handleChatInputKeyDown(e)}
                      value={chatInputValue}
                      variant="borderless"
                      autoSize={{ minRows: 2, maxRows: 7 }}
                    />
                    <Flex className="send-operate" gap={8}>
                      <StickerSelector onUpdate={onStickerUpdate} />
                      <ImageSend onUpdate={onImageSend} />
                      <VideoSend onUpdate={onVideoSend} onSubmiting={onVideoSubmiting} />
                      <FileSend onUpdate={onFileSend} />
                      <Flex onClick={(e) => handleSendText(e)} className={classNames(["send-text", "operate-item", { "send-text-disabled": !hasChatInputValue }])} align="center" justify={"center"}>
                        <SvgIcon svgName="icon_send" />
                      </Flex>
                    </Flex>
                  </Flex>
              }
            </>
            : null
        }
      </Flex>

      {/* 视频播放 */}
      {viewVideoInfo.visible ? <VideoPlayer url={viewVideoInfo.url} onClose={() => setViewVideoInfo({ visible: false, url: '' })} /> : null}
      {/* 查看图片全屏 */}
      {viewImageInfo.visible ? <ViewImage url={viewImageInfo.url} isCenter={true} onClose={() => setViewImageInfo({ visible: false, url: '' })} /> : null}
      {/* 新增群组/群组邀人 */}
      {createGroupResource.visible ?
        <CreateGroup onClose={() => setCreateGroupResource({ visible: false, groupedUsers: null })}
          resource={{
            currentUserName,
            currentUserAvatar,
            groupedUsers: createGroupResource.groupedUsers,
            groupId: createGroupResource.groupId,
            singleUser: createGroupResource.singleUser
          }}
          onCreateGroup={createGroup}
          onCreateOneConversation={onCreateOneConversation}
        />
        : null}
      {/*设置群 */}
      {settingGroupResource.visible ? <SettingGroup ref={settingGroupRef} resource={settingGroupResource} usersInfoMap={usersInfoMap} onClose={() => onCloseGroupSetting()}
        onInviteUser={onInviteUser} onSendMessageToUser={onSendMessageToUser} /> : null}
      {/*添加好友 */}
      {addFriendResource.visible ? <AddFriend ref={settingGroupRef} onClose={() => onCloseAddFriend()}
        onSendMessageToUser={onSendMessageToUser} /> : null}
    </div>
  )
}

// // 输入框
// const MyTextArea = (props:any)=>{
//   const chatInputRef = useRef<any>(null)
//   const { t } = useTranslation()
//   const {onUpdate, onSend,onUpdateSelection,onUpdateHasChatInputValue}= props

//   const [chatInputValue, setChatInputValue] = useState<string>("") //对话输入框
//   const [chatInputSelection, setChatInputSelection] = useState({ start: 0, end: 0 });
//   const [hasChatInputValue, setHasChatInputValue] = useState(false); // 输入框是否有输入值

//   useEffect(() => {
//     setHasChatInputValue(chatInputValue?.trim() !== "")
//   }, [chatInputValue])

//   // 输入框值改变
//   const onChangeChatInputValue = (e: any) => {
//     const inputValue = e.target.value;
//     // console.log(`---------onChangeMsgInput, ${inputValue}`);
//     setChatInputValue(inputValue)
//     var _hasChatInputValue = inputValue?.trim() !== ""
//     setHasChatInputValue(_hasChatInputValue)
//     onUpdateHasChatInputValue(_hasChatInputValue)
//     onUpdate(inputValue)
//   }

//   // 输入框KeyDown
//   const handleChatInputKeyDown = (e: any) => {
//     // console.log('allConversationList',allConversationList)
//     // handleSendText(e)
//     if (e.shiftKey && e.keyCode === 13) { // shift+enter换行
//       return;
//     } else if (e.keyCode === 13) { // enter发送
//       e.preventDefault();
//       const inputValue = e.target.value;
//       if (inputValue?.trim() === "") return
//       onSend(e);
//     }
//   }
//     // 输入框Selection
//     const handleChatInputSelection = () => {
//       // console.log('selectionEnd',chatInputRef.current)
//       if (chatInputRef.current) {
//         const target = chatInputRef.current.resizableTextArea.textArea
//         // console.log('selectionStart',target?.selectionStart)
//         const selection = {
//           start: target?.selectionStart || 0,
//           end: target?.selectionEnd || 0
//         }
//         setChatInputSelection(selection);
//         onUpdateSelection(selection)
//       }
//     }

//   return <TextArea ref={chatInputRef}
//     maxLength={3000} placeholder={t('chat.type-a-message') as string}
//     onChange={(e: any) => onChangeChatInputValue(e)}
//     onClick={handleChatInputSelection}
//     onSelect={handleChatInputSelection}
//     onKeyUp={handleChatInputSelection}
//     onKeyDown={(e: any) => handleChatInputKeyDown(e)}
//     value={chatInputValue}
//     variant="borderless"
//     autoSize={{ minRows: 2, maxRows: 7 }}
//   />
// }

const mapStateToProps = (state: any) => ({
  userInfo: state.app.userInfo,
  activeCompanyId: state.app.activeCompanyId,
  userProfile: state.app.userProfile,
  userProfilePersonal: state.app.userProfilePersonal,
  isPrivate: state.app.isPrivate,
});

const mapDispatchToProps = (dispatch: any) => ({
})

export default connect(mapStateToProps, mapDispatchToProps)(Chats);