import {connect} from "react-redux";
import { useTranslation } from 'react-i18next';
import React,{ useState, useRef, useEffect, forwardRef, useImperativeHandle } from "react";
import { useLocation,useNavigate } from 'react-router-dom';
import './index.scss'
import SvgIcon from '@components/svgicon'; // 根据实际路径引入
import classnames from "classnames";
import { api_get_ai_list } from '@/api/robot'
import classNames from "classnames";
import { Flex } from "antd";

// 侧边菜单-机器人
const SideMenuComponent = forwardRef((props: any, ref:React.LegacyRef<HTMLDivElement> | any) => {
    // 自定义暴露给父组件的实例值
    useImperativeHandle(ref, () => ({
        // 初始化
        emit_init() {
        },
        // 获取机器人列表数据
        emit_refresh_robotlist() {
            // console.log('获取机器人列表数据')
            init(false)
        }
    }))
    const { t } = useTranslation();
    const navigate = useNavigate();
    const location = useLocation();
    // 获取传递的 state
    const tab = location.state?.tab;
    // console.log('tab',tab)
    const {activeCompanyId,isPrivate}= props
    // console.log('activeCompanyId:',activeCompanyId)

    const containerRef = useRef(null)
    const shareRef = useRef(null)
    const [homebotList, setHomebotList] = useState<any>([]); // homebot机器人
    const [activeRobot, setActiveRobot] = useState<any>({}); // 当前选中的机器人
    const [robotTypeList, setRobotTypeList] = useState<any>([]); // 机器人类型列表（包括 我的机器人、与我共享的机器人）
    const [activeMenu, setActiveMenu] = useState<any>(null); // 当前选中的菜单
    
    /**method **/
    
    // 初始化 
    useEffect(() => {
    }, [])


    const init = async(judgeManage=true) => {
        // console.log('-------机器人init isPrivate',isPrivate)
        const res:any = await api_get_ai_list({},isPrivate).catch((err:any)=>{
            console.log(err)
            props?.onInitError && props?.onInitError()
        })
        if (!res) {
            props?.onInitError && props?.onInitError()
            // return
        }
        // console.log('res', res)
        const resource = res || {}
        let {homeBotList=[],myBotList=[],sharedWithMeRobotList=[]}:any = resource

        // 个人空间：Homebot；企业：Homebot、my robot、share bot
        setHomebotList(homeBotList)
        // 个人空间
        if (activeCompanyId+''==='0'){
            const activeBot= [...homeBotList][0]
            setRobotTypeList([])
            setActiveRobot(activeBot)
            setActiveMenu(null)
            props.onInit(activeBot) // 通知初始化bot
            props.onUpdateContent(activeBot)
            return
        }
        // 企业-我的机器人
        const myRobotList = {
            type: "user",
            title: 'robot.my-robot',
            isToggle: true, // 默认展开
            list: myBotList,
            emptyText: 'no-my-bot'
        }
        //  企业-与我共享的机器人
        const sharedRobotList = {
            type: "share",
            title: 'robot.share-robot',
            isToggle: sharedWithMeRobotList.length>0, // 默认展开
            list: sharedWithMeRobotList,
            emptyText: 'no-share-bot'
        }
        //  企业-助手管理
        const manageBot = {
            type: "manage",
            title: 'robot-manage.label',
            noToggle: true,
            icon: <SvgIcon svgName="icon_robot_manage"/>,
            way:'router'
        }
        // console.log('homebot', homebot)
        const allList = [...homeBotList,...myBotList, ...sharedWithMeRobotList]
        const activeBot = allList.length>0? allList[0]:{}
        // console.log('init tab',tab)
        setHomebotList(homeBotList)
        setRobotTypeList([myRobotList,sharedRobotList,manageBot])
        if (!judgeManage){
            // props.onInit(activeBot) // 通知初始化
            return
        }
        if (tab ==="manage"){
            onUpdateMenu({...manageBot})
        }else{
            props.onInit(activeBot) // 通知初始化
            setActiveRobot(activeBot)
            setActiveMenu(null)
            props.onUpdateContent(activeBot)
        }
        
    }

    // 更新机器人菜单
    const onUpdateRobot=(activeBot:any)=>{
        setActiveRobot(activeBot)
        setActiveMenu(null)
        props.onUpdate(activeBot)
        props.onUpdateContent(activeBot)
    }

    // 更新非机器人菜单
    const onUpdateMenu=(activeMenu:any)=>{
        setActiveMenu(activeMenu.type)
        setActiveRobot({})
        props.onUpdateContent(activeMenu)
    }

    // 选择机器人
    const handleSelectRobot = (target?:any)=>{
        onUpdateRobot(target)
    }

    // 展开伸缩菜单
    const handleToggleMenu = (target: any) => {
        if (target?.way !== 'router'){
            setRobotTypeList((prev: any) => 
            prev.map((i:any) => i.type === target.type ? {...i,isToggle: !i.isToggle} : i))
            props.onUpdateContent(target)
        } else{
            // setActiveMenu(target.type)
            // setActiveRobot({})
            // props.onUpdateContent(target)
            onUpdateMenu({...target})
        }
    };

    // 滚动
    const scrollSideMenu = ()=>{
        try {
            // 计算子元素距离其父容器顶部的偏移量
            const childElement:any = shareRef?.current
            // console.log('childElement', childElement)
            const elementTop = childElement?.offsetTop;
            const containElement:any = containerRef?.current
            const containerTop = containElement?.scrollTop;
            if (!elementTop || !containerTop) return
            const distanceToTop = elementTop - containerTop - 150;
            // 如果子元素的距离顶部小于等于0，说明子元素已经滑到顶部
            if (distanceToTop <= 0) {
            //   console.log('子元素已滑到顶部');
              childElement.style.top='36px'
              childElement.style.bottom='auto'
            } else {
              childElement.style.top='auto'
              childElement.style.bottom='36px'
            }
          } catch (error) {
            
          }
    }

    return (
        <div className="side-menu">
            <div className="side-menu-title">
                <span>{t('app.create-dialog')}</span>
                {/* <SearchOutlined /> */}
            </div>
            {
                homebotList ?
                homebotList.map((hItem:any,hIndex:number)=>{
                    return(
                        <div key={hItem.robot_id+''+hIndex} onClick={()=> handleSelectRobot(hItem)} 
                        className={classnames(["menut-item-content", "home-bot",{active: activeRobot.robot_id==hItem.robot_id}])}>
                            <SvgIcon svgName="homebot_logo"/>
                            <span>{hItem.name}</span>
                        </div>
                    )
                }):null
            }
            <div className="menu-item-container" ref={containerRef} onScroll={()=> scrollSideMenu()}>
                {
                    robotTypeList.map((typeItem: any, index:number) => {
                        return(
                            <React.Fragment key={typeItem.type + ''+index}>
                                <div
                                    ref={typeItem.type==="share"?shareRef:null} 
                                    className={classnames(["menu-item-title",typeItem.type,{active: activeMenu === typeItem.type,'no-toggle':typeItem.noToggle} ])} 
                                    onClick={()=> handleToggleMenu(typeItem)}>
                                        <span className={classnames(["menu-item-title-text"])}>
                                            {typeItem.icon?typeItem.icon:null}
                                            <span>{t(typeItem.title)}</span>
                                        </span>
                                        {
                                            !typeItem.noToggle?
                                            <span  className={classNames(['toggle-icon',typeItem.isToggle? 'toggle' :'close'])}>
                                                <SvgIcon svgName="arrow_down_line"/>
                                            </span>
                                            :null
                                        }
                                </div>
                                {
                                    !typeItem.noToggle?
                                    <div className={classnames(["menut-item-list", {"is-close" : !typeItem.isToggle }])}>
                                        {
                                            typeItem.list.map((robotItem: any) => {
                                                return(
                                                <div key={typeItem.type + 'bot' + robotItem.robot_id} className={classnames(["menut-item-content",{active: activeRobot.robot_id == robotItem.robot_id}])} onClick={()=> handleSelectRobot(robotItem)}>
                                                    <SvgIcon svgName="bot_default"></SvgIcon>
                                                    <span>{robotItem.name}</span>
                                                </div>
                                                )
                                            })
                                        }
                                        {
                                            !typeItem.list || typeItem.list.length===0?
                                            <Flex className="no-data" align="center" justify="center">
                                                {t(typeItem.emptyText)}
                                            </Flex>:null
                                        }
                                    </div>
                                    :null
                                }
                            </React.Fragment>
                        )
                    })
                }
            </div>
            {/* <UserMenu/> */}
        </div>
    );
});
const mapStateToProps =(state:any) =>({
  userInfo: state.app.userInfo,
  activeCompanyId: state.app.activeCompanyId
})
const mapDispatchToProps = (dispatch: any) => ({
    // dispatch_api_: (data:any) => dispatch(dispatch_api_(data))
  })

// 使用 connect 连接 Redux store，并启用 forwardRef
const ConnectedCounter = connect(mapStateToProps, mapDispatchToProps, null, { forwardRef: true })(SideMenuComponent);
export default ConnectedCounter;

