import { connect } from "react-redux";
import React, { useState, useRef, useEffect } from "react";
import { Flex } from 'antd'
import { useTranslation } from 'react-i18next';
// import SvgIcon from '@components/svgicon';
import SideMenu from './sideMenu';
import Announcements from './announcements';
import ManageAnn from './manage-ann';

import './index.scss'
import classNames from "classnames";

// 消息中心
const InformationHub = (props: any) => {
    const { t } = useTranslation();
    const sideMenuRef = useRef<any>(null)
    const [activeMenu, setActiveMenu] = useState<any>(null); // 当前选中的菜单


    // 菜单切换
    const onChangeMenu = (key: any) => {
        setActiveMenu(key)
    }

   

    return (
        <Flex className="infohub-page">
            <SideMenu ref={sideMenuRef} activeMenu={activeMenu}
                onChangeMenu={onChangeMenu}
                />
            { // 公告
                 activeMenu === 'Ann' ?<div className={classNames(['con'])}>
                    <Announcements />
                </div>
                :null
            }
            { // 公共管理
                activeMenu === 'ManageAnn' ?
                    <ManageAnn  />
                    : null
            }
        </Flex>
    )
}
const mapStateToProps = (state: any) => ({
    companies: state.app.companies,
    activeCompanyId: state.app.activeCompanyId
})
const mapDispatchToProps = (dispatch: any) => ({
    // dispatch_api_: (data:any) => dispatch(dispatch_api_(data))
})
export default connect(mapStateToProps, mapDispatchToProps)(InformationHub);