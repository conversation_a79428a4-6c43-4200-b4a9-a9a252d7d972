import React, { useState, useEffect, forwardRef, useRef, useImperativeHandle } from "react";
import { connect } from "react-redux";
import { Modal,Flex } from "antd";
import classNames from "classnames";
import i18next from "i18next";

import './index.scss'
import Svgicon from "@/components/svgicon";

// 向量详情
const EmbeddingDetail = forwardRef((props: any, ref) => {
  /** 自定义暴露给父组件的实例值 **/

  /* props */
  const isVisible = props.visible
  const resource = props.resource
  console.log('resource', resource)
  /* state */


  /* method */
  // 取消
  const handleIsModalCancel = () => {
    props.onClose()
  }

  useEffect(() => {
  }, [])
  return (
    <>
      <Modal title={i18next.t("robot-manage.embedding-search-detail")}
        wrapClassName="embedding-detail-modal"
        width={500}
        open={isVisible}
        footer={null}
        maskClosable={false}
        onCancel={handleIsModalCancel}
        centered
      >
        <Flex className="ed-con" vertical>
          <div className="ed-title">{resource.documentName}</div>
          <div className="ed-content">{resource.chunkText}</div>
          <Flex className="ed-tag" align="center" gap={20}>
            <Flex align="center" gap={10}>
              <Svgicon svgName="text" />
              <span>{resource.length}</span>
            </Flex>
            <Flex align="center" gap={10}>
              <Svgicon svgName="hit" />
              <span>{resource.tokenCount}</span>
            </Flex>
            {/* <Flex align="center" gap={10}>
              <Svgicon svgName="token" />
              <span></span>
            </Flex> */}
          </Flex>
        </Flex>
      </Modal>
    </>
  );
});
const mapStateToProps = (state: any) => ({
});
const mapDispatchToProps = (dispatch: (e: any) => void) => ({
});
export default connect(mapStateToProps, mapDispatchToProps)(EmbeddingDetail);