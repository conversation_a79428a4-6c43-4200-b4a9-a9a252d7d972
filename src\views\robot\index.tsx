import React from "react";
import { connect } from "react-redux";
import classnames from "classnames";
import SideMenu from "./components/sideMenu";
import RobotManage from "@/views/robot-manage";
import BotsMarketplace from "@/views/bots-marketplace";
import OrphanedManage from "@/views/orphaned-manage";

import { change_app_globalLang, change_app_userInfo } from "../../store/action";
import { ContextState } from "./index_type"
import RobotChat from './robotChat'


class Robot extends React.Component<any, ContextState> {
  public sideMenuRef: React.LegacyRef<HTMLDivElement> | any;
  public RobotChatRef: React.LegacyRef<HTMLDivElement> | any;

  constructor(props: any) {
    super(props);
    this.sideMenuRef = React.createRef();
    this.RobotChatRef = React.createRef();
    this.state = {
      rightAreaId: 'default',
      pageOriginId: 'ROBOT'
    }
  }

  // 机器人切换
  onToggleDialog = async (_d: any) => {
    // console.log('this.RobotChatRef?.current', this.RobotChatRef?.current)
    this.RobotChatRef?.current?.$_handleToggleDialog(_d)
  }


  // 获取第一个机器人会话数据
  onInitFirstRobot = (_activeRobot: any, _activeCompanyId: any) => {
    this.RobotChatRef?.current?.$_handleInitFirstRobot(_activeRobot, _activeCompanyId)
  }

  // 初始化机器人数据失败
  onInitError = () => {
    this.RobotChatRef?.current?.$_handleInitError()
  }

  onUpdateSideMenuContent = (target: any, isPersonal: boolean) => {
    // console.log('target',target)
    if (['manage', "botsMarketplace", "orphaned"].includes(target?.type)) {
      // this.setState({ currentDialog: { ...this.state.currentDialog, robotId: '' } })
      this.RobotChatRef?.current?.$_updateCurrentDialog({robotId: ''})
      const map: any = { manage: "robotManage", botsMarketplace: 'botsMarketplace', orphaned: "orphaned" }
      this.setState({ rightAreaId: map[target?.type] })
    } else {
      this.setState({ rightAreaId: 'default' },()=>{
        if (!isPersonal) this.RobotChatRef?.current?.$_handleToggleDialog(target)
      })
    }
  }

  handleClickHomeWrapper = () => {
    this.RobotChatRef?.current?.$_handleClickHomeWrapper()
  }

  // robotmanage通知更新机器人类别
  onUpdateRobotList = () => {
    // console.log('onUpdateRobotList',this.sideMenuRef)
    this.sideMenuRef?.current?.emit_refresh_robotlist && this.sideMenuRef?.current.emit_refresh_robotlist()
  }

  render() {
    const { rightAreaId } = this.state;
    return (
      <div className={"robot-wrapper"} onClick={this.handleClickHomeWrapper}>
        <div className={"robot-center"}>
          <div className={"robot-content"}>
            <div className={"left"}>
              <SideMenu ref={this.sideMenuRef} onUpdate={this.onToggleDialog} onUpdateContent={this.onUpdateSideMenuContent}
                onInit={this.onInitFirstRobot} onInitError={this.onInitError} />
            </div>
            <div className={classnames(["right", rightAreaId])}>
              {
                rightAreaId === 'default' ?
                  <RobotChat ref={this.RobotChatRef} pageOriginId="ROBOT" />
                  : null
              }
              { // 机器人管理
                rightAreaId === 'robotManage' ?
                  <RobotManage router={this.props.router} onUpdateRobotList={this.onUpdateRobotList} /> : null
              }
              { // 机器人回收管理
                rightAreaId === 'orphaned' ?
                  <OrphanedManage router={this.props.router} onUpdateRobotList={this.onUpdateRobotList} /> : null
              }
              { // 机器人广场
                rightAreaId === 'botsMarketplace' ?
                  <BotsMarketplace router={this.props.router} onUpdateRobotList={this.onUpdateRobotList} /> : null
              }
            </div>
          </div>
        </div>
      </div>
    )
  }
}

const mapStateToProps = (state: any) => ({
  userInfo: state.app.userInfo,
  actionList: state.app.actionList,
  globalRegion: state.app.globalRegion,
  globalLang: state.app.globalLang,
  themeInfo: state.app.themeInfo,
  activeCompanyId: state.app.activeCompanyId,
  isPrivate: state.app.isPrivate,
});

const mapDispatchToProps = (dispatch: any) => ({
  change_app_globalLang: (globalLang: string) => dispatch(change_app_globalLang(globalLang)),
  change_app_userInfo: (data: any) => dispatch(change_app_userInfo(data))
})

export default connect(mapStateToProps, mapDispatchToProps)(Robot);
