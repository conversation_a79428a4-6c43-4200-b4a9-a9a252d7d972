import React, { useState, useEffect, forwardRef, useRef, useImperativeHandle } from "react";
import { connect } from "react-redux";
import { Modal, message, Input, Flex,Form } from "antd";
import classNames from "classnames";
import i18next from "i18next";
import {$trim} from '@/utils/common'

import { api_create_api_key_params,api_update_api_key_params } from "@/api/robotManage";
import { dispatch_api_create_api_key,dispatch_api_update_api_key } from "@/store/api";

import './index.scss'
import Svgicon from "@/components/svgicon";

// 编辑文档
const UpdateDocument = forwardRef((props: any, ref) => {
  /** 自定义暴露给父组件的实例值 **/
  // useImperativeHandle(ref, () => ({
  //   init() {
  //   }
  // }))

  /* props */
  const isVisible = props.visible
  const resource = props.resource
  console.log('resource', resource)
  const robotId = resource?.robotId
  const isEdit = resource.type === "edit"
  const modalTitle = !isEdit ? i18next.t("robot-manage.api-manage.create-api-key") : i18next.t("robot-manage.api-manage.update-api-key")
  /* state */
  const [isSubmiting, setIsSubmiting] = useState(false) // 是否提交中

  const [form] = Form.useForm(); // 表单
  const formRef = useRef<any>(null);

  /* method */
  // 确认
  const handleIsModalOk = async() => {
    try {
      const values:any = await form.validateFields();
      const {apiName }= values
      const { userInfo, globalRegion: region } = props;
      const base = isEdit?api_update_api_key_params:api_create_api_key_params
      let data:any = {
        ...base,
        seq: 0,
        region,
        account: userInfo.account,
        robotId: robotId,
        name:  $trim(apiName)
      }
      if (isEdit){
        data.keyId= resource.keyId
      }
      setIsSubmiting(true)
      const request = isEdit? props.dispatch_api_update_api_key:props.dispatch_api_create_api_key
      console.log('request', request)
      console.log('isEdit',isEdit, data.keyId)
      // return
      request(data).then(() => {
        setIsSubmiting(false)
        const msg = isEdit? "common.edit-success":"robot-manage.api-manage.create-api-key-success"
        message.open({ type: "success", content: i18next.t(msg) })
        handleIsModalCancel()
        props.onSucess()
      }).catch((err: any) => {
        setIsSubmiting(false)
        console.log(err)
      })
    } catch (errorInfo) {
      console.log('Failed:', errorInfo);
    }
    
  }
  // 取消
  const handleIsModalCancel = () => {
    props.onClose()
  }

  // 表单必填校验
  const formRequiredValidator = ({}) => ({
    validator(_:any, value:any) {
      const _value = $trim(value) 
      if (_value!='' && _value!=null&& _value!=undefined) {
        return Promise.resolve();
      }
      return Promise.reject(new Error(i18next.t('common.input-required') as string));
    },
  })

  useEffect(() => {
    if(isEdit){
      form.setFieldsValue({
        apiName:resource.apiName
      })
    }
    
  }, [isEdit])

  return (
    <>
      <Modal title={modalTitle}
        wrapClassName="operate-robot-api-modal"
        width={500}
        open={isVisible}
        onOk={handleIsModalOk}
        onCancel={handleIsModalCancel}
        footer={undefined}
        okText={i18next.t("app.ok")}
        cancelText={i18next.t("app.cancel")}
        maskClosable={false}
        centered
        cancelButtonProps={{variant:"filled",color:"default"}}
      >
        <div>
          <Form ref={formRef} className="common-form" name="validateOnly" layout="vertical" form={form} autoComplete="off" requiredMark={false} disabled={isSubmiting}>
            {/* 名稱 */}
            <Form.Item shouldUpdate={true} name="apiName" label={i18next.t("robot-manage.api-manage.name")} rules={[formRequiredValidator]}>
              <Input variant="filled" showCount maxLength={20} placeholder={i18next.t("robot-manage.api-manage.name") as string} />
            </Form.Item>
          </Form>
        </div>
      </Modal>
    </>
  );
});
const mapStateToProps = (state: any) => ({
  userInfo: state.app.userInfo,
  globalRegion: state.app.globalRegion,
  globalLang: state.app.globalLang,
});
const mapDispatchToProps = (dispatch: (e: any) => void) => ({
  dispatch_api_create_api_key: (data = api_create_api_key_params) => dispatch(dispatch_api_create_api_key(data)),
  dispatch_api_update_api_key: (data = api_update_api_key_params) => dispatch(dispatch_api_update_api_key(data)),
});
export default connect(mapStateToProps, mapDispatchToProps)(UpdateDocument);