import { connect } from "react-redux";
import React, { useState, useRef, useEffect, forwardRef, useImperativeHandle } from "react";
import { Flex, Button, Spin, Empty } from 'antd'
import { useTranslation } from 'react-i18next';
import UserAvatar from "@/components/userAvatar";
import { useOpenIM } from '@/contexts/OpenIMContext';
import SvgIcon from '@components/svgicon';
import { $isNull } from '@/utils/common'
import './index.scss'
import { api_all_users_profile } from '@/api/chat'

// 新的好友
const NewContacts = forwardRef((props: any, ref) => {
    // 自定义暴露给父组件的实例值
    useImperativeHandle(ref, () => ({
        // 更新申请列表
        emit_update_new_contact(resource: any) {
            console.log('更新申请列表 resource', resource)
            handleRealtimeReceivedDatas(resource)
        }
    }))

    const { t } = useTranslation();
    const { activeCompanyId } = props
    const { service: ImService, isConnected: isConnectedIm, newContacts, notifyRoot } = useOpenIM();
    const newContactsRef = useRef([])

    const [isLoading, setIsLoading] = useState<any>(null)
    const [allDatas, setAllDatas] = useState<any>([]) // 所有数据
    const [usersInfoMap, setUsersInfoMap] = useState<any>({}) // 已有的用户信息映射
    const [receivedDatas, setReceivedDatas] = useState<any>([]) // 收到的实时好友申请数据

    useEffect(() => {

    }, [])

    useEffect(() => {
        if (!ImService || !isConnectedIm) return
        addImListeners()
        query()
    }, [ImService, isConnectedIm])

    useEffect(() => {
        // console.log('新的好友 newContacts change', newContacts)
        newContactsRef.current = newContacts
        if (allDatas && allDatas.length > 0) {
            const _allDatas = allDatas.map(((i: any) => {
                i = updateFromNewContactCallback(i)
                return i
            }))
            // console.log('更新_allDatas', _allDatas)
            setAllDatas(_allDatas)
        }
    }, [newContacts])

    // 查询列表 
    const query = async () => {
        try {
            setIsLoading(true)
            // 获取用户信息Map
            const _usersInfoMap = await getAllUsersProfile()
            // 发出的好友请求列表
            let applicantList = await ImService.getFriendApplicationListAsApplicant().catch(() => {
            })
            applicantList = applicantList ? JSON.parse(JSON.stringify(applicantList)) : []
            // 添加发起者标识
            applicantList = applicantList.map(((i: any) => {
                i.userId = i.toUserID
                i.showName = _usersInfoMap[i.toUserID]?.userName || i.toNickname
                i.avatar = _usersInfoMap[i.toUserID]?.avatar
                i.handleType = 'send'
                return i
            }))
            // 收到的好友请求列表
            let recipientList = await ImService.getFriendApplicationListAsRecipient().catch(() => {
                setIsLoading(false)
            })
            setIsLoading(false)
            recipientList = recipientList ? JSON.parse(JSON.stringify(recipientList)) : []
            // 添加添加者标识
            recipientList = recipientList.map(((i: any) => {
                i.userId = i.fromUserID
                i.showName = _usersInfoMap[i.fromUserID]?.userName || i.fromNickname
                i.avatar = _usersInfoMap[i.fromUserID]?.avatar
                i.isRejecting = false
                i.isAgreeing = false
                i.handleType = 'accept'
                updateFromNewContactCallback(i)
                return i
            }))
            // 处理结果HandleResult： -1已拒绝、0等待处理、1已同意
            console.log('我加别人的', applicantList)
            console.log('我收到的', recipientList)
            let allDatas = [...applicantList, ...recipientList]
            // 根据创建时间倒序
            allDatas = allDatas.sort((a: any, b: any) => {
                const key = 'createTime'
                const timeA = a[key]
                const timeB = b[key]
                return timeB - timeA;// 倒序
            })
            console.log('最终申请列表数据', allDatas)
            setAllDatas(allDatas)
        } catch (error) {

        }
    }

    // 对比 好友申请新增通知回调 返回的数据,更新最新状态
    const updateFromNewContactCallback = (item: any) => {
        const newContactsData: any = newContactsRef.current
        if (item.handleType === 'accept') {
            if (newContactsData) {
                const exitNewContact = newContactsData.filter((ni: any) =>
                    item.fromUserID === ni.fromUserID && ni.handleTime >= item.handleTime
                )
                // console.log('exitNewContact:',exitNewContact)
                if (exitNewContact.length > 0) {
                    item.handleResult = exitNewContact[0].handleResult
                    item.reqMsg = exitNewContact[0].reqMsg
                }
            }
        }
        return item
    }

    // 添加监听
    const addImListeners = () => {
        // 1. 好友申请被同意
        ImService.registerCallBack("OnFriendApplicationAccepted", async (data: any) => {
            console.log('-->好友申请同意：', data)
            query() // 刷新列表
        });
        // 2. 好友申请被拒绝
        ImService.registerCallBack("OnFriendApplicationRejected", async (data: any) => {
            console.log('-->好友申请拒绝：', data)
            query() // 刷新列表
        });
    }

    const handleRealtimeReceivedDatas = (resource: any) => {
        setReceivedDatas([...receivedDatas, resource])
    }

    // 获取用户信息Map
    async function getAllUsersProfile() {
        if (Object.keys(usersInfoMap).length > 0) return usersInfoMap
        const res: any = await api_all_users_profile({ currentTenantId: activeCompanyId }).catch((err: any) => {
            return usersInfoMap
        })
        if (!res) return usersInfoMap
        const infoMap = { ...res }
        setUsersInfoMap(infoMap)
        // console.log('infoMap', infoMap)
        return infoMap
    }

    const getHandleResultText = (handleResult: any) => {
        const result = String(handleResult)
        let text = ''
        switch (result) {
            case '-1':
                text = t('contacts.rejected') //'已拒绝'
                break;
            case '0':
                text = t('contacts.wait-for-verify') //'等待处理'
                break;
            case '1':
                text = t('contacts.added')//'已同意'
                break;
            default:
                break;
        }
        return text
    }

    const toggleLoading = (index: number, operateType: string, isOpen = false) => {
        const _allDatas = allDatas.map((i: any, idx: number) => {
            if (index === idx) {
                i[operateType] = isOpen
            }
            return i
        })
        setAllDatas(_allDatas)
    }

    // 拒绝
    const onReject = async (target: any, index: number) => {
        toggleLoading(index, 'isRejecting', true)
        // console.log('target', target)
        const res = await ImService.refuseFriendApplication(target.fromUserID).catch(() => {
            toggleLoading(index, 'isRejecting')
        })
        toggleLoading(index, 'isRejecting')
        // if (res) query()
        notifyRoot && notifyRoot('UpdateNewContacts', { type: 'Reject', userID: target.fromUserID }) // 通知根父母刷新
    }

    // 同意
    const onAgree = async (target: any, index: number) => {
        toggleLoading(index, 'isAgreeing', true)
        const res = await ImService.acceptFriendApplication(target.fromUserID).catch(() => {
            toggleLoading(index, 'isAgreeing')
        })
        toggleLoading(index, 'isAgreeing')
        // if (res) query()
        notifyRoot && notifyRoot('UpdateNewContacts', { type: 'Agree', userID: target.fromUserID }) // 通知根父母刷新
    }

    return (
        <Flex className='new-contact-area contacts-area' vertical>
            <div className="area-title">{t('contacts.new-contacts')}</div>
            <div className="list-con">
                {
                    isLoading ?
                        <Spin className="full-spin" size="large" />
                        :
                        <Flex className="list" vertical gap={12}>
                            {allDatas && allDatas.map((item: any, index: number) => (
                                <Flex key={index} className="list-item" gap={8} align="center">
                                    <Flex className="li-user-info" gap={8} align="center">
                                        <UserAvatar className="li-avatar" name={item.showName} src={item?.avatar} userId={item?.userId}/>
                                        <Flex vertical className="li-user">
                                            <div>{item.showName}</div>
                                            {
                                                item.handleType === 'send' ?
                                                    <div>{t('chat.me')}: {$isNull(item.reqMsg) ? t('contacts.want-to-add-you') : item.reqMsg}</div>
                                                    : null
                                            }
                                            {item.handleType === 'accept' ?
                                                <div>
                                                    {t('contacts.want-to-add-you')}{$isNull(item.reqMsg) ? '' : ': ' + item.reqMsg}
                                                </div>
                                                : null}
                                        </Flex>
                                    </Flex>
                                    { // 我发出的
                                        item.handleType === 'send' ?
                                            <Flex gap={8} align="center" className="send-status li-status">
                                                <SvgIcon svgName="icon_arrow_up_right" />
                                                {getHandleResultText(item.handleResult)}
                                            </Flex>
                                            : null
                                    }
                                    { // 我收到的
                                        item.handleType === 'accept' ?
                                            <>
                                                { // 待处理
                                                    item.handleResult == 0 ?
                                                        <Flex className="accept-status li-status" gap={12}>
                                                            <Button disabled={item.isRejecting} onClick={() => onReject(item, index)} color="default" variant="filled">{t('contacts.reject')}</Button>
                                                            <Button disabled={item.isAgreeing} onClick={() => onAgree(item, index)} type="primary">{t('contacts.agree')}</Button>
                                                        </Flex>
                                                        :
                                                        <div className="li-status">
                                                            {getHandleResultText(item.handleResult)}
                                                        </div>
                                                }
                                            </>
                                            : null
                                    }
                                </Flex>
                            ))}
                        </Flex>
                }
                {
                    isLoading !== null && isLoading === false && (!allDatas || allDatas.length === 0) ?
                        <Empty className="page-empty full" image={null} description={
                            <>
                                <div>{t('common.no-data')}</div>
                                <div></div>
                            </>}
                        /> : null
                }
            </div>
        </Flex>
    )
})
const mapStateToProps = (state: any) => ({
    userInfo: state.app.userInfo,
    activeCompanyId: state.app.activeCompanyId,
})
const mapDispatchToProps = (dispatch: any) => ({
    // dispatch_api_: (data:any) => dispatch(dispatch_api_(data))
})
const ConnectedCounter = connect(mapStateToProps, mapDispatchToProps, null, { forwardRef: true })(NewContacts);
export default ConnectedCounter;