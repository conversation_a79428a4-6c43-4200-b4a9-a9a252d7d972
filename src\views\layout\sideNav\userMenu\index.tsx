import { connect } from "react-redux";
import { getMenuItem } from "@components/base";
import { Dropdown, Menu, MenuProps, message, Modal, Segmented, Flex,Button } from "antd";
import { ArrowLeftOutlined } from '@ant-design/icons';
import i18next from "i18next";
import { useState, useRef, useEffect, forwardRef, useImperativeHandle } from "react";
import SvgIcon from '@components/svgicon'; // 根据实际路径引入
import "./index.scss"
import classnames from "classnames";
import { changeTheme,changeFaviconTitle } from "@/theme"
import { dispatch_api_account_feed_back } from '@/api/platform'
import { dispatch_api_logout } from "@/api/user";
import {change_app_themeInfo,change_app_globalLang} from "@/store/action";
import { useOpenIM } from '@/contexts/OpenIMContext';
import Settings from '@/views/settings'
import UserAvatar from '@/components/userAvatar'


interface LanguageItems {
    zh: string;
    hk: string;
    en: string;
}
// 用户中心菜单
const UserMenuComponent = forwardRef((props: any, ref) => {
    
    const themeInfo = props.themeInfo
    const { userInfo, globalRegion: region, theme } = props;
    const [settingModalVisible, setSettingModalVisible] = useState(false) // 系统设置弹窗
    const [themeActive, setThemeActive] = useState("auto") // 外观选中
    const [feedbackVisible, setFeedbackVisible] = useState(false) // 是否显示意见反馈
    const [feedbackText, setFeedbackText] = useState('') // 意见反馈输入框
    const [isSubmit,setIsSubmit] = useState(false) // 提交中
    const { service:ImService } = useOpenIM();


    // 外观主题设置项
    const themeItems: any[] = [
        { label: i18next.t('automatic'), value: "auto", key:"auto" }, // 跟随操作系统颜色
        { label: i18next.t('light'), value: "light", key:"light" },
        { label: i18next.t('dark'), value: "dark", key:"dark" },
    ];

    const langeItems:LanguageItems= {
        "zh": "简体中文",
        "hk": "繁體中文",
        "en": "English"
    }


    // 获取当前语言文本
    const getActiveLangText = () => {
        return props.globalLang ? getLanguageItem(props.globalLang) : props.globalLang
    }

    // 意见反馈 提交按钮启用状态判断
    const getConfirmBtnEnable=()=>{
        const enable = (feedbackText && feedbackText.trim()!='') && !isSubmit
        return enable
    }

    // 用户菜单
    const renderUserMenu = () => {
        const items: MenuProps['items'] = [
            getMenuItem(i18next.t(["nav.logout"]), "logout", <SvgIcon svgName="icon_signout"/>),
            // getMenuItem(i18next.t(["control-center"]), "switchAdminLogin", <SvgIcon svgName="icon_switch"/>),
            getMenuItem(i18next.t(["system-setting"]), "setting", <SvgIcon svgName="icon_settings"/>),
        ];
        return (
            <Menu onClick={handleUserMenuClick} items={items} />
        )
    }

    // 用户菜单选择
    const handleUserMenuClick: MenuProps['onClick'] = (item: any) => {
        if (["logout"].includes(item.key)) { // 退出登录
            const data = {
                account: userInfo.account,
                region
            };

            props.dispatch_api_account_logout(data,ImService).then(() => {
                message.success(i18next.t("login.logout"));
                props.router.navigate("/login");
            }).catch((err:any)=>{
                console.log(err)
            })
        } else if (["switchAdminLogin"].includes(item.key)) { // 切换
            const location = window.location
            const adminUrl = location.origin + '/admin'
            window.open(adminUrl, adminUrl)
        }  else if (item.key === 'setting'){ // 系统设置
            setFeedbackVisible(false)
            setSettingModalVisible(true)
        } else {
            props.router.navigate(item.key);
        }
    }

    // 外观切换
    const handleChangeTheme = async(theme:any)=>{
        setThemeActive(theme)
        // 记录用户所选外观
        localStorage.setItem("AMA_APPEARANCE",theme)
        const base = {
            colorPrimarySecondary: props.themeInfo.colorPrimarySecondary,
            colorPrimaryContrasting:props.themeInfo.colorPrimaryContrasting
        }
        const result = await changeTheme(base, theme)
        const dynamicColors = {...props.themeInfo,...result?.themeColors, theme: result?.activeTheme}
        if(theme!=='auto') dynamicColors.theme = theme
        await props.change_app_themeInfo(dynamicColors)
        // 修改浏览器标签页角标
        changeFaviconTitle()
    }

    // 关闭弹窗
    const handleCloseSettingModal = (value:any)=>{
        setSettingModalVisible(false)
    }

    function getLanguageItem(lang: keyof typeof langeItems) {
        return langeItems[lang]; // 这里 lang 的类型是 'zh' | 'hk' | 'en'
    }

    // 语言下拉选项填充
    const renderLangMenu = ()=> {
        const keys =  Object.keys(langeItems) as Array<keyof LanguageItems>
        const items: MenuProps['items'] = keys.map(key=>{
            return getMenuItem(getLanguageItem(key), key, null)
        })
        return (
        <Menu onClick={(d) =>  props.change_app_globalLang(d.key)} style={{ minWidth: 128 }} items={items} selectedKeys={[props.globalLang]}/>
        )
    }

    // 显示意见反馈
    const showFeedback =()=>{
        setFeedbackText('')
        setFeedbackVisible(true)
    }

    // 意见反馈输入框
    const handleChangeFeedbackText=(e:any)=>{
        setFeedbackText(e.target.value)
    }

    // 返回设置
    const rebackToSetting =()=>{
        setFeedbackText('')
        setFeedbackVisible(false)
    }

    // 确认提交反馈
    const confirmFeedback=()=>{
        const data = { content: feedbackText };
        setIsSubmit(true)
        props.dispatch_api_account_feed_back(data).then((result: any) => {
          setIsSubmit(false)
          message.open({ type: 'success', content: i18next.t(["success.submit"]) });
          rebackToSetting()
        }).catch((err: any) => {
          setIsSubmit(false)
        })
    
    }

    const onCloseSetting = ()=>{
        setSettingModalVisible(false)
    }

    useEffect(()=>{
        const storageTheme = localStorage.getItem("AMA_APPEARANCE")
        if(storageTheme){
            setThemeActive(storageTheme)
        }
        setFeedbackVisible(false)
    },[])

    return (
        <div className="user-menu">
            <Dropdown className="user-setting" placement="topLeft" overlayClassName="user-setting-dropdown" dropdownRender={() => renderUserMenu()} arrow={false}>
                <div>
                     <UserAvatar isLoginUser={true}/>
                </div>
            </Dropdown>
            {
                settingModalVisible ?<Settings onClose={onCloseSetting} />: null
            }
        </div>
    );
});
const mapStateToProps = (state: any) => ({
    userInfo: state.app.userInfo,
    themeInfo: state.app.themeInfo,
    globalLang: state.app.globalLang,
})
const mapDispatchToProps = (dispatch: any) => ({
    dispatch_api_account_logout: (data:any,ImService:any) => dispatch(dispatch_api_logout(data,ImService)),
    dispatch_api_account_feed_back: (data:any) => dispatch(dispatch_api_account_feed_back(data)),
    change_app_themeInfo: (data: any) => dispatch(change_app_themeInfo(data)),
    change_app_globalLang: (globalLang: string) => dispatch(change_app_globalLang(globalLang)),
})
export default connect(mapStateToProps, mapDispatchToProps)(UserMenuComponent);
