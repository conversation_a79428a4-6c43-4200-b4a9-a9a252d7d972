import React, { useState, useRef, useEffect } from 'react';
import { Input, InputProps } from 'antd';

interface StrictNumberInputProps extends InputProps {
  maxLength?: number;
}

const StrictNumberInput = React.forwardRef<any, StrictNumberInputProps>(
  ({ value: propValue, onChange, ...restProps }, ref) => {
    const [value, setValue] = useState<string>(propValue?.toString() || '');
    const isComposing = useRef(false);
    const inputRef = useRef<any>(null);

    // 同步外部value变化
    useEffect(() => {
      const newValue = propValue?.toString() || '';
      if (newValue !== value) {
        setValue(newValue);
      }
    }, [propValue]);

    
    // 处理内容变化（修复删除问题）
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const inputValue = e.target.value;
        // console.log('inputValue',inputValue)

        // 输入法组合过程中不处理
        if (isComposing.current) {
             // 强制更新状态（即使值与之前相同）
            setValue(inputValue);
            onChange?.({
                ...e,
                target: { ...e.target, value: inputValue }
            } as any);
            return;
        }
        // 过滤非数字字符
        const filtered = inputValue.replace(/[^\d]/g, '');
        setValue(filtered);
        // console.log('filtered',filtered)
        onChange?.({
            ...e,
            target: { ...e.target, value: filtered }
        } as any);
    };

    // 输入法开始
    const onCompositionStart=() => {
        isComposing.current = true
    }

    // 输入法结束
    const onCompositionEnd=() => {
        isComposing.current = false
        const finalValue = value.replace(/[^\d]/g, '')
        // 输入法结束后执行最终过滤
        // 更新状态
        setValue(finalValue);
        onChange?.({
            target: { value: finalValue }
        } as any);
    }

    return (
      <Input
        {...restProps}
        ref={(node) => {
          inputRef.current = node;
          if (typeof ref === 'function') ref(node);
          else if (ref) ref.current = node;
        }}
        variant="filled"
        type="tel"
        value={value}
        onChange={handleChange}
        onCompositionStart={onCompositionStart}
        onCompositionEnd={onCompositionEnd}
      />
    );
  }
);

export default StrictNumberInput;