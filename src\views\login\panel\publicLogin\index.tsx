import React, { useState, useEffect } from 'react';
import { connect } from "react-redux";
import { useNavigate, useLocation } from "react-router-dom";
import { Button, Form, Input, Flex, Tabs, Checkbox, Space, Select, Modal, message } from 'antd';
import type { TabsProps } from 'antd';
import type { FormInstance } from 'antd';
import { useTranslation } from 'react-i18next';
import classnames from "classnames";
import PasswordInput from '@/components/form/passwordInput'
import MobileInput from '@/components/form/mobileInput'
import VerifyCode from '@/components/form/verifyCode'
import { CountdownProvider } from '@/components/form/verifyCode/CountdownProvider';
import ForgotPasswordPanel from '../forgotPassword'
import { api_auth_sessions, api_get_email_code, api_get_mobile_code, api_get_dept_invitation } from '@/api/user'
import { Encrypt } from '@/utils/forge'
import { RequestFailedMessage } from '@/utils/userRequest'

import { PasswordRules, EmailRules, MobileRules, VerifyCodeRules } from '@/views/common/formRule'
import ErrorCodeMap from '@/views/common/formErrorCodeMap'
import FormUtils from '@/views/common/formUtils'
import { change_app_userInfo } from "@/store/action";

import './index.scss'
import { loginedDefaultPath } from '@/routes';
import SvgIcon from '@components/svgicon'; // 根据实际路径引入

// sass 登录

const items1: TabsProps['items'] = [
    {
        key: 'Email',
        label: 'email',
        children: null,
    },
    {
        key: 'Mobile',
        label: 'mobile',
        children: null,
    }
];

const PublicLogin = (props: any) => {
    const { isInvite = false, inviteCode = null, onForgotPassword, themeInfo, isLogin, onLoginedRefresh } = props // inviteCode不为空时，表示从'邀请'页面过来
    const navigate = useNavigate();
    const { t, i18n } = useTranslation();
    const location = useLocation();
    const [modal, contextHolder] = Modal.useModal();

    const [isAgree, setIsAgree] = useState(false)

    const [accountType, setAccountType] = useState('Email') // 账号类型 ：邮箱 Email 或  手机号
    const [passwordType, setPasswordType] = useState('Password') // 密码类型：密码 Password 或 验证码 Code
    const [activePanel, setActivePanel] = useState('login')//登录 、忘记密码、注册
    const [showPrivacyAgree, setShowPrivacyAgree] = useState(false) // 隐私政策同意弹窗显示
    const [isSubmiting, setIsSubmiting] = useState(false)
    const [form] = Form.useForm();
    const [formValues, setFormValues] = useState<any>({});
    const [inviteData, setInviteData] = useState<any>({});
    const privacyRender = (`<a href="/privacyPolicy" target={"_blank"}>${t('login.privacy-policy')}</a>`)
    const termsRender = (`<a href="/termsConditions" target={"_blank"}>${t('login.terms-conditions')}</a>`)

    // 初始化
    // useEffect(() => {
    // }, [])

    // 语言切换时，重新渲染表单校验信息
    useEffect(() => {
        const fun: any = () => FormUtils.refreshValidate(form, t)
        // 监听语言变化
        i18n.on('languageChanged', fun);

        // 清理监听器
        return () => {
            i18n.off('languageChanged', fun);
        };
    }, [i18n]);


    // 获取邀请详情
    const getInvitorInfo = async () => {
        try {
            const res = await api_get_dept_invitation(inviteCode) as any;
            if (!res) return
            setInviteData(res?.data)
        } catch (error: any) {
        }
    };

    const onChangeTab1 = (key: string) => {
        // console.log(key);
        setAccountType(key)
    };

    // 登录提交
    const handleLoginSubmit = (ignoreAgree?: boolean) => {
        FormUtils.resetValidate(form)
        if (!ignoreAgree && !isAgree) {
            setShowPrivacyAgree(true)
            return
        }

        let { email, mobile, code, password } = formValues
        const params: any = {}
        if (inviteCode) params.invitationCode = inviteCode
        const isPsd = passwordType == 'Password'
        if (isPsd) {
            password = Encrypt(password)
            params.password = password
        } else {
            params.code = code
        }
        // 登录
        if (accountType == 'Email') { // 邮箱
            params.loginType = isPsd ? 'EMAIL_PASSWORD' : 'EMAIL_CODE'
            params.email = email
        } else { // 手机号
            params.areaCode = '+' + mobile?.code?.label
            params.mobile = mobile?.number
            params.loginType = isPsd ? 'MOBILE_PASSWORD' : 'MOBILE_CODE'
        }
        // console.log('params',params)
        // return
        setIsSubmiting(true)
        props.dispatch_api_auth_sessions(params).then((res: any) => {
            setIsSubmiting(false)
            let _code = res.code
            if (_code === 0) {
                // 邀请场景
                if (isInvite) {
                    navigate('/inviteSuccess?code='+inviteCode)
                } else {// 登录场景
                    message.success(t("login.sign-success"));
                    navigate(loginedDefaultPath)
                }

            } else {
                const accountErrors = ErrorCodeMap.account // 账号错误码
                const errors = [...accountErrors]
                if (!errors.includes(_code)) {
                    const msg = RequestFailedMessage(res?.msg,_code)
                    message.error(msg);
                    return
                }
                const errMsg = t(`errorCode.${_code}`)
                form.setFields([
                    { name: accountType === 'Email' ? 'email' : 'mobile', errors: [''] },
                    { name: isPsd ? 'password' : "code", errors: [errMsg], warnings: [`errorCode.${_code}`] }
                ]) //warnings 为了解决 setFields 检验信息 国际化问题
            }

        }).catch((err: any) => {
            setIsSubmiting(false)
            console.log(err)
        })
    }

    // 主动跳转到注册
    const onToRegisterMyself = () => {
        navigate('/register', { state: { inviteCode } });
    }


    // 表单项 输入改变事件
    const onFiledValueChange = (changedFields: any, allValues: any) => {
        try {
            // console.log('changedFields', changedFields)
            // console.log('allValues',allValues)
            setFormValues({ ...formValues, ...allValues });
            Object.keys(changedFields).forEach((fieldName: any) => {
                const fieldError = form?.getFieldError(fieldName)
                if (fieldError.length > 0) FormUtils.clearValidate(form, fieldName)
            })
        } catch (error) {

        }
    }

    const handleOnFormItemKeyUp = (e: any) => {
    }

    const onChangeLoginType = () => {
        // console.log('sdfsdf',form.getFieldError('email'))
        const isPsd = passwordType == 'Password'
        form.resetFields([isPsd ? 'password' : 'code'])
        setPasswordType((isPsd ? 'Code' : 'Password'))
        const emailValidateError = form.getFieldError('email')
        if (emailValidateError && emailValidateError.length == 1 && emailValidateError[0] == '') {
            FormUtils.clearValidate(form, 'email')
        }
    }

    // 忘记密码->登录页面
    const backToLogin = (condition: any = {}) => {
        // console.log('condition',condition)
        // 忘记密码前 的邮箱或手机号信息记录
        const { type, data } = condition
        if (data) {
            form.setFieldValue(type == 'Email' ? 'email' : 'mobile', data)
        }
        setActivePanel('login')
        // 邀请场景，需要通知父组件
        onForgotPassword && onForgotPassword(true)
    }

    const onChangeForgotPassword = () => {
        setActivePanel('forgotPsd')
        // 邀请场景，需要通知父组件
        onForgotPassword && onForgotPassword(false)
    }
    const onChangeAgree = (e: any) => {
        setIsAgree(e.target.checked)
    }

    // 同意用户协议
    const onPrivacyAgreeOk = () => {
        setIsAgree(true)
        setShowPrivacyAgree(false)
        // 登录
        handleLoginSubmit(true)
    }

    const onPrivacyAgreeCancel = () => {
        setShowPrivacyAgree(false)
    }


    // 区号切换时需要校验手机号
    const onValidteMobile = () => {
        form.validateFields(["mobile"])
    }

    return (
        <CountdownProvider>
            <div className="content public-login">
                {/* 登录面板 */}
                {
                    activePanel == 'login' ?
                        <>
                            {!isInvite ? (
                                <div className='content-title-content'>
                                    <div className="content-tittle">
                                        {/* {themeInfo?.platformName || t("login.control-center")} */}
                                        {t("login.welcom-to", { platName: themeInfo?.platformName || t("login.control-center") })}
                                    </div>
                                    <div className="content-describe">{t('login.login-tip')}</div>
                                </div>)
                                : null
                            }

                            <Form
                                form={form}
                                className="login-from common-form"
                                name="basic"
                                autoComplete="off"
                                onValuesChange={onFiledValueChange}
                                validateTrigger="onBlur"
                                initialValues={{
                                    email: '',
                                    mobile: {
                                        number: '', // 手机号
                                        code: { label: null, value: null }// 区号
                                    },
                                    password: '',
                                    code: ''
                                }}
                            >

                                <Tabs className="common-tab without-border" activeKey={accountType} defaultActiveKey="Email"
                                    items={items1?.map((item: any) => ({
                                        key: item.key,
                                        label: t(`login.${item.label}`), // 确保这里使用翻译函数
                                        children: item.children,
                                    }))} onChange={onChangeTab1} />
                                {/* 邮箱 */}
                                <Form.Item name="email" label={""} rules={[{ required: true, whitespace: true, message: '' }, ...EmailRules(t)]}
                                    className={accountType == 'Mobile' ? 'hidden' : ''}>
                                    <Input autoComplete="new-email" onKeyUp={(e) => handleOnFormItemKeyUp(e)}
                                        className="ipt" variant="filled" maxLength={300} placeholder={t('login-pls.email') as string} />
                                </Form.Item>
                                {/* 手机号 */}
                                <Form.Item shouldUpdate name="mobile" label={""} help={null} rules={MobileRules(t)}
                                    className={classnames(['mobile-form-item', { hidden: accountType == 'Email' }])}>
                                    <MobileInput onValidte={onValidteMobile} />
                                </Form.Item>
                                { /* 密码 或 验证码 */}
                                <Form.Item className={classnames({ 'hidden': passwordType == 'Code' })} name="password"
                                    rules={[{ required: true, whitespace: true, message: '' }, ...PasswordRules(t)]}>
                                    <PasswordInput
                                        key="login-password"
                                        onKeyUp={(e: any) => handleOnFormItemKeyUp(e)} />
                                </Form.Item>

                                {/* 验证码 */}
                                <Form.Item className={classnames({ 'hidden': passwordType == 'Password' })} name="code" label={""}
                                    rules={VerifyCodeRules(t)}
                                >
                                    <VerifyCode
                                        api={accountType == 'Email' ? api_get_email_code : api_get_mobile_code}
                                        scene="MEMBER_LOGIN"
                                        target={accountType.toLocaleLowerCase()}
                                        {
                                        ...
                                        (accountType == 'Email' ? { email: form.getFieldValue("email") } : { mobile: form.getFieldValue("mobile") })
                                        }
                                        targetValidate={form.validateFields} onKeyUp={(e: any) => handleOnFormItemKeyUp(e)}
                                    />
                                </Form.Item>
                                {/* 切换密码方式 + 忘记密码 */}
                                <Flex className="operate-item" justify="space-between">
                                    <div onClick={onChangeLoginType}>
                                        {passwordType == 'Password' ? t('login.verifiy-code-login') : t('login.password-login')}
                                    </div>
                                    {
                                        passwordType == 'Password' ? <div onClick={onChangeForgotPassword}>{t('login.forgot-password')}</div>:null
                                    }
                                </Flex>
                                {/* 隐私政策 */}
                                <Space className='privacy-term' align='center'>
                                    <Checkbox checked={isAgree} onChange={onChangeAgree}></Checkbox>
                                    <div dangerouslySetInnerHTML={{
                                        __html: t('login.agree-privacy-terms',
                                            { privacy: privacyRender, terms: termsRender }) as string,
                                    }} />
                                </Space>
                                {/* 登录按钮 */}
                                <div className={"form-submit"}>
                                    <SubmitButton key="SubmitButton"
                                        accountType={accountType}
                                        passwordType={passwordType}
                                        form={form}
                                        disabled={isSubmiting}
                                        onClick={() => handleLoginSubmit()}>
                                        {isInvite ? t('invite.apply-join') : t("login.sign")}
                                    </SubmitButton>
                                </div>
                            </Form>
                        </>
                        : null
                }

                {/* 忘记密码面板 */}
                {activePanel == 'forgotPsd' ?
                    <ForgotPasswordPanel
                        isInvite={isInvite}
                        inviteCode={inviteCode}
                        condition={{ type: accountType, data: form.getFieldValue(accountType == 'Email' ? 'email' : 'mobile') }}
                        onBack={backToLogin}
                    />
                    : null}

                {/* 隐私政策 同意弹窗 */}
                <Modal width={272} title={t('login.agree-privacy-terms-tip')} maskClosable={false} centered closable={false} okText={t('login.agree')} cancelText={t("app.cancel")}
                    open={showPrivacyAgree} wrapClassName="common-confirm-modal" onOk={onPrivacyAgreeOk} onCancel={onPrivacyAgreeCancel}>
                    <div className='modal-privacy-content'>
                        <div dangerouslySetInnerHTML={{
                            __html: t('login.privacy-terms',
                                { privacy: privacyRender, terms: termsRender }) as string,
                        }} />
                    </div>
                </Modal>
            </div>

            {
                // 登录注册
                !isInvite ?
                    <Space className={"concat-me"} align="center">
                        <div className={"unable-login"}>{t('login.no-account')}</div>
                        <span onClick={onToRegisterMyself} className="concat-link">{t('login.register-btn')}</span>
                    </Space> : null
            }
            {contextHolder}
        </CountdownProvider>
    )
};

interface SubmitButtonProps {
    form: FormInstance;
    accountType: string;
    passwordType: string;
    disabled?: boolean,
    onClick: any
}

const SubmitButton: React.FC<React.PropsWithChildren<SubmitButtonProps>> = ({ form, children, onClick, disabled, accountType, passwordType }) => {
    const [submittable, setSubmittable] = React.useState<boolean>(false);

    // Watch all values
    const values = Form.useWatch([], form);
    let fields = accountType === 'Email' ? ['email'] : ['mobile']
    fields = [...fields, passwordType === 'Password' ? 'password' : 'code']
    // console.log('SubmitButton fields', fields)

    useEffect(() => {
        form
            .validateFields(fields, { validateOnly: true })
            .then(() => {
                setSubmittable(true)
            })
            .catch((err) => {
                // console.log('SubmitButton1 errerr:',err)
                setSubmittable(false)
            });
    }, [form, values, accountType, passwordType]);

    useEffect(() => {
        // console.log('disabled,',disabled)
        setSubmittable(!disabled)
    }, [disabled])

    return (
        <Button size="large" type="primary" onClick={onClick} disabled={!submittable} className={"btn"}>
            {children}
        </Button>
    );
};

const mapStateToProps = (state: any) => ({
    themeInfo: state.app.themeInfo,
    isLogin: state.app.isLogin,
});

const mapDispatchToProps = (dispatch: any) => ({
    dispatch_api_auth_sessions: (data: any, isInvite?: boolean) => dispatch(api_auth_sessions(data, isInvite)),
    change_app_userInfo: (data: any) => dispatch(change_app_userInfo(data))
})
export default connect(mapStateToProps, mapDispatchToProps)(PublicLogin);