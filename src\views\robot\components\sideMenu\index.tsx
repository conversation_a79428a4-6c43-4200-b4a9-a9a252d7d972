import { connect } from "react-redux";
import { useTranslation } from 'react-i18next';
import React, { useState, useRef, useEffect, forwardRef, useImperativeHandle } from "react";
import { useLocation, useNavigate } from 'react-router-dom';
import './index.scss'
import SvgIcon from '@components/svgicon'; // 根据实际路径引入
import classnames from "classnames";
import { api_get_ai_list } from '@/api/robot'
import classNames from "classnames";
import { Flex } from "antd";



// 侧边菜单-机器人
const SideMenuComponent = forwardRef((props: any, ref: React.LegacyRef<HTMLDivElement> | any) => {
    // 自定义暴露给父组件的实例值
    useImperativeHandle(ref, () => ({
        // 初始化
        emit_init() {
        },
        // 获取机器人列表数据
        emit_refresh_robotlist() {
            // console.log('获取机器人列表数据')
            init(false)
        }
    }))
    const { t } = useTranslation();
    const navigate = useNavigate();
    const location = useLocation();
    const robotTypeListRef = useRef<any>(null)
    // 获取传递的 state
    const tab = location.state?.tab;
    // console.log('tab',tab)
    const { activeCompanyId, isRefreshCompanies, isPrivate, isActiveCompanyAdmin } = props
    // console.log('activeCompanyId:',activeCompanyId)

    const defaultFunctionsList = [
        //  机器人广场
        {
            type: "botsMarketplace",
            title: 'bots-marketplace.title',
            icon: <SvgIcon svgName="icon_bot_market" />,
            way: 'router'
        },
        //  机器人管理
        {
            type: "manage",
            title: 'robot-manage.label',
            icon: <SvgIcon svgName="icon_robot_manage" />,
            way: 'router'
        },
        //  机器人回收管理,管理员可见
        {
            type: "orphaned",
            title: 'orphaned-manage.title',
            icon: <SvgIcon svgName="icon_delete" />,
            way: 'router',
            hidden: true
        }
    ]

    const normalRobotTypeList: any = [
        // 我的机器人
        {
            type: "user",
            title: 'robot.my-robot',
            isToggle: true, // 默认展开
            list: [],
            emptyText: 'no-my-bot'
        },
        //  与我共享的机器人
        {
            type: "share",
            title: 'robot.share-robot',
            isToggle: false, // 默认展开
            list: [],
            emptyText: 'no-share-bot'
        }
    ]

    const containerRef = useRef(null)
    const shareRef = useRef(null)
    const [homebotList, setHomebotList] = useState<any>([]); // homebot机器人
    const [activeRobot, setActiveRobot] = useState<any>({}); // 当前选中的机器人
    const [robotTypeList, setRobotTypeList] = useState<any>(normalRobotTypeList); // 机器人类型列表（包括 我的机器人、与我共享的机器人）
    const [functionsList, setFunctionsList] = useState<any>([...defaultFunctionsList]); // 功能列表
    const [activeFunctionMenu, setActiveFunctionMenu] = useState<any>(null); // 当前选中的菜单



    /**method **/
    // 读取路由参数
    useEffect(() => {
        if (tab) {
            //   tabNameRef.current=tab
            // 自动清除 state（替换当前历史记录）
            navigate(location.pathname, { replace: true, state: {} });
        }
    }, [tab, navigate, location.pathname]);

    // 初始化 
    useEffect(() => {
        // console.log('初始化',props?.onInitError)
        // props?.onInitError && props?.onInitError()
        // init()
    }, [])

    useEffect(() => {
        // console.log('useEffect activeCompanyId',activeCompanyId)
        // console.log('useEffect isRefreshCompanies',isRefreshCompanies)
        console.log('```tab```', tab)
        if (!isRefreshCompanies) init()
    }, [activeCompanyId, isRefreshCompanies, isPrivate])

    // admin 才有机器人回收管理菜单
    useEffect(() => {
        // console.log('isActiveCompanyAdmin', isActiveCompanyAdmin)
        setFunctionsList(functionsList.map((item: any) => {
            if (item.type === 'orphaned') {//  机器人回收管理,管理员可见
                item.hidden = !isActiveCompanyAdmin
            }
            return item
        }))
    }, [isActiveCompanyAdmin])

    const init = async (judgeManage = true) => {
        // console.log('-------机器人init isPrivate',isPrivate)
        const res: any = await api_get_ai_list({}, isPrivate).catch((err: any) => {
            console.log(err)
            props?.onInitError && props?.onInitError()
        })
        if (!res) {
            props?.onInitError && props?.onInitError()
            // return
        }
        // console.log('res', res)
        const resource = res || {}
        let { homeBotList = [], myBotList = [], sharedWithMeRobotList = [] }: any = resource

        // 个人空间：Homebot；企业：Homebot、my robot、share bot等
        setHomebotList(homeBotList)
        // 个人空间
        if (activeCompanyId + '' === '0') {
            const activeBot = [...homeBotList][0]
            setRobotTypeList([])
            setActiveRobot(activeBot)
            setActiveFunctionMenu(null)
            props.onInit(activeBot, activeCompanyId) // 通知初始化bot
            props.onUpdateContent(activeBot, true)
            return
        }
        // 设置我的机器人-列表数据
        const _robotTypeList = robotTypeList.length > 0 ? [...robotTypeList] : [...normalRobotTypeList]
        // console.log('-------_robotTypeList', _robotTypeList)
        let manageBot: any = functionsList[1]
        _robotTypeList.forEach((item: any) => {
            if (item.type === 'user') { // 设置 我的机器人列表
                item.list = myBotList
            }
            if (item.type === 'share') { // 设置 与我共享机器人列表
                item.isToggle = sharedWithMeRobotList.length > 0
                item.list = sharedWithMeRobotList
            }
        })
        const allList = [...homeBotList, ...myBotList, ...sharedWithMeRobotList]
        const activeBot = allList.length > 0 ? allList[0] : {}
        setHomebotList(homeBotList)
        // console.log('_robotTypeList', _robotTypeList)
        setRobotTypeList(_robotTypeList)
        if (!judgeManage) {
            return
        }
        if (["manage"].includes(tab)) { // 选中机器人管理 功能菜单
            onUpdateMenu({ ...manageBot })
        } else {
            props.onInit(activeBot, activeCompanyId) // 通知初始化
            setActiveRobot(activeBot)
            setActiveFunctionMenu(null)
            props.onUpdateContent(activeBot)
        }
    }

    // 更新机器人菜单
    const onUpdateRobot = (activeBot: any) => {
        setActiveRobot(activeBot)
        setActiveFunctionMenu(null)
        props.onUpdate(activeBot)
        props.onUpdateContent(activeBot)
    }

    // 更新非机器人菜单
    const onUpdateMenu = (activeFunctionMenu: any) => {
        setActiveFunctionMenu(activeFunctionMenu.type)
        setActiveRobot({})
        props.onUpdateContent(activeFunctionMenu)
    }

    // 选择机器人
    const handleSelectRobot = (target?: any) => {
        onUpdateRobot(target)
    }

    // 选择功能
    const onSelectFunction = (target?: any) => {
        // console.log('target', target)
        onUpdateMenu(target)
    }

    // 展开伸缩菜单
    const handleToggleMenu = (target: any) => {
        if (target?.way !== 'router') {
            setRobotTypeList((prev: any) =>
                prev.map((i: any) => i.type === target.type ? { ...i, isToggle: !i.isToggle } : i))
            // props.onUpdateContent(target)
        } else {
            onUpdateMenu({ ...target })
        }
    };

    // 滚动
    const scrollSideMenu = () => {
        try {
            // 计算子元素距离其父容器顶部的偏移量
            const childElement: any = shareRef?.current
            // console.log('childElement', childElement)
            const elementTop = childElement?.offsetTop;
            const containElement: any = containerRef?.current
            const containerTop = containElement?.scrollTop;
            if (!elementTop || !containerTop) return
            const distanceToTop = elementTop - containerTop - 150;
            // 如果子元素的距离顶部小于等于0，说明子元素已经滑到顶部
            if (distanceToTop <= 0) {
                //   console.log('子元素已滑到顶部');
                childElement.style.top = '34px'
                childElement.style.bottom = 'auto'
            } else {
                childElement.style.top = 'auto'
                childElement.style.bottom = '0'
            }
        } catch (error) {

        }
    }

    return (
        <div className="side-menu robot-side-menu">
            <div className="side-menu-title">
                <span>{t('app.create-dialog')}</span>
            </div>
            {/* homebot */}
            {
                homebotList ?
                    homebotList.map((hItem: any, hIndex: number) => {
                        return (
                            <div key={hItem.robot_id + '' + hIndex} onClick={() => handleSelectRobot(hItem)}
                                className={classnames(["menut-item-content", "home-bot", { active: activeRobot.robot_id == hItem.robot_id }])}>
                                <SvgIcon svgName="homebot_logo" />
                                <span>{hItem.name}</span>
                            </div>
                        )
                    }) : null
            }
            {/* functions */}
            {
                activeCompanyId !== '0' ?
                    <Flex gap={4} vertical className="function-area">
                        <div className="functioin-title">{t('robot.functions')}</div>
                        {
                            functionsList.filter((item: any)=>!item.hidden).map((item: any, index:number) => (
                                <Flex key={index + ''} onClick={() => onSelectFunction(item)} className={classNames(['functioin-item'], { active: activeFunctionMenu === item.type })} align="center" gap={8}>
                                    {item.icon ? item.icon : null}
                                    <span>{t(item.title)}</span>
                                </Flex>
                            ))
                        }
                    </Flex> : null
            }

            {/* bots */}
            <div className={`menu-item-container ${isActiveCompanyAdmin ? 'is-admin' : ''}`} ref={containerRef} onScroll={() => scrollSideMenu()}>
                {
                    robotTypeList.filter((i: any) => !i.hidden).map((typeItem: any, index: number) => {
                        return (
                            <React.Fragment key={typeItem.type + '' + index}>
                                <div
                                    ref={typeItem.type === "share" ? shareRef : null}
                                    className={classnames(["menu-item-title", typeItem.type, { active: activeFunctionMenu === typeItem.type }])}
                                    onClick={() => handleToggleMenu(typeItem)}>
                                    <span className={classnames(["menu-item-title-text"])}>
                                        {typeItem.icon ? typeItem.icon : null}
                                        <span>{t(typeItem.title)}</span>
                                    </span>
                                    <span className={classNames(['toggle-icon', typeItem.isToggle ? 'toggle' : 'close'])}>
                                        <SvgIcon svgName="arrow_down_line" />
                                    </span>
                                </div>
                                <div className={classnames(["menut-item-list", { "is-close": !typeItem.isToggle }])}>
                                    {
                                        typeItem.list.map((robotItem: any) => {
                                            return (
                                                <div key={typeItem.type + 'bot' + robotItem.robot_id} className={classnames(["menut-item-content", { active: activeRobot.robot_id == robotItem.robot_id }])} onClick={() => handleSelectRobot(robotItem)}>
                                                    <SvgIcon svgName="bot_default"></SvgIcon>
                                                    <span>{robotItem.name}</span>
                                                </div>
                                            )
                                        })
                                    }
                                    {
                                        !typeItem.list || typeItem.list.length === 0 ?
                                            <Flex className="no-data" align="center" justify="center">
                                                {t(typeItem.emptyText)}
                                            </Flex> : null
                                    }
                                </div>
                            </React.Fragment>
                        )
                    })
                }
            </div>
            {/* <UserMenu/> */}
        </div>
    );
});
const mapStateToProps = (state: any) => ({
    userInfo: state.app.userInfo,
    globalRegion: state.app.globalRegion,
    isRefreshCompanies: state.app.isRefreshCompanies,
    activeCompanyId: state.app.activeCompanyId,
    isActiveCompanyAdmin: state.app.isActiveCompanyAdmin,
    isPrivate: state.app.isPrivate
})
const mapDispatchToProps = (dispatch: any) => ({
    // dispatch_api_: (data:any) => dispatch(dispatch_api_(data))
})

// 使用 connect 连接 Redux store，并启用 forwardRef
const ConnectedCounter = connect(mapStateToProps, mapDispatchToProps, null, { forwardRef: true })(SideMenuComponent);
export default ConnectedCounter;

