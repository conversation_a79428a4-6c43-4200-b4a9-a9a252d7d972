import {connect} from "react-redux";
import { Switch, Input, Toolt<PERSON>,Popover,Flex  } from "antd";
import type { InputRef } from 'antd';
import { useTranslation } from 'react-i18next';
import { useState, useRef, useEffect, forwardRef, useImperativeHandle } from "react";
import './style/index.scss'
import Svgicon from "@/components/svgicon";
import classNames from "classnames";
const { TextArea } = Input;

const QuestionInputComponent = forwardRef((props: any, ref) => {
  const { t } = useTranslation();
  // console.log('ref', ref)
  // 自定义暴露给父组件的实例值
  useImperativeHandle(ref, () => ({
    // 初始化
    emit_init() {
      inputRef.current?.focus() // 输入框自动聚焦
    },
    // 更新发送状态
    emit_update_generate_answer_status(isGeneratingAnswer: boolean) {
      setIsGeneratingAnswer(isGeneratingAnswer)
    },
    emit_clear_input_value(){
      setPrompt("");
      setHasInputValue(false)
    }
  }))

  const themeInfo = props.themeInfo
  const inputRef = useRef<InputRef>(null);
  const [prompt, setPrompt] = useState("");
  const [isGoogleSearch, setIsGoogleSearch] = useState(false);
  const [isGeneratingAnswer, setIsGeneratingAnswer] = useState(false); // 是否生成答案中
  const [hasInputValue, setHasInputValue] = useState(false); // 输入框是否有输入值
  const [hasEnter, setHasEnter] = useState(false); // 输入框是否有输入
  const inputMaxLength = props.maxLength
  const [maxLength, setMaxLength] = useState(3000);
  const isGeneratingAnswerRef = useRef(isGeneratingAnswer); // 创建一个 ref 来存储最新的状态
  
 /**method **/
  const handleOnChangePrompt = (e: any) => {
    // e.preventDefault();
    const value = e.target.value
    setPrompt(value);
    if (value?.trim()!== ""){
      setHasInputValue(true)
    } else{
      setHasInputValue(false)
    }
  };

  const handleKeyDownEnter = (e: any) => {
    if (e.shiftKey && e.keyCode === 13) {
      return;
    } else if (e.keyCode === 13) {
      e.preventDefault();
      if (!hasInputValue) return
      handleSend();
    }
  };

  const handleChangeGoogleSearch = (value: boolean) => {
    setIsGoogleSearch(value);
    props.onUpdateGoogleSearch(value);
  };

  // 发送请求
  const handleSend = async () => {
    if (prompt?.length>maxLength || !hasInputValue) return
    const res = await props.onSend(prompt, isGoogleSearch);
    // if (res !== 1) {
    //   // 返回1，不能清空输入框
    //   setPrompt("");
    //   setHasInputValue(false)
    // }
  };

  // 停止
  const handlePause = async () => {
    // console.log('停止')
    const res = await props.onStopStreaming().catch()
    if (!res) return
    const isSucess = res?.result
    setIsGeneratingAnswer(!isSucess)// 若请求失败，显示停止按钮，允许再次点击停止
  }

  // 粘贴-上传文件
  const handlePaste = (event: any) => {
    try {
      // event.preventDefault();
      // event.stopPropagation()
      const { clipboardData } = event;
      // console.log('navigator.clipboard', navigator.clipboard)
      // console.log("event",event);
      const { items } = clipboardData;
      // console.log("items++, "+ items.length);
      const { length } = items;
      let blob = null;
      let fileItems = []
      for (let i = 0; i < length; i++) {
        const item = items[i];
        // console.log('item.type', item)
        blob = item.getAsFile(); // 获取粘贴的文件
        // console.log('粘贴的文件 blob', blob); // 处理文件
        if (blob) fileItems.push(blob)
      }
      if (fileItems.length>0) props.onUpdateFileList(fileItems)
    } catch (error) {
      console.error('粘贴error:', error);
      // 提供用户反馈或替代方案
    }
  }

  useEffect(() => {
    inputRef.current?.focus() // 输入框自动聚焦
  }, [])

  useEffect(() => {
    // console.log('监听父组件属性inputMaxLength', inputMaxLength)
    if (inputMaxLength!=null){
      // if (prompt?.length>inputMaxLength){ // 超出做截断
      //   setPrompt(prompt.slice(0,inputMaxLength))
      // }
      setMaxLength(inputMaxLength)
    }
  }, [inputMaxLength])

  // 初始化，添加ESC 键监听事件
  useEffect(() => {
    // 处理 ESC 键按下事件
    const handleKeyDown = (event:any) => {
      if (event.key === 'Escape') {
        // 如果在生成答案中，ESC 停止生成答案
        if (isGeneratingAnswerRef.current) {
          handlePause()
        }
      }
    };
    // 添加事件监听器
    window.addEventListener('keydown', handleKeyDown);

    // 清理函数，组件卸载时移除事件监听器
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, []); // 空依赖数组，确保只在组件挂载和卸载时执行

  // 更新 ref 的值
  useEffect(() => {
    isGeneratingAnswerRef.current = isGeneratingAnswer;
  }, [isGeneratingAnswer]);

  useEffect(()=>{
    const bool = prompt!=''&&prompt!=null && prompt!=undefined
    setHasEnter(bool)
  },[prompt])

  return (
    <div className="question-input-send">
      <div className="text-area">
        <TextArea
          key="questionTextArea"
          ref={inputRef}
          onChange={(e:any) => handleOnChangePrompt(e)}
          onKeyDown={(e:any) => handleKeyDownEnter(e)}
          onPaste={handlePaste}
          value={prompt}
          // placeholder={t('placeholder.msg-placeholder', {platformName: themeInfo?.platformName || 'vama'}) as string}
          // autoSize
          autoSize={{ minRows: 1, maxRows: 7 }}
          maxLength={maxLength}
        />
        <span className={classNames(["placeholder",{'hidden': hasEnter}])}>{t('placeholder.msg-placeholder', {platformName: themeInfo?.platformName || 'vama'}) as string}</span>
        {
          prompt?.length>=maxLength?
          <div className="max-count-error">{t('app.max-input-count', {n: maxLength})}</div>
          :null
        }
      </div>
      {/* Home bot 无谷歌搜索 */}
      {!props.isHomeBot ? (
        <div className={"switch"}>
          <Popover placement="top" arrow={false} overlayClassName="google-search-popover"
            title={'Google Search'} content={
            <div>
              {t('google-seatch')}
            </div>
          }>
            <Switch
              checked={isGoogleSearch}
              onChange={handleChangeGoogleSearch}
            />
          </Popover>
        </div>
      ) : null}
      {
        !isGeneratingAnswer ?
          <Flex onClick={handleSend} className={classNames(["enter-img",{"enter-img-disabled":!hasInputValue}])} align="center" justify={"center"}>
            <Svgicon draggable="false" svgName="icon_send"/>
          </Flex>
          :
          <Tooltip
            className="pause-trigger-tooltip pause-trigger-tooltip-overlay"
            placement="top"
            title={t("error.stop-generating-answers")}
            arrow={false}
          >
            <div className="pause" onClick={handlePause}>
              <Svgicon draggable="false" svgName="icon_pause" />
            </div>
          </Tooltip>
      }
    </div>
  );
});
const mapStateToProps = (state: any) => ({
  themeInfo: state.app.themeInfo
});
// 使用 connect 连接 Redux store，并启用 forwardRef
const ConnectedCounter = connect(mapStateToProps, null, null, { forwardRef: true })(QuestionInputComponent);
export default ConnectedCounter;
