
import { connect } from "react-redux";
import { useEffect, useState, useRef, forwardRef } from "react";
import { Modal, message, Flex, Button, Input } from "antd"
import { useTranslation } from 'react-i18next';
import { $trim, $isNull, $deduplicateByProp } from '@/utils/common'
import { useOpenIM } from '@/contexts/OpenIMContext';
import './index.scss'
import StrictNumberInput from '@components/form/numberInput'
import UserInfoCard from '@/components/userInfoCard';

const AddFriend = forwardRef((props: any, ref: React.LegacyRef<HTMLDivElement> | any) => {
    const { t } = useTranslation()
    const { service: ImService, isConnected: isConnectedIm } = useOpenIM();

    const { userInfo, onSendMessageToUser, activeCompanyId } = props
    const currentUserId = userInfo.imUserId


    const [panel, setPanel] = useState('search') // 面板，搜索或用户名片，默认搜索 search or user
    const [isSearchAble, setIsSearchAble] = useState(false) // 搜索按钮是否可用
    const [isSearching, setIsSearching] = useState(false) // 是否搜索中

    const [searchUserId, setSearchUserId] = useState('') // userid 搜索 '17021164800'
    const [searchUserInfo, setSearchUserInfo] = useState<any>({}) // 搜索到的用户信息


    // 初始化
    useEffect(() => {
    }, [])

    useEffect(() => {
        const able = !$isNull($trim(searchUserId))
        setIsSearchAble(able)
    }, [searchUserId])

    // 搜索
    const onSearch = async () => {
        // console.log('searchUserId', searchUserId)
        setIsSearching(true)

        // 获取用户信息
        const res = await ImService.getUsersInfo([searchUserId]).catch(() => {
            setIsSearching(false)
        })
        setIsSearching(false)
        if (!res) {
            message.error(t('chat.search-no-user'))
            return
        }
        console.log('res', res)
        setSearchUserInfo(res[0] || {})
        setPanel('user')
    }

    const onPressEnter = () => {
        if (isSearchAble) {
            onSearch()
        }
    }

    // 取消
    const onCancel = () => {
        props.onClose()
    }

    // 输入UserID
    const onChangeUserId = (e: any) => {
        const value = e.target.value
        setSearchUserId(value)
    }

    return (
        <div className="chat-add-friend">
            {
                panel === 'search' ?
                    <Modal
                        className={"chat-add-friend-search-modal"}
                        title={t('chat.add-contact')}
                        open={panel === 'search'}
                        onOk={onSearch}
                        onCancel={onCancel}
                        mask={false}
                        maskClosable={true}
                        centered={true}
                        width={400}
                        okText={t('search')}
                        cancelText={t("app.cancel")}
                        cancelButtonProps={{ variant: "filled", color: "default" }}
                        okButtonProps={{ disabled: !isSearchAble || isSearching }}
                        footer={undefined}
                    >
                        {/* userId  */}
                        <StrictNumberInput value={searchUserId} onChange={onChangeUserId} placeholder={t('chat.search-user-id') as string} variant="filled" maxLength={20}
                            onPressEnter={onPressEnter} />
                    </Modal> : null
            }
            {
                panel === 'user' ?
                    <UserInfoCard
                        isSearchAdd={true}
                        userInfo={{ userID: searchUserInfo.userID, userName: searchUserInfo.nickname }}
                        onClose={onCancel}
                        onSendMessageToUser={onSendMessageToUser}
                    ></UserInfoCard>
                    : null
            }

        </div>
    )
})

const mapStateToProps = (state: any) => ({
    companies: state.app.companies,
    activeCompanyId: state.app.activeCompanyId,
    activeCompanyName: state.app.activeCompanyName,
    themeInfo: state.app.themeInfo,
    userInfo: state.app.userInfo,
})
const mapDispatchToProps = (dispatch: any) => ({
    // dispatch_api_: (data:any) => dispatch(dispatch_api_(data))
})
const ConnectedCounter = connect(mapStateToProps, mapDispatchToProps, null, { forwardRef: true })(AddFriend);
export default ConnectedCounter;