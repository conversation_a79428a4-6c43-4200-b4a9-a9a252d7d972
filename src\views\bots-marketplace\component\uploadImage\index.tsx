import { connect } from "react-redux";
import { Upload, Flex, Modal, message } from "antd";
import { useTranslation } from 'react-i18next';
import { useState, useRef, useEffect, useCallback, forwardRef } from "react";
import Cropper from 'react-cropper';
import classNames from "classnames";
import Svgicon from "@/components/svgicon";
import './index.scss'
import "cropperjs/dist/cropper.css";

const UploadImageComponent = forwardRef((props: any, ref) => {
    const { t } = useTranslation();
    const [file, setFile] = useState<any>('') // 机器人logo
    const [cropperImage, setCropperImage] = useState<any>('') // 裁剪图片
    const [isCropperModalOpen, setIsCropperModalOpen] = useState(false) // 裁剪弹窗显示
    const MAX_FILE_SIZE = 1 * 1024 * 1024 // 文件最大限制为1M
    const FILE_SUPPORT_TYPES = ".png,.jpg,.jpeg,.gif,.svg" // 文件支持的格式
    const cropperRef = useRef<any>(null);
    const beforeUpload = () => {
        return false
    }


    // 上传文件
    const handleFileOnChange = (e: any) => {
        e.preventDefault();
        let files;

        if (e.target) {
            const currentFiles = e.target.files[0]
            // 格式校验
            const fileType = currentFiles?.name.substring(currentFiles?.name.lastIndexOf("."))
            if (currentFiles &&!FILE_SUPPORT_TYPES.split(",").includes(fileType)) {
                message.open({ type: "error", content: t(["robot-manage.file-format-error"]) || "" })
                return
            }
            // 大小校验
            if (currentFiles && currentFiles?.size > MAX_FILE_SIZE) {
                message.open({ type: "error", content: t(["robot-manage.upload-document-max-size-error"]) || "" })
                return;
            }
        }

        if (e.dataTransfer) {
            files = e.dataTransfer.files;
        } else if (e.target) {
            files = e.target.files;
        }

        const reader = new FileReader();
        reader.onload = () => {
            setCropperImage(reader.result)
            setIsCropperModalOpen(true)
        };

        if (files.length) {
            reader.readAsDataURL(files[0]);
        }

    }
    // 裁剪确认
    const handleCropperModalOk = () => {
        if (typeof cropperRef.current?.cropper !== "undefined") {
            const res = cropperRef.current?.cropper.getCroppedCanvas().toDataURL()
            // console.log('res', res)
            //   setRobotLogo(res)
            setFile(res)
            props.onUpdate(res)
            handleCropperModalCancel()
        }
    }

    // 裁剪取消
    const handleCropperModalCancel = () => {
        setIsCropperModalOpen(false)
        setCropperImage('')

    }

    // 裁剪弹窗
    const renderCropperModal = () => {
        return (
            <Modal title={t("robot-manage.create-robot.bot-logo-edit")}
                wrapClassName="create-robot-cropper-modal"
                open={isCropperModalOpen}
                onOk={handleCropperModalOk}
                onCancel={handleCropperModalCancel}
                okText={t("app.ok")}
                cancelText={t("app.cancel")}
                maskClosable={false}
                cancelButtonProps={{variant:"filled",color:"default"}}
                centered
            >
                <Cropper ref={cropperRef}
                    src={cropperImage}
                    className="cropper-container"
                    style={{ height: 400, width: '100%', marginTop: "20px" }}
                    zoomTo={0.5}
                    initialAspectRatio={1}
                    preview=".img-preview"
                    viewMode={1}
                    minCropBoxHeight={100}
                    minCropBoxWidth={100}
                    maxLength={200}
                    background={false}
                    responsive={true}
                    autoCropArea={1}
                    checkOrientation={false}
                    guides={true}
                >
                </Cropper>
            </Modal>
        )
    }

    useEffect(()=>{
    //    console.log('props.value',props.value)
       if (props.value) setFile(props.value)
    },[props.value])

    return (
        <div className="avatar-uploader">
            {/* <Upload
                name="avatar"
                listType="picture-card"
                className="avatar-uploader"
                accept={accept}
                beforeUpload={beforeUpload}
                showUploadList={{
                    showPreviewIcon: false, // 隐藏预览图标
                    showRemoveIcon: false,   // 隐藏删除图标
                }}
            // onChange={handleChangeUpload}
            >
                <Flex className="upload-trigger" vertical align={"center"} justify={"center"}>
                    {props.icon ? props.icon : <Svgicon svgName="plus" />}
                    {
                        props.showTriggerText ? <div style={{ marginTop: 8 }}>{props.triggerText || '上传图片'}</div> : null
                    }
                </Flex>
            </Upload> */}
            <Flex className={classNames(["upload-trigger", { 'is-uploaded': file, 'is-square': props.isSquare }])} vertical align={"center"} justify={"center"}
                style={file ? { background: `url(${file}) no-repeat center / contain` } : {}}>
                {!file ? (
                    props.icon ? props.icon : <Svgicon svgName="plus" />
                ) : null}
                {
                    props.showTriggerText && !file ? <div>{props.triggerText || t('robot-manage.create-robot.upload-image')}</div> : null
                }
                <input type="file" className={"upload-file"} accept={FILE_SUPPORT_TYPES} onChange={handleFileOnChange} />
            </Flex>
            {renderCropperModal()}
        </div>
    );
});
const mapStateToProps = (state: any) => ({
    themeInfo: state.app.themeInfo
});
// 使用 connect 连接 Redux store，并启用 forwardRef
const ConnectedCounter = connect(mapStateToProps, null, null, { forwardRef: true })(UploadImageComponent);
export default ConnectedCounter;
