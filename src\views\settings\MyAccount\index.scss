@import '../../../styles/mixin.scss';
.myaccount-wrapper {
  // padding: 16px;
  .title {
    // color: var(--colorText);
    // font-size: 18px;
    // font-weight: 600;
    // padding: 16px 0 24px;
  }
  .account-content {
    padding-top: 24px;
    .account-item {
      display: flex;
      align-items: center;
      margin-bottom: 26px;
      .account-title {
        width: 120px;
        max-width: 200px;
        color: var(--colorTextSecondary);
      }
      .account-val {
        margin-left: 60px;
      }
      .ant-btn {
        margin-left: auto;
      }
    }
  }
  .delete-wrapper {
    .delete-title {
      @include font(18px,24px,600);
    }
    .delete-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .left-content {
        margin-right: 70px;
        @include font(14px,22px,400,var(--colorTextSecondary));
      }
      .delete-btn {
        background: var(--colorErrorBg);
        color: var(--colorError);
      }
    }
  }
}
