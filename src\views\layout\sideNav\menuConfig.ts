export  const routeMenuItemsDefault: any[] = [
    {
        name: 'robot',
        key: 'robotpage',
        icon: "nav_bot_default",
        iconActive: "nav_bot_select",
        tooltip: 'app.create-dialog',
        path: '/'
    },
    {
        name: 'chats',
        key: 'chatspage',
        icon: "nav_chat_default",
        iconActive: "nav_chat_select",
        path: '/chats',
        tooltip: 'chat.title'
    },
    {
        name: 'contacts',
        key: 'contactpage',
        icon: "nav_contact_default",
        iconActive: "nav_contact_select",
        tooltip: 'contacts.title',
        path: '/contacts'
    },
    // 个人空间没有以下菜单
    {
        name: 'docs',
        key: 'docs',
        icon: "nav_docs",
        iconActive: "nav_docs",
        tooltip: 'nav.docs',
        content: "nav.docs-desc",
        path: '/docs',
        isHidden: true,
        disabled: true,
        personalHide: true
    },
    {
        name: 'drive',
        key: 'drive',
        icon: "nav_drive",
        iconActive: "nav_drive",
        tooltip: 'nav.drive',
        content: "nav.drive-desc",
        path: '/drive',
        isHidden: true,
        disabled: true,
        personalHide: true,
    },
    {
        name: 'email',
        key: 'email',
        icon: "nav_email",
        iconActive: "nav_email",
        tooltip: 'nav.email',
        content: "nav.email-desc",
        path: '/email',
        isHidden: true,
        disabled: true,
        personalHide: true,
    },
    {
        name: 'ann',
        key: 'ann',
        icon: "nav_ann",
        iconActive: "nav_ann_active",
        path: '/inforHub',
        isBottom: true,
        isHidden: true,
        personalHide: true,
    },
    {
        name: 'approvals',
        key: 'approvals',
        icon: "nav_approvals",
        iconActive: "nav_approvals",
        tooltip: 'nav.approvals',
        content: "nav.approvals-desc",
        path: '/approvals',
        isHidden: true,
        disabled: true,
        personalHide: true,
    },
    {
        name: 'wiki',
        key: 'wiki',
        icon: "nav_wiki",
        iconActive: "nav_wiki",
        tooltip: 'nav.wiki',
        content: "nav.wiki-desc",
        path: '/wiki',
        disabled: true,
        isHidden: true,
        personalHide: true,
    },
    {
        name: 'project',
        key: 'project',
        icon: "nav_project",
        iconActive: "nav_project",
        tooltip: 'nav.project',
        content: "nav.project-desc",
        path: '/project',
        disabled: true,
        isHidden: true,
        personalHide: true,
    },
]