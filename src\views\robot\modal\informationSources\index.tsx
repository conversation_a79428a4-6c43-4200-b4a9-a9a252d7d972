
import { useState, useEffect,useRef,forwardRef,useImperativeHandle } from "react";
import { useTranslation } from 'react-i18next';
import classnames from "classnames";
import { FileOutlined, LoadingOutlined } from "@ant-design/icons";
import { Drawer, Tabs, Spin, Input} from "antd";
import { TabsProps,Flex } from 'antd';
import {getFileType} from "../enhance/utils"
import Markdown from "@/components/markdown";
import { getFileTypeInfo, formatFileSize } from "../../components/fileUpload/util";
import "./index.scss";
import {connect} from "react-redux";
import {dispatch_api_get_included_chat_detail} from '@/api/robot'
import {dispatch_api_get_user_filebox} from '@/api/filebox'

// interface Props{
//   type: string,
//   resource:any
//   visible: boolean,
//   updateVisible:Function
//   dispatch_api_get_user_uploaded_files: Function
// }
const { TextArea } = Input;
// 信息来源弹窗（附加文件、引用知识、历史会话、谷歌搜索）
const InformationSourcesComponent = forwardRef((props: any, ref:React.LegacyRef<HTMLDivElement> | any) => {
   /**自定义暴露给父组件的实例值，接收父组件通知调用 */
   useImperativeHandle(ref,()=>({
    // 更新逻辑推理 内容
    emit_update_logical_reasoning:(newText:any) => {
      if (tabItems?.filter((i:any)=> i.key =='logicalReasoning').length==0) return
      if (activeTabKey !='logicalReasoning'){
        setActiveTabKey('logicalReasoning')
      }
      updateLogicalReasoning(newText)
    }
}))
  
  // const tabsRef = useRef(null);
  /* props */
  let resource = props.resource;
  const sourceType = props.type;
  const isVisible = props.visible
  const appendParent = props.appendParent
  const { userInfo, globalRegion: region} = props;
  const { t,i18n } = useTranslation();

  /* state */
  const [robotId, setRobotId] = useState("")
  const [loading, setLoading] = useState(true);
  const [activeTabKey, setActiveTabKey] = useState(sourceType);
  const [attachments, setAttachments] = useState([]);
  const [historyChat, setHistoryChat] = useState([]);


  // 解决 tab label 文字国际化问题
  const tabName:any ={
    'attachments': "upload.attachFile",
    'historyChat': "home.historical-chat",
    'reference': "app.references",
    'googleSearch': "home.google-search",
    'logicalReasoning': 'logical-reasoning' //逻辑推理
  }
  const initTabItems: TabsProps['items'] = [
    {
      key: 'attachments',
      // label: t(tabName.attachments),
      label: t("upload.attachFile"),
      forceRender: true,
      children: ""
    },
    {
      key: 'historyChat',
      // label: t(tabName.historyChat), 
      label:t("home.historical-chat"),
      forceRender: true,
      children: ""
    },
    {
      key: 'reference',
      // label: t(tabName.reference),
      label:t("app.references"),
      forceRender: true,
      children: ""
    },
    {
      key: 'googleSearch',
      // label: t(tabName.googleSearch),
      label:t("home.google-search"),
      forceRender: true,
      children: ""
    },
    {
      key: 'logicalReasoning',
      label:t("logical-reasoning"),
      forceRender: true,
      children: ""
    }
  ]
  const [tabItems, setTabItems] =useState<TabsProps['items']>(JSON.parse(JSON.stringify(initTabItems)))
  const tabItemsRef = useRef(tabItems); // 创建一个 ref 来存储最新的 tabItem,为了在任何地方访问它，而不必等待组件重新渲染

  // 额外公共参数
  const common_params:any = {
    seq:0,
  }
  /* methods */
  const renderTabContent =(_sourceType: string, dataList:any)=>{
    // console.log('dataList',dataList)
    const getHTML=(target: any, index:number)=>{
      let html = null
      switch(_sourceType){
        case "attachments": // 附加文件
          html = (
            <>
            <div className="card-title">
              <div>{target.fileName}.{target.fileType}</div>
            </div>
            <div className="file-type-size">
              {getFileTypeInfo(target.fileType).image? (
                <img draggable="false" src={getFileTypeInfo(target.fileType).image} alt="" />
              ) : (
                <FileOutlined />
              )}
              <span className="type-text">
                {target.fileType}
              </span>
              <span className="size">
                  ·{formatFileSize(target.fileByteSize)}
                  {/* · { file?.fileLength>0 ? `${file.fileLength} ${file.fileLength<=1 ?'token':'tokens'}` : file.fileLength } */}
              </span>
            </div>
          </>
          )
          break
        case "reference": // 引用知识
          html = (
          <>
            <div className="card-title card-file">
              <img src={getFileType(target.documentType)} alt="" />
              <div>{target.documentName+ '.' + target.documentType}</div>
            </div>
            {/* <div className="card-text">{target.chunkText}</div>  */}
            <TextArea
              className={"card-textarea"}
              size={"large"}
              value={target.chunkText}
              autoSize={true}
              readOnly={true}
            />
            <div className="card-msg">
              <div>
                <span>{t(["home.upload-time"])}：</span>
                <span>{target.createTime || "-"}</span>
              </div>
              <div>
                <span>{t(["home.relevance"])}：</span>
                {
                  target.score!=undefined && target.score!=null && target.score!=''?
                  <span>{target.score.toFixed(2)}</span>:'-'
                }
              </div>
              <div>
                <span>{t(["home.size"])}：</span>
                {
                  target.tokenCount!=undefined && target.tokenCount!=null && target.tokenCount!=''?
                  <span>{target.tokenCount} token{target.tokenCount>1?`s`:''}</span>:'-'
                }
              </div>
              <div>{target.isEdited ? t(["app.edited"]) : ''}</div>
            </div>
          </>
          )
          break
        case "historyChat": // 历史会话
          html = <>
            <Flex justify={"space-between"} className="card-title2" gap={20}>
              <span>{target.role == "assistant"? target.llmModel || target.robotName: target.account}</span>
              <span>{target.messageTime}</span>
            </Flex>
            <div className="card-text"><Markdown content={target.content} /></div> 
          </>
          break
        case "googleSearch": // 谷歌搜索
          html = (
            <>
               <div className="card-title">
                <span>{(index + 1) + '.'}&nbsp;</span>
                <div>{target.title}</div>
              </div>
              <div className="internet-card-msg">
                <img draggable="false" src={target.image} alt="" />
                <span>{target.displayLink}</span>
              </div>
              <div className="card-text">{target.chunkText}</div>
            </>
          )
          break
        case "logicalReasoning": // 逻辑推理
          html = (
            <>
              <div className="card-text">
                {/* {target} */}
                <Markdown content={target} />
              </div>
            </>
          )
          break
      }
      return html
    }
    return(
      dataList?.map((d: any, index: number) =>
        <div className={`common-antd-drawer-card information-sources-card ${_sourceType} ${_sourceType=='logicalReasoning'?"no-bg-card":""}`} 
        key={sourceType + index} onClick={() => openDocument(_sourceType,d)}>
          {getHTML(d,index)}
        </div>
      )
    )
  }
  // 关闭弹框
  const handleClose = () => {
    // setVisible(false);
    props.updateVisible(false)
  };

  // 查看详情
  const openDocument = (_sourceType:string,d: any) => {
    if (['historyChat','logicalReasoning'].includes(_sourceType)) return
    let url = d.url
    if (_sourceType==='attachments'){
      url=d.downloadPath
    } else if (d.internetSearch === false){
      url=d.documentPath
    }
    window.open(url,url)
  };

  // 切换tab
  const onChangeTab = (key: string) => {
    // console.log(key);
    setActiveTabKey(key)
  }

  // 获取所有附件数据，筛选
  const queryAttachment= (_tabItems:TabsProps['items'],robotId:string, attachmentIds:any[])=>{
    setLoading(true)
    common_params.robotId = robotId
    props.dispatch_api_get_user_filebox({...common_params}).then((res:any)=>{
      let attachments = res?.attachmentList || []
      // 筛选
      const attachmentsDatas = attachments.filter((i:any) => attachmentIds.includes(i.attachmentId))
      setAttachments(attachmentsDatas) // 存储
      const newTabItems = _tabItems?.map(item=>{
        if(item.key ==="attachments") {
          item.children = renderTabContent("attachments", attachmentsDatas)
        }
        return item
      })
      setTabItems(newTabItems)
      setLoading(false)
    }).catch((error:any)=>{
      setLoading(false)
    })
  }

  // 获取历史会话详情
  const queryHistoryChat= (_tabItems:TabsProps['items'],robotId:string, messageIds:any[])=>{
    setLoading(true)
    common_params.robotId = robotId
    props.dispatch_api_get_included_chat_detail({...common_params,messageIds: messageIds}).then((res:any)=>{
      const datas = res || []
      setHistoryChat(datas) // 存储
      const newTabItems =_tabItems?.map(item=>{
        if(item.key ==="historyChat") {
          item.children = renderTabContent("historyChat", datas)
        }
        return item
      })
      setTabItems(newTabItems)
      setLoading(false)
    }).catch((error:any)=>{
      setLoading(false)
      console.log('报错了吗',loading)
    })
  }

  // 更新逻辑推理内容
  const updateLogicalReasoning=(newText:any)=>{
      // 新问答，逻辑推理过程只先显示逻辑推理
      const newTabItems:any = initTabItems.filter((i:any)=> i.key =='logicalReasoning')
      if(newTabItems.length==0) return
      newTabItems[0].children = renderTabContent('logicalReasoning', [newText])
      setTabItems(newTabItems)
      // TODO 滚动条滚到底部？
      // try {
      //   const tabsDom:any = tabsRef?.current
      //   if (tabsDom){
      //     const tabContentDom:any = tabsDom.querySelector('.ant-tabs-content-holder')
      //     console.log('tabContent',tabContentDom)
      //   }
      // } catch (error) {
      // }
  }

  const initPage=()=>{
    try {
      // console.log('initPage resource', resource)
      setRobotId(resource.robotId)
      const subTypeMap = resource.subTypeMap
      const sourceTypes = resource.types
      const dataMap:any ={
        attachments: null, // 附加文件Id
        historyChat: null, // 历史会话
        reference:resource.referenceChunkList || [], // 引用知识
        googleSearch: resource.searchResults || [], // 谷歌搜索,
        logicalReasoning: [resource.thinkingText] || '' // 逻辑推理
      }
      const tabItemFinal:TabsProps['items'] = initTabItems?.filter((tab:any)=>sourceTypes.includes(tab.key)).map((tab:any)=>{
        tab.children = dataMap[tab.key] ? 
        renderTabContent(tab.key, dataMap[tab.key]) : 
        (loading ? <Spin className="modal-spin" size="large" indicator={<LoadingOutlined style={{fontSize: 24}} spin/>}/>: "")
        return tab
      })
      // console.log('tabItemFinal', tabItemFinal)
      setTabItems(tabItemFinal)
      setActiveTabKey(sourceType)
      if (sourceTypes.includes("attachments")){
        const attachmentsIds = resource[subTypeMap["attachments"]] || [] // 附加文件Id
        queryAttachment(tabItemFinal, resource.robotId,attachmentsIds) // 请求接口查询附件
      }
      if (sourceTypes.includes("historyChat")){
        const historyChatIds = resource[subTypeMap["historyChat"]] || [] // 历史会话Id
        // console.log('historyChatIds',historyChatIds)
        queryHistoryChat(tabItemFinal, resource.robotId,historyChatIds) // 请求接口查询历史会话
      }
    } catch (error) {
      console.log('error', error)
    }
  }

  useEffect(() => {
    if (!isVisible) return
    initPage()
  }, [isVisible, resource])


  const handleLanguageChange = (lng:any) => {
    const dataMap:any ={
    //   attachments: attachments, // 附加文件Id
    //   historyChat: historyChat, // 历史会话
      reference:resource.referenceChunkList || [], // 引用知识
    //   googleSearch: resource.searchResults || [] // 谷歌搜索
    }
    const tabItemFinal:TabsProps['items'] = tabItemsRef?.current?.map((tab:any)=>{
      // 重新渲染 引用知识
      if (tab.key =='reference' && dataMap[tab.key]) tab.children = renderTabContent(tab.key, dataMap[tab.key]) 
      return tab
    })
    setTabItems(tabItemFinal)
  };

  // 语言切换时，重新渲染tabs 标题 和 内容
  useEffect(() => {

    // 监听语言变化
    i18n.on('languageChanged', handleLanguageChange);

    // 清理监听器
    return () => {
      i18n.off('languageChanged', handleLanguageChange);
    };
  }, [i18n,resource]);

  useEffect(() => {
    tabItemsRef.current = tabItems; // 在每次 count 更新时同步 ref
  }, [tabItems]);

  return (
    <div className={classnames(["information-sources-modal",{"drawer-append-parent":appendParent}])}>
      <Drawer open={isVisible}
        width={560}
        title={t(["app.information-source"])}
        className={classnames(["information-sources-drawer", 'common-antd-drawer'])}
        onClose={handleClose}
        footer={null}
        mask={!appendParent}
        rootStyle={!appendParent?{}:{position: "relative",flex: 1, width: '560px',height: '100%',boxShadow:'none'}}
        getContainer={appendParent? false:'body'}
      >
      { //  key={props.globalLang}
        tabItems && tabItems?.length>0?
          // <div ref={tabsRef} style={{height:"100%"}}>
            <Tabs locale={props.globalLang} className="common-tab without-border" activeKey={activeTabKey} defaultActiveKey={sourceType} 
              items={tabItems?.map((item:any) => ({
                key: item.key,
                label: t(tabName[item.key]), // 确保这里使用翻译函数
                children: item.children,
              }))}
              onChange={onChangeTab} indicator={{ size: (origin) => origin / 3 }}>
            </Tabs>
          // </div>
          :null
        }
      </Drawer>
    </div>
  );
});
const mapStateToProps = (state: any) => ({
  userInfo: state.app.userInfo,
  globalRegion: state.app.globalRegion,
  globalLang:state.app.globalLang
});
const mapDispatchToProps = (dispatch: any) => ({
  dispatch_api_get_user_filebox: (data:any) => dispatch(dispatch_api_get_user_filebox(data)),
  dispatch_api_get_included_chat_detail: (data:any) => dispatch(dispatch_api_get_included_chat_detail(data))
})
const ConnectedCounter = connect(mapStateToProps, mapDispatchToProps, null, { forwardRef: true })(InformationSourcesComponent);
export default ConnectedCounter;
