.mobile-input{
    width: 100%;
    .ant-space-item:first-child{
        // width: 90px;
    }
    .ant-space-item:last-child{
        flex: 1;
    }
    .ant-select{
        height: 100%;
        width: 90px;
    }
    
    .ipt,.ant-space-item{
        height: 100%;
    }
}

.areacode-options-dropdown{
    width: 300px!important;
    max-height: 400px;
    .name{
        color: var(--colorText);
        display: inline-block;
        max-width: 180px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .code{
        color: var(--colorTextSecondary);
    }
}