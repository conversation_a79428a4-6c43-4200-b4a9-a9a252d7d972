@import "@/styles/mixin.scss";
.infohub-page {
    height: 100%;
    width: 100%;
    .nav-area {
        flex-shrink: 0;
        padding: 16px;
        padding-bottom: 10px;
    }
    .infohub-area {
        flex: 1;
        height: 100%;
        overflow: hidden;
    }
    .con {
        height: 100%;
        width: 100%;
        overflow: hidden;
    }
    .ant-tag {
        max-width: 100px;
        cursor: pointer;
        @include ellipsis();
    }
    .add-tag{
        padding-left:4px;
        padding-right: 4px;
    }
}

.infohub-page .common-ann-page {
    height: 100%;
    width: 100%;
    // padding: 14px 0;
    padding-bottom: 0;
    background: var(--colorBgLayout);

    .page-nav {
        flex-shrink: 0;
        width: 100%;
        min-height: var(--page-nav-height);
        padding: 0 20px;
        .nav-title {
            flex-shrink: 0;
            font-weight: 600;
            font-size: 18px;
            color: var(--colorText);
            line-height: 24px;
        }
        @mixin common {
            background: var(--colorBgBase);
            border-radius: 12px !important;
        }
        .button-icon {
            height: 40px;
            width: 40px;
            color: var(--colorIconSecondary);
            font-size: 16px;
            @include common;
        }
    }
    .segmented-types {
        margin-top: 8px;
        flex-shrink: 0;
        padding-left: 16px;
    }

    .common-wrap {
        background: var(--colorBgLayout);
    }
    .segmented-types {
        flex-shrink: 0;
    }
    .page-main{
        flex: 1;
        overflow: hidden;
    }
    .common-tab {
        flex-shrink: 0;
        padding: 0 20px;
    }
    .search-area {
        // padding: 0 20px;
        // margin-top: 8px;
        flex-shrink: 0;
        .toggle-unread {
            margin-left: auto;
        }
        .search-input {
            width: 250px;
        }
        .ant-select {
            width: 180px;
        }
    }
    .ann-list-con {
        flex: 1;
        overflow: auto;
        padding: 20px;
        // padding-top: 0;

        // .ann-list {
        //     width: 900px;
        //     margin: 0 auto;
        // }
        .list-item {
            width: 900px;
            // height: 201px;
            margin: 0 auto;
            position: relative;
            z-index: 10;
            // &:not(:last-child){
            //     margin-bottom: 20px;
            // }
            // &:first-child{
            //     margin-top: 20px;
            // }
            // &.is-pin{
            //     position: sticky;
            //     z-index: 20;
            //     // padding-bottom: 20px;
            //     // margin-bottom: 0;
            //     margin-top: 0;
            //     margin-bottom: 0;
            //     &:first-of-type{

            //     }
            //     &::before{
            //         content: "";
            //         position: absolute;
            //         top: 0;
            //         left: 0;
            //         width: 100%;
            //         background: var(--colorBgLayout);
            //     }
            // }
        }
        .li-pin {
            font-size: 16px;
            // color: var(--colorPrimary);
            position: absolute;
            top: -4px;
            left: -1px;
            transform: rotate(270deg);
        }
        .li-card {
            flex: 1;
            overflow: hidden;
            border: 1px solid var(--colorBorderSecondary);
            border-radius: 10px;
            cursor: pointer;
            padding: 10px 16px;
            background: var(--colorBgContainer);
            &.Archived {
                background: var(--colorFillTertiary);
                .li-card-top {
                    // border-bottom: 1px solid var(--colorBorder);
                }
            }
            &:hover {
                border-color: var(--colorBorder);
                box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
                .li-card-operate {
                    display: flex;
                }
                // .li-card-tags{
                //     display: none;
                // }
                .more{
                    display: none;
                }
            }
        }
        .li-card-bottom {
            min-height: 35px;
            .li-card-tags {
                max-width: 80%;
            }
            .more{
                font-size: 18px;
                margin-left: auto;
            }
        }
        .li-card-operate {
            display: none;
            // width: 100%;
            margin-left: auto;
            margin-top: auto;
        }
        .li-operate {
            flex-shrink: 0;
            width: 90px;
        }
        .li-card-top {
            // border-bottom: 1px solid var(--colorBorderSecondary);
            padding-bottom: 8px;
        }
        .li-card-priority {
            flex-shrink: 0;
            color: var(--colorError);
            font-size: 16px;
            font-weight: 600;
        }
        .li-card-title {
            flex: 1;
            @include font(14px, 22px, 600);
            @include ellipsis();
        }
        .li-card-operator{
            @include font(13px, 22px, 400, var(--colorText));
            @include ellipsis();
        }
        .li-card-content {
            @include font(14px, 22px, 400, var(--colorTextTertiary));
            @include ellipsis-multiline();
            margin-top: 8px;
            margin-bottom: 8px;
            max-height: 50px;
        }
        .li-card-time {
            flex-shrink: 0;
            margin-left: auto;
            @include font(12px, 18px, 500);
        }
        .li-operate-icon {
            cursor: pointer;
            font-size: 24px;
            // color: var(--colorIcon);
            color: var(--colorPrimary);
            // width: 50px;
            // height: 50px;
            // background: var(--colorBgElevated);
            // border-radius: 12px;
            // border: 1px solid var(--colorBorderSecondary);
            // color: var(--colorIcon);
        }
        .li-card-tag {
            height: auto;
        }
    }
    .pagition {
        margin-top: auto;
        flex-shrink: 0;
        // margin-left: auto;
        // padding-top: 16px;
        // padding-right: 16px;
        padding: 16px;
        border-top: 1px solid var(--colorBorderSecondary);
        width: auto;
    }
    .hidden{
        display: none;
    }
}
