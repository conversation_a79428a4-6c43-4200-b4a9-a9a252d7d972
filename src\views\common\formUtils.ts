export default {
    // 清除某个表单项的校验信息
    clearValidate: (form:any,fieldName:string)=> {
        form.setFields([
            {
            name: [fieldName], // 表单项的 name
            errors: [], // 设置为空数组，清除错误信息
            }
        ]);
    },
    // 重置表单校验信息
    resetValidate:(form:any)=>{
        const fieldsError = form?.getFieldsError()
        if (fieldsError?.length > 0 && fieldsError.filter((f: any) => f?.errors?.length > 0).length > 0) {
            // console.log('有错')
            const removeFieldsError = fieldsError.map((f: any) => {
                f.errors = []
                return f
            })
            form?.setFields(removeFieldsError)
        }
    },
    // 语言切换时，重新渲染表单校验信息
    refreshValidate: (form:any, t:any,callback?:any) => {
        const fieldsError = form.getFieldsError()
        if (fieldsError?.length > 0) {
            const frontErrors:any = fieldsError.filter((f: any) => f?.errors?.length > 0 && f?.warnings?.length == 0)
            const backendErrors:any = fieldsError.filter((f: any) => f?.errors?.length > 0 && f?.warnings?.length > 0)
            // console.log('frontErrors', frontErrors)
            // console.log('backendErrors', backendErrors)
            // 处理后端接口报错的表单提示信息
            if (backendErrors.length>0){
                backendErrors.forEach((item:any) => {
                    const targetMsg = item?.warnings[0]
                    form.setFields([
                        { name: item.name[0], errors: [t(targetMsg)], warnings:[targetMsg]}
                    ])
                });
                if (callback) callback(backendErrors)
            }
            if (frontErrors.length > 0) {
                // console.log('有错')
                const errorFileds = frontErrors.map((f: any) => f?.name)
                form?.validateFields(errorFileds)
            }
            return {frontErrors,backendErrors}
        }
        return {}
    }
}