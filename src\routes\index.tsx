import React from "react";
import { createBrowserRouter, redirect } from "react-router-dom";
import { with<PERSON>outer } from "@/components/base";
import login from "@/views/login";
// import login from "@/views/login/index2";
import register from "@/views/register";
import invite from "@/views/invite/index";
import inviteSuccess from "@/views/invite/inviteSuccess/index";
import layout from "@/views/layout";
// import landing from "@/views/landing";
import contacts from "@/views/contacts";
import chats from "@/views/chats";
import robot from "@/views/robot";
import robotSetting from "@/views/robot-setting";
import infoHub from "@/views/information-hub";
import error from "@/views/error";
import privacyPolicy from "@/views/legal-documents/privacyPolicy";
import termsConditions from "@/views/legal-documents/termsConditions";
import serviceAgreement from "@/views/legal-documents/serviceAgreement";
import organizeAgreement from "@/views/legal-documents/organizeAgreement";
// import widget from "@/views/widget/index";
import widgetBotChat from "@/views/widget/bot-chat";

import store from "@/store";
import { api_get_mycompanies } from '@/api/company'
import { dispatch_api_get_configurations } from "@/api/platform";
import * as actions from "@/store/action";


const Login = withRouter(login);
const Layout = withRouter(layout);
// const Landing = withRouter(landing);
const Robot = withRouter(robot);
const RobotSetting = withRouter(robotSetting);

const Contacts = withRouter(contacts);
const Chats = withRouter(chats);
const Error = withRouter(error);
const Register = withRouter(register);
const PrivacyPolicy = withRouter(privacyPolicy);
const TermsConditions = withRouter(termsConditions);
const ServiceAgreement = withRouter(serviceAgreement);
const OrganizeAgreement = withRouter(organizeAgreement);
const Invite = withRouter(invite);
const InviteSuccess = withRouter(inviteSuccess);
const InfoHub = withRouter(infoHub);
// const Widget = withRouter(widget);
const WidgetBotChat = withRouter(widgetBotChat);


// export const loginedDefaultPath = '/robots' // 登录后 默认路由
export const loginedDefaultPath = '/' // 登录后 默认路由

// 非登录状态路由
const staticRoutes = [
  {
    path: "/login",
    element: <Login />,
    loader: async (obj: any) => {
      storeCompany()
      // const requestUrl = new URL(obj.request.url);
      // const isInvite = requestUrl.searchParams.get('code') // 登录邀请不校验登录状态
      // console.log('isInvite',isInvite)
      const isLogin = store.getState().app.isLogin;
      if (isLogin) {
        return redirect(loginedDefaultPath)
      }
      return obj;
    },
    errorElement: <Error />
  },
  {
    path: "/register",
    element: <Register />,
    loader: async (obj: any) => {
      // const isPrivate = store.getState().app.isPrivate;
      // console.log('isPrivate', isPrivate)
      // if (isPrivate) return redirect("/login") // 私有化登录没有注册功能
      return obj;
    },
    errorElement: <Error />
  },
  { // 邀请
    path: "/invite",
    element: <Invite />,
    loader: async (obj: any) => {
      // const isPrivate = store.getState().app.isPrivate;
      // console.log('isPrivate', isPrivate)
      // if (isPrivate) return redirect("/login") // 私有化登录没有邀请功能
      return obj;
    },
    errorElement: <Error />
  },
  { // 接受邀请成功
    path: "/inviteSuccess",
    element: <InviteSuccess />,
    loader: async (obj: any) => {
      // const isPrivate = store.getState().app.isPrivate;
      // console.log('isPrivate', isPrivate)
      // if (isPrivate) return redirect("/login") // 私有化登录没有邀请功能
      return obj;
    },
    errorElement: <Error />
  },
  {
    path: "/privacyPolicy",
    element: <PrivacyPolicy />,
    loader: async (obj: any) => {
      return obj;
    },
    errorElement: <Error />
  },
  {
    path: "/termsConditions",
    element: <TermsConditions />,
    loader: async (obj: any) => {
      return obj;
    },
    errorElement: <Error />
  },
  {
    path: "/serviceAgreement",
    element: <ServiceAgreement />,
    loader: async (obj: any) => {
      return obj;
    },
    errorElement: <Error />
  },
  {
    path: "/organizeAgreement",
    element: <OrganizeAgreement />,
    loader: async (obj: any) => {
      return obj;
    },
    errorElement: <Error />
  },
  {
    // -插件机器人聊天
    path: "widget-bot",
    element: <WidgetBotChat />,
    loader: async (obj: any) => {
      return obj;
    },
    errorElement: <Error />,
    // children: [
    //   {
    //     // index: true,
    //     path: "/widget/bot",
    //     element: <WidgetBotChat />,
    //     // loader: async (obj: any) => {
    //     //   return obj;
    //     // },
    //     // errorElement: <Error />
    //   }
    // ]
  }
]

// 保存企业信息
const storeCompany = async () => {
  try {
    // 切换企业，不同域名新打开窗口时，接受 企业ID传参
    const urlParams = new URLSearchParams(window.location.search);
    const companyId = urlParams.get('CID'); // 企业ID
    const companyName = urlParams.get('CNAME'); // 企业名称
    if (companyId) {
      console.log('----保存企业信息 companyId,companyName', companyId, companyName)
      // 保持企业信息
      await store.dispatch(actions.change_app_active_company({ id: companyId, name: companyName }))
    }
  } catch (error) {
    console.log('error', error)
  }
}

const packRouteLoader = async (object: any) => {
  storeCompany()

  const isLogin = store.getState().app.isLogin;
  if (!isLogin) {
    return redirect("/login")
  }
  // 登录后，获取部署方式
  const get_public_config = dispatch_api_get_configurations()
  const { isSaaS }: any = await get_public_config(store.dispatch).catch(() => {
  })
  // console.log('isSaaS',isSaaS)
  if (!isSaaS) return object;
  // 若是sass化部署先获取企业数据
  const get_mycompanies = api_get_mycompanies()
  const { data: companies }: any = await get_mycompanies(store.dispatch).catch(() => {
  })
  return object
}

const router = createBrowserRouter([
  ...staticRoutes,
  {
    // 首页
    path: "/",
    element: <Layout />,
    loader: packRouteLoader,
    children: [
      {
        // 机器人
        path: "",
        index: true,
        // path: "/robots",
        element: <Robot />,
        errorElement: <Error />
      },
      {
        path: '/robotSetting',
        element: <RobotSetting />,
        errorElement: <Error />
      },
      {
        // 聊天
        path: "/chats",
        element: <Chats />,
        errorElement: <Error />
      },
      {
        // 通讯录
        path: "/contacts",
        element: <Contacts />,
        errorElement: <Error />
      },
      {
        // 消息中心
        path: "/inforHub",
        element: <InfoHub />,
        errorElement: <Error />
      },

    ],
    errorElement: <Error />
  },
])
export default router;
export { staticRoutes }
