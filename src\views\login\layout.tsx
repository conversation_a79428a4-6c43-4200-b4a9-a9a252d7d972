import React from "react";
import { Link } from "react-router-dom";
import { Select, Flex } from 'antd';
import { connect } from "react-redux";
import i18next from "i18next";
import { change_app_globalLang } from "@/store/action";
import Svgicon from '@/components/svgicon'
import Carousel from './components/carousel'
import { capitalize } from '@/utils/common'

// 公共布局（登录、注册、邀请）
const CommonLayout = (props: any) => {
    const { globalLang, themeInfo: customThemeInfo, defaultThemeInfo, children, hideCarousel = false, isInvite = false } = props;
    const targetTheme :any  = isInvite ? customThemeInfo : defaultThemeInfo
    const loginLogoIcon = targetTheme['logoLogin' + capitalize(targetTheme?.theme)]

    // 语言切换
    const handleChangeGlobalLang = (lang: string) => {
        props.change_app_globalLang(lang)
    }

    return (
        <div className="login-wrapper">
            <div className="login-center">
                {
                    !hideCarousel ?
                        <div className="publicity">
                            <Carousel />
                        </div> : null
                }
                <Flex className="right">
                    <div className="logo-tittle">
                        {
                            loginLogoIcon ? <div className="logo" style={{ background: `url(${loginLogoIcon}) no-repeat center / contain` }}></div>
                                : null
                        }
                        <Select className={"select-lang"}
                            variant="borderless"
                            value={globalLang}
                            popupClassName="select-lang-popup"
                            options={[
                                { label: "简体中文", value: "zh", emoji: 'CN', desc: '简体中文' },
                                { label: "繁體中文", value: "hk", emoji: 'HK', desc: '繁體中文' },
                                { label: "English", value: "en", emoji: 'EN', desc: 'English' }
                            ]}
                            onChange={handleChangeGlobalLang}
                            suffixIcon={<Svgicon svgName="arrow_down_line" />}
                            optionRender={(option) => (
                                <span>{option.data.desc}</span>
                            )}
                        />
                    </div>
                    {children}
                </Flex>
            </div>
            <Flex className={"login-meta"} gap={12} justify={'center'}>
                <span>© ALL RIGHTS RESERVED 2025 Vintech Innovation Technology Ltd.</span>
                <Link className="link" to="/privacyPolicy" target={"_blank"}>{i18next.t("login.privacy-policy")}</Link>
                <Link className="link" to="/termsConditions" target={"_blank"}>{i18next.t("login.terms-conditions")}</Link>
                {/* <span className="link" onClick={handleLoginAdmin}>{i18next.t("login.user-center")}</span> */}
            </Flex>
        </div>
    )
}
const mapStateToProps = (state: any) => ({
    globalLang: state.app.globalLang,
    globalRegion: state.app.globalRegion,
    themeInfo: state.app.themeInfo,
    defaultThemeInfo: state.app.defaultThemeInfo,
})

const mapDispatchToProps = (dispatch: any) => ({
    change_app_globalLang: (globalLang: string) => dispatch(change_app_globalLang(globalLang))
})

export default connect(mapStateToProps, mapDispatchToProps)(CommonLayout);