import { Cascader, message,Input,Skeleton,Flex } from "antd";
import i18next from "i18next";
import { useState, useEffect, forwardRef, useImperativeHandle } from "react";
import './index.scss'
import Svgicon from "@/components/svgicon"
import classNames from "classnames";

const LLModelCascaderComponent = forwardRef((props: any, ref) => {
  interface CascaderOption {
    value: string;
    label: string;
    children?: CascaderOption[];
    disabled?: boolean,
    image?: any;
    Icon?: any;
    imageSelected?: any;
  }
  // 自定义暴露给父组件的实例值
  useImperativeHandle(ref, () => ({
    // // 初始化
    // emit_init() {
    // },
    // // 更新选项
    // emit_update_options(options:any){
    //   setLlmModelList(options)
    // }
  }))

  /** props **/
  const options = props.options
  const value = props.value
  const isLoading = props.loading
  /**state **/
  const [llmModelList, setLlmModelList] = useState([]) // 下拉框选项数据
  const [llmModelSelected, setLlmModelSelected] = useState([]) // 下拉框选中值
  const [searchValue, setSearchValue] = useState("") // 搜索框
  const [svgText,setSvgText] =useState("")
  
  const parentSVGImageMap :any ={
    Perplexity: "perplexity",
    Cloud: "cloud",
    Poe: "poe",
    Local: "local"
  }

  /**method **/
  // llm Cascader 置灰选项，点击提示信息
  const handleLlmCascaderOptionClick =(disabled?:boolean|undefined)=>{
    if (disabled){
      message.open({ type: "error", content: i18next.t(["error.llm-disabled-tip"]) })
      return
    }
  }

  // llm Cascader change
  const handleLlmCascaderOptionChange = async(value:any, selectedOptions:any) => {
    props.onUpdate(value)
  };

  const handleLlmCascaderOptionChangeFocus=()=>{
    setSearchValue('')
  }
  
  const getImage=( value: string | number, image?: any)=>{
    let isParent = false
    if (Object.keys(parentSVGImageMap).includes(value as string)){
      isParent = true
    }
    return(
      <>
        {
          !isParent ?
          (image ? <img draggable="false" className="cascader-option-image" src={image} alt="" /> : null)
          :<Svgicon className="cascader-option-parent-image" svgName={parentSVGImageMap[value]} />
        }
      </>
    )
  }

  // 自定义llm 模型Cascader组件选项，显示图标
  const llmCascaderOptionRender: React.FC<{ label: string, value: string | number, image?: any, Icon?: any, disabled?: boolean,text_output:any, image_output:any }> = ({ label, value, image, Icon, disabled,text_output, image_output}) => {
    // console.log('Icon',Icon)
    // let str:any =''
    // if (Icon){
    //   fetchSvg("https://cdn.worldvectorlogo.com/logos/manchester-united.svg")
    // }
    // let isParent = false
    // if (Object.keys(parentSVGImageMap).includes(value as string)){
    //   isParent = true
    // }
    let isParent = false
    if (Object.keys(parentSVGImageMap).includes(value as string)){
      isParent = true
    }
    const tags = []
    if(text_output){
        tags.push(i18next.t('text-generation'))
    }
    if(image_output){
        tags.push(i18next.t('image-generation'))
    }
    return (
      <Flex gap={10} className={classNames(["custom-cascader-option",{parent:isParent}])} key={value} onClick={() => handleLlmCascaderOptionClick(disabled)}>
        {
          getImage(value, image)
        }
        {/* {
          Icon ? (
            // <div className="svg" data-icon={require('@/assets/icon_llm_cloud.svg')}></div>
            // <svg aria-hidden="true" className="cascader-option-image" style={{}}>
            //   <use href="https://cdn.worldvectorlogo.com/logos/manchester-united.svg" />
            // </svg>
           <img draggable="false" className="cascader-option-image" src={require('@/assets/icon_llm_cloud.svg')} alt="" /> 
          // <div
          //   className="svg cascader-option-image"
          //   dangerouslySetInnerHTML={{ __html: svgText }} // 使用 dangerouslySetInnerHTML 插入 SVG
          // />
          ):null
        } */}
        <Flex  className="cascader-option-texts" vertical gap={4}>
          <div className="cascader-option-label">{label}</div>
          { 
              tags.length>0 ?
              <div className="cascader-option-tag">{tags.join("、")}</div>
              :null
          }
        </Flex>
      </Flex>
    );
  };

  const fetchSvg = async (svgUrl:any) => {
    try {
      const response = await fetch(svgUrl);
      const svgText = await response.text();
      // console.log('svgText', svgText)
      setSvgText(svgText)
      return svgText
    } catch (error) {
      console.error('Error fetching SVG:', error);
      return ''
    }
};

  // 自定义llm 模型Cascader组件 已选项
  const displayLlmCascaderRender: any = (labels: string[], selectedOptions = []) => {
    const result = labels.map((label, i) => {
      const option: CascaderOption = selectedOptions[i];
      if (i === labels.length - 1) {
        return (
          <div key={option?.value} className="cascader-option-selected" >
            {/* {option.imageSelected || option.image ? <img id="cascader-option-selected-image" src={option.imageSelected || option.image} alt="" /> : null} */}
            <span id="cascader-option-selected-image" data-image={option?.image}>{getImage(option?.value, option?.image)}</span>
            <span id="cascader-option-selected-name">{label}</span></div>
        )
      }
      return '';
    });
    return result
  }

  const changeSearchValue =(e:any)=>{
    setSearchValue(e.target.value)
  }

  const dropdownRender = (menus: React.ReactNode) => (
    <div className="cascader-custom-search">
      <Input className="cascader-search-input custom-input" variant="filled" value={searchValue} onChange={changeSearchValue} placeholder={i18next.t("search") as string}/>
      {menus}
    </div>
  );
  const { SHOW_CHILD } = Cascader;

  useEffect(() => {
    setLlmModelList(options)
  }, [options])
  
  useEffect(() => {
    // console.log('value',value)
    setLlmModelSelected(value)
  }, [value])

  return (
    <>
      {
        isLoading ?
        <Skeleton.Node  style={{ width: 120, height:36 }} active />
        :<Cascader
            className={"user-model-cascader " + (searchValue!=''? 'searching':'')}
            disabled={props.disabled}
            allowClear={false}
            options={llmModelList}
            value={llmModelSelected}
            onChange={handleLlmCascaderOptionChange}
            onFocus={handleLlmCascaderOptionChangeFocus}
            displayRender={displayLlmCascaderRender}
            popupClassName={'pointer-cascader-dropdown user-model-cascader-dropdown ' + (searchValue!=''? 'searching':'')}
            optionRender={llmCascaderOptionRender}
            dropdownRender={dropdownRender}
            searchValue={searchValue}
            showCheckedStrategy={SHOW_CHILD}
            showSearch={false}
            // showSearch={true}
            suffixIcon={<Svgicon svgName="arrow_down"/>}
            expandIcon={<Svgicon svgName="arrow_right_line"/>}
          />
      }
    </>
  );
});

export default LLModelCascaderComponent;
