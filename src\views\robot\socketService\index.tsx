interface WebSocketDataParams {
  type: string;
  action: string;
  chartName: string;
  value: boolean;
  text: string,
  seq: number,
  robotId: string,
}

interface WebSocketDataProps {
  type: string;
  action?: string;
  isStreaming: boolean;
  streamingToken: string;
  isFinished: boolean;
  response: any;
}

class SocketService {
  /**
   * 单例 保证拿到的都是同一个实例
   */
  static instance: SocketService;
  static get Instance() {
    if (!this.instance) {
      this.instance = new SocketService();
    }
    return this.instance;
  }

  baseUrl: string;
  ws: WebSocket;
  callBackMapping: Record<string, any>;
  isconnect: boolean;
  sendRetryCount: number; //重复发送次数
  connectRetryCount: number;
  socketTypes: string[];
  HEARTBEAT_INTERVAL: number;
  heartbeatInterval: any

  constructor(url: string = "") {
    this.baseUrl = `${window.location.protocol === "http:" ? "ws" : "wss"}://${window.location.host
      }/botws/chat`;
    this.ws = {} as WebSocket;
    this.callBackMapping = {};
    this.isconnect = false;
    this.sendRetryCount = 0;
    this.connectRetryCount = 0;
    this.socketTypes = ["chatMsg", "homeChatMsg", "enhanceChatMsg"];
    this.HEARTBEAT_INTERVAL = 10000; // 心跳检查间隔时间 30秒
    this.heartbeatInterval = null
  }

  //和服务端创建的stocket对象

  //定义连接服务器的方法
  connect(tenantId?:any) {
    if (!window.WebSocket) {
      return console.log("您的浏览器不支持WebSocket");
    }
    const url = this.baseUrl;
    const token:any = localStorage.getItem('AMA_USER_TOKEN')
    const isPrivate = localStorage.getItem("A_PRT") === '1' // 私有化部署tenantId都传1
    tenantId = isPrivate ? 1 : (tenantId || localStorage.getItem('ACTCID'))
    // console.log('tenantId',tenantId)
    this.ws = new WebSocket(`${url}?token=${token}&tenantId=${tenantId}`); // ,[token]

    //连接监听
    this.ws.onopen = () => {
      console.log("😄连接服务端成功，企业信息：",tenantId);
      this.connectRetryCount = 0;
      this.isconnect = true;

      // 启动心跳机制
      // this.startHeartbeat()
    }

    // 连接关闭的回调
    this.ws.onclose = (event) => {
      console.log('event', event)
      this.connectRetryCount++;
      console.log("😰连接失败/关闭");
      clearInterval(this.heartbeatInterval); // 清除心跳定时器
      this.isconnect = false;
      if (event.code === 3001){ // 客户端主动发起关闭
        return
      } 
      //当连接关闭后进行重新连接尝试
      this.reconnect()
    };

    // 收到消息的回调
    this.ws.onmessage = (res) => {
      const recvData: WebSocketDataProps = JSON.parse(res.data);
      //   console.log('recvData', recvData)
      try {
        const stocketType = recvData.type; //与后端约定type
        //如果存在，直接调用
        const callBack = this.callBackMapping[stocketType]; //执行订阅的回调
        if (callBack) {
          //将数据传给回调函数
          callBack.call(this, recvData);
        }
        if (stocketType === 'pong') {
          // console.log('心跳响应');
        }
      } catch (error) {
        console.log(error)
      }
    };

    // 监听窗口关闭事件，当窗口关闭时，主动去关闭websocket连接，防止连接还没断开就关闭窗口，server端会抛异常。
    window.onbeforeunload = this.onbeforeunload
    // 连接错误
    this.ws.onerror = (error) => {
      console.error('😰连接错误:', error);
      this.isconnect = false;
    }
  }

  // 心跳检查
  startHeartbeat() {
    // 启动心跳机制
    this.heartbeatInterval = setInterval(() => {
      if (this.ws.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify({ type: 'ping' })); // 发送心跳消息
        // console.log('发送心跳');
      }
    }, this.HEARTBEAT_INTERVAL);
  }

  // 重连
  reconnect(){
    setTimeout(() => {
      this.connect();// 重新连接函数
    }, this.connectRetryCount * 500);
  }
  
  //注册回调函数,可能有多个scoket
  registerCallBack(
    socketType: string,
    callBack: (data: Record<string, any>) => void
  ) {
    this.callBackMapping[socketType] = callBack;
  }

  unRegisterCallBack(socketType: string) {
    this.callBackMapping[socketType] = null;
  }

  send(data: WebSocketDataParams) {
    if (this.isconnect) {
      this.sendRetryCount = 0; //发送成功重置为0
      this.ws.send(JSON.stringify(data));
    } else {
      setTimeout(() => {
        this.sendRetryCount++;
        this.send(data);
      }, this.sendRetryCount * 500);
    }
  }
  disconent(){
    if (this.ws) {
      this.ws.close(3001, '客户端主动关闭')
    }
  }

  onbeforeunload() {
    if (this.ws) {
      this.ws.close(3001, '客户端主动关闭')
    }
  }

}
const wss = SocketService.Instance;
export { wss };
