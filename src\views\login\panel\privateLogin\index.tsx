import React, { useState, useEffect, useRef } from 'react';
import { connect } from "react-redux";
import { useNavigate } from "react-router-dom";
import type { FormProps } from 'antd';
import { Button, Form, Input, Flex, message } from 'antd';
import type { FormInstance } from 'antd';
import i18next from "i18next";
import { useTranslation } from 'react-i18next';
import { Encrypt } from '@/utils/forge'
import Svgicon from '@/components/svgicon'
import classnames from "classnames";
import PasswordInput from '@/components/form/passwordInput'
import VerifyCode from '@/components/form/verifyCode'
import { CountdownProvider } from '@/components/form/verifyCode/CountdownProvider';
import ForgotPasswordPanel from '../forgotPassword'

import { PasswordRules, EmailRules, VerifyCodeRules } from '@/views/common/formRule'
import FormUtils from '@/views/common/formUtils'
import ErrorCodeMap from '@/views/common/formErrorCodeMap'
import { api_auth_sessions, api_get_email_code } from '@/api/user'
import { change_app_userInfo } from "@/store/action";
import { RequestFailedMessage } from '@/utils/userRequest'
import { loginedDefaultPath } from '@/routes'

type FieldType = {
    account?: string;
    password?: string;
};

const PrivateLogin = (props: any) => {


    const navigate = useNavigate();
    const { t, i18n } = useTranslation();
    const [isSubmiting, setIsSubmiting] = useState(false)
    const [loginType, setLoginType] = useState('psd')//密码或者验证码登录
    const [activePanel, setActivePanel] = useState('login')//登录 还是忘记密码
    const [form] = Form.useForm();
    const [formValues, setFormValues] = useState<any>({});
    const { themeInfo } = props
    const forgotPasswordRef = useRef()

    // 私有化提交
    const handleLoginSubmit = () => {
        const fields = loginType == 'psd' ? ["email", "password"] : ["email", "code"]
        form.validateFields(fields).then(() => {
            // console.log('---getFieldsValue', form.getFieldsValue())
            const values: any = form.getFieldsValue()

            const data: any = { email: values.email }
            if (loginType == 'psd') {
                data.password = Encrypt(values.password) // 加密
                // console.log('data.password',data.password)
                data.loginType = "EMAIL_PASSWORD"
            } else {
                data.code = values.code
                data.loginType = "EMAIL_CODE"
            }
            // console.log('data', data)
            setIsSubmiting(true)
            props.dispatch_api_auth_sessions(data).then(async (result: any) => {
                setIsSubmiting(false)
                // console.log('result',result)
                let code = result.code
                if (code === 0) {
                    message.success(t("login.sign-success"));
                    navigate(loginedDefaultPath)
                    return
                }
                const accountErrors = ErrorCodeMap.account // 账号或密码 错误码
                const codeErros = ErrorCodeMap.code// 验证码错误码
                const errors = [...accountErrors, ...codeErros]
                if (!errors.includes(code)) {
                    const msg = RequestFailedMessage(result?.msg, code)
                    message.error(msg);
                    return
                }
                if (code == **********) code = '**********_1' // 特殊处理
                const errMsg = t(`errorCode.${code}`)
                if (loginType == 'psd') {
                    form.setFields([
                        { name: 'email', errors: [''] },
                        { name: 'password', errors: [errMsg], warnings: [`errorCode.${code}`] }
                    ])
                } else {
                    if (code == **********) {// 用户不存在
                        form.setFields([{ name: 'email', errors: [errMsg], warnings: [`errorCode.${code}`] }])
                    } else {
                        form.setFields([{ name: 'code', errors: [errMsg], warnings: [`errorCode.${code}`] }])
                    }
                }

            }).catch((err: any) => {
                setIsSubmiting(false)
                console.log('err', err)
                // message.error(t('error.request-failed'));
            });
        }).catch((err) => {
            console.log('err', err)
        });
    }

    // 表单项 输入改变事件
    const onFiledValueChange = (changedFields: any, allValues: any) => {
        try {
            // 更新状态以触发重新渲染
            // console.log('allValues',allValues)
            setFormValues(allValues);
            Object.keys(changedFields).forEach((fieldName: any) => {
                const fieldError = form?.getFieldError(fieldName)
                if (fieldError.length > 0) {
                    FormUtils.clearValidate(form, fieldName)
                }
            })
        } catch (error) {

        }
    }

    const handleOnFormItemKeyUp = (e: any) => {
        // const email = form.getFieldValue('email');
        // const password = form.getFieldValue('password');
        // // console.log('e.keyCode', e.keyCode)
        // if (e.keyCode === 13) {
        //     if (email && password) {
        //         handleLoginSubmit();
        //     }
        // }
    }

    // 忘记密码->登录页面
    const backToLogin = () => {
        setActivePanel('login')
    }
    const onChangeForgotPassword = () => {
        // form.resetFields()
        setActivePanel('forgotPsd')

    }
    const onChangeLoginType = () => {
        // console.log('sdfsdf',form.getFieldError('email'))
        const isPsd = loginType == 'psd'
        form.resetFields([isPsd ? 'password' : 'code'])
        setLoginType((isPsd ? 'code' : 'psd'))
        const emailValidateError = form.getFieldError('email')
        if (emailValidateError && emailValidateError.length == 1 && emailValidateError[0] == '') {
            FormUtils.clearValidate(form, 'email')
        }
    }

    useEffect(() => {
        props.onUpdateConTact(activePanel == 'forgotPsd')
    }, [activePanel])

    // 语言切换时，重新渲染表单校验信息
    useEffect(() => {
        const fun: any = () => {
            const callback = (backendErrors: any) => {
                // console.log('单独处理 backendErrors',backendErrors)
                if (backendErrors && backendErrors.length > 0) {
                    if (loginType == 'psd' && backendErrors.filter((i: any) => i.name[0] == 'password').length > 0) {
                        // console.log('yyyy')
                        setTimeout(() => {
                            form.setFields([{ name: 'email', errors: [''], touched: true }])
                        }, 10)
                    }
                }
            }
            FormUtils.refreshValidate(form, t, callback)
            // 单独处理 邮箱 + 密码 报错状态
        }
        // 监听语言变化
        i18n.on('languageChanged', fun);

        // 清理监听器
        return () => {
            i18n.off('languageChanged', fun);
        };
    }, [i18n]);

    return (
        <CountdownProvider>
            <div className="content">
                {/* 登录面板 */}
                {
                    <div className={activePanel == 'login' ? "" : 'hidden'}>
                        <div className="content-tittle">
                        {themeInfo?.platformName || i18next.t("login.control-center")}
                        </div>
                        <div className="content-describe">{t('login.login-tip2')}</div>

                        <Form
                            form={form}
                            className="login-from common-form"
                            name="basic"
                            // initialValues={{ remember: true }}
                            autoComplete="off"
                            onValuesChange={onFiledValueChange}
                            validateTrigger="onBlur"
                        >
                            {/* 邮箱 */}
                            <Form.Item name="email" label={""} rules={[{ required: true, whitespace: true, message: '' }, ...EmailRules(t)]}>
                                <Input autoComplete="new-email" onKeyUp={(e) => handleOnFormItemKeyUp(e)}
                                    className="ipt" variant="filled" maxLength={300} placeholder={t('login-pls.email') as string} />
                            </Form.Item>

                            {/* 密码 */}
                            <Form.Item className={classnames(["psd-form-item"], { 'hidden': loginType == 'code' })} name="password"
                                rules={[{ required: true, whitespace: true, message: '' }, ...PasswordRules(t)]}>
                                <PasswordInput
                                    key="login-password"
                                    onKeyUp={(e: any) => handleOnFormItemKeyUp(e)} />
                            </Form.Item>

                            {/* 验证码 */}
                            <Form.Item status='success' className={classnames(["psd-form-item"], { 'hidden': loginType == 'psd' })} name="code" label={""}
                                rules={VerifyCodeRules(t)}>
                                <VerifyCode api={api_get_email_code} scene="MEMBER_LOGIN"
                                    target="email" email={form.getFieldValue('email')} targetValidate={form.validateFields}
                                    onKeyUp={(e: any) => handleOnFormItemKeyUp(e)} />
                                {/* email={formValues.email} */}
                            </Form.Item>

                            <Flex className="operate-item" justify="space-between">
                                <div onClick={onChangeForgotPassword}>{t('login.forgot-password')}</div>
                                <div className='oi-r' onClick={onChangeLoginType}>{loginType == 'psd' ? t('login.verifiy-code-login') : t('login.password-login')}</div>
                            </Flex>

                            <div className={"form-submit"}>
                                <SubmitButton disabled={isSubmiting} form={form} loginType={loginType} onClick={handleLoginSubmit}>{i18next.t("login.sign")}</SubmitButton>
                            </div>
                        </Form>
                    </div>
                }

                {/* 忘记密码面板 */}
                <div className={activePanel == 'forgotPsd' ? "" : 'hidden'} >
                    <ForgotPasswordPanel condition={{ type: 'Email', data: form.getFieldValue('email') }} ref={forgotPasswordRef} onBack={backToLogin} />
                </div>
            </div>
        </CountdownProvider>
    )
};

interface SubmitButtonProps {
    form: FormInstance;
    loginType: string;
    disabled?: boolean,
    onClick: any
}

const SubmitButton: React.FC<React.PropsWithChildren<SubmitButtonProps>> = ({ form, children, onClick, loginType, disabled }) => {
    const [submittable, setSubmittable] = React.useState<boolean>(false);

    // Watch all values
    const values = Form.useWatch([], form);
    // console.log('Watch values', values)
    const fields = loginType == 'psd' ? ["email", "password"] : ["email", "code"]
    // console.log('fields', fields)
    useEffect(() => {
        form
            .validateFields(fields, { validateOnly: true })
            .then(() => setSubmittable(true))
            .catch(() => setSubmittable(false));
    }, [form, values, loginType]);

    useEffect(() => {
        setSubmittable(!disabled)
    }, [disabled])


    return (
        <Button size="large" type="primary" onClick={onClick} disabled={!submittable} className={"btn"}>
            {children}
        </Button>
    );
};

const mapStateToProps = (state: any) => ({
    themeInfo: state.app.themeInfo,
});

const mapDispatchToProps = (dispatch: any) => ({
    change_app_userInfo: (data: any) => dispatch(change_app_userInfo(data)),
    dispatch_api_auth_sessions: (data: any) => dispatch(api_auth_sessions(data))
})
export default connect(mapStateToProps, mapDispatchToProps)(PrivateLogin);