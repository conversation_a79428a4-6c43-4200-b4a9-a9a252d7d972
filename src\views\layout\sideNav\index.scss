@import "../../../styles/mixin.scss";
.side-nav-container{
    @include flex($flex-direction:column);
    width: 68px;
    height: 100%;
    padding: 8px 11px 25px;
    background:var(--colorFillSecondary);
    .side-nav-logo{
        flex-shrink: 0;
        img{
            width: 30px;
            height: 30px;
            cursor: pointer;
            border-radius: 10px;
        }
        .svg-icon{
            color: var(--colorIcon);
            font-size: 16px;
        }
    }
    .side-nav-menu{
        flex: 1;
        padding: 12px 4px;
        @include flex($flex-direction:column);
        gap: 12px 0;
        .nav-menu-item{
            position: relative;
            font-size: 22px;
            cursor: pointer;
            width: 38px;
            height: 38px;
            @include flex($justify-content:center);
            color: var(--colorIcon);
            &.active{
                color:var(--colorPrimary);
                border-radius: 50%;
            }
            
            &:hover{
                background:var(--colorFillTertiary);
                border-radius: 50%;
            }
            .nav-chat-unred{
                position: absolute;
                top: 0;
                right: 0;
                background: var(--colorError);
                @include font(10px,10px,500,#fff);
                z-index: 20;
                border-radius: 16px;
                padding: 2px 4px;
            }
            .nav-ann-unread{
                font-size: 22px;
                .svg-icon{

                }
            }
            &.is-bottom{
                margin-top: auto;
            }
        }
    }
    .side-nav-operate{
        margin-top: auto;
    }
    
}
.ant-popover.user-center-popover{
    width: 300px;
    border-radius: 12px;
    box-shadow:  0px 0px 20px 0px rgba(0, 0, 0, 0.10);
    border: 1px solid var(--colorBorder);
    .ant-popover-inner{
        background: var(--colorBgLayout);
        padding: 0;
        border-radius: 12px;
    }
    .user-center-popover-content{
        height: auto;
        padding: 12px 8px;
        padding-right: 0;
        max-height: calc(100vh - 60px);
    }
    .svg-icon{
        font-size: 16px;
        width: 16px;
        height: 16px;
        color: var(--colorPrimary);
        flex-shrink: 0;
    }
    .menu-title{
        font-size: 12px;
        line-height: 18px;
        font-weight: 600;
        margin-bottom: 8px;
        flex-shrink: 0;
        padding-right: 8px;
    }
    .menu-content{
        flex: 1;
        overflow:auto;
        padding-right: 8px;
    }
    .menu-content-item{
        border-radius: 12px;
        background: var(--colorBgElevated);
        padding: 8px;
        cursor: pointer;
    }
    .menu-operate{
        flex-shrink: 0;
        padding-top: 8px;
        padding-right: 8px;
        .ant-btn{
            flex: 1;
            // height: 34px;
            background: var(--colorFillSecondary);
        }
    }
    .team-logo{
        flex-shrink: 0;
        width: 32px;
        height:32px;
        border-radius: 10px;
        &.personal{
            font-size: 12px;
            line-height: 18px;
            font-weight: 600;
            color: #00474F;
            background: #B5F5EC;
        }
    }
    .team-name{
        flex: 1;
        @include font(14px,22px,500);
        @include ellipsis-multiline();
        margin-right: 8px;
    }
    .team-selected{
        flex-shrink: 0;
        font-size: 16px;
    }
}

.ant-popover.side-nav-menu-item-popover{
    .ant-popover-inner{
        padding: 8px;
    }
    .menu-item-popover-detail{
        width: 244px;
        .title{
            @include font(14px,22px,600);
            word-wrap: break-word;
            word-break: break-word;
        }
        .content{
            @include font(14px,22px,400, var(--colorTextSecondary));
            word-wrap: break-word;
            word-break: break-word;
        }
    }
}