import { Button, Flex, message, Modal } from "antd";
import { connect } from "react-redux";
import { SearchOutlined } from '@ant-design/icons';
import CopyToClipboard from 'copy-to-clipboard';
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import EditEmail from './EditEmail/index'
import EditPassword from './EditPassword/index'
import EditMobile from './EditMobile/index'
import DeleteAccount from './DeleteAccount/index'
import { api_get_my_profile } from '@/api/setting'

import './index.scss'

// 我的账号
const MyAccount = (props: any) => {
  const { t } = useTranslation();
  const [modal, contextHolder] = Modal.useModal();

  const [currentAccountInfo, setCurrentAccountInfo] = useState({ userID: '', mobile: '', email: '' }) // 当前登录账号信息
  const [editEmailModal, setEditEmailModal] = useState<any>({ visible: false, email: '', mobile: '' }) // 是否显示 编辑邮箱 弹窗
  const [editPasswordModal, setEditPasswordModal] = useState({ visible: false, email: '', mobile: '' }) // 是否显示 编辑密码 弹窗
  const [editMobileModal, setEditMobileModal] = useState<any>({ visible: false, email: '', mobile: null }) // 是否显示 编辑手机号 弹窗
  const [deleteAccountModal, setDeleteAccountModall] = useState<any>({ visible: false }) // 是否显示 注销账号 弹窗

  // 初始化
  useEffect(()=>{
    query()
  },[])

  const query = async()=>{
    const res:any = await api_get_my_profile('0').catch(()=>{
    })
    if (!res) return
    // console.log('res',res)
    res.userID = res.imUserId
    res.email = res.corpEmail
    res.mobile = res.corpPhone
    setCurrentAccountInfo(res)
  }

  // 复制user id
  const handleCopyUserId = () => {
    const userID = currentAccountInfo.userID
    CopyToClipboard(userID)
    message.success(t("chat.copy-success"))
  }

  // 编辑邮箱
  const handleEditEmail = () => {
    // console.log('currentAccountInfo',currentAccountInfo)
    setEditEmailModal({ email: currentAccountInfo.email, mobile: currentAccountInfo.mobile, visible: true })
  }

  //编辑邮箱-成功
  const onEditEmailUpdate = () => {
    onCloseEditEmail()
    query()
  }

  //关闭编辑邮箱
  const onCloseEditEmail = () => {
    setEditEmailModal({ visible: false, email: '', mobile: '' })
  }
  // 编辑密码
  const handleEditPassword = () => {
    setEditPasswordModal({ visible: true, email: currentAccountInfo.email, mobile: currentAccountInfo.mobile })
  }
   //编辑密码-成功
  const onEditPasswordUpdate = () => {
    onCloseEditPassword()
    query()
  }

  //关闭修改密码
  const onCloseEditPassword = () => {
    setEditPasswordModal({ visible: false, email: '', mobile: '' })
  }

  // 编辑手机号
  const handleEditMobile = () => {
    setEditMobileModal({ visible: true, mobile: currentAccountInfo.mobile || null, email: currentAccountInfo.email })
    // setEditMobileModal({ visible: true, mobile: '', email: currentAccountInfo.email  })
  }
  
  //编辑手机号-成功
  const onEditMobileUpdate = () => {
    onCloseEditMobile()
    query()
  }
  
  //关闭修改手机号
  const onCloseEditMobile = () => {
    setEditMobileModal({ visible: false, mobile: null, email: '' })
  }

  //关闭注销账号
  const onCloseDeleteAccount = () => {
    setDeleteAccountModall({ visible: false})
  }

  // 注销账号
  const handleDeleteAccount = () => {
    // 打开注销弹窗
    setDeleteAccountModall({ visible: true})
  }

  return (
    <>
      <div className="myaccount-wrapper wrapper">
        <div className="title">{t('setting.accountDetails')}</div>
        <div className="account-content">
          <div className="account-item">
            <span className="account-title">{t('contacts.organize.user-id')}</span>
            <span className="account-val">{currentAccountInfo.userID}</span>
            <Button icon={<SearchOutlined />} onClick={() => handleCopyUserId()} className="" color="default" variant="filled">{t('app.copyed')}</Button>
          </div>
          <div className="account-item">

            <span className="account-title">{t('login.email')}</span>
            <span className="account-val">{currentAccountInfo.email}</span>
            <Button onClick={() => handleEditEmail()} className="" color="default" variant="filled">{t('app.edit')}</Button>
          </div>
          <div className="account-item">
            <span className="account-title">{t('login.mobile')}</span>
            <span className="account-val">{currentAccountInfo.mobile || t('setting.no-set')}</span>
            <Button onClick={() => handleEditMobile()} className="" color="default" variant="filled">{t('app.edit')}</Button>
          </div>
          <div className="account-item">
            <span className="account-title">{t('login.password')}</span>
            <span className="account-val">{t('setting.password-set')}</span>
            <Button onClick={() => handleEditPassword()} className="" color="default" variant="filled">{t('app.edit')}</Button>
          </div>
        </div>
        <Flex vertical gap={8} className="delete-wrapper">
          <div className="delete-title">{t('setting.deleteAccount')}</div>
          <div className="delete-content">
            <div className="left-content">{t('setting.deleteDes')}</div>
            <Button onClick={() => handleDeleteAccount()} className="delete-btn" color="default" variant="filled">{t('setting.deleteAccount')}</Button>
          </div>
        </Flex>
        {/* 修改邮箱 */}
        {
          editEmailModal.visible ?
            <EditEmail onUpdate={onEditEmailUpdate} email={editEmailModal.email} mobile={editEmailModal.mobile} onClose={onCloseEditEmail} /> : null
        }
        {/* 修改密码 */}
        {
          editPasswordModal.visible ? <EditPassword onUpdate={onEditPasswordUpdate} email={editPasswordModal.email} mobile={editPasswordModal.mobile} onClose={onCloseEditPassword} /> : null
        }
        {/* 修改手机号 */}
        {
          editMobileModal.visible ? <EditMobile onUpdate={onEditMobileUpdate} mobile={editMobileModal.mobile} email={editMobileModal.email} onClose={onCloseEditMobile} /> : null
        }
        {/* 注销账号 */}
        {
          deleteAccountModal.visible ? <DeleteAccount  onClose={onCloseDeleteAccount} /> : null
        }
        {contextHolder}
      </div>
    </>
  );
};

const mapStateToProps = (state: any) => ({
  activeCompanyId: state.app.activeCompanyId,
})
const mapDispatchToProps = (dispatch: any) => ({
})
export default connect(mapStateToProps, mapDispatchToProps)(MyAccount);
