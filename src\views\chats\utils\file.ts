// 视频处理方法
const FileUtils ={
    // 大小
    formatFileSize: (bytes:any, decimals = 1) =>{
        if (typeof bytes !== 'number' || bytes < 0) return '';
      
        const unitBase =  1024;
        const unitLabels = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
      
        if (bytes === 0) return `0 ${unitLabels[0]}`;
      
        let unitIndex = 0;
        let size = bytes;
      
        while (size >= unitBase && unitIndex < unitLabels.length - 1) {
          size /= unitBase;
          unitIndex++;
        }
      
        // 处理小数显示
        let formattedSize;
        if (unitIndex === 0) {
          formattedSize = size.toString(); // Bytes 不显示小数
        } else {
          formattedSize = size.toFixed(decimals).replace(/\.?0+$/, ''); // 去除末尾多余的零
        }
      
        return `${formattedSize} ${unitLabels[unitIndex]}`;
      }
}
export default FileUtils