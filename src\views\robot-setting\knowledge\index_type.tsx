import { CurrentRobotInfoFace } from "@/views/robot-manage/interface";
export interface TableColumnsFace {
    key?: string,
    documentId: string | number,
    documentName: string,
    embeddingSearchStatus:  number,
    updateTime: string,
    hitCount: string,
    characters: number
  }
  
  export interface CurrentDocumentParamsFace {
    account?: string,
    characters?: number,
    createTime?: string,
    documentContent: string,
    documentId: string,
    documentName: string,
    hitCount?: 0,
    region?: string,
    robotId?: string,
    status?: string | number,
    type?: string,
    updateTime?: string
  }
  
  export interface ContextState {
    count: number,
    dataSource: TableColumnsFace[],
    selectedRowKeys: React.Key[],
    selectedRows: object[],
    createModal: {
      visible: boolean,
      robotId: any
    },
    uploadModal: {
      visible: boolean,
      robotId: any,
      documentId: any,
    },
    robotId: string | null,
    tableLoading: boolean,
    currentDoc: CurrentDocumentParamsFace,
    interfaceDocumentName: string,
    interfaceDocumentStatus: number,
    currentRobotInfo: CurrentRobotInfoFace,
    deleteModal: {
      visible: boolean,
      ids: any
    },
  }