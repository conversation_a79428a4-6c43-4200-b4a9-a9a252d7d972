/* robot模块*/
import HTTP from "@/utils/request";
import * as actions from "@/store/action";
import { AxiosRequestConfig } from 'axios';

// 获取机器人列表
export const api_get_ai_list = (data:any,isPrivate?:boolean): Promise<unknown>  => new Promise((resolve, reject) => {
  HTTP({
    method: 'post',
    url: "/robot/frontPageBotList",
    data:{seq:0,...data},
    isPrivate
  }as AxiosRequestConfig).then(result => {
    resolve(result?.data?.data || {});
  }).catch(err => reject(err))
})


// 获取 机器认Token 限制
export const api_get_robot_token_limit = (data:any): Promise<unknown>  => new Promise((resolve, reject) => {
  HTTP({
    method: 'post',
    url: "/robot/robotTokenLimit",
    data:{seq:0,...data},
  }as AxiosRequestConfig).then(result => {
    resolve(result?.data?.data || {});
  }).catch(err => reject(err))
})

// homebot 获取llm模型数据
export const api_get_robot_llmmModel_params = { seq: 0, isHomeBot: true };
export const api_get_robot_llmmModel = (data = api_get_robot_llmmModel_params) => HTTP.post("/robot/robotLlmModel", data);
export const dispatch_api_get_robot_llmmModel = (params: any) => (dispatch: any) => new Promise((resolve, reject) => {
  const data = { ...api_get_robot_llmmModel_params, ...params }
  api_get_robot_llmmModel(data).then(result => {
    resolve(result?.data?.data);
  }).catch(err => reject(err))
})

// 查看机模型Token是否超出
export const api_check_max_token = (data:any): Promise<unknown>  => new Promise((resolve, reject) => {
  HTTP({
    method: 'post',
    url: "/chat/maxTokenCheck",
    data:{seq:0,...data},
  }as AxiosRequestConfig).then(result => {
    resolve(result?.data?.data || {});
  }).catch(err => reject(err))
})

// 分页加载历史会话
export const api_query_paged_chat_history = (data:any): Promise<unknown>  => new Promise((resolve, reject) => {
  HTTP({
    method: 'post',
    url: "/chat/pagedChatHistory",
    data,
    tenant: 0
  }as AxiosRequestConfig).then(result => {
    resolve(result?.data?.data || {});
  }).catch(err => reject(err))
})

// 删除历史会话
export const api_delete_messages_params = { seq: 0, robotId: "", messageIds: null };
export const api_delete_messages = (data = api_delete_messages_params) => HTTP.post("/chat/deleteMessages", data);
export const dispatch_api_delete_messages = (params: any) => (dispatch: any) => new Promise((resolve, reject) => {
  const data = { ...api_delete_messages_params, ...params }
  api_delete_messages(data).then(result => {
    resolve(result.data.data);
  }).catch(err => reject(err))
})

// 清除上下文
export const api_clear_current_chat = (data:any): Promise<unknown>  => new Promise((resolve, reject) => {
  HTTP({
    method: 'post',
    url: "/chat/clearCurrentChat",
    data,
    tenant: 0
  }as AxiosRequestConfig).then(result => {
    resolve(result?.data?.data || {});
  }).catch(err => reject(err))
})

// 查询历史会话详情
export const api_get_included_chat_detail_params = { seq: 0, robotId: "", messageIds: null };
export const api_get_included_chat_detail = (data = api_get_included_chat_detail_params) => HTTP.post("/chat/getIncludedChatDetail", data);
export const dispatch_api_get_included_chat_detail = (params: any) => (dispatch: any) => new Promise((resolve, reject) => {
  const data = { ...api_get_included_chat_detail_params, ...params }
  api_get_included_chat_detail(data).then(result => {
    resolve(result.data.data);
  }).catch(err => reject(err))
})

/***问答模块***/
// 普通对话
export const api_ai_message_params = { seq: 0, robot_id: "", text: "", action: "chat" };
export const dispatch_api_ai_message = (params: any) => (dispatch: any) => new Promise((resolve, reject) => {
  const data = { ...api_ai_message_params, ...params }
  const url = data.msgType == 'homeChatMsg' ? '/chat/homeChatMsg' : '/chat/chatMsg'
  delete data.msgType; //删除不需要的参数
  HTTP.post(url, data).then(result => {
    resolve(result);
  }).catch(err => reject(err))
})

// 普通对话
export const api_ai_homebot_message_params = { seq: 0, robotId: "", text: "", action: "chat"};
export const api_ai_homebot_message = (data = api_ai_message_params) => HTTP.post("/chat/homeChatMsg", data);
export const dispatch_api_ai_homebot_message = (params: any) => (dispatch: any) => new Promise((resolve, reject) => {
  const data = { ...api_ai_homebot_message_params, ...params }
  api_ai_homebot_message(data).then(result => {
    resolve(result);
  }).catch(err => reject(err))
})

// 增强对话
export const api_ai_enhance_message_params = { seq: 0, robotId: "", text: "", action: "chat" };
export const api_ai_enhance_message = (data = api_ai_enhance_message_params) => HTTP.post("/chat/enhanceChatMsg", data);
export const dispatch_api_ai_enhance_message = (params: any) => (dispatch: any) => new Promise((resolve, reject) => {
  const data = { ...api_ai_enhance_message_params, ...params }
  api_ai_enhance_message(data).then(result => {
    resolve(result);
  }).catch(err => reject(err))
})

// 停止生成
export const api_stop_streaming_params = { seq: 0, robotId: "" };
export const api_stop_streaming = (data = api_stop_streaming_params) => HTTP.post("/chat/stopStreaming", data);
export const dispatch_api_stop_streaming = (params: any) => (dispatch: any) => new Promise((resolve, reject) => {
  const data = { ...api_stop_streaming_params, ...params }
  api_stop_streaming(data).then(result => {
    resolve(result.data.data);
  }).catch(err => reject(err))
})

// 数据确认弹框判断
export const api_ai_privacy_check_params = { isEnhance: false, seq: 0, robotId: "", text: "", action: "chat" };
export const api_ai_privacy_check = (data = api_ai_privacy_check_params) => HTTP.post("/chat/privacyPopupCheck", data);
export const dispatch_api_privacy_check = (params: any) => (dispatch: any) => new Promise((resolve, reject) => {
  const data = { ...api_ai_privacy_check_params, ...params }
  api_ai_privacy_check(data).then(result => {
    resolve(result);
  }).catch(err => reject(err))
})


// 获取机器人市场列表数据
export const api_get_bot_marketplace_datas = (data:any): Promise<unknown>  => new Promise((resolve, reject) => {
  HTTP({
    method: 'post',
    url: "robot/marketPlaceBots",
    data,
  }as AxiosRequestConfig).then(result => {
    resolve(result?.data?.data || {});
  }).catch(err => reject(err))
})

// 收藏机器人
export const api_set_robot_favorite = (data:any): Promise<unknown>  => new Promise((resolve, reject) => {
  HTTP({
    method: 'post',
    url: "/robot/setFavoriteRobot",
    data,
  }as AxiosRequestConfig).then(result => {
    resolve(result?.data?.data || {});
  }).catch(err => reject(err))
})

// 取消收藏机器人
export const api_remove_robot_favorite = (data:any): Promise<unknown>  => new Promise((resolve, reject) => {
  HTTP({
    method: 'post',
    url: "/robot/removeFavoriteRobot",
    data,
  }as AxiosRequestConfig).then(result => {
    resolve(result?.data?.data || {});
  }).catch(err => reject(err))
})

// 获取回收管理-机器人
export const api_get_orphaned_robots = (data:any): Promise<unknown>  => new Promise((resolve, reject) => {
  HTTP({
    method: 'post',
    url: "/robot/getOrphanedRobots",
    data,
  }as AxiosRequestConfig).then(result => {
    resolve(result?.data?.data || {});
  }).catch(err => reject(err))
})