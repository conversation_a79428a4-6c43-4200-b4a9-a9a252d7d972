import React from "react";
import { connect } from "react-redux";
import i18next from "i18next";
import classNames from "classnames";
import Svgicon from "@/components/svgicon"
import { Flex, Button, Table, Modal, message, Empty, Dropdown, Tag, Input } from "antd";
import { ColumnProps } from "antd/es/table";
import type { MenuProps } from 'antd';
import {
  api_create_api_key_params,
  api_update_api_key_params,
  api_remove_api_key_params,
  api_query_api_key_list_params
} from "@/api/robotManage";

import {
  dispatch_api_create_api_key,
  dispatch_api_update_api_key,
  dispatch_api_remove_api_key,
  dispatch_api_query_api_key_list
} from "@/store/api"

import './index.scss'
import '../common/style/index.scss'

import { ContextState, TableColumnsFace } from './index_type'
import OperateApi from './modal/operateApi'


// 基本信息-机器人知识
class RobotSettingAPI extends React.Component<any, ContextState> {
  private timer: NodeJS.Timeout | null = null;
  constructor(props: any) {
    super(props);
    this.timer = null;
    this.state = {
      robotId: "",
      apiName: "",
      selectedRowKeys: [],
      selectedRows: [],
      dataSource: [],
      tableLoading: false,
      operateModal: {// 操作
        visible: false,
        robotId: null,
        keyId: null,
        type: 'add',
        apiName: ''
      },
      deleteModal: {// 删除确认弹窗
        isBatch: false,
        visible: false,
        ids: null
      },
    }
  };

  // 打开操作弹窗
  handleShowOperateModal=(record?:any)=>{
    const type = record!=undefined ? "edit":'add'
    const {robotId }=this.state
    let obj ={}
    if (type=='edit'){
      obj={keyId: record.keyId, apiName: record.name }
    }
    this.setState({ operateModal: { robotId, type, visible: true,...obj } })
  }

  // 关闭 操作弹窗
  handleCloseOperateModal=()=>{
    this.setState({ operateModal: { robotId:null,visible: false, type:'', keyId: null, apiName: null }})
  }
  

  // 名称输入框
  handleInApiKeyChange = (e: any) => {
    this.setState({ apiName: e.target.value })
  }

  // 重置
  handleResetParams = () => {
    this.setState({ apiName: ""}, () => {
      this.handleQueryList();
    })
  }

  // 取消删除
  handleDeleteModalCancel = () => {
    this.setState({ deleteModal: {ids:null,visible: false,isBatch: false } })
  }

  // 确认删除
  handleDeleteModalOk = () => {
    this.handleDeleteRequest()
  }
  
  // 批量删除
  handleDeleteRequest = () => {
    try {
      const { robotId,deleteModal } = this.state;
      // return
      const { userInfo ,globalRegion: region} = this.props;
      const data = {
        ...api_remove_api_key_params,
        seq: 0,
        region,
        account: userInfo.account,
        robotId: robotId,
        keyIds: deleteModal.ids
      }

      this.props.dispatch_api_remove_api_key(data).then(() => {
        message.open({ type: "success", content: i18next.t("common.delete-success") });
        this.setState({ selectedRowKeys: [], selectedRows: [], deleteModal: { ids:null,visible: false,isBatch: false} }, () => {
          this.handleQueryList();
        })
      }).catch((err: any) => console.log(err))
    } catch (error) {
      console.log(error)
    }
  }

  // 点击批量删除
  handleClickBatchDelete = () => {
    this.setState({ deleteModal: { ids:this.state.selectedRows.map((d: any) => d.keyId),visible: true,isBatch: true } })
  }

  // 单个删除
  handleDeleteDocumentRow = (record: any) => {
    this.setState({ deleteModal: { ids: [record.keyId],visible: true,isBatch: false} })
  }

  // 查询列表
  handleQueryList = () => {
    const { robotId, apiName } = this.state;
    const { userInfo, globalRegion: region } = this.props;

    this.setState({ tableLoading: true });

    if (robotId) {
      const data = {
        ...api_query_api_key_list_params,
        robotId,
        name: apiName,
        status: -1,
      };
      this.props.dispatch_api_query_api_key_list(data).then((result: any) => {
        const list =  result?.keys || []
        list.forEach((d: TableColumnsFace, index: number) => d.key = index.toString());
        this.setState({ dataSource: list, tableLoading: false })
      }).catch((err: any) => {
        console.log(err)
        this.setState({ tableLoading: false });
      })
    }
  }

  handleTableSelect = (selectedRowKeys: React.Key[], selectedRows: any) => {
    this.setState({ selectedRowKeys, selectedRows });
  }

  componentDidMount() {
    const params = new URLSearchParams(window.location.search);
    const robotId = params.get("robotid");
    this.setState({ robotId }, () => {
      this.handleQueryList();
    })
    this.timer = setInterval(() => {
      this.handleQueryList();
    }, 15000);
  }

  componentWillUnmount() {
    if (this.timer) {
      clearInterval(this.timer); // 在组件卸载前清除定时器
    }
  }

  getColumns = () => {
    const colorMap:any ={
      0: 'cyan',
      2: 'red'
    }
    let columns:ColumnProps<TableColumnsFace>[] = [
      {
        title: i18next.t(["robot-manage.api-manage.name"]) || "", dataIndex: 'name', key: 'name', width: 200
      },
      {
        title: i18next.t(["robot-manage.api-manage.api-key"]) || "", dataIndex: 'authorization', key: 'authorization', fixed: 'left' as 'left',
        render: (text: any) => <span>{text}</span>,
      },
      {
        title: i18next.t(["robot-manage.api-manage.status"]) || "", dataIndex: 'status', key: 'status',
        render: (text: any) => <Tag bordered={false} color={ colorMap[text]|| 'purple'}>{ 
          text === -1 ? i18next.t(["common.status-all"]) 
        : text === 0 ? i18next.t(["common.status-enable"]) 
        : text === 1 ? i18next.t(["common.status-disable"]) 
        : text === 2 ? i18next.t(["robot-manage.api-manage.status-delete"]) 
        : i18next.t(["common.status-enable"]) }</Tag>
      },
      {
        title: i18next.t(["robot-manage.api-manage.update-time"]) || "", dataIndex: 'update_time', key: 'update_time'
      },
      {
        title: i18next.t(["robot-manage.api-manage.action"]) || "", dataIndex: 'action', key: 'action', width: 120, fixed:"right",
        render: (_: any, record: any) => {
          const { permissionMap } = this.props;
          const defaultItems: MenuProps['items'] = [
            {
              key: 'edit',
              label: i18next.t("common.edit"),
            },
            {
              key: 'delete',
              label: <div className="error-color">{i18next.t("common.delete")}</div>,
            },
          ];

          const items: MenuProps['items'] = [];

          for(let i = 0; i < defaultItems.length; i++) {
            let item: any = defaultItems[i];
            items.push(item);
          }

          return items.length>0?(
            <div className={"custom-table-action"}>
              <Dropdown placement="bottomRight" arrow={false}
                menu={{ items, onClick: (e) => this.handleCardMoreClick(e, record) }}>
                <Svgicon svgName="more_dot" />
              </Dropdown>
            </div>
          ):null
        }
      }
    ];

    return columns;
  };

  handleCardMoreClick = (e: any, record: any) => {
    if (e.key === "edit") {
      this.handleShowOperateModal( record);
    }else if (e.key === "delete") {
      // 删除
      this.handleDeleteDocumentRow(record)
    }
  }
  render() {
    const { robotId, deleteModal,operateModal, selectedRowKeys, selectedRows, dataSource, tableLoading,apiName} = this.state;
    let tableColumns = this.getColumns();
    const { paginationConfig } = this.props;
    const rowSelection = { selectedRowKeys, onChange: this.handleTableSelect };

    return (
      <Flex className={"robot-api-wrap common-wrap"} vertical>
        <Flex className="wrap-nav" align="center">
          <span className="wn-title">{i18next.t("robot-manage.api-manage-label") }</span>
          <Flex className="wn-btns">
            <Button onClick={()=>this.handleShowOperateModal()} type="primary">{i18next.t("robot-manage.api-manage.create-api-key")}</Button>
          </Flex>
        </Flex>
        {/* 筛选 */}
        <Flex className="wrap-filters" gap={16}>
          <Input className="filter-input" value={apiName} variant="filled"
            placeholder={i18next.t(["robot-manage.api-manage.name-hint"]) || ""}
            onChange={this.handleInApiKeyChange}
            prefix={<Svgicon svgName="search" />}
          />
          <Button type="primary" onClick={this.handleQueryList}>{i18next.t("common.query")}</Button>
          <Button color="default" variant="filled" onClick={this.handleResetParams}>{i18next.t("common.reset")}</Button>
          {
            selectedRows.length > 0?
              <Button onClick={() => this.handleClickBatchDelete()} type={"primary"} color="danger" variant="filled">
                {i18next.t("robot-manage.api-manage.multi-delete-api-key")}</Button> : null
          }
        </Flex>
        {/* 列表 */}
        <div className="wrap-content">
          <Table columns={tableColumns}
            className="table-with-pagination"
            scroll={{ x: 1500 }}
            dataSource={dataSource}
            rowSelection={rowSelection}
            loading={{spinning:tableLoading,size:'large'}}
            pagination={{
              ...paginationConfig,
              total: dataSource.length
            }}
            locale={{ emptyText: !tableLoading?<Empty className="page-empty" image={null} description={
              <><div>{i18next.t('robot-manage.api-manage.no-api')}</div>
                <div>{i18next.t('robot-manage.api-manage.no-api-desc')}</div>
              </>}/>:null }}
          />
        </div>
        {// 新增或编辑
          operateModal.visible ?
          <OperateApi visible={operateModal.visible} resource={operateModal} onClose={this.handleCloseOperateModal} onSucess={this.handleQueryList} />: null
        }
        { // 删除确认弹窗
          <Modal width={272} title={i18next.t(deleteModal.isBatch?'robot-manage.api-manage.is-multi-delete-api-key':'robot-manage.confirm-del')} maskClosable={false} centered closable={false}
            okText={i18next.t("app.ok")} cancelText={i18next.t("app.cancel")} wrapClassName="common-confirm-modal"
            open={deleteModal.visible} onCancel={() => this.handleDeleteModalCancel()} onOk={() => this.handleDeleteModalOk()}>
            <div>{deleteModal.isBatch?i18next.t("robot-manage.api-manage.multi-delete-api-key-notice"):i18next.t("robot-manage.confirm-delete-tip")}</div>
          </Modal>
        }
      </Flex>
    )
  }
}

const mapStateToProps = (state: any) => ({
  userInfo: state.app.userInfo,
  globalLang: state.app.globalLang,
  globalRegion: state.app.globalRegion,
  permissionMap: state.app.permissionMap,
  paginationConfig: state.app.paginationConfig,
  themeInfo: state.app.themeInfo
});

const mapDispatchToProps = (dispatch: (e: any) => void) => ({
  dispatch_api_create_api_key: (data = api_create_api_key_params) => dispatch(dispatch_api_create_api_key(data)),
  dispatch_api_update_api_key: (data = api_update_api_key_params) => dispatch(dispatch_api_update_api_key(data)),
  dispatch_api_remove_api_key: (data = api_remove_api_key_params) => dispatch(dispatch_api_remove_api_key(data)),
  dispatch_api_query_api_key_list: (data = api_query_api_key_list_params) => dispatch(dispatch_api_query_api_key_list(data)),
});

export default connect(mapStateToProps, mapDispatchToProps)(RobotSettingAPI);
