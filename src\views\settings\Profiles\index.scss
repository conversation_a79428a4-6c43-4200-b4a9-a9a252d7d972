@import "../../../styles/mixin.scss";
.profiles-wrapper {
  height: 100%;
  overflow: hidden;
  padding: 0 !important;
  .profiles-title-content {
    padding: 16px 16px 0;
    .profiles-subtitle {
      margin-top: 8px;
      @include font(var(--font-size-base), 22px, 400, var(--colorTextTertiary));
    }
  }
  .profiles-title{
    .svg-icon{
      flex-shrink: 0;
      width: 20px;
      font-size: 20px;
    }
    .title{
      max-width: calc(100% - 20px);
      @include ellipsis-multiline();
    }
  }
  .profiles-content {
    padding: 16px;
    padding-top: 24px;
    flex: 1;
    overflow: auto;
    .profiles-data-content {
      flex: 1;
      .userinfo-content {
        padding: 24px 0;
        .userinfo-item:not(:last-child) {
          margin-bottom: 24px;
        }
      }
    }
    .profiles-data-preview {
      flex-shrink: 0;
      width: 280px;
    }
  }
  .config-title{
    @include font(12px, 18px, 500);
    margin-bottom: 6px;
  }
  .bottom-operate {
    margin-top: auto;
    flex-shrink: 0;
  }
  .no-save-tip {
    @include font(14px, 22px, 400);
    margin-right: auto;
  }
  .avatar{
    position: relative;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    overflow: hidden;
    cursor: pointer;
    background: #fff;
    .avatar-update{
      display: none;
      background: var(--colorBgMask);
      width: 100%;
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 100;
      border-radius: 50%;
    }
    img{
      width: 100%;
      height: 100%;
      border-radius: 50%;
    }
    &:hover{
      .avatar-update{
        display: flex;
        font-size: 24px;
      }
    }
  }
  .user-card {
    $borderRadius: 12px;
    width: 100%;
    min-height: 380px;
    border-radius: $borderRadius;
    display: flex;
    flex-direction: column;
    background: url("../../../icons/png/contacts/contact-user-bg.png") no-repeat left top;
    border-radius: $borderRadius;
    box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.1);
    .name-avatar-position {
      padding: 54px 20px 8px;
      flex-shrink: 0;
    }

    .UserAvatar--inner {
      border: 2px solid var(--colorBgElevated);
    }

    .name-position {
      flex: 1;
      overflow: hidden;
      > div.name {
        @include font($line-height: 22px, $color: var(--colorTextLightSolid));
        @include ellipsis();
      }
      > div.post {
        @include font(12px, 18px, 400, var(--colorTextSecondary));
        word-wrap: break-word;
        // @include ellipsis();
      }
    }
    .infos {
      flex: 1;
      padding: 8px 20px 20px;
      overflow-y: auto;
    }
    .field-name {
      @include font(12px, 18px, 400, var(--colorTextTertiary));
      flex-shrink: 0;
      width: 72px;
      word-wrap: break-word;
      word-break: break-word;
    }
    .field-value {
      @include font(12px, 18px, 400);
      word-break: break-word;
      word-wrap: break-word;
    }
  }
}
.ant-popover.user-center-popover.setting-profile .menu-content{
  max-height: 500px;      
}