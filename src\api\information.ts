import HTTP, { APP_HTTP, ADMIN_HTTP } from "@/utils/userRequest";
import * as actions from "@/store/action";
import { AxiosRequestConfig } from 'axios';
import store from "@/store";

// 保存通知
export const api_save_notification = (data: any): Promise<unknown> => new Promise((resolve, reject) => {
    APP_HTTP({
        method: 'post',
        url: "/system/admin/notification",
        data,
    } as AxiosRequestConfig).then(result => {
        const res = result?.data || {}
        resolve(res)
    }).catch(err => reject(err))
})

// 公告管理-分页获取公告通知
export const api_get_admin_notification = (params: any): Promise<unknown> => new Promise((resolve, reject) => {
    APP_HTTP({
        method: 'get',
        url: "/system/admin/notification",
        params,
    } as AxiosRequestConfig).then(result => {
        const res = result?.data || {}
        resolve(res)
    }).catch(err => reject(err))
})

// 公告通知-分页获取公告通知
export const api_get_user_notification = (params: any): Promise<unknown> => new Promise((resolve, reject) => {
    APP_HTTP({
        method: 'get',
        url: "/system/notification",
        params,
    } as AxiosRequestConfig).then(result => {
        const res = result?.data || {}
        resolve(res)
    }).catch(err => reject(err))
})

// 发布通知
export const api_publish_notification = (data: any): Promise<unknown> => new Promise((resolve, reject) => {
    APP_HTTP({
        method: 'put',
        url: "/system/admin/notification/publish",
        data,
    } as AxiosRequestConfig).then(result => {
        const res = result?.data || {}
        // 通知更新未读数
        store.dispatch( actions.change_app_is_refresh_ann_unread(true))
        resolve(res)
    }).catch(err => reject(err))
})

// 删除通知
export const api_delete_notification = (id: any): Promise<unknown> => new Promise((resolve, reject) => {
    APP_HTTP({
        method: 'delete',
        url: "/system/admin/notification/" + id,
        data: {},
    } as AxiosRequestConfig).then(result => {
        const res = result?.data || {}
        resolve(res)
    }).catch(err => reject(err))
})

// 撤回通知
export const api_recall_notification = (id: any): Promise<unknown> => new Promise((resolve, reject) => {
    APP_HTTP({
        method: 'put',
        url: `/system/admin/notification/${id}/recall`,
        data: {},
    } as AxiosRequestConfig).then(result => {
        const res = result?.data || {}
        resolve(res)
    }).catch(err => reject(err))
})

// 获取公告管理-通知详情
export const api_get_admin_notification_details = (id: any): Promise<unknown> => new Promise((resolve, reject) => {
    APP_HTTP({
        method: 'get',
        url: `/system/admin/notification/${id}`,
        params: {},
    } as AxiosRequestConfig).then(result => {
        const res = result?.data?.data || {}
        resolve(res)
    }).catch(err => reject(err))
})

// 获取公告t通知-通知详情
export const api_get_user_notification_details = (id: any): Promise<unknown> => new Promise((resolve, reject) => {
    APP_HTTP({
        method: 'get',
        url: `/system/notification/${id}`,
        params: {},
    } as AxiosRequestConfig).then(result => {
        const res = result?.data?.data || {}
        resolve(res)
    }).catch(err => reject(err))
})

// 获取通知操作日志
export const api_get_notification_operation_log = (id: any): Promise<unknown> => new Promise((resolve, reject) => {
    APP_HTTP({
        method: 'get',
        url: `/system/admin/notification/${id}/operation-logs`,
        params: {},
    } as AxiosRequestConfig).then(result => {
        const res = result?.data?.data || []
        resolve(res)
    }).catch(err => reject(err))
})

// 归档-取消归档通知
export const api_update_notification_archive = (data: any): Promise<unknown> => new Promise((resolve, reject) => {
    APP_HTTP({
        method: 'put',
        url: `/system/notification/${data.id}/archive`,
        data,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    } as AxiosRequestConfig).then(result => {
        const res = result?.data?.data || []
        resolve(res)
    }).catch(err => reject(err))
})

// 置顶-取消置顶通知
export const api_update_notification_pin = (data: any): Promise<unknown> => new Promise((resolve, reject) => {
    APP_HTTP({
        method: 'put',
        url: `/system/notification/${data.id}/pin`,
        data,
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
    } as AxiosRequestConfig).then(result => {
        const res = result?.data?.data || []
        resolve(res)
    }).catch(err => reject(err))
})

// 批量上传通知附件
export const api_upload_attachments = (data: any): Promise<unknown> => new Promise((resolve, reject) => {
    APP_HTTP({
        method: 'post',
        url: "system/admin/notification/attachments",
        data,
        headers: {
            'Content-Type': 'multipart/form-data',
        },
        hideTip: true
    } as AxiosRequestConfig).then(result => {
        const res = result?.data?.data || {}
        resolve(res)
    }).catch(err => reject(err))
})

// 上传公告图片
export const api_upload_image = (data: any): Promise<unknown> => new Promise((resolve, reject) => {
    APP_HTTP({
        method: 'post',
        url: "/system/admin/notification/image",
        data,
        headers: {
            'Content-Type': 'multipart/form-data',
        }
    }).then(result => {
        const res = result?.data?.data || {}
        resolve(res)
    }).catch(err => reject(err))
})

// 下载通知附件
export const api_download_attachment = (id: any, attachId: any): Promise<unknown> => new Promise((resolve, reject) => {
    APP_HTTP({
        method: 'get',
        url: `/system/notification/${id}/attachment/${attachId}`,
        params: {},
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        responseType: 'blob',
    }).then(result => {
        const res = result
        resolve(res)
    }).catch(err => reject(err))
})

// 获取未读通知数
export const api_get_notification_unread_num = (): Promise<unknown> => new Promise((resolve, reject) => {
    APP_HTTP({
        method: 'get',
        url: `/system/notification/unread-num`,
        params: {},
    } as AxiosRequestConfig).then(result => {
        const res = result?.data?.data || []
        resolve(res)
    }).catch(err => reject(err))
})

// 获取公告管理权限
export const api_get_ann_manage_permission = (): Promise<unknown> => new Promise((resolve, reject) => {
    APP_HTTP({
        method: 'get',
        url: `/system/admin/notification/permission`,
        params: {},
    } as AxiosRequestConfig).then(result => {
        const res = result?.data?.data || []
        resolve(res)
    }).catch(err => reject(err))
})

// 生成摘要
export const api_generate_ann_summary = (id: any): Promise<unknown> => new Promise((resolve, reject) => {
    APP_HTTP({
        method: 'get',
        url: `/system/notification/${id}/generate-summary`,
        params: {},
    } as AxiosRequestConfig).then(result => {
        const res = result?.data?.data || []
        resolve(res)
    }).catch(err => reject(err))
})

/** 标签相关开始 **/
// 获取用户所有标签
export const api_get_notify_tags = (): Promise<unknown> => new Promise((resolve, reject) => {
    APP_HTTP({
        method: 'get',
        url: "/system/notification/tags",
        params: {},
    } as AxiosRequestConfig).then(result => {
        const res = result?.data || {}
        resolve(res)
    }).catch(err => reject(err))
})

// 新增标签
export const api_create_notify_tag = (data: any): Promise<unknown> => new Promise((resolve, reject) => {
    APP_HTTP({
        method: 'post',
        url: "/system/notification/tags",
        data,
    } as AxiosRequestConfig).then(result => {
        const res = result?.data || {}
        resolve(res)
    }).catch(err => reject(err))
})

// 修改标签
export const api_update_notify_tag = (data: any): Promise<unknown> => new Promise((resolve, reject) => {
    APP_HTTP({
        method: 'put',
        url: "/system/notification/tags/" + data.id,
        data,
    } as AxiosRequestConfig).then(result => {
        const res = result?.data || {}
        resolve(res)
    }).catch(err => reject(err))
})

// 删除标签
export const api_delete_notify_tag = (data: any): Promise<unknown> => new Promise((resolve, reject) => {
    APP_HTTP({
        method: 'delete',
        url: "/system/notification/tags/" + data.id,
    } as AxiosRequestConfig).then(result => {
        const res = result?.data || {}
        resolve(res)
    }).catch(err => reject(err))
})

// 为通知分配标签
export const api_tag_notify = (data: any): Promise<unknown> => new Promise((resolve, reject) => {
    APP_HTTP({
        method: 'post',
        url: `/system/notification/${data.id}/tags`,
        data: data.tags
    } as AxiosRequestConfig).then(result => {
        const res = result?.data || {}
        resolve(res)
    }).catch(err => reject(err))
})

// 从通知移除标签
export const api_delete_tag_notify = (id: any, data: any): Promise<unknown> => new Promise((resolve, reject) => {
    APP_HTTP({
        method: 'delete',
        url: `/system/notification/${id}/tags`,
        data
    } as AxiosRequestConfig).then(result => {
        const res = result?.data || {}
        resolve(res)
    }).catch(err => reject(err))
})
/** 标签相关结束 **/
