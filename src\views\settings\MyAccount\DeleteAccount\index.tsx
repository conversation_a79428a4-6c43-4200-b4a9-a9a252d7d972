
import { connect } from "react-redux";
import { useEffect, useState, useRef, forwardRef } from "react";
import { Modal, message, Flex, Button, Input, Form, Tabs, TabsProps } from "antd"
import { useTranslation } from 'react-i18next';
import { $trim, $isNull } from '@/utils/common'
import './index.scss'
import ErrorCodeMap from '@/views/common/formErrorCodeMap'
import FormUtils from '@/views/common/formUtils'
import classnames from "classnames";
import { EmailRules, PasswordRules, VerifyCodeRules } from "@/views/common/formRule";
import { api_get_email_code } from '@/api/user'
import VerifyCode from "@/components/form/verifyCode";
import { GetMobileFormValue } from '../EditMobile/index'
import PasswordInput from "@/components/form/passwordInput";
import { dispatch_api_logout } from "@/api/user";
import { useOpenIM } from "@/contexts/OpenIMContext";
import { useNavigate } from "react-router-dom";
import { Encrypt } from '@/utils/forge'
import { api_verify_password, api_delete_account, api_is_exists_in_tenant } from '@/api/setting'
import { RequestFailedMessage } from "@/utils/userRequest";

const scene = 'MEMBER_DELETE_ACCOUNT'

// 注销账号
const DeleteAccount = forwardRef((props: any, ref: React.LegacyRef<HTMLDivElement> | any) => {
    const { t } = useTranslation()
    const { service: ImService } = useOpenIM();
    const navigate = useNavigate();
    const [modal, contextHolder] = Modal.useModal();

    const { userInfo, email, mobile } = props

    const [step, setStep] = useState(1)// 步骤
    const [isOkBtnAble, setIsOkBtnAble] = useState(false) // 按钮是否可用
    const [form] = Form.useForm();
    const [formValues, setFormValues] = useState<any>({});
    const [step1CheckCode, setStep1CheckCode] = useState<any>(null);


    // 初始化
    useEffect(() => {
    }, [])

    useEffect(() => {
        // console.log('email', email)
        form.setFieldValue('email', email)
    }, [email])

    useEffect(() => {
        // console.log('mobile', mobile)
        setMobileFormValue()
    }, [mobile])

    // 表单项输入值监听, 控制提交按钮状态
    const values = Form.useWatch([], form);

    useEffect(() => {
        let fields: any = []
        if (step === 1) {
            fields = ['password']
        } else if (step === 2) {
            setIsOkBtnAble(true)
            return
        } else if (step === 3) {
            fields = ["email", "code"]
        }
        // console.log('fields', fields)
        form.validateFields(fields, { validateOnly: true }).then(() => {
            setIsOkBtnAble(true)
        }).catch((err) => {
            // console.log('SubmitButton1 errerr:', err)
            setIsOkBtnAble(false)
        });
    }, [step, form, values])


    const setMobileFormValue = () => {
        const { number, codeLabel } = GetMobileFormValue(mobile)
        form.setFieldValue('mobile', {
            number: number,
            code: { label: codeLabel, value: '' }
        })
    }

    const onOk = async () => {
        if (step === 1) {
            onSubmitStep1()
            return
        }
        if (step === 2) {
            setStep(3)
            return
        }
        if (step === 3) {
            onSubmitStep3()
            return
        }
    }
    // 步骤1 提交
    const onSubmitStep1 = async () => {
        let { password } = formValues
        const data: any = {
            password: Encrypt(password),
            scene
        }
        // console.log('data', data)
        setIsOkBtnAble(false)
        const res: any = await api_verify_password(data).catch(() => {
            setIsOkBtnAble(true)
        })
        setIsOkBtnAble(true)
        // console.log('res', res)
        if (!res) return
        const result = res.data || {}
        if (res?.code === 0) {
            if (result.success) { // 成功
                setStep1CheckCode(result?.tempCode)
                // 校验是否已退出所有公司
                const isQuitAll =  await isQuitAllCompany()
                if (isQuitAll) {
                    setStep(2)
                } else{
                    message.error(t('setting.cannot-delete-account'))
                }
            } else {
                const msg = RequestFailedMessage(res?.msg, res?.code)
                message.error(msg);
            }
        } else {
            const _code = res?.code
            const passwordErrors = ErrorCodeMap.password// 密码错误码
            const errors = [...passwordErrors]
            if (!errors.includes(_code)) {
                const msg = RequestFailedMessage(res?.msg, _code)
                message.error(msg);
                return
            }
            const errMsg = t(`errorCode.${_code}`)
            form.setFields([{ name: "password", errors: [errMsg], warnings: [`errorCode.${_code}`] }]) //warnings 为了解决 setFields 检验信息 国际化问题
        }
    }

    const isQuitAllCompany = async()=>{
        const res:any = await api_is_exists_in_tenant().catch(()=>{
            return false
        })
        if (!res) return false
        return !res?.exists
    }

    const onSubmitStep3 = async () => {

        let { email, code } = formValues
        const data: any = {
            scene,
            email: email,
            code,
            tempCode: step1CheckCode
        }
        // console.log('data', data)
        setIsOkBtnAble(false)
        const res: any = await api_delete_account(data).catch(() => {
            setIsOkBtnAble(true)
        })
        setIsOkBtnAble(true)
        // console.log('res', res)
        if (!res) return
        let _code = res.code
        if (_code === 0) {
            // 注销成功 退出登录
            props.dispatch_api_account_logout({}, ImService).then(() => {
                message.success(t('setting.delete-account-success'))
                navigate("/login");
            }).catch((err: any) => {
                console.log(err)
            })
        } else {
            //   用户没有退出所有公司，不能注销
            if (String(_code) == '**********') {
                // 不能注销
                modal.error({
                    width: 450,
                    title: t('common.warning'),
                    icon: null,
                    content: t('setting.cannot-delete-account'),
                    okText: t('ok'),
                    className: 'no-icon',
                    centered: true,
                    closable: true,
                    onCancel: () => {
                        onCancel()
                    },
                    onOk: async () => {
                        onCancel()
                    }
                });
            } else {
                const emailErros = ErrorCodeMap.email// 邮箱错误码
                const codeErros = ErrorCodeMap.code// 验证码错误码
                const errors = [...codeErros, ...emailErros]
                // console.log('errors', errors, !errors.includes(_code))
                if (!errors.includes(_code)) {
                    const msg = RequestFailedMessage(res?.msg, _code)
                    message.error(msg);
                    return
                }
                const errMsg = t(`errorCode.${_code}`)
                const field = emailErros.includes(_code) ? "email" : "code"
                form.setFields([{ name: field, errors: [errMsg], warnings: [`errorCode.${_code}`] }]) //warnings 为了解决 setFields 检验信息 国际化问题
            }
        }
    }

    // 取消
    const onCancel = () => {
        props.onClose()
    }

    // 表单项 输入改变事件
    const onFiledValueChange = (changedFields: any, allValues: any) => {
        try {
            // console.log('changedFields', changedFields)
            // console.log('allValues',allValues)
            setFormValues({ ...formValues, ...allValues });
            Object.keys(changedFields).forEach((fieldName: any) => {
                const fieldError = form?.getFieldError(fieldName)
                if (fieldError.length > 0) FormUtils.clearValidate(form, fieldName)
            })
        } catch (error) {

        }
    }

    // 弹窗标题
    const getTitle = () => {
        let title = t('setting.authentication')
        if (step === 2) {
            title = t('setting.delete-confirm-tip')
        }
        if (step === 3) {
            title = t('setting.delete-confirm')
        }
        return title
    }

    // // 处理步骤2  获取验证码失败 对应表单输入框错误提示
    // const onVerifyCodeError = (err: any) => {
    //     const _code = err?.code
    //     const Errors = ErrorCodeMap.email
    //     if (!Errors.includes(_code)) return
    //     const errMsg = t(`errorCode.${_code}`)
    //     form.setFields([{ name: 'email', errors: [errMsg], warnings: [`errorCode.${_code}`] }])
    // }

    return (
        <div className="setting-delete-account">
            <Modal
                className={`setting-delete-account-modal border no-padding step${step}`}
                title={getTitle()}
                open={true}
                onOk={onOk}
                onCancel={onCancel}
                mask={true}
                maskClosable={true}
                centered={true}
                width={450}
                okText={step === 3 ? t('setting.confirm-delete') : t('login.continue')}
                cancelText={t("app.cancel")}
                cancelButtonProps={{ variant: "filled", color: "default" }}
                okButtonProps={{ disabled: !isOkBtnAble }}
                footer={undefined}
            >
                <Form
                    form={form}
                    className="setting-delete-account-form common-form mediumn"
                    name="basic"
                    autoComplete="off"
                    layout="vertical"
                    onValuesChange={onFiledValueChange}
                    validateTrigger="onBlur"
                    requiredMark={false}
                    initialValues={{
                        password: '',
                        email: '',
                        code: '',
                    }}
                >
                    {/* 步骤一:输入密码 */}
                    {
                        step === 1 ?
                            <Flex vertical gap={24}>
                                <div className="psd-tip">{t('setting.verify-password')}</div>
                                <Form.Item name="password"
                                    label={""}
                                    className={classnames(["psd-form-item"])}
                                    rules={[{ required: true, whitespace: true, message: '' }, ...PasswordRules(t)]}>
                                    <PasswordInput key="setting-password" />
                                </Form.Item>
                            </Flex>
                            : null
                    }

                    {/* 步骤二:注销提示 */}

                    {
                        step === 2 ?
                            <Flex vertical gap={24}>
                                <ul className="delete-warning-text">
                                    <li>{t('setting.delete-confirm-text1')}</li>
                                    <li>{t('setting.delete-confirm-text2')}</li>
                                    <li>{t('setting.delete-confirm-text3')}</li>
                                    <li>{t('setting.delete-confirm-text4')}</li>
                                </ul>
                                <div className="delete-tip">
                                {t('setting.delete-confirm-text5')}
                                </div>
                            </Flex>
                            : null
                    }

                    {/* 步骤三:输入邮箱+验证码 */}
                    {
                        step === 3 ?
                            <>
                                {/* 邮箱 */}
                                <Form.Item name="email"
                                    label={t('login.email-address')} rules={[{ required: true, whitespace: true, message: '' }, ...EmailRules(t)]}>
                                    <Input autoComplete="new-email" variant="filled" maxLength={300} placeholder={t('login-pls.email') as string} />
                                </Form.Item>

                                {/* 验证码 */}
                                <Form.Item name="code"
                                    className={classnames(["psd-form-item"])} label={t('login.verify-code')} rules={VerifyCodeRules(t)}>
                                    <VerifyCode
                                        step={step}
                                        id="delete-account-code1"
                                        api={api_get_email_code}
                                        scene={scene}
                                        email={form.getFieldValue("email")}
                                        target='email'
                                        targetValidate={form.validateFields}
                                    // onError={onVerifyCodeError}
                                    />
                                </Form.Item>
                            </>
                            : null
                    }
                </Form>
            </Modal>
            {contextHolder}
        </div>
    )
})

const mapStateToProps = (state: any) => ({
    userInfo: state.app.userInfo,
})
const mapDispatchToProps = (dispatch: any) => ({
    dispatch_api_account_logout: (data: any, ImService: any) => dispatch(dispatch_api_logout(data, ImService)),
    // dispatch_api_: (data:any) => dispatch(dispatch_api_(data))
})
const ConnectedCounter = connect(mapStateToProps, mapDispatchToProps, null, { forwardRef: true })(DeleteAccount);
export default ConnectedCounter;