/* 自定义编辑器样式 */
.ama-richtext-editor {
	--se-bg-color: var(--colorBgContainer); /* 编辑器背景 */
	--se-text-color: var(--colorText); /* 文字颜色 */
	--se-toolbar-bg: #252525; /* 工具栏背景 */
	--se-toolbar-color: #b0b0b0; /* 工具栏文字 */
	--se-border-color: #444; /* 边框颜色 */
	--se-active-btn-bg: #333; /* 激活按钮背景 */
	--se-dropdown-bg: var(--colorIcon); /* 下拉菜单背景 */
	--se-dialog-bg: var(--colorBgMask); /* 对话框背景 */
	--se-popover-bg: var(--colorBgElevated); /* 对话框背景 */
	--se-menu-hover-bg: rgba(255, 255, 255, 0.08); /* 对话框背景 */
	--se-menu-active-bg: var(--colorPrimaryBg); /* 对话框背景 */

	.sun-editor {
		color: var(--colorText);
		$br: 8px;
		border-radius: $br;
		.se-container {
			border-radius: $br;
		}
		.se-dialog {
			background: var(--colorBgMask);
		}
		.se-dialog .se-dialog-inner .se-dialog-content {
			margin: 0 auto !important;
			transform: translateY(-50%);
			position: relative;
			top: 50%;
			border-radius: 12px;
			box-shadow: none;
			border: 0;
		}
		.se-wrapper{
			min-height: 500px;
		}
		.se-wrapper .se-wrapper-wysiwyg {
			// padding-bottom: 200px !important;
		}
		.se-component.__se__float-left {
			margin: 0 0 10px 0;
		}
		.se-component.__se__float-right {
			margin: 0 0 10px 0;
		}
		a {
			color: var(--colorLink) !important;
			text-decoration: none !important;
		}
		.se-toolbar {
			border-radius: $br $br 0 0;
		}
		.se-resizing-bar {
			border-radius: 0 0 $br $br;
		}
		.se-btn-module-border {
			border-radius: 4px;
		}
		.se-btn-primary {
			color: var(--colorPrimaryContrasting);
			background: var(--colorPrimary);
			border: 0;
		}
		// .se-dialog .se-dialog-inner .se-dialog-form .se-dialog-form-files>input._se_image_file{
		// 	display: flex;
		// 	align-items: center;
		// }
		._se_tab_content._se_tab_content_image {
			.se-dialog-tabs,
			.se-dialog-form-footer {
				display: none!important;
			}
			.se-dialog-form:not(:first-of-type){
				display: none!important;
			}
			.se-dialog-body >div:first-of-type{
				border-bottom: 0!important;
			}
		}
				
		.se-dialog-image{
			.se-dialog-footer>div:first-of-type{
				display: none!important;
			}
			.se-dialog-tabs{
				display: none;
			}
		}
		.se-controller .se-btn-group button[data-command="update"]{
			display: none;
		}
		.se-controller.se-controller-link .se-btn-group button[data-command="update"]{
			display: inline-block;
		}
		.se-list-layer{
			box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
			border: 0;
			border-radius: 12px;
			background-color: var(--colorBgElevated);
			button{
				color: var(--se-text-color) !important;
				border: 0;
				&:hover{
					background-color: var(--se-menu-hover-bg) !important;
				}
			}
		}
		.se-list-inner .se-list-basic li button{
			&.active{
				border: 0;
				color: var(--colorText);
				background-color: var(--se-menu-active-bg);
				&:active{
					box-shadow: none;
				}
			}
			&:hover{
				background-color: var(--se-menu-hover-bg);
			}
		}
		.sun-editor button{
			color: var(--colorText);
		}
		
		.se-btn-list:active{
			background: transparent;
			box-shadow: none;
		}
		button:active{
			background: transparent!important;
			box-shadow: none!important;
		}
		.se-submenu .se-form-group .se-color-input{
			// border: 0;
		}
		.se-submenu .se-form-group .se-color-input:focus{
			box-shadow: none;
			border-width: 1px;
		}
		.sun-editor-editable .se-video-container video{
			outline: 1px solid var(--colorBorderSecondary);
			border-radius: 2px;
		}
		.sun-editor-editable pre{
			background-color: var(--colorBgContainer);
		}
	}
	// 自定义黑色主题
	&.dark {
		/* 暗色主题 CSS */
		.sun-editor {
		}

		/* 编辑器容器 */
		.sun-editor .se-container {
			background-color: var(--se-bg-color);
			// border: 1px solid var(--se-border-color);
		}

		/* 工具栏 */
		.sun-editor .se-toolbar {
			background-color: var(--se-toolbar-bg);
			border-bottom: 1px solid var(--se-border-color);
		}

		/* 内容区域 */
		.sun-editor .se-wrapper {
			background-color: var(--se-bg-color);
			color: var(--se-text-color);
			.se-wrapper-wysiwyg {
				background-color: var(--se-bg-color);
				color: var(--se-text-color);
			}
		}

		/* 按钮样式 */
		.sun-editor .se-btn {
			color: var(--se-toolbar-color);
		}
		// .sun-editor .se-btn:disabled, .sun-editor button:disabled{
		// 	color: #ddd;
		// }
		.sun-editor .se-btn:hover {
			background-color: var(--se-active-btn-bg);
		}

		.sun-editor .se-btn.active {
			background-color: var(--se-active-btn-bg);
			color: #fff;
		}

		/* 下拉菜单 */
		.sun-editor .se-dropdown {
			background-color: var(--se-dropdown-bg);
			border: 1px solid var(--se-border-color);
		}

		.sun-editor .se-list-item:hover {
			background-color: #3a3a3a;
		}

		/* 对话框 */
		.sun-editor .se-dialog {
			// background-color: var(--se-dialog-bg);
			// border: 1px solid var(--se-border-color);
		}

		.sun-editor .se-dialog-header {
			border-bottom: 1px solid var(--se-border-color);
		}

		/* 输入框 */
		.sun-editor input[type="text"],
		.sun-editor textarea {
			background-color: #333;
			color: #fff;
			border: 1px solid #555;
		}

		/* 调整图标颜色 */
		.sun-editor .se-icon {
			filter: invert(0.8);
		}

		.se-resizing-bar {
			background-color: var(--se-bg-color);
			color: var(--se-text-color);
		}

		/*上传图片弹窗*/
		.se-dialog-content {
			background-color: var(--se-bg-color) !important;
			color: var(--se-text-color) !important;
		}
		.sun-editor .se-dialog label {
			color: var(--se-text-color) !important;
		}
		.se-controller .se-arrow.se-arrow-up:after{
			border-bottom-color: var(--se-popover-bg) !important;
		}
		.se-controller-link .link-content{
			background-color: var(--se-popover-bg) !important;
		}
		.sun-editor .se-controller .se-btn-group button:hover:enabled{
			background-color: var(--se-active-btn-bg)!important;
			border-color: var(--se-border-color)!important;
		}
		.sun-editor .se-selector-table{
			background-color: var(--se-active-btn-bg)!important;
			border-color: var(--se-border-color)!important;
		}
		.sun-editor .se-btn:enabled.on{
			background-color: var(--se-active-btn-bg);
		}
		.sun-editor .se-btn-list.default_value{
			background-color: var(--se-active-btn-bg);
		}
		.sun-editor .se-list-layer{
			// background-color: var(--colorFillTertiary)!important;
			color: var(--colorTextSecondary);

		}
		.sun-editor .se-list-layer.se-list-format ul{
			h3,h1,h2,h4,h5,h6{
				color: var(--colorTextSecondary);
			}
			.sun-editor .se-list-layer.se-list-format ul pre {
				background-color: var(--colorPrimaryHover);
    			border: 0;
			}
		}
		.se-list-inner .se-list-basic li button{
			&.active{
				background-color: var(--se-menu-hover-bg);
			}
		}
		// 分割线
		.sun-editor-editable hr{
			border-color:var(--colorBorder);
		}
		// 输入框
		.sun-editor .se-dialog input{
			color: var(--colorTextSecondary);
		}
	}

	// 禁用样式
	&.disabled .sun-editor{
		border: 0;
		.se-wrapper .se-wrapper-wysiwyg{
			border-radius: 8px;
			background: var(--colorFillTertiary);
		}
		
		.se-wrapper{
			min-height: auto;
		}
		.se-component{
			outline: 0;
		}
		.se-resizing-bar{
			display: none;
		}
	}

	// 只读样式
	&.read-only .sun-editor{
		.se-wrapper .se-wrapper-wysiwyg{
			border-radius: 8px;
			background: transparent;
		}
		.se-wrapper{
			min-height: auto;
		}
	}
}
