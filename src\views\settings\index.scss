@import "../../styles/mixin.scss";
.settings-modal{
    .settings-content{
        height: 670px;
        .settings-ringht-wrapper {
            width: 100%;
            overflow-y: auto;
        }
    }
    .ant-modal-body{
        padding: 0!important;
        // border-top: 1px solid var(--colorBorderSecondary);
    }
    .ant-menu{
        padding: 16px 12px;
        .ant-menu-item-group-title{
            padding: 6px 8px;
            @include font(12px,18px,600);
        }
        .ant-menu-item{
            padding: 6px 8px;
            margin:4px 0;
            height:auto;
            width: 100%;
            @include font(14px,20px,500);
            &.ant-menu-item-selected{
                background: var(--colorPrimary);
                color: var(--colorPrimaryContrasting);
            }
        }
        .ant-menu-item-group{
            margin-bottom: 12px;
        }
    }
    .wrapper{
        padding: 16px;
    }
    .title {
        @include font(18px,24px,600);
    }
    .desc{
      @include font(12px,18px,400,var(--colorTextTertiary));
      margin-top: 4px;
    }
    .bottom-operate{
        margin-top: auto;
        padding: 12px 16px;
        border-top: 1px solid var(--colorBorderSecondary);
    }
}

