import React, { useState, useEffect, forwardRef, useRef, useImperativeHandle } from "react";
import { connect } from "react-redux";
import { Modal, message, Input, Flex, Form, Select } from "antd";
import classNames from "classnames";
import { useTranslation } from 'react-i18next';
import { $trim } from '@/utils/common'

import { api_create_robot } from "@/api/robotManage2";

import './index.scss'
import Svgicon from "@/components/svgicon";
import UploadImage from "../../component/uploadImage";
const { TextArea } = Input;


// 新建机器人弹框
const CreateRobotModal = forwardRef((props: any, ref) => {
  const { t } = useTranslation();
  /** 自定义暴露给父组件的实例值 **/
  // useImperativeHandle(ref, () => ({
  //   init() {
  //   }
  // }))

  /* props */
  const { visible: isVisible, activeCompanyName } = props

  /* state */
  const [robotLogo, setRobotLogo] = useState<any>('') // 机器人logo
  const [isSubmiting, setIsSubmiting] = useState(false) // 是否提交中
  const [submittable, setSubmittable] = useState<boolean>(false);
  const [departmentList, setDepartmentList] = useState<any>([]); // 当前用户所在部门的数据列表

  const botTypes = [
    {//个人机器人
      value: '1',
      label: 'Personal Bot'
    },
    {//部门机器人
      value: '2', // 仅supervisor创建时显示此选项，选项显示部门名
      label: 'Department Bot',
      desc: 'Create Bot as Department'
    },
    {//公司机器人
      value: '3', // 仅公司管理员创建时显示此选项，选项显示公司名
      label: 'Company Bot',
      desc: 'Create Bot as Company'
    }
  ]

  const [form] = Form.useForm(); // 表单
  const formRef = useRef<any>(null);
  // Watch all values
  const values = Form.useWatch([], form);

  useEffect(() => {
    // console.log('activeCompanyName',activeCompanyName)
    form.setFieldValue('companyBot', activeCompanyName)
  }, []);

  useEffect(() => {
    form
      .validateFields({ validateOnly: true })
      .then(() => setSubmittable(true))
      .catch(() => setSubmittable(false));
  }, [form, values]);


  /* method */
  // 确认
  const handleIsRobotModalOk = async () => {
    try {
      const values: any = await form.validateFields();
      const { robotName, robotIntroduction } = values
      const data = {
        seq: 0,
        robotName: $trim(robotName),
        robotLogo: robotLogo,
        robotIntroduction: $trim(robotIntroduction)
      }
      setIsSubmiting(true)
      api_create_robot(data).then(() => {
        setIsSubmiting(false)
        message.success(t("robot-manage.create-robot-success"))
        handleIsRobotModalCancel()
        props.onSumit()
      }).catch((err: any) => {
        setIsSubmiting(false)
        console.log(err)
      })
    } catch (errorInfo) {
      console.log('Failed:', errorInfo);
    }

  }
  // 取消
  const handleIsRobotModalCancel = () => {
    props.onClose()
  }


  // 表单必填校验
  const formRequiredValidator = ({ }) => ({
    validator(_: any, value: any) {
      const _value = $trim(value)
      if (_value != '' && _value != null && _value != undefined) {
        return Promise.resolve();
      }
      return Promise.reject(new Error(t('common.input-required') as string));
    },
  })

  const handleRoboLogoChange = (res: any) => {
    setRobotLogo(res)
  }

  useEffect(() => {

  }, [])

  return (
    <>
      <Modal title={t(["robot-manage.create-robot-button"])}
        wrapClassName="create-robot-modal"
        open={isVisible}
        onOk={handleIsRobotModalOk}
        onCancel={handleIsRobotModalCancel}
        footer={undefined}
        okText={t("app.ok")}
        cancelText={t("app.cancel")}
        maskClosable={false}
        centered
        cancelButtonProps={{ variant: "filled", color: "default" }}
        okButtonProps={{ disabled: isSubmiting || !submittable }}
      >
        <div>
          <div className={"upload-con"}>
            <Flex vertical align={"center"}>
              <UploadImage onUpdate={handleRoboLogoChange} icon={<Svgicon svgName="upload_avatar" />} />
              <div className="upload-text">{t('robot-manage.create-robot.robot-logo')}</div>
            </Flex>
          </div>
          <Form ref={formRef} className="common-form" name="validateOnly" layout="vertical" form={form} autoComplete="off" requiredMark={false} disabled={isSubmiting}>
            {/* 机器人名称 */}
            <Form.Item shouldUpdate={true} name="robotName" label={t("robot-manage.create-robot.robot-name")} rules={[formRequiredValidator]}>
              <Input variant="filled" showCount maxLength={40} placeholder={t("robot-manage.create-robot.robot-name-hint") as string} />
            </Form.Item>
            {/* 机器人类型 个人机器人、公司机器人、部门机器人 */}
            <Form.Item shouldUpdate={true} name="robotType" label={t("bots-marketplace.robot-type")} rules={[formRequiredValidator]}>
              <Select variant="filled" popupClassName="bot-type-selector" placeholder={t("common.please-select") as string} suffixIcon={<Svgicon svgName="icon_select_suffix" />}
                options={botTypes}
                optionRender={(option) => (
                  <Flex className="bot-type-selector-text" vertical gap={8}>
                    <div>{option.data.label}</div>
                    {option.data.desc ? <div className="desc">{option.data.desc}</div> : null}
                  </Flex>
                )}
              />
            </Form.Item>
            {/* 部门机器人类型显示部门选择框 */}
            {
              form.getFieldValue('robotType') === '2' ?
                <Form.Item shouldUpdate={true} name="departBot" label={'Department Bot'} rules={[formRequiredValidator]}>
                  <Select variant="filled" popupClassName="departments-selector" placeholder={t("common.please-select") as string} suffixIcon={<Svgicon svgName="icon_select_suffix" />}
                    options={departmentList}
                    optionRender={(option) => (
                      <Flex className="bot-type-selector-text" vertical gap={8}>
                        <div>{option.data.label}</div>
                      </Flex>
                    )}
                  />
                </Form.Item> : null
            }
            {/* 公司机器人类型显示公司名称 */}{
              form.getFieldValue('robotType') === '3' ?
                <Form.Item name="companyBot" label={'Company Bot'}>
                  <Input variant="filled" disabled placeholder={t("company.company-name") as string} />
                </Form.Item> : null
            }
            {/* 机器人简介 */}
            <Form.Item name="robotIntroduction" label={t("robot-manage.create-robot.robot-introduction")}>
              <TextArea
                placeholder={t("robot-manage.create-robot.robot-introduction-hint") as string}
                autoSize={{ minRows: 4, maxRows: 8 }}
                variant="filled"
                showCount={true}
                maxLength={500} />
            </Form.Item>
          </Form>
        </div>
      </Modal>
    </>
  );
});
const mapStateToProps = (state: any) => ({
  userInfo: state.app.userInfo,
  globalRegion: state.app.globalRegion,
  globalLang: state.app.globalLang,
  activeCompanyName: state.app.activeCompanyName,
});
const mapDispatchToProps = (dispatch: (e: any) => void) => ({
});
export default connect(mapStateToProps, mapDispatchToProps)(CreateRobotModal);