.robot-detail-drawer{

  .info {
    margin-top: 8px;
    display: flex;
    margin-bottom: 30px;
    img {
      width: 60px;
      height: 60px;
      margin-right: 10px;
      border-radius: 12px;
      border: 1px solid var(--colorBorderSecondary);
    }

    .text {
      display: flex;
      flex-direction: column;
      margin-top: 4px;
      span:first-child{
      color: var(--colorText);
      margin-bottom: 4px;
      
      font-weight: 600;
      font-size: 18px;
      line-height: 24px;
    }

    span:last-child {
      color: var(--colorTextSecondary);
      
      font-weight: 400;
      font-size: 14px;
      line-height: 24px;
    }
  }
}

  .describe {
    
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    color: var(--colorText);
    margin-top: 32px;
    word-break: break-word;
  }

  .model {
    margin-top: 24px;
    background: var(--colorFillQuaternary);
    border-radius: 10px;
    margin-bottom: 20px;
  }

  .model-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap:0 10px;
    padding: 10px 0px;
    margin: 0px 10px;
    
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    gap: 10;
    span:first-child {
      color: var(--colorTextSecondary);
      flex-shrink: 0;
    }

    span:last-child {
      color: var(--colorText);
      flex: 1;
      white-space: normal;
      word-wrap: break-word;
      text-align: right;
    }

    &:not(:last-child) {
      border-bottom: 0.5px solid var(--colorBorderSecondary);
    }
  }

  .props {
    margin-top: 24px;
  }

  .prop-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    padding: 10px 0px;
    margin: 0px 10px;
    gap:0 10px;

    .name {
      color: var(--colorTextSecondary);
    }

    .value {
      color: var(--colorText);
    }

    &:not(:last-child) {
      border-bottom: 0.5px solid var(--colorBorderSecondary);
    }
  }
}