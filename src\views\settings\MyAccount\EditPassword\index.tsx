
import { connect } from "react-redux";
import { useEffect, useState, useRef, forwardRef } from "react";
import { Modal, message, Flex, Button, Input, Form, Tabs, TabsProps } from "antd"
import { useTranslation } from 'react-i18next';
import { $trim, $isNull } from '@/utils/common'
import './index.scss'
import MobileInput from "@/components/form/mobileInput";
import ErrorCodeMap from '@/views/common/formErrorCodeMap'
import FormUtils from '@/views/common/formUtils'
import classnames from "classnames";
import PasswordInput from "@/components/form/passwordInput";
import { EmailRules, MobileRules, PasswordRules, VerifyCodeRules } from "@/views/common/formRule";
import { api_get_email_code, api_get_mobile_code } from '@/api/user'
import { api_change_password } from '@/api/setting'
import VerifyCode from "@/components/form/verifyCode";
import { GetMobileFormValue } from "../EditMobile";
import { RequestFailedMessage } from "@/utils/userRequest";
import { Encrypt } from '@/utils/forge'

const scence: any = {
    Email: 'MEMBER_CHANGE_PASSWORD_BY_EMAIL',
    Mobile: 'MEMBER_CHANGE_PASSWORD_BY_MOBILE',
}
const TabItems: any = [
    {
        key: 'Email',
        label: 'email-verifiy',
        children: null,
    },
    {
        key: 'Mobile',
        label: 'mobile-verifiy',
        children: null,
        visible: false // // 用户绑定过手机号,才有手机号 tab
    }
];

// 修改密码
const EditPassword = forwardRef((props: any, ref: React.LegacyRef<HTMLDivElement> | any) => {
    const { t } = useTranslation()

    const { userInfo, email, mobile, onUpdate } = props
    const currentUserId = userInfo.imUserId

    const [step, setStep] = useState(1)// 步骤
    const [tabItems, setTabItems] = useState([...TabItems]) //tabs
    const [accountType, setAccountType] = useState('Email') // 邮箱 Email 或  手机号 Mobile
    const [isOkAble, setIsOkAble] = useState(false) // 按钮是否可用
    const [form] = Form.useForm();
    const [formValues, setFormValues] = useState<any>({});


    // 初始化
    useEffect(() => {
    }, [])

    useEffect(() => {
        // console.log('email', email)
        form.setFieldValue('email', email)
    }, [email])

    useEffect(() => {
        // console.log('mobile change', mobile)
        setTabItems(tabItems.map((i: any) => {
            i.visible = !$isNull(mobile)
            return i
        }))
        if (mobile) {
            setMobileFormValue()
        }
    }, [mobile])

    // 表单项输入值监听, 控制提交按钮状态
    const values = Form.useWatch([], form);
    useEffect(() => {
        let fields: any = []
        if (accountType == 'Email') {
            fields = ['email', "code", "password"]
        } else {
            fields = ["mobile", "code", "password"]
        }
        // console.log('fields', fields)
        form.validateFields(fields, { validateOnly: true }).then(() => {
            setIsOkAble(true)
        }).catch((err) => {
            // console.log('SubmitButton1 errerr:', err)
            setIsOkAble(false)
        });
    }, [accountType, form, values])

    const setMobileFormValue = () => {
        const { number, codeLabel } = GetMobileFormValue(mobile)
        form.setFieldValue('mobile', {
            number: number,
            code: { label: codeLabel, value: '' }
        })
    }

    const onOk = async () => {
        // setStep(2)
        // return
        let { email, mobile, code, password } = formValues
        const data: any = {
            scene: scence[accountType],
            code,
            newPassword: Encrypt(password)
            // email,
            // areaCode: '+' + mobile?.code?.label,
            // mobile: mobile?.number"email": "<EMAIL>",
        }
        if (accountType === 'Email') {
            data.email = email
        } else {
            data.areaCode = '+' + mobile?.code?.label
            data.mobile = mobile?.number
        }
        // console.log('data', data)
        // return
        setIsOkAble(false)
        const res: any = await api_change_password(data).catch(() => {
            setIsOkAble(true)
        })
        setIsOkAble(true)
        // console.log('res', res)
        if (!res) return
        let _code = res.code
        if (_code === 0 && res?.data?.success) {
            message.success(t('setting.change-password-ok'))
            onUpdate && onUpdate()
            onCancel()
        } else {
            const codeErros = ErrorCodeMap.code// 验证码错误码
            const errors = [...codeErros]
            if (!errors.includes(_code)) {
                const msg = RequestFailedMessage(res?.msg, _code)
                message.error(msg);
                return
            }
            const errMsg = t(`errorCode.${_code}`)
            form.setFields([{ name: "code", errors: [errMsg], warnings: [`errorCode.${_code}`] }]) //warnings 为了解决 setFields 检验信息 国际化问题
        }
    }

    // 取消
    const onCancel = () => {
        props.onClose()
    }

    // tab切换
    const onChangeTab = (key: string) => {
        // console.log(key);
        setAccountType(key)
    };

    // 区号切换时需要校验手机号
    const onValidteMobile = () => {
        form.validateFields(["mobile"])
    }

    // 表单项 输入改变事件
    const onFiledValueChange = (changedFields: any, allValues: any) => {
        try {
            // console.log('changedFields', changedFields)
            // console.log('allValues',allValues)
            setFormValues({ ...formValues, ...allValues });
            Object.keys(changedFields).forEach((fieldName: any) => {
                const fieldError = form?.getFieldError(fieldName)
                if (fieldError.length > 0) FormUtils.clearValidate(form, fieldName)
            })
        } catch (error) {

        }
    }
    //  获取验证码失败 对应表单输入框错误提示
    const onVerifyCodeError = (err: any) => {
        const _code = err?.code
        const emailErrors = ErrorCodeMap.email
        const mobileErrors = ErrorCodeMap.mobile
        const errMsg = t(`errorCode.${_code}`)
        let targetField: any = null
        if (emailErrors.includes(_code)) targetField = 'email'
        if (mobileErrors.includes(_code)) targetField = 'mobile'
        if (!targetField) return
        form.setFields([{ name: targetField, errors: [errMsg], warnings: [`errorCode.${_code}`] }])
    }
    return (
        <div className="setting-edit-password">
            <Modal
                className={"setting-edit-password-modal border no-padding"}
                title={t('setting.change-password')}
                open={true}
                onOk={onOk}
                onCancel={onCancel}
                mask={true}
                maskClosable={true}
                centered={true}
                width={450}
                okText={t('app.ok')}
                cancelText={t("app.cancel")}
                cancelButtonProps={{ variant: "filled", color: "default" }}
                okButtonProps={{ disabled: !isOkAble }}
                footer={undefined}
            >
                <Form
                    form={form}
                    className="setting-edit-password-form common-form mediumn"
                    name="basic"
                    autoComplete="off"
                    layout="vertical"
                    onValuesChange={onFiledValueChange}
                    validateTrigger="onBlur"
                    requiredMark={false}
                    initialValues={{
                        email: '',
                        mobile: {
                            number: '', // 手机号
                            code: { label: '', value: '' }// 区号 label: 1, value: 'US'
                        },
                        code: '',
                        password: ''
                    }}
                >
                    {
                        tabItems.filter(i => i.visible).length > 0 ?
                            <Tabs className="common-tab without-border" activeKey={accountType} defaultActiveKey="Email"
                                items={tabItems.filter(i => i.visible)?.map((item: any) => ({
                                    key: item.key,
                                    label: t(`setting.${item.label}`), // 确保这里使用翻译函数
                                    children: item.children,
                                }))} onChange={onChangeTab} />
                            : null}
                    {/* 邮箱 */}
                    <Form.Item name="email"
                        label={t('login.email-address')} rules={[{ required: true, whitespace: true, message: '' }, ...EmailRules(t)]}
                        className={accountType == 'Mobile' ? 'hidden' : ''}>
                        <Input disabled autoComplete="new-email" variant="filled" maxLength={300} placeholder={t('login-pls.email') as string} />
                    </Form.Item>
                    {/* 手机号 */}
                    <Form.Item shouldUpdate name="mobile" label={t('login.mobile')} help={null} rules={MobileRules(t)}
                        className={classnames(['mobile-form-item', { hidden: accountType == 'Email' }])}>
                        <MobileInput disabled onValidte={onValidteMobile} height={34} />
                    </Form.Item>

                    {/* 验证码 */}
                    <Form.Item name="code"
                        className={classnames(["psd-form-item"])} label={t('login.verify-code')} rules={VerifyCodeRules(t)}>
                        <VerifyCode
                            id="edit-password-verify-code"
                            step={step}
                            api={accountType == 'Email' ? api_get_email_code : api_get_mobile_code}
                            scene={scence[accountType]}
                            target={accountType.toLocaleLowerCase()}
                            {
                            ...
                            (accountType == 'Email' ? { email: form.getFieldValue("email") } : { mobile: form.getFieldValue("mobile") })
                            }
                            targetValidate={form.validateFields}
                            onError={onVerifyCodeError}
                        />
                    </Form.Item>
                    <Form.Item name="password"
                        label={t('setting.new-password')}
                        className={classnames(["psd-form-item"])}
                        rules={[{ required: true, whitespace: true, message: '' }, ...PasswordRules(t)]}>
                        <PasswordInput key="setting-password" />
                    </Form.Item>
                </Form>
            </Modal>

        </div>
    )
})

const mapStateToProps = (state: any) => ({
    userInfo: state.app.userInfo,
})
const mapDispatchToProps = (dispatch: any) => ({
    // dispatch_api_: (data:any) => dispatch(dispatch_api_(data))
})
const ConnectedCounter = connect(mapStateToProps, mapDispatchToProps, null, { forwardRef: true })(EditPassword);
export default ConnectedCounter;