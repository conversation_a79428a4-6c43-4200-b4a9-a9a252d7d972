

import React, { useState, useEffect } from 'react';
import { Input, message,InputNumber  } from 'antd';
import './index.scss';
import { t } from 'i18next';
import classNames from 'classnames';
import ErrorCodeMap from '@/views/common/formErrorCodeMap'
import StrictNumberInput from '@components/form/numberInput'
import { useCountdown } from '@/components/form/verifyCode/CountdownProvider';
import {RequestFailedMessage} from '@/utils/userRequest'

// const CAPTCHA_DURATION = 60; // 倒计时时长（秒）
const inputMaxLength = 6
/* 获取验证码
* 1.邮箱
* 2.手机号（区号+号码）
* 3.注意一定要使用配合CountdownProvider 使用
*/
const VerifyCode = (props: any) => {
    const { id, value, onChange, api:apiRequest,target,targetValidate,scene } = props;

    const { countdowns, startCountdown } = useCountdown();

    // 唯一标识符
    const currentKey = `${id}${scene}`; 
    // console.log('currentKey',currentKey)
    // 获取当前倒计时状态
    const currentCountdown = countdowns.get(currentKey);
    // console.log('currentCountdown',currentCountdown)

    const [code, setCode] = useState('')
    const [emailInner,setEmailInner] =useState("") // 邮箱
    const [mobileInner,setMobileInner] =useState<any>({}) // 手机
    // const [countdown, setCountdown] = useState(0);
    const [isBtnDisabled, setIsBtnDisabled] = useState(true); // 获取验证码按钮是否 禁用，邮件或手机号 规则检验通过才 启用

    const triggerChange = (_value: string) => {
        onChange?.(_value);
    };

    // // 倒计时计时器
    // useEffect(() => {
    //     if (countdown > 0) {
    //         const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
    //         return () => clearTimeout(timer);
    //     }
    // }, [countdown]);

    useEffect(()=>{
        // console.log('---useEffect email:',props.email)
        setEmailInner(props.email)
    },[props.email])

    useEffect(()=>{
        // console.log('---useEffect mobile:',props.mobile)
        setMobileInner(props.mobile)
    },[props.mobile])

    useEffect(()=>{
        // console.log('---useEffect email or mobile:',props.email,props.mobile)
        setEmailInner(props.email)
        targetValidate([target],{ validateOnly: true }).then(() => {
            // console.log('no Errors :')
            setIsBtnDisabled(false)
        }).catch((err:any) => {
            // console.log('catch Errors :', err)
            const hasErrors = err && err.errorFields  && err.errorFields.length>0
            setIsBtnDisabled(hasErrors)
        });
    },[props.email,props.mobile])

    // 发送验证码
    const sendCaptcha = async () => {
        // if (props.onError) props.onError()
        // return
        try {
            const params:any = {scene}
            if (target =='email' || target?.toLocaleLowerCase().indexOf('email') != -1){
                params.email = emailInner
            } 
            if (target =="mobile" || target?.toLocaleLowerCase().indexOf('mobile') != -1){
                params.areaCode = '+' + mobileInner.code.label
                params.mobile = mobileInner.number
            }
            // console.log('params',params)
            // startCountdown(currentKey)
            // return

            setIsBtnDisabled(true)
            apiRequest && apiRequest(params).then((res:any) =>{
                setIsBtnDisabled(false)
                const _code = res.code
                // console.log('res',res)
                if (_code===0 &&res?.data?.success){
                    message.success(t('login-msg.send-success'))
                    // setCountdown(CAPTCHA_DURATION); // 启动倒计时
                    startCountdown(currentKey)
                    setCode("")
                    triggerChange("");
                    if (props.onOk) props.onOk()
                } else{
                    // const codeErros1 =  ErrorCodeMap.code
                    const emailErrors = ErrorCodeMap.email
                    const mobileErrors = ErrorCodeMap.mobile
                    // console.log('props.onError', props.onError)
                    const inputErrors = [...emailErrors,...mobileErrors]
                    if (inputErrors.includes(_code) && props.onError){ // 输入框下面提示错误信息
                        props.onError(res)
                    }else{ // 其他全局提示
                        const msg = RequestFailedMessage(res?.msg,_code)
                        // console.log('errors:', errors, msg)
                        message.error(msg);
                    }
                    
                }
                
            }).catch((err:any)=>{
                setIsBtnDisabled(false)
                console.log('err',err)
                // if (props.onError) props.onError(err)
            })
        } catch (error) {
            // message.success(t('login-msg.send-success'))
            // message.error('发送失败，请重试');
        }
    };

    // 输入值变化
    const onChangeInput = (e: any) => {
        const value= e.target.value
        setCode(value)
        triggerChange(value);
    }
    
    // 键盘按键物理释放
    const onKeyUp = (e: any) => {
        if (props?.onKeyUp) props?.onKeyUp(e)
    }

    const onBlur = (e: any)=>{
        if(props.onBlur) props.onBlur()
    }
    
    // 输入框规则：仅支持输入数字，最长6个数字
    return (
        <StrictNumberInput
            value={value?.code || code}
            onKeyUp={(e) => onKeyUp(e)}
            onChange={(e:any)=>onChangeInput(e)}
            onBlur={onBlur} // 关键！确保 onBlur 被触发
            className="verify-code ipt" 
            maxLength={inputMaxLength} 
            placeholder={t('login-pls.verify-code') as string} 
            suffix={
                <div className='suffix'>
                    {
                        currentCountdown?.countdown > 0 ? `${currentCountdown?.countdown}s`: 
                        (<div 
                            className={classNames(['suffix-btn',{disabled: isBtnDisabled || currentCountdown?.isCounting}])} 
                            onClick={(e)=>{e.stopPropagation();return (isBtnDisabled || currentCountdown?.isCounting? null:sendCaptcha())}}>
                            {t('login.get-code')}
                        </div>)
                    }
                </div>
            } />
    )
    
}
export default VerifyCode