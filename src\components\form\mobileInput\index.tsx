

import React, { useState, useEffect } from 'react';
import { connect } from "react-redux";
import { Input, message, Form, Space,Flex } from 'antd';
import { useTranslation } from 'react-i18next';
import Svgicon from '@/components/svgicon'
import FilterSelect from '../select'
import classnames from "classnames";
import areaCodeJson from './areaCode.json'
// import areaCodeLimit from './areaCodeLimit'
// import {api_get_phone_rules} from '@/api/user'
import {dispatch_api_get_ip_areacode} from '@/api/platform'
import { sortObjectArrayByFistWord,sortObjectArrayByPingyin } from '@/utils/common'
import StrictNumberInput from '@components/form/numberInput'
import './index.scss'

let areaCodeJsonDatas:any = JSON.parse(JSON.stringify(areaCodeJson))

// areaCodeDatas = areaCodeDatas.map((i:any)=>{
//     i.limit = areaCodeLimit[i.label] ?areaCodeLimit[i.label]?.bit:null
//     if (i.limit==null) console.log('空',i.label)
//     return i
// })
// console.log('areaCodeDatas',JSON.stringify(areaCodeDatas))

// function findDuplicates(arr:any) {
//     const seen = new Set();
//     return arr.filter((item:any) => {
//       if (seen.has(item)) {
//         return true; // 如果已经存在，说明是重复元素
//       }
//       seen.add(item); // 否则添加到 Set 中
//       return false;
//     });
//   }

//   function findDiff(arr1:any, arr2:any) {
//     let set1:any = new Set(arr1);
//     let set2:any = new Set(arr2);

//     let diff1 = [...set1].filter(x => !set2.has(x));
//     let diff2 = [...set2].filter(x => !set1.has(x));

//     return { diff1, diff2 };
// }
// const a1 = Object.keys(areaCodeLimit).map(i=>Number(i))
// let a2:any = areaCodeDatas.map((i:any)=>Number(i.label))
// // a2 = Array.from(new Set(a2))
// console.log('areaCodeDatas',a1, a2)

// console.log('findDiff',findDiff(a1,a2))

const zh_areaCodeDatas = sortObjectArrayByPingyin(areaCodeJsonDatas, 'name_zh')
const hk_areaCodeDatas = sortObjectArrayByPingyin(areaCodeJsonDatas, 'name_hk')
const en_areaCodeDatas = sortObjectArrayByFistWord(areaCodeJsonDatas, 'name_en')
const inputMaxLength = 50

// 区号+手机号 组件
const MobileInput = (props: any) => {
    const { t, i18n } = useTranslation();

    const { 
        id, value={}, onChange,  onValidte, height= 38, // 父组件属性
        globalLang:lang,ipAreaCode, // mapStateToProps
        ...restProps // 剩余属性
    } = props;
    const [number, setNumber] = useState('') // 手机号
    const [code, setCode] = useState({label: 1, value: 'US'}) // 区号
    const [areaCodeDatasObj, setAreaCodeDatasObj]=useState<any>({
        zh: zh_areaCodeDatas,
        hk: hk_areaCodeDatas,
        en: en_areaCodeDatas
    }) // 不同语言对应 区号数据
    
    // 初始化
    useEffect(()=>{
        const codeValue = value?.code?.value || ipAreaCode
        const codeLabel = value?.code?.label
        if (codeLabel){ // 明确区号label
            initCodeByLabel(codeLabel)
            return
        }
        if (codeValue) { // 明确区号代码
            initCode(codeValue)
            return
        }
        triggerChange({code:code})
        getIpAreaCode()
    },[])

    useEffect(()=>{
        // console.log('value.code',value.code)
        setCode(value.code)
    },[value.code])

    useEffect(()=>{
        // console.log('value.number',value.number)
        setNumber(value.number)
    },[value.number])

    // 获取IP地区代码
    const getIpAreaCode=async ()=>{
        try {
            const res:any = await props.dispatch_api_get_ip_areacode().catch()
            if (!res) return
            const areaCode = res.data
            if (areaCode) {
                initCode(areaCode)
            }
        } catch (error) {
            console.log('error',error)
        }
    }

    const initCode = (areaCode:any)=>{
        const target = areaCodeJsonDatas.filter((i:any)=>i.value==areaCode)
        if (target.length>0) {
            const codeSelected = {value:areaCode,label:target[0].label}
            setCode(codeSelected)
            triggerChange({code:codeSelected})
            if (number && onValidte) onValidte()
        } else{
            triggerChange({code:code})
        }
    }

    const initCodeByLabel = (targetLabel:any)=>{
        const target = areaCodeJsonDatas.filter((i:any)=>i.label==targetLabel)
        if (target.length>0) {
            const codeSelected = {value:target[0].value,label:targetLabel}
            setCode(codeSelected)
            triggerChange({code:codeSelected})
            if (number && onValidte) onValidte()
        } else{
            triggerChange({code:code})
        }
    }
    const triggerChange = (changedValue: { number?: any; code?: any; }) => {
        onChange?.({ number, code, ...value, ...changedValue });
    }

    // 手机号修改
    const onChangeNumberInput = (e: any) => {
        const inputValue = e.target.value;
        setNumber(inputValue);
        triggerChange({number:inputValue})
    }

    // 键盘按键物理释放
    const onKeyUp = (e: any) => {
        if (props?.onKeyUp) props?.onKeyUp(e)
    }

    const onBlur = (e: any)=>{
        if(props.onBlur) props.onBlur()
    }
    
    // 区号修改
    const onChangeAreaCode = (obj: any) => {
        let { value,label } =obj
        // label = '+' +label
        const areaCode={value,label}
        setCode(areaCode)
        triggerChange({code:areaCode})
        if (number && onValidte) onValidte()
    }

    return (
        <Space className='mobile-input' id={id} direction="horizontal" size={12} style={{height:parseInt(height) + 'px'}}>
            <Form.Item noStyle validateStatus="success">
                <FilterSelect
                    {...restProps}
                    options={areaCodeDatasObj[lang]}
                    value={value.code || code} 
                    onChange={onChangeAreaCode}
                    popupClassName="areacode-options-dropdown"
                    labelInValue={true}
                    filterable={true}
                    filterOption={
                        (input:any, option:any) =>{
                            try {
                                const _input = input.toLowerCase() || ''
                                const name = option?.['name_' +lang] || ''
                                let label= (option?.label?? '')
                                label =label.toString()
                                return  (label)?.toLowerCase().includes(_input) || (name ?? '').toLowerCase().includes(_input)
                            } catch (error) {
                                console.log('error',error)
                                return true
                            }
                    }}
                    labelRender={(option:any) => ('+' + option.label)}
                    optionTextRender={ 
                        (option:any,index:number)=>(
                            <> <span className='name'>{option.data['name_' +lang]}</span>
                            <span className='code'>(+{option.data.label})</span></>
                    )}
                />
            </Form.Item>
                <StrictNumberInput 
                    {...restProps}
                    autoComplete="new-mobile"
                    value={value?.number || number}
                    onKeyUp={(e) => onKeyUp(e)}
                    onChange={(e: any) => onChangeNumberInput(e)}
                    onBlur={onBlur} // 关键！确保 onBlur 被触发
                    className="ipt" 
                    maxLength={inputMaxLength} 
                    placeholder={t('login-pls.mobile') as string} variant="filled"/>
        </Space>
    )
}

const mapStateToProps = (state: any) => ({
    globalLang: state.app.globalLang,
    ipAreaCode: state.app.ipAreaCode,
  })
  
  const mapDispatchToProps = (dispatch: any) => ({
    dispatch_api_get_ip_areacode: () => dispatch(dispatch_api_get_ip_areacode())
  })
  
  export default connect(mapStateToProps, mapDispatchToProps)(MobileInput);