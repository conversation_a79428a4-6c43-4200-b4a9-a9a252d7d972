import * as apis from "@/api/robotManage";
import * as actions from "./action";

export const dispatch_api_create_bots = (data = apis.api_create_bots_params) => (dispatch: any) => new Promise((resolve, reject) => {
  apis.api_create_bots(data).then(result => {
    if(result.data.code === 0) {
      resolve(result.data.data);
    } else {
      reject(result);
    }
  }).catch(err => reject(err))
})

export const dispatch_api_query_robot = (data = apis.api_query_robot_params) => (dispatch: any) => new Promise((resolve, reject) => {
  apis.api_query_robot(data).then(result => {
    if(result.data.code === 0) {
      resolve(result.data.data);
    } else {
      reject(result);
    }
  }).catch(err => reject(err))
})

export const dispatch_api_update_robot = (data = apis.api_update_robot_params) => (dispatch: any) => new Promise((resolve, reject) => {
  apis.api_update_robot(data).then(result => {
    if(result.data.code === 0) {
      resolve(result.data.data);
    } else {
      reject(result);
    }
  }).catch(err => reject(err))
})

export const dispatch_api_delete_robot = (data = apis.api_delete_robot_params) => (dispatch: any) => new Promise((resolve, reject) => {
  apis.api_delete_robot(data).then(result => {
    if(result.data.code === 0) {
      resolve(result.data.data);
    } else {
      reject(result);
    }
  }).catch(err => reject(err))
})

export const dispatch_api_query_robot_list = (data = apis.api_query_robot_list_params) => (dispatch: any) => new Promise((resolve, reject) => {
  apis.api_query_robot_list(data).then(result => {
    if(result.data.code === 0) {
    //   dispatch(actions.change_app_robot_list(result.data.data.robotList));
      resolve(result.data.data);
    } else {
      reject(result);
    }
  }).catch(err => reject(err))
})

export const dispatch_api_add_robot_document = (data = apis.api_add_robot_document_params, config?: any) => (dispatch: any) => new Promise((resolve, reject) => {
  apis.api_add_robot_document(data, config).then(result => {
    if(result.data.code === 0) {
      resolve(result.data.data);
    } else {
      reject(result);
    }
  }).catch(err => reject(err))
})

export const dispatch_api_delete_robot_document = (data = apis.api_delete_robot_document_params) => (dispatch: any) => new Promise((resolve, reject) => {
  apis.api_delete_robot_document(data).then(result => {
    if(result.data.code === 0) {
      resolve(result.data.data);
    } else {
      reject(result);
    }
  }).catch(err => reject(err))
})

export const dispatch_api_query_robot_document = (data = apis.api_query_robot_document_params) => (dispatch: any) => new Promise((resolve, reject) => {
  apis.api_query_robot_document(data).then(result => {
    if(result.data.code === 0) {
      resolve(result.data.data);
    } else {
      reject(result);
    }
  }).catch(err => reject(err))
})

export const dispatch_api_update_robot_document = (data = apis.api_update_robot_document_params) => (dispatch: any) => new Promise((resolve, reject) => {
  apis.api_update_robot_document(data).then(result => {
    if(result.data.code === 0) {
      resolve(result.data.data);
    } else {
      reject(result);
    }
  }).catch(err => reject(err))
})

export const dispatch_api_query_robot_document_list = (data = apis.api_query_robot_document_list_params) => (dispatch: any) => new Promise((resolve, reject) => {
  apis.api_query_robot_document_list(data).then(result => {
    if(result.data.code === 0) {
      resolve(result.data.data);
    } else {
      reject(result);
    }
  }).catch(err => reject(err))
})

export const dispatch_api_query_embedding_chunk_List = (data = apis.api_query_embedding_chunk_List_params) => (dispatch: any) => new Promise((resolve, reject) => {
  apis.api_query_embedding_chunk_List(data).then(result => {
    if(result.data.code === 0) {
      resolve(result.data.data);
    } else {
      reject(result);
    }
  }).catch(err => reject(err))
})

export const dispatch_config_query_robot_Llm_model = (data = apis.config_query_robot_Llm_model_params) => (dispatch: any) => new Promise((resolve, reject) => {
  apis.config_query_robot_Llm_model(data).then(result => {
    if(result.data.code === 0) {
      let llmModelList = result.data.data;
      let robotLlmModel:any[] = [];

      console.log(robotLlmModel);


      for(let key in llmModelList) {
        let category = key;
        let modelList = llmModelList[key];

        let childrenList = [];
        for(let key1 in modelList) {
          let model = modelList[key1];
          let name = model.name;

          childrenList.push({
            label: name,
            value: name
          })
        }

        let Level1 = {
          label: category,
          value: category,
          children: childrenList
        }

        robotLlmModel.push(Level1);
      }

    //   dispatch(actions.change_app_robot_lim_model(llmModelList));
      resolve(result.data.data);
    } else {
      reject(result);
    }
  }).catch(err => reject(err))
})

export const dispatch_config_query_robot_embedding_model = (data = apis.config_query_robot_embedding_model_params) => (dispatch: any) => new Promise((resolve, reject) => {
  apis.config_query_robot_embedding_model(data).then(result => {
    if(result.data.code === 0) {
      let resultEmbeddingModelList = result.data.data.embeddingModelList;
      let embeddingModelList = [];

      for(let key in resultEmbeddingModelList) {
        embeddingModelList.push({
          label: resultEmbeddingModelList[key],
          value: resultEmbeddingModelList[key]
        })
      }

    //   dispatch(actions.change_app_embedding_model(embeddingModelList));
      resolve(result.data.data);
    } else {
      reject(result);
    }
  }).catch(err => reject(err))
})

export const dispatch_config_query_phone_area_list = (data = apis.config_query_phone_area_list_params) => (dispatch: any) => new Promise((resolve, reject) => {
  apis.config_query_phone_area_list(data).then(result => {
    if(result.data.code === 0) {
      let phoneAreaList = result.data.data.phoneAreaList;
    //   dispatch(actions.change_app_phone_area_list(phoneAreaList));
      resolve(result.data.data);
    } else {
      reject(result);
    }
  }).catch(err => reject(err))
})

export const dispatch_api_get_application_theme = (data = apis.api_get_application_theme_params) => (dispatch: any) => new Promise((resolve, reject) => {
  apis.api_get_application_theme(data).then(result => {
    if(result.data.code === 0) {

      resolve(result.data.data);
    } else {
      reject(result);
    }
  }).catch(err => reject(err))
})

export const dispatch_api_save_application_theme = (data = apis.api_save_application_theme_params) => (dispatch: any) => new Promise((resolve, reject) => {
  apis.api_save_application_theme(data).then(result => {
    if(result.data.code === 0) {
      resolve(result.data.data);
    } else {
      reject(result);
    }
  }).catch(err => reject(err))
})

export const dispatch_api_chat_message = (data = apis.api_chat_message_params) => (dispatch: any) => new Promise((resolve, reject) => {
  apis.api_chat_message(data).then(result => {
    resolve(result);
  }).catch(err => reject(err))
})

export const dispatch_api_query_current_chat = (data = apis.api_query_current_chat_params) => (dispatch: any) => new Promise((resolve, reject) => {
  apis.api_query_current_chat(data).then(result => {
    if(result.data.code === 0) {
      resolve(result.data.data);
    } else {
      reject(result);
    }
  }).catch(err => reject(err))
})

export const dispatch_api_clear_current_chat = (data = apis.api_clear_current_chat_params) => (dispatch: any) => new Promise((resolve, reject) => {
  apis.api_clear_current_chat(data).then(result => {
    if(result.data.code === 0) {
      resolve(result.data.data);
    } else {
      reject(result);
    }
  }).catch(err => reject(err))
})

export const dispatch_api_query_user_power = (data = apis.api_query_user_power_params) => (dispatch: any) => new Promise((resolve, reject) => {
  apis.api_query_user_power(data).then(result => {
    if(result.data.code === 0) {
      resolve(result.data.data);
    } else {
      reject(result);
    }
  }).catch(err => reject(err))
})

export const dispatch_api_query_employee_list = (data = apis.api_query_employee_list_params) => (dispatch: any) => new Promise((resolve, reject) => {
  apis.api_query_employee_list(data).then(result => {
    if(result.data.code === 0) {
      resolve(result.data.data);
    } else {
      reject(result);
    }
  }).catch(err => reject(err))
})

export const dispatch_api_query_employee_tree = (data = apis.api_query_employee_tree_params) => (dispatch: any) => new Promise((resolve, reject) => {
  apis.api_query_employee_tree(data).then(result => {
    if(result.data.code === 0) {
      resolve(result.data.data);
    } else {
      reject(result);
    }
  }).catch(err => reject(err))
})

export const dispatch_api_update_permission_user = (data = apis.api_update_permission_user_params) => (dispatch: any) => new Promise((resolve, reject) => {
  apis.api_update_permission_user(data).then(result => {
    if(result.data.code === 0) {
      resolve(result.data.data);
    } else {
      reject(result);
    }
  }).catch(err => reject(err))
})

export const dispatch_api_create_permission_role = (data = apis.api_create_permission_role_params) => (dispatch: any) => new Promise((resolve, reject) => {
  apis.api_create_permission_role(data).then(result => {
    if(result.data.code === 0) {
      resolve(result.data.data);
    } else {
      reject(result);
    }
  }).catch(err => reject(err))
})

export const dispatch_api_update_permission_role = (data = apis.api_update_permission_role_params) => (dispatch: any) => new Promise((resolve, reject) => {
  apis.api_update_permission_role(data).then(result => {
    if(result.data.code === 0) {
      resolve(result.data.data);
    } else {
      reject(result);
    }
  }).catch(err => reject(err))
})

export const dispatch_api_delete_permission_role = (data = apis.api_delete_permission_role_params) => (dispatch: any) => new Promise((resolve, reject) => {
  apis.api_delete_permission_role(data).then(result => {
    if(result.data.code === 0) {
      resolve(result.data.data);
    } else {
      reject(result);
    }
  }).catch(err => reject(err))
})

export const dispatch_api_update_role_power = (data = apis.api_update_role_power_params) => (dispatch: any) => new Promise((resolve, reject) => {
  apis.api_update_role_power(data).then(result => {
    if(result.data.code === 0) {
      resolve(result.data.data);
    } else {
      reject(result);
    }
  }).catch(err => reject(err))
})

export const dispatch_api_query_role_list = (data = apis.api_query_role_list_params) => (dispatch: any) => new Promise((resolve, reject) => {
  apis.api_query_role_list(data).then(result => {
    if(result.data.code === 0) {
      resolve(result.data.data);
    } else {
      reject(result);
    }
  }).catch(err => reject(err))
})

export const dispatch_api_update_robot_permission = (data = apis.api_update_robot_permission_params) => (dispatch: any) => new Promise((resolve, reject) => {
  apis.api_update_robot_permission(data).then(result => {
    if(result.data.code === 0) {
      resolve(result.data.data);
    } else {
      reject(result);
    }
  }).catch(err => reject(err))
})

export const dispatch_api_create_api_key = (data = apis.api_create_api_key_params) => (dispatch: any) => new Promise((resolve, reject) => {
  apis.api_create_api_key(data).then(result => {
    if(result.data.code === 0) {
      resolve(result.data.data);
    } else {
      reject(result);
    }
  }).catch(err => reject(err))
})

export const dispatch_api_update_api_key = (data = apis.api_update_api_key_params) => (dispatch: any) => new Promise((resolve, reject) => {
  apis.api_update_api_key(data).then(result => {
    if(result.data.code === 0) {
      resolve(result.data.data);
    } else {
      reject(result);
    }
  }).catch(err => reject(err))
})

export const dispatch_api_remove_api_key = (data = apis.api_remove_api_key_params) => (dispatch: any) => new Promise((resolve, reject) => {
  apis.api_remove_api_key(data).then(result => {
    if(result.data.code === 0) {
      resolve(result.data.data);
    } else {
      reject(result);
    }
  }).catch(err => reject(err))
})

export const dispatch_api_query_api_key_list = (data = apis.api_query_api_key_list_params) => (dispatch: any) => new Promise((resolve, reject) => {
  apis.api_query_api_key_list(data).then(result => {
    if(result.data.code === 0) {
      resolve(result.data.data);
    } else {
      reject(result);
    }
  }).catch(err => reject(err))
})


