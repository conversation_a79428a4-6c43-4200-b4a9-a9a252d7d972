import React from "react";
import { connect } from "react-redux";
import i18next from "i18next";
import classNames from "classnames";
import Svgicon from "@/components/svgicon"
import { Flex, Button, Table, Modal, message, Empty, Tooltip, Dropdown, Tag, Input, Select } from "antd";
import { ColumnProps } from "antd/es/table";
import type { MenuProps } from 'antd';
import {
  api_update_robot_document_params
} from "@/api/robotManage";

import {
  dispatch_api_update_robot_document
} from "@/store/api"

import {api_query_robot_document,api_delete_robot_document} from '@/api/robotManage2'

import './index.scss'
import '../common/style/index.scss'

import { ContextState, TableColumnsFace } from './index_type'
import UpdateDocument from './modal/updateDocument'
import CreateDocument from './modal/createDocument'


// 基本信息-机器人知识
class RobotSettingKnowledge extends React.Component<any, ContextState> {
  private timer: NodeJS.Timeout | null = null;
  constructor(props: any) {
    super(props);
    this.timer = null;
    this.state = {
      count: 1,
      selectedRowKeys: [],
      selectedRows: [],
      robotId: "",
      tableLoading: false,
      dataSource: [],
      createModal: {// 添加数据集
        visible: false,
        robotId: null
      }, 
      uploadModal: { // 编辑文档
        visible: false,
        documentId: null,
        robotId:null
      },
      currentDoc: {
        documentContent: "",
        documentId: "",
        documentName: ""
      },
      interfaceDocumentName: "",
      interfaceDocumentStatus: -1,
      currentRobotInfo: {
        robotId: "",
        robotIdentityPrompt: "",
        robotIntroduction: "",
        robotLogo: "",
        robotName: "",
        robotType: "",
        robotWelcomeMsg: "",
        isIrrelevantQuestionsBoolean: false
      },
      deleteModal: {// 删除确认弹窗
        visible: false,
        ids: null
      },
    }
  };

  // 添加数据集
  handleShowCreateModal=()=>{
    const {robotId }=this.state
    this.setState({ createModal: { robotId,visible: true } })
  }
  
  // 编辑文档
  handleIsShowEditModal = (record: any) => {
    const {uploadModal,robotId }=this.state
    this.setState({ uploadModal:{...uploadModal, visible: true, robotId, ...record } })
  }

  // 关闭 添加数据集弹窗
  handleCloseCreateModal=()=>{
    this.setState({ createModal: {robotId:null,visible: false }})
  }

  // 关闭 编辑文档弹窗
  handleCloseUpdateModal =()=>{
    this.setState({ uploadModal:{ visible: false, robotId:null,documentId:null } })
  }

  // 文档名称输入框
  handleInterfaceDocNameChange = (e: any) => {
    this.setState({ interfaceDocumentName: e.target.value })
  }

  // 全部状态切换
  handleInterfaceDocStatus = (interfaceDocumentStatus: number) => {
    this.setState({ interfaceDocumentStatus })
  }

  // 重置
  handleResetParams = () => {
    this.setState({ interfaceDocumentName: "", interfaceDocumentStatus: -1 }, () => {
      this.handleQueryRobotDocumentList();
    })
  }

  // 取消删除
  handleDeleteModalCancel = () => {
    this.setState({ deleteModal: {ids:null,visible: false } })
  }

  // 确认删除
  handleDeleteModalOk = () => {
    this.handleDeleteRequest()
  }
  
  // 批量删除
  handleDeleteRequest = () => {
    try {
      const { robotId,deleteModal } = this.state;
      // return
      const data = {
        robotId,
        documentIdList:deleteModal.ids
      }

      api_delete_robot_document(data).then(() => {
        message.open({ type: "success", content: i18next.t("common.delete-success") });
        this.setState({ selectedRowKeys: [], selectedRows: [],deleteModal: {ids:null,visible: false} }, () => {
          this.handleQueryRobotDocumentList();
        })
      }).catch((err: any) => console.log(err))
    } catch (error) {
      console.log(error)
    }
  }

  // 点击批量删除
  handleClickBatchDelete = () => {
    this.setState({ deleteModal: { ids:this.state.selectedRows.map((d: any) => d.documentId),visible: true } })
  }

  // 单个删除
  handleDeleteRow = (record: any) => {
    this.setState({ deleteModal: { ids: [record.documentId],visible: true} })
  }

  // 查询列表
  handleQueryRobotDocumentList = () => {
    const { robotId, interfaceDocumentName, interfaceDocumentStatus } = this.state;

    this.setState({ tableLoading: true });

    if (robotId) {
      const data = {
        seq: 0,
        robotId,
        documentName: interfaceDocumentName,
        status: -1,
      };

      api_query_robot_document(data).then((result: any) => {
        let documentList: TableColumnsFace[] = [];
        let documentkey: number = 0;
        result.forEach((d: TableColumnsFace, index: number) => {
          const status = d.embeddingSearchStatus
          if (interfaceDocumentStatus === -1) {
            d.key = documentkey.toString();
            documentkey = documentkey + 1;
            documentList.push(d);
          } else if (interfaceDocumentStatus === 0) {
            if (status === 0 || status === 2) {
              d.key = documentkey.toString();
              documentkey = documentkey + 1;
              documentList.push(d);
            }
          } else if (interfaceDocumentStatus === 1) {
            if (status === 1) {
              d.key = documentkey.toString();
              documentkey = documentkey + 1;
              documentList.push(d);
            }
          } else if (interfaceDocumentStatus === 2) {
            if (status === 3 || status === 4 || status === 5 || status === 6 || status === 7) {
              d.key = documentkey.toString();
              documentkey = documentkey + 1;
              documentList.push(d);
            }
          }
        });

        this.setState({ dataSource: documentList, tableLoading: false })
      }).catch((err: any) => {
        console.log(err)
        this.setState({ tableLoading: false })
      })
    }
  }

  handleTableSelect = (selectedRowKeys: React.Key[], selectedRows: any) => {
    this.setState({ selectedRowKeys, selectedRows });
  }

  componentDidMount() {
    const params = new URLSearchParams(window.location.search);
    const robotId = params.get("robotid");
    this.setState({ robotId }, () => {
      this.handleQueryRobotDocumentList();
    })
    this.timer = setInterval(() => {
      this.handleQueryRobotDocumentList();
    }, 15000);
  }

  componentWillUnmount() {
    if (this.timer) {
      clearInterval(this.timer); // 在组件卸载前清除定时器
    }
  }
  getColumns = () => {
    const colorMap:any ={
      1: 'cyan',
      4: 'red'
    }
    let knowledgeColumns: ColumnProps<TableColumnsFace>[] = [
      { // 文档名称
        title: i18next.t(["robot-manage.document.document-name"]) || "", dataIndex: 'documentName', key: 'documentName', fixed: 'left' as 'left', minWidth: 300, ellipsis: { showTitle: true },
        render: (text: any) => <span>{text}</span>,
      },
      {
        title: i18next.t(["robot-manage.document.document-id"]) || "", dataIndex: 'documentId', key: 'documentId', width: 200
      },
      {
        title: i18next.t(["robot-manage.document.status"]) || "", dataIndex: 'embeddingSearchStatus', key: 'embeddingSearchStatus',
        render: (text: any) =>
          <Tooltip className={"knowlege-table-status-tip"}
            arrow={false}
            title={text === 0 ? i18next.t(["robot-manage.document.status-0"])
              : text === 1 ? i18next.t(["robot-manage.document.status-1"])
                : text === 2 ? i18next.t(["robot-manage.document.status-2"])
                  : text === 3 ? i18next.t(["robot-manage.document.status-3"])
                    : text === 4 ? i18next.t(["robot-manage.document.status-4"])
                      : text === 5 ? i18next.t(["robot-manage.document.status-5"])
                        : text === 6 ? i18next.t(["robot-manage.document.status-6"])
                          : text === 7 ? i18next.t(["robot-manage.document.status-7"])
                            : i18next.t(["robot-manage.document.status-4"])} >
            <Tag className={"knowlege-table-status-tag"} bordered={false}
              color={colorMap[text]|| 'purple'}>{
                text === 0 ? i18next.t(["robot-manage.document.select-status-0"])
                  : text === 1 ? i18next.t(["robot-manage.document.select-status-1"])
                    : text === 2 ? i18next.t(["robot-manage.document.select-status-0"])
                      : text === 3 ? i18next.t(["robot-manage.document.select-status-2"])
                        : text === 4 ? i18next.t(["robot-manage.document.select-status-2"])
                          : text === 5 ? i18next.t(["robot-manage.document.select-status-2"])
                            : text === 6 ? i18next.t(["robot-manage.document.select-status-2"])
                              : text === 7 ? i18next.t(["robot-manage.document.select-status-2"])
                                : i18next.t(["robot-manage.document.select-status-2"])}
            </Tag>
          </Tooltip>
      },
      {
        title: i18next.t(["robot-manage.document.characters"]) || "", dataIndex: 'characters', key: 'characters',
        sorter: (a: any, b: any) => a.characters - b.characters,
      },
      // {
      //   title: i18next.t(["robot-manage.document.hit-count"]) || "", dataIndex: 'hitCount', key: 'hitCount',
      //   sorter: (a: any, b: any) => a.hitCount - b.hitCount,
      // },
      {
        title: i18next.t(["robot-manage.document.account"]) || "", dataIndex: 'userName', key: 'userName', minWidth: 200
      },
      {
        title: i18next.t(["robot-manage.document.update-time"]) || "", dataIndex: 'updateTime', key: 'updateTime', width: 200
      },
      {
        title: i18next.t(["robot-manage.document.action"]) || "", dataIndex: 'action', key: 'action', width: 120, fixed: 'right',
        render: (_: any, record: any) => {
          // const { permissionMap } = this.props;
          const defaultItems: MenuProps['items'] = [
            {
              key: 'delete',
              label: (<div className="error-color">{i18next.t("common.delete")}</div>),
            },
          ];

          const items: MenuProps['items'] = defaultItems;

          // for (let i = 0; i < defaultItems.length; i++) {
          //   let item: any = defaultItems[i];

          //   if (permissionMap.knowledgeEditFileBtn && item.key === "edit") {
          //     items.push(item);
          //   }

          //   if (permissionMap.knowledgeSingleDeleteBtn && item.key === "delete") {
          //     items.push(item);
          //   }
          // }

          return items.length>0?(
            <div className={"custom-table-action"}>
              <Dropdown placement="bottomRight" arrow={false}
                menu={{ items, onClick: (e) => this.handleCardMoreClick(e, record) }}>
                <Svgicon svgName="more_dot" />
              </Dropdown>
            </div>
          ):null
        }
      }
    ];

    return knowledgeColumns;
  };

  handleCardMoreClick = (e: any, record: any) => {
    if (e.key === "edit") {
      this.handleIsShowEditModal(record);
    }else if (e.key === "delete") {
      // 删除
      this.handleDeleteRow(record)
    }
  }
  render() {
    const { robotId, deleteModal,uploadModal, createModal } = this.state;
    const {
      selectedRowKeys, selectedRows, dataSource, count, tableLoading,
      interfaceDocumentName, interfaceDocumentStatus, currentRobotInfo
    } = this.state;

    let knowledgeColumns = this.getColumns();
    const { permissionMap,paginationConfig } = this.props;
    const rowSelection = { selectedRowKeys, onChange: this.handleTableSelect };

    return (
      // common-wrap gray-bg
      <Flex className={"robot-knowledge-wrap common-wrap"} vertical>
        <Flex className="wrap-nav" align="center">
          <span className="wn-title">{i18next.t("robot-manage.robot-knowlege")}</span>
          {
            // permissionMap.knowledgeAddDataBtn ?
            <Flex className="wn-btns">
            <Button onClick={this.handleShowCreateModal} type="primary">{i18next.t(["robot-manage.document.add-document"])}</Button>
            </Flex>
            // :null
          }
        </Flex>
        {/* 筛选 */}
        <Flex className="wrap-filters" gap={16}>
          {/* 文档名称 */}
          <Input className="filter-input" value={interfaceDocumentName} variant="filled"
            placeholder={i18next.t(["robot-manage.document.document-name-hint"]) || ""}
            onChange={this.handleInterfaceDocNameChange}
            prefix={<Svgicon svgName="search" />}
          />
          <Select placeholder={i18next.t(["robot-manage.document.status-hint"]) || ""}
            value={interfaceDocumentStatus} variant="filled"
            suffixIcon={<Svgicon svgName="icon_select_suffix"/>}
            options={[
              { label: i18next.t(["common.status-all"]) || "", value: -1 },
              { label: i18next.t(["robot-manage.document.select-status-0"]) || "", value: 0 },
              { label: i18next.t(["robot-manage.document.select-status-1"]) || "", value: 1 },
              { label: i18next.t(["robot-manage.document.select-status-2"]) || "", value: 2 },
            ]}
            onChange={this.handleInterfaceDocStatus}
          />
          <Button type="primary" onClick={this.handleQueryRobotDocumentList}>{i18next.t(["common.query"]) || ""}</Button>
          <Button color="default" variant="filled" onClick={this.handleResetParams}>{i18next.t(["common.reset"]) || ""}</Button>
          {
            selectedRows.length > 0  ?
              <Button onClick={() => this.handleClickBatchDelete()} type={"primary"} color="danger" variant="filled">{i18next.t("robot-manage.document.multi-delete-document")}</Button> 
              : null
          }
        </Flex>
        {/* 列表 */}
        <div className="wrap-content">
          <Table columns={knowledgeColumns}
            className="table-with-pagination"
            scroll={{ x: 1500 }}
            dataSource={dataSource}
            rowSelection={rowSelection}
            loading={{spinning:tableLoading,size:'large'}}
            pagination={{
              ...paginationConfig,
              total: dataSource.length
            }}
            locale={{ emptyText: !tableLoading?<Empty className="page-empty" image={null} description={
              <><div>{i18next.t('robot-manage.no-knowlege')}</div>
                <div>{i18next.t('robot-manage.no-knowlege-desc')}</div>
              </>}/>:null }}
          />
        </div>
        {// 添加数据集
          createModal.visible ?
          <CreateDocument visible={createModal.visible} resource={createModal} onClose={this.handleCloseCreateModal} onSucess={this.handleQueryRobotDocumentList} />: null
        }
        {// 编辑文档
          uploadModal.visible ?
          <UpdateDocument visible={uploadModal.visible} resource={uploadModal} onClose={this.handleCloseUpdateModal} onSucess={this.handleQueryRobotDocumentList} />: null
        }
        { // 删除确认弹窗
          <Modal width={272} title={i18next.t("robot-manage.confirm-del")} maskClosable={false} centered closable={false}
            okText={i18next.t("app.ok")} cancelText={i18next.t("app.cancel")} wrapClassName="common-confirm-modal"
            open={deleteModal.visible} onCancel={() => this.handleDeleteModalCancel()} onOk={() => this.handleDeleteModalOk()}>
            <div>{i18next.t("robot-manage.confirm-delete-tip")}</div>
          </Modal>
        }
      </Flex>
    )
  }
}

const mapStateToProps = (state: any) => ({
  userInfo: state.app.userInfo,
  permissionMap: state.app.permissionMap,
  themeInfo: state.app.themeInfo,
  paginationConfig:state.app.paginationConfig
});

const mapDispatchToProps = (dispatch: (e: any) => void) => ({
  dispatch_api_update_robot_document: (data = api_update_robot_document_params) => dispatch(dispatch_api_update_robot_document(data)),
});

export default connect(mapStateToProps, mapDispatchToProps)(RobotSettingKnowledge);
