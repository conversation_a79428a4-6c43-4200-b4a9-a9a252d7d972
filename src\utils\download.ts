export const downloadImage = (url: string, filename: string) => {
    try {
        return new Promise<void>(async (resolve, reject) => {
            const response = await fetch(url);
            const blob = await response.blob();
            const blobUrl = URL.createObjectURL(blob);

            const link = document.createElement('a');
            link.href = blobUrl;
            link.download = filename;

            document.body.appendChild(link);
            link.click();
            resolve()
            document.body.removeChild(link);

            URL.revokeObjectURL(blobUrl); // 释放内存
        })
    } catch (error) {
        console.log(error)
    }
};

// 下载视频
export const downloadVideo = (videoUrl: string, name: string) => {
    const link = document.createElement('a');
    link.href = videoUrl;
    link.download = name || 'video.mp4'; // 设置下载文件名
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
};

// 下载文件
export const downloadFile = ( name: string, url: string,) => {
    // 创建隐藏的下载链接
    const link = document.createElement('a');
    link.href = url;
    link.download = name || 'file'; // 默认文件名
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
};