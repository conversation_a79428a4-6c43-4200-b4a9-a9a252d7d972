1、说明： jsonToExcel 和 excelToJson 是自定义方法，用于excel 和 JSON 文件转换

2、使用方法：
    1）请确保当前系统目录（i18n/locales）下的语言自定义文件 zh.json、hk.json、en.json 存在，且key值一致
    2）执行顺序：
        a. 先执行 jsonToExcel.js , 终端执行命令为 node jsonToExcel.js
           生成 VAMA Translations.xlsx，位置在jsonToExcel 目录下（可供“相关翻译人员” 使用）

        b. 再执行  excelToJson.js, 终端执行命令为 node excelToJson.js
           生成 zh.json、hk.json、en.json 语言文件，位置在excelToJson目录下（生成的文件可用于当前系统）